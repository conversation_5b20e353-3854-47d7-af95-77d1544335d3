<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Holiday_calender extends Model
{
    use HasFactory;

    protected $fillable = [
        
        'holiday_name',
        'location_id',
        'department_id',
        'team_id',
        'holiday_start_date',
        'holiday_end_date',
        'day_of_week',
        'days',
        'created_by',
        'updated_by',
    ];
    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function teams()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function locations()
    {
        return $this->belongsTo(Location::class, 'location_id');
    }
}
