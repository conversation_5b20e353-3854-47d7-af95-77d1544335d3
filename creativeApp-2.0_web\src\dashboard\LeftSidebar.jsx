import React, { useEffect, useState } from "react";
import { Link, Navigate, useLocation, useNavigate } from "react-router-dom";
import Avatar from "./../assets/images/avatar.png";
import { useRoleBasedAccess } from "../common/useRoleBasedAccess";
import { API_URL } from './../common/fetchData/apiConfig';
import useFetchApiData from "./../common/fetchData/useFetchApiData.jsx";

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

const LeftSidebar = ({ isOpen }) => {
  const location = useLocation(); // Get the current location
  const [controlCenterOpen, setControlCenterOpen] = useState(true);
  const [teamManagementOpen, setTeamManagementOpen] = useState(true);
  const [taskAndProjectManagementOpen, setTaskAndProjectManagementOpen] = useState(true);
  const [resourceAndProjectManagementOpen, setResourceAndProjectManagementOpen] = useState(true);
  const [toolsAndProjectManagementOpen, setToolAndProjectManagementOpen] = useState(true);
  const [taskDetailsOpen, setTaskDetailsOpen] = useState(true);
  const [timeCardOpen, setTimeCardOpen] = useState(true);
  const [timeLoomOpen, setTimeLoomOpen] = useState(true);
  const [attendanceOpen, setAttendanceOpen] = useState(true);
  const [holidayOpen, setHolidayOpen] = useState(true);
  const [helpmenu, setHelpMenu] = useState(true);

  const { rolePermissions } = useRoleBasedAccess();


  // Function to check if the current path matches the link's path
  const isActive = (path) => {
    return location.pathname === path ? "bg-primarySeafoam dark:bg-gray-600 text-gray-900 dark:text-gray-200 font-bold" : "text-gray-700";
  };

  return (
    <div className="flex flex-col justify-between h-[100vh] overflow-y-auto scrollbar-vertical dark:bg-gray-800 sticky top-0">
      <nav className="navbar-toggle">
        {/* Control Center Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() => setControlCenterOpen(!controlCenterOpen)}
            >
              Control Center
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>
          {controlCenterOpen && (
            <>
              <li className="">
                <Link
                  to="/"
                  className={`px-4 py-1 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-2 text-neutral-500 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    view_comfy_alt
                  </span>
                  <div
                    className={`min-w-48 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Dashboard
                  </div>
                </Link>
              </li>
              
                <li className="">
                  <Link
                    to="/quickaccesshub"
                    className={`px-4 py-1 text-neutral-500 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-2 dark:text-gray-400 dark:hover:text-gray-300 ${isActive(
                      "/quickaccesshub"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      rocket_launch
                    </span>
                    <div
                      className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Quick Access Hub
                    </div>
                  </Link>
                </li>




                <li>
                <div
                  className="px-2 py-1 text-neutral-500 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-2 dark:text-gray-400 dark:hover:text-gray-300"
                  onClick={() => setHolidayOpen(!holidayOpen)}
                >
                  <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                  beach_access
                  </span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Holiday Calender
                  </div>
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    remove
                  </span>
                </div>
              </li>
              {holidayOpen && (
                <div className="px-4">
                  <li className="">
                    <Link
                      to="/holidaycalenders"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/holidaycalenders"
                      )}`}
                    >
                      <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                      overview
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                       Holiday Overview
                      </div>
                    </Link>
                  </li>
                  
                  <li className="">
                    <Link
                      to="/events"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/events"
                      )}`}
                    >
                      <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                      person_celebrate
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Happenings
                      </div>
                    </Link>
                  </li>
                  {/* <li className="">
                    <Link
                      to="/seat-plan"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/events"
                      )}`}
                    >
                      <span class="material-symbols-outlined">
                      chair
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Seat Plan
                      </div>
                    </Link>
                  </li> */}
                </div>
              )}










           
              
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  pinboard_unread
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Notice Board 
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-outlined">
                    {" "}
                    sentiment_very_satisfied{" "}
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Surprise Me
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-outlined">data_check</span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-400 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Notification Log
                  </div>
                </Link>
              </li>
            </>
          )}
        </ul>
        
        {/* Team and People Management Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left  font-bold text-sm text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() => setTeamManagementOpen(!teamManagementOpen)}
            >
              Team and People Management
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>

          {teamManagementOpen && (
            <>
              <ul>
              {/* {rolePermissions.hasManagerRole && (
                <li className="">
                  <Link
                    to="/member-onboard"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/member-onboard"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      group_add
                    </span>
                    <div
                      className={`min-w-48 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Member Onboarding
                    </div>
                  </Link>
                </li>
              )} */}

                <li className="">
                  <Link
                    to="/team-members"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/team-members"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      diversity_1
                    </span>
                    <div
                      className={`min-w-48 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Team Members
                    </div>
                  </Link>
                </li>

                {rolePermissions.hasTeamLeadRole && (
                  <li>
                    <Link
                      to="/member-index"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive("/member-index")}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                        diversity_2
                      </span>
                      <div className={`min-w-48 ${isOpen ? "block" : "hidden"} sidebarToggle text-sm`}>
                        Member Index
                      </div>
                    </Link>
                  </li>
                )}

                <li className="">
                  <Link
                    to="/team-snapshot"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/team-snapshot"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      tenancy
                    </span>
                    <div
                      className={`min-w-48 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Team Snapshot
                    </div>
                  </Link>
                </li>
                {/* <li>
                  <Link
                    to="/shift-plan"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/shift-plan"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      diversity_2
                    </span>
                    <div
                      className={`min-w-48 font-bold  ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Shift Planner
                    </div>
                  </Link>
                </li> */}
                {rolePermissions.hasShiftLeadRole && (
                <li>
                  <Link
                    to="/schedule-planners"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/schedule-planners"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    view_timeline
                    </span>
                    <div
                      className={`min-w-48 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Schedule Planner
                    </div>
                  </Link>
                </li>
                )}

                <li>
                <div
                  className="px-3 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800"
                  onClick={() => setAttendanceOpen(!attendanceOpen)}
                >
                  <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                  
co_present

                  </span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Attendance
                  </div>
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    remove
                  </span>
                </div>
              </li>
              {attendanceOpen && (
                <div className="px-4">
                  <li className="">
                    <Link
                      to="/attendance"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/attendance"
                      )}`}
                    >
                      <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}
                      >
                      check_in_out
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        In Out Break
                      </div>
                    </Link>
                  </li>
                  
                  {rolePermissions.hasAdminRole && (
                  <li className="">
                    <Link
                      to="/attendance-formation"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/attendance-formation"
                      )}`}
                    >
                      <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`} >
                      settings_applications
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Attendance Formation
                      </div>
                    </Link>
                  </li>
                  )}

                </div>
              )} 

                <li className="">
                  <Link
                    to="/welcome"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/welcome"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    cake
                    </span>
                    <div
                      className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Birthday
                    </div>
                  </Link>
                </li>

                <li className="">
                  <Link
                    to="/welcome"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/welcome"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    bloodtype
                    </span>
                    <div
                      className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      Blood Bank
                    </div>
                  </Link>
                </li>
              </ul>
            </>
          )}
        </ul>

        {/* Team and Project Management Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left  font-bold text-sm text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() =>
                setTaskAndProjectManagementOpen(!taskAndProjectManagementOpen)
              }
            >
              Task and Project Management
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>

          {taskAndProjectManagementOpen && (
            <>
             {rolePermissions.hasShiftLeadRole && (
              <li>
                <div
                  className="px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center  justify-between gap-4"
                  onClick={() => setTaskDetailsOpen(!taskDetailsOpen)}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">work</span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Task Details
                  </div>
                  <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                    remove
                  </span>
                </div>
              </li>
             )}
             
              {taskDetailsOpen && (
                <div className="px-4">
                  {rolePermissions.hasShiftLeadRole && (
                    <li className="">
                      <Link
                        to="/reporters"
                        className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive("/reporters")}`}
                      >
                        <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                          group
                        </span>
                        <div
                          className={`min-w-48 ${isOpen ? "block" : "hidden"} sidebarToggle text-sm`}
                        >
                          Reporter Directory
                        </div>
                      </Link>
                    </li>
                  )}

                  {rolePermissions.hasShiftLeadRole && (
                  <li className="">
                    <Link
                      to="/task-records"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/task-records"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      assignment_add
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Add Task Details
                      </div>
                    </Link>
                  </li>
                  )}

                  {rolePermissions.hasTeamLeadRole && (
                  <li className="">
                    <Link
                      to="/formation"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/formation"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      filter_vintage
                      </span>
                      <div
                        className={`min-w-48 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Task Details Formation
                      </div>
                    </Link>
                  </li>
                  )}

                </div>
              )}


          {rolePermissions.hasShiftLeadRole && (
              <li>
                <div
                  className="px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center  justify-between gap-4"
                  onClick={() => setTimeCardOpen(!timeCardOpen)}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  more_time
                  </span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Time Cards
                  </div>
                  <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                    remove
                  </span>
                </div>
              </li>
             )}
             
              {timeCardOpen && (
                <div className="px-4">
                   {rolePermissions.hasTeamMemberRole && (
                <li className="">
                  <Link
                    to="/time-cards"
                    className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                      "/time-cards"
                    )}`}
                  >
                    <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    history_edu
                    </span>
                    <div
                      className={`min-w-48 ${isOpen ? "block" : "hidden"
                        } sidebarToggle text-sm`}
                    >
                      RAW Data
                    </div>
                  </Link>
                </li>
                )}

                  {rolePermissions.hasShiftLeadRole && (
                  <li className="">
                    <Link
                      to="/welcome"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/welcome"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      data_table
                      </span>
                      <div
                        className={`min-w-48 text-gray-300 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Data Archive
                      </div>
                    </Link>
                  </li>
                  )}

                  {rolePermissions.hasTeamLeadRole && (
                  <li className="">
                    <Link
                      to="/welcome"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/welcome"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      bar_chart
                      </span>
                      <div
                        className={`min-w-48 text-gray-300 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Summery Report
                      </div>
                    </Link>
                  </li>
                  )}

                </div>
              )}

              

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  add_task
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Tasky
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  editor_choice
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Achieve+
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  article
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Quick Note
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    calendar_today
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Calendar
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  bug_report
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    QA Pool
                  </div>
                </Link>
              </li>
            </>
          )}
        </ul>
        {/* Recources and Document Management Dropdown*/}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left  font-bold text-sm whitespace-nowrap text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() =>
                setResourceAndProjectManagementOpen(
                  !resourceAndProjectManagementOpen
                )
              }
            >
              Resources & Document Managements
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>

          {resourceAndProjectManagementOpen && (
            <>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  google_home_devices
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Device Inventory
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  home_storage
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Documents Hub
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  school
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Training Management
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    manage_history
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    KPI Management
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    summarize
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Census Report
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    payments
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Payroll
                  </div>
                </Link>
              </li>
            </>
          )}
        </ul>

        {/* Tools & Utilities Dropdown)}*/}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left  font-bold text-sm text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() =>
                setToolAndProjectManagementOpen(!toolsAndProjectManagementOpen)
              }
            >
              Tools & Utilities
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>
        </ul>

        {/* Settings Link */}
        <ul className="flex justify-start flex-col text-left">
          {toolsAndProjectManagementOpen && (
            <>
              <li>
                <div
                  className="px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center  justify-between gap-4"
                  onClick={() => setTimeLoomOpen(!timeLoomOpen)}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  share_eta
                  </span>
                  <div
                    className={`min-w-48 font-bold cursor-pointer dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Time Loom
                  </div>
                  <span className={`material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800 ${isOpen ? "block" : "hidden"
                        } sidebarToggle`}>
                    remove
                  </span>
                </div>
              </li>
              {timeLoomOpen && (
                <div className="px-4">
                  <li className="">
                    <Link
                      to="/world-time"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/world-time"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                        globe
                      </span>
                      <div
                        className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        World Time
                      </div>
                    </Link>
                  </li>
                  <li className="">
                    <Link
                      to="/time-zone-convert"
                      className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                        "/time-zone-convert"
                      )}`}
                    >
                      <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                      routine
                      </span>
                      <div
                        className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                          } sidebarToggle text-sm`}
                      >
                        Time Zone Converter
                      </div>
                    </Link>
                  </li>
                </div>
              )}

              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                    encrypted
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Password Manager
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  wand_stars
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    External Tools
                  </div>
                </Link>
              </li>

              {rolePermissions.hasAdminRole && (
              <li className="">
                <Link
                  to="/settings"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/settings"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">tune</span>
                  <div
                    className={`min-w-48 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    App Configaration
                  </div>
                </Link>
              </li>
              )}
              
            </>
          )}
        </ul>

        {/* Control Help Dropdown */}
        <ul className="flex justify-start flex-col text-left">
          <div className="flex flex-row justify-between px-4 pt-2 border-b border-gray-300 dark:border-gray-600 cursor-pointer">
            <h5
              className={`w-full text-left font-bold text-sm text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 ${isOpen ? "block" : "hidden"
                } sidebarToggle`}
              onClick={() => setHelpMenu(!helpmenu)}
            >
              Help
            </h5>
            <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
              keyboard_arrow_down
            </span>
          </div>
          {helpmenu && (
            <>
              <li className="">
                <Link
                  to="/welcome"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/welcome"
                  )}`}
                >
                  <span className="material-symbols-rounded text-xl dark:text-gray-400 dark:hover:text-gray-800">
                  speed_camera
                  </span>
                  <div
                    className={`min-w-48 text-gray-300 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Activity Log
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/change-log"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/change-log"
                  )}`}
                >
                  <span className="material-symbols-outlined">format_list_bulleted</span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Change Log
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/app-support"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/app-support"
                  )}`}
                >
                  <span className="material-symbols-outlined">support_agent</span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    App Support
                  </div>
                </Link>
              </li>
              <li className="">
                <Link
                  to="/about-the-app"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/about-the-app"
                  )}`}
                >
                  <span className="material-symbols-outlined">menu_book</span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    About The App
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/report-problem"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/report-problem"
                  )}`}
                >
                  <span className="material-symbols-outlined"> report </span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Report Problem
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/give-feedback"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/give-feedback"
                  )}`}
                >
                  <span className="material-symbols-outlined">forum</span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Give Feedback
                  </div>
                </Link>
              </li>

              <li className="">
                <Link
                  to="/creative-tools"
                  className={`px-4 py-3 hover:bg-gray-200 dark:hover:bg-gray-500 flex flex-row items-center gap-4 dark:text-gray-400 dark:hover:text-gray-800 ${isActive(
                    "/creative-tools"
                  )}`}
                >
                  <span className="material-symbols-outlined">forum</span>
                  <div
                    className={`min-w-48 dark:text-gray-300 ${isOpen ? "block" : "hidden"
                      } sidebarToggle text-sm`}
                  >
                    Creative Tools
                  </div>
                </Link>
              </li>
            </>
          )}
        </ul>
      </nav>
    </div>
  );
};

export default LeftSidebar;
