import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import { useNavigate } from 'react-router-dom';
import EditTeam from './EditTeam';
import TablePagination from '../../common/table/TablePagination';

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TeamList = ({ searchTerm }) => {
  const [teams, setTeams] = useState([]);
  const [error, setError] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState(null);
  const [filteredTeams, setFilteredTeams] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;

  const navigate = useNavigate();

  const columnNames = [
    { label: "SL", key: "id" },
    { label: "Created Date", key: "created_at" },
    { label: "Icon", key: "icon" },
    { label: "Logo", key: "logo" },
    { label: "Department", key: "department" },
    { label: "Team Name", key: "name" },
    { label: "POC", key: "poc" },
    { label: "Manager", key: "manager" },
    { label: "Team Lead", key: "team_lead" },
    { label: "Workday", key: "workday" },
    { label: "Launch Date", key: "launch" },
  ];

  // Fetch teams data
  useEffect(() => {
    const fetchTeams = async () => {
      if (!isTokenValid()) {
        setError('No authentication token found.');
        return;
      }
  
      const token = localStorage.getItem('token');
  
      try {
        const response = await fetch(`${API_URL}/teams`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
  
        const data = await response.json();
        console.log('Teams Data', data);
  
        const mappedTeams = data.teams.map(team => ({
          id: team.id,
          created_at: team.created_at,
          icon: team.icon,
          logo: team.logo,
          department: team.departments.length > 0 ? team.departments[0].name : '',
          name: team.name,
          poc: team.poc,
          manager: team.manager,
          team_lead: team.team_lead,
          workday: team.workday,
          launch: team.launch,
        }));
  
        setTeams(mappedTeams);
        setFilteredTeams(mappedTeams);
  
      } catch (error) {
        setError(error.message);
      }
    };
  
    fetchTeams();
  }, [currentPage, itemsPerPage]);

  useEffect(() => {
    const normalizedSearchTerm = searchTerm.toLowerCase().trim();

    if (!normalizedSearchTerm) {
      setFilteredTeams(teams);
      return;
    }

    const filtered = teams.filter(team => {
      return Object.values(team).some(value =>
        value && value.toString().toLowerCase().includes(normalizedSearchTerm)
      );
    });

    setFilteredTeams(filtered);
  }, [searchTerm, teams]);

  // Pagination logic
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentPageTeams = filteredTeams.slice(startIndex, startIndex + itemsPerPage);

  const handleDelete = async (id) => {
    if (!isTokenValid()) {
      setError('No authentication token found.');
      return;
    }

    const token = localStorage.getItem('token');

    try {
      const response = await fetch(`${API_URL}/teams/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete team: ' + response.statusText);
      }

      setTeams(prevTeams => prevTeams.filter(team => team.id !== id));
    } catch (error) {
      setError(error.message);
    }
  };

  const handleEdit = (id) => {
    setSelectedTeamId(id);
    setModalVisible(true);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }
  
  return (
    <div>
      <TableContent
        tableContent={currentPageTeams} // Pass filtered and paginated teams
        columnNames={columnNames}
        onDelete={handleDelete}
        onEdit={handleEdit}
        setModalVisible={setModalVisible}
        setSelectedServiceId={setSelectedTeamId}
        renderCustomCell={(column, team) => {
            if (column.key === 'icon' && team.icon) {
              const iconSrc = team.icon.startsWith('images/')
                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${team.icon}`
                : team.icon;
              return <img src={iconSrc} alt="Team Icon" className="w-6 object-cover" />;
            }
            if (column.key === 'logo' && team.logo) {
              const logoSrc = team.logo.startsWith('images/')
                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${team.logo}`
                : team.logo;
              return <img src={logoSrc} alt="Team Logo" className="w-32 object-cover" />;
            }
            return team[column.key];
          }}
      />
      <TablePagination
        currentPage={currentPage}
        totalItems={filteredTeams.length}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
      />
      {modalVisible && (
        <EditTeam
          isVisible={modalVisible}
          setVisible={setModalVisible}
          teamId={selectedTeamId}
        />
      )}
    </div>
  );
};

export default TeamList;
