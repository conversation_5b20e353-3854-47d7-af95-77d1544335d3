import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from "./EditorToolbar";
import 'react-quill/dist/quill.snow.css';
import { alertMessage } from '../../common/coreui';

import {API_URL} from './../../common/fetchData/apiConfig.js';


const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddAboutTheApp = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [definition, setDefinition] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const handleSubmit = async (event) => {
        event.preventDefault();

        if ( !definition) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            const response = await fetch(`${API_URL}about-the-app`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    definition,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to add about the app.');
            }

            const result = await response.json();
            //setSuccessMessage('About the App added successfully!');

            alertMessage('success');

            setDefinition('');
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-about-the-app';

    const handleClose = () => {
        navigate('/about-the-app');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-2xl relative overflow-y-auto max-h-[80vh]">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New About The App</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="definition" className="block text-sm font-medium text-gray-700 pb-4">
                                    Definition
                                </label>
                                <EditorToolbar />
                                <ReactQuill
                                    id="definition"
                                    value={definition}
                                    onChange={setDefinition}
                                    className="bg-white border border-gray-300 rounded-md shadow-sm"
                                    theme="snow"
                                    modules={modules}
                                    formats={formats}
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add About The App
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddAboutTheApp;
