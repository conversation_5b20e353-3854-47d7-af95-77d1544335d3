import React, { useEffect, useState } from 'react';
import Avatar from './../assets/images/avatar.png';
import { Link } from 'react-router-dom';
import ContactNav from '../pages/team-contact/ContactNav';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TeamContacts = () => {
    const [userData, setUserData] = useState([]); // Store user data (for all teams)
    const [teamData, setTeamData] = useState([]); // Store team data
    const [error, setError] = useState(null); // Handle errors
    const [loading, setLoading] = useState(true); // Loading state
    const [searchQuery, setSearchQuery] = useState(''); // State to handle search input
    const [selectedTeam, setSelectedTeam] = useState(null); // Store selected team
    const [selectedTeamUsers, setSelectedTeamUsers] = useState([]); // Store users of selected team

    // Fetch teams data from API
    useEffect(() => {
        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch teams: ' + response.statusText);
                }

                const data = await response.json();
                setTeamData(data); // Set fetched team data
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTeams();
    }, []);

    // Handle search query changes
    const onSearch = (query) => {
        setSearchQuery(query);
    };

    // Handle team selection (passed from ContactNav)
    const onSelectTeam = (teamId) => {
        // Find the selected team based on its ID
        const team = teamData.find((team) => team.id === teamId);
        if (team) {
            setSelectedTeam(team); // Update the selected team
            setSelectedTeamUsers(team.users); // Set the users of the selected team
            console.log(`Selected team: ${teamId}`); // Log the selected team ID
        }
    };

    // Filter users based on search query
    const filteredUsers = selectedTeamUsers
        .filter(user =>
            `${user.fname} ${user.lname}`.toLowerCase().includes(searchQuery.toLowerCase())
        );

    // If the data is still loading, show a loading message
    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // If an error occurs, display the error message
    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div className='bg-white dark:bg-gray-900 rounded-xl p-4'>
            <div className='border border-gray-200 rounded-xl flex justify-start items-start flex-row'>
                {/* Contacts Navigation */}
                <div className='text-left w-1/4 p-6'>
                    <h4 className='text-xl font-medium pb-8'>Your Contacts</h4>
                    <ContactNav 
                        teams={teamData} // Pass teams data to ContactNav
                        onSelectTeam={onSelectTeam} // Pass the team selection handler
                    />
                </div>
                {/* All Contacts */}
                <div className='border-x border-gray-200 p-6 w-1/3 h-[80vh]'>
                    {/* Search Bar */}
                    <form className="flex items-center pb-4">
                        <label htmlFor="simple-search" className="sr-only">Search</label>
                        <div className="relative w-full">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <input
                                type="text"
                                id="simple-search"
                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search"
                                onChange={(e) => onSearch(e.target.value)} // Update search query on change
                            />
                        </div>
                    </form>
                    {/* Contact List */}
                    <div className="rounded-xl">
                        {filteredUsers.length > 0 ? (
                            <div>
                                {filteredUsers.map((user) => (
                                    <Link key={user.id} to={`/user/${user.id}`} className="flex flex-row items-center justify-start gap-4 text-left mb-4 relative py-1">
                                        <div className="relative">
                                            <img src={Avatar} alt="Profile" className="m-auto text-sm w-12" />
                                            <span className="flex w-4 h-4 bg-green-500 rounded-full absolute top-0 left-9 border-2 border-white"></span>
                                        </div>
                                        <div className="block">
                                            <h4 className="text-left font-bold text-sm whitespace-nowrap">{user.fname} {user.lname}</h4>
                                            <p className="text-left">
                                                {user?.designations?.[0]?.name || "Designation not found"}
                                            </p>
                                        </div>
                                        <span className="material-symbols-rounded text-xl absolute right-0 text-gray-400">award_star</span>
                                    </Link>
                                ))}
                            </div>
                        ) : (
                            <div>No users found for this team</div>
                        )}
                    </div>
                </div>
                {/* Contact Profile */}
                {userData ? (
                    <div className='p-6'>
                        <div className="flex flex-row justify-between">
                            <div className="flex flex-row justify-center gap-8 items-center">
                                <div>
                                    <img src={Avatar} alt="Profile" className="w-38" />
                                </div>
                                <div className="text-left">
                                    <h3 className="text-2xl font-medium pb-3">
                                        {userData.fname} {userData.lname}
                                    </h3>
                                    <p className="text-xl pb-4">{userData?.designations?.[0]?.name}</p>
                                    <span className="bg-gray-200 p-3 text-primary text-base rounded-full block text-center">
                                        {userData?.resource_types?.[0]?.name || "Role not found"}
                                    </span>
                                </div>
                            </div>
                        </div>   
                        <div className="flex flex-row justify-between p-4 mt-6">
                            <div className="flex flex-row justify-center gap-8 items-center">
                            <div className="w-full rounded-lg">
                                <div className="flex flex-row justify-between items-start flex-wrap gap-6">
                                {/* Joining date */}
                                <div className="text-left min-w-[45%]">
                                    <p className="text-gray-700">Email</p>
                                    <p className="text-gray-900 font-medium">{userData.email}</p>
                                </div>
                                    {/* Birthdate */}
                                    <div className="text-left min-w-[45%]">
                                    <p className="text-gray-700">Emergency Contact Number</p>
                                    <p className="text-gray-900 font-medium">{userData.emergency_contact}</p>
                                    </div>
                                    {/* Primary Contact */}
                                    <div className="text-left min-w-[45%]">
                                    <p className="text-gray-700">Primary Phone Number</p>
                                    <p className="text-gray-900 font-medium">{userData.primary_contact}</p>
                                    </div>
                                    {/* Relation with Contact */}
                                    <div className="text-left min-w-[45%]">
                                    <p className="text-gray-700">Emergency Contact Relationship</p>
                                    <p className="text-gray-900 font-medium">{userData.relation_contact}</p>
                                    </div>
                                    {/* Secondary Contact */}
                                    <div className="text-left min-w-[45%]">
                                    <p className="text-gray-700">Secondary Contact Number</p>
                                    <p className="text-gray-900 font-medium">{userData.emergency_contact}</p>
                                    </div>
                                    {/* Present Address*/}
                                    <div className="text-left min-w-[100%]">
                                    <p className="text-gray-700">Present Address</p>
                                    <p className="text-gray-900 font-medium">{userData.present_address}</p>
                                    </div>
                                    {/* Present Address*/}
                                    <div className="text-left min-w-[100%]">
                                    <p className="text-gray-700">Permanent Address</p>
                                    <p className="text-gray-900 font-medium">{userData.permanent_address}</p>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>           
                    </div>
                ) : (
                    <div>No user data available</div>
                )}
            </div>
        </div>
    );
};

export default TeamContacts;
