import React, { useState, useEffect } from "react";
import Select from "react-select";
import moment from "moment";
import "tailwindcss/tailwind.css";

const getCurrentYear = () => new Date().getFullYear();

const customStyles = {
  menu: (provided) => ({
    ...provided,
    zIndex: 9999, 
  }),
  menuPortal: (base) => ({ ...base, zIndex: 9999 }),
  control: (provided) => ({
    ...provided,
    width: "100%",
    minWidth: "100%",
  }),
  input: (provided) => ({
    ...provided,
    width: "100% !important",
    minWidth: "100% !important",
    flex: "1 1 auto",
  }),
  valueContainer: (provided) => ({
    ...provided,
    width: "100%",
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    width: "auto",
  }),
};

const HOLIDAY_KEYWORDS = [
  "holiday", "new year's day", "independence day", "eid", "christmas", 
  "puja", "shab", "martyrs' day", "bengali new year", "May Day"
];

const HolidayCalenderGoogleListonepage = () => {
  const [regions, setRegions] = useState([]);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedYear, setSelectedYear] = useState({ value: getCurrentYear(), label: getCurrentYear().toString() });
  const [holidays, setHolidays] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const API_KEY = process.env.REACT_APP_GOOGLE_API_KEY || "AIzaSyBuRzdDIOljoVutin7ygRZdRbcQSBfJlHY";

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const response = await fetch("https://restcountries.com/v3.1/all");
        const data = await response.json();
        
        const countryList = data
          .map((country) => ({
            value: `en.${country.cca2.toLowerCase()}`,
            label: country.name.common,
          }))
          .sort((a, b) => a.label.localeCompare(b.label));

        setRegions([{ value: "en.bd", label: "Bangladesh" }, ...countryList]);
        setSelectedRegion({ value: "en.bd", label: "Bangladesh" });
      } catch (err) {
        console.error("Failed to load countries:", err);
      }
    };

    fetchRegions();
  }, []);

  const yearOptions = Array.from({ length: 10 }, (_, i) => {
    const year = getCurrentYear() - i;
    return { value: year, label: year.toString() };
  });

  useEffect(() => {
    if (!selectedRegion || !selectedYear) return;

    const fetchHolidays = async () => {
      setLoading(true);
      setError(null);
      setHolidays([]);

      try {
        if (!API_KEY) {
          throw new Error("Google API Key is missing! Set it in your .env file.");
        }

        let calendarId = encodeURIComponent(`${selectedRegion.value}#<EMAIL>`);
        
        const calendarUrl = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events?key=${API_KEY}&timeMin=${selectedYear.value}-01-01T00:00:00Z&timeMax=${selectedYear.value}-12-31T23:59:59Z&orderBy=startTime&singleEvents=true`;

        const response = await fetch(calendarUrl);
        const data = await response.json();

        if (data.error) {
          throw new Error(data.error.message);
        }

        const formattedEvents = data.items.map((holiday) => {
          const isHoliday = HOLIDAY_KEYWORDS.some(keyword => holiday.summary.toLowerCase().includes(keyword));
          return {
            id: holiday.id,
            title: holiday.summary,
            date: new Date(holiday.start.date || holiday.start.dateTime),
            isHoliday,
          };
        });

        setHolidays(formattedEvents);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHolidays();
  }, [selectedRegion, selectedYear, API_KEY]);

  return (
    <div className="w-full p-4 bg-white">
      <h2 className="text-2xl font-bold mb-4">Events</h2>
      <div className="flex flex-col md:flex-row gap-4 mb-4 w-full">
        <div className="w-full md:w-1/2">
          <label htmlFor="year" className="block font-medium mb-2">Select a year:</label>
          <Select
            options={yearOptions}
            value={selectedYear}
            onChange={setSelectedYear}
            placeholder="Select a year"
            styles={customStyles}
            menuPortalTarget={document.body}
          />
        </div>
        <div className="w-full md:w-1/2">
          <label htmlFor="region" className="block font-medium mb-2">Select a country:</label>
          <Select
            options={regions}
            value={selectedRegion}
            onChange={setSelectedRegion}
            placeholder="Select a country"
            styles={customStyles}
            menuPortalTarget={document.body}
          />
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center text-gray-500">Loading...</div>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {holidays.length > 0 ? (
            holidays.map((holiday) => (
              <div
                key={holiday.id}
                className={`p-4 border rounded shadow-md ${
                  holiday.isHoliday ? "bg-red-300" : "bg-green-300"
                }`}
              >
                <h3 className="text-lg font-semibold">{holiday.title}</h3>
                <p className="text-gray-600">
                  {holiday.date.toDateString()}
                </p>
                <p className="text-sm text-gray-800">
                  Type: {holiday.isHoliday ? "Holiday" : "Event"}
                </p>
              </div>
            ))
          ) : (
            <p className="text-gray-500">No holidays or events found for {selectedYear?.value}.</p>
          )}
        </div>
      )}
    </div>
  );
};

export default HolidayCalenderGoogleListonepage;
