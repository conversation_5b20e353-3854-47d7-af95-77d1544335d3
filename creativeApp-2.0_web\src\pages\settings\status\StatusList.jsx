import React, { useEffect, useState } from 'react';
import useFetchApiData from '../../../common/fetchData/useFetchApiData';
import TableContent from '../../../common/table/TableContent';
import TablePagination from '../../../common/table/TablePagination';
import DateTimeFormat from '../../../common/DateTimeFormat';
import Swal from 'sweetalert2';

const StatusList = ({ fetchData, setFetchData }) => {
    // modal state
    const [isOpen, setIsOpen] = useState(false);
    const [status, setStatus] = useState([]);
    const [selectedTeamId, setSelectedTeamId] = useState(null);
    // for drop down 
    const [openDropdown, setOpenDropdown] = useState(null);
    // for hiding delete button
    const [hideDeleteButton, serHideDeleteButton] = useState(false);


    // pagination
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 4;
    const token = localStorage.getItem('token');
    const API_URL = process.env.REACT_APP_BASE_API_URL;
    const statusData = useFetchApiData(`${API_URL}/status`, token, fetchData);

    useEffect(() => {
        if (statusData.data) {
            setStatus(statusData.data.status);
            setFetchData(false);
        }
    }, [statusData.data])

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Status", key: "name" },
        { label: "Tailwind Background Class", key: "bg_cls" },
        { label: "Tailwind text color Class", key: "text_cls" },
        { label: "Created Date", key: "created_at" },
    ];
    const isTokenValid = () => {
        const token = localStorage.getItem('token');
        return token !== null;
    };
    const handleDelete = async (id) => {

        /*if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/status/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete status: ' + response.statusText);
            }

            setTeams(prevTeams => prevTeams.filter(team => team.id !== id));
        } catch (error) {
            setError(error.message);
        }*/

        Swal.fire({
            title: 'Warring',
            text: `Wow! You are not authentic user to delete this Data`,
            icon: 'warning',
            showConfirmButton: true,
            confirmButtonColor: '#DC2626',
        })
    };

    const handleEdit = (id) => {
        /*setSelectedTeamId(id);
        setIsOpen(true);*/
    };
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };
    // drop down
    const toggleDropdown = (id) => {
        setOpenDropdown((prevId) => (prevId === id ? null : id));
    };
    return (
        <>
            {/* <TableContent
                tableContent={status} // Pass filtered and paginated teams
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setIsOpen}
                setSelectedServiceId={setSelectedTeamId}
            /> */}

            <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        {columnNames.map((col, index) => (
                            <th key={index} scope="col" className="px-4 py-3 whitespace-nowrap">
                                {col.label}
                            </th>
                        ))}
                        <th scope="col" className="px-4 py-3">
                            <span className="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                {/* table row render */}
                <tbody>
                    {status.map((tableData) => (
                        <tr key={tableData.id} className="border-b dark:border-gray-700 relative">
                            {columnNames.map((col, index) => (
                                col.key === 'name' ?
                                    <td key={index} className="px-4 py-3 whitespace-nowrap">
                                        <span className={`${tableData.bg_cls} ${tableData.text_cls} p-1 px-2 rounded-xl`}> {tableData[col.key]}</span>
                                    </td>
                                    :
                                    <td key={index} className="px-4 py-3 whitespace-nowrap">
                                        {(col.key === 'created_at'
                                            ? <DateTimeFormat isoString={tableData[col.key]} />
                                            : (tableData[col.key] || '-')
                                        )
                                        }
                                    </td>
                            ))}
                            <td className="px-4 py-3 flex items-center justify-end">
                                <button
                                    id={`${tableData.id}-dropdown-button`}
                                    onClick={() => toggleDropdown(tableData.id)}
                                    className="inline-flex items-center p-0.5 text-sm font-medium text-center text-gray-500 hover:text-gray-800 rounded-lg focus:outline-none dark:text-gray-400 dark:hover:text-gray-100"
                                    type="button"
                                >
                                    <span className="material-symbols-outlined">more_vert</span>
                                </button>
                                {openDropdown === tableData.id && (
                                    <div
                                        id={`${tableData.id}-dropdown`}
                                        className="z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600 absolute top-0"
                                    >
                                        <div className="relative">
                                            <button
                                                className="absolute right-1 top-1 z-20 cursor-pointer"
                                                onClick={() => toggleDropdown(null)}
                                            >
                                                <span className="material-symbols-outlined">close</span>
                                            </button>
                                            <ul
                                                className="py-1 text-sm text-gray-700 dark:text-gray-200"
                                                aria-labelledby={`${tableData.id}-dropdown-button`}
                                            >
                                                <li>
                                                    <button
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            setSelectedTeamId(tableData.id);
                                                            handleEdit(tableData.id); // Use onEdit here
                                                            setIsOpen(true);
                                                        }}
                                                        className="inline-flex items-center w-full p-0.5 text-sm font-medium text-center text-gray-500 hover:text-gray-800 rounded-lg focus:outline-none"
                                                        type="button"
                                                    >
                                                        <span className="text-left w-full block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            Edit
                                                        </span>
                                                    </button>
                                                </li>
                                            </ul>
                                            {!hideDeleteButton && ( // Hide delete button if hideDeleteButton is true
                                                <div className="py-1">
                                                    <button
                                                        onClick={() => handleDelete(tableData.id)}
                                                        className="w-full text-left block py-2 px-4 text-sm text-red-600 hover:bg-red-100 dark:hover:bg-red-600 dark:text-red-200"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>

            <TablePagination
                currentPage={currentPage}
                totalItems={status.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
        </>
    );
};

export default StatusList;