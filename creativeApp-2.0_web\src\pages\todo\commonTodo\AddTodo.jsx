import { useEffect, useState } from "react";
import { API_URL } from "../../../common/fetchData/apiConfig";
import Modal from "../../../common/modal/Modal";
import Swal from "sweetalert2";
import { Controller, useForm } from "react-hook-form";
import Select from "react-select";
import useFetchApiData from "../../../common/fetchData/useFetchApiData";

const AddTodo = () => {
    // modal state
    const [isOpen, setIsOpen] = useState(false);
    const { register, formState: { errors }, handleSubmit, control, reset } = useForm();
    const [priorityData, setPriorityData] = useState([]);
    const [statusData, setStatusData] = useState([]);
    // userToken
    const API_URL = process.env.REACT_APP_BASE_API_URL;
    const token = localStorage.getItem('token');

    // fetch data to populate dropdowns
    const users = useFetchApiData(`${API_URL}/users`, token);
    const assigneesData = users.data?.map(user => ({ value: user.id, label: user.fname, src: user.photo }));
    const pData = useFetchApiData(`${API_URL}/priorities`, token)
    const sData = useFetchApiData(`${API_URL}/status`, token)
    const tags = useFetchApiData(`${API_URL}/todo-tags`, token);
    const tagsData = tags?.data?.todoTags;
    // after fetch priority data, then set it for render
    useEffect(() => {
        if (pData.data) {
            setPriorityData(pData.data.priorities);
        }
    }, [pData.data]);
    // after fetch status data, then set it for render
    useEffect(() => {
        if (sData.data) {
            setStatusData(sData.data.status);
        }
    }, [sData.data]);



    // console.log("fetched users", users, pData, sData, tagsData)

    // react form hook
    const onSubmit = async (data) => {
        let creator_id = localStorage.getItem('user_id');
        data = { ...data, creator_id }
        console.log(data);
        reset();

        /*
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                Swal.fire({
                    title: 'Error',
                    text: 'Authentication token is missing. Please login again.',
                    icon: 'warning',
                    showConfirmButton: true,
                });
                return;
            }
            // API trigger
            const response = await fetch(`${API_URL}/todo-tag`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                throw new Error('Failed to add Tag!');
            }

            const result = await response.json();
            console.log(result);
            //fire success message alert
            Swal.fire({
                title: `${result.message}!`,
                icon: "success",
                showConfirmButton: true,
                confirmButtonColor: '#3085d6',
                // timer: 3000,
            });
            setIsOpen(false); // modal close
            reset();// This resets the form to the default values defined in useForm.
        } catch (error) {
            //fire error message alert
            Swal.fire({
                title: `${error.message}`,
                icon: "error",
                showConfirmButton: true,
                confirmButtonColor: '#DC2626',
                // timer: 3000,
            });
        }*/
    };


    return (
        <>
            <button className='w-full md:w-auto flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700'
                onClick={() => setIsOpen(true)}>
                <span className="material-symbols-rounded text-xl">add_circle_outline</span> Add New Achieve
            </button>

            <Modal isOpen={isOpen} setIsOpen={setIsOpen} ModalTitle="Create New Todo" modalWidth="w-full max-w-4xl">
                <form className="p-4 md:p-5" onSubmit={handleSubmit(onSubmit)}>
                    <div className="grid gap-4 mb-4 grid-cols-2">
                        <div className="col-span-2">
                            <label htmlFor="title" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Title</label>
                            <input
                                {...register("name", { required: 'Title is required', maxLength: { value: 80, message: "Tag name cannot exceed 80 characters" } })}
                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Task Title..." />
                            {errors.title && <p className='text-red-600 col-span-2' role="alert">{errors.title.message}</p>}
                        </div>
                        <div className="col-span-2">
                            <label htmlFor="assignees" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Assignees</label>
                            <Controller
                                name="assignees"
                                control={control}
                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"

                                render={({ field: { onChange, value } }) => (
                                    <Select
                                        inputId="assignees"
                                        options={assigneesData}
                                        isMulti
                                        // onChange={(selected) => field.onChange(selected)}
                                        // value={field.value}
                                        value={assigneesData.filter(option => value?.includes(option.value))}
                                        onChange={(selectedOptions) =>
                                            onChange(selectedOptions ? selectedOptions?.map(option => option.value) : [])
                                        }

                                    />
                                )}
                            />
                        </div>
                        <div className="col-span-1 sm:col-span-1">
                            <label htmlFor="priority" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Priority Level</label>
                            <select id="priority" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                {...register('priority_id', { required: 'priority field is required' })}>
                                <option select="">Select Priority Level</option>
                                {/* dynamic options here */}
                                {priorityData.length > 0 ? (priorityData?.map(priority => (
                                    <option key={priority?.id} value={priority?.id}>{priority?.name}</option>
                                ))) : "Loading"}
                            </select>
                            {errors.priority_id && <p className='text-red-600 col-span-2' role="alert">{errors.priority_id.message}</p>}
                        </div>
                        <div className="col-span-1 sm:col-span-1">
                            <label htmlFor="status" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                            <select id="status" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                {...register('status_id', { required: 'status field is required' })}>
                                <option select="">Select Status</option>
                                {/* dynamic options here */}
                                {statusData.length > 0 ? (statusData?.map(status => (
                                    <option key={status?.id} value={status?.id}>{status?.name}</option>
                                ))) : "Loading"}
                            </select>
                            {errors.status_id && <p className='text-red-600 col-span-2' role="alert">{errors.status_id.message}</p>}
                        </div>
                        <div className="col-span-1 sm:col-span-1">
                            <label htmlFor="date" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Due Date</label>
                            <input type="datetime-local" name="dueDate" id="dueDate" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                {...register('due_date', { required: 'Due Date is required' })} />
                            {errors.due_date && <p className='text-red-600 col-span-2' role="alert">{errors.due_date.message}</p>}
                        </div>
                        {/* <div className="col-span-1 sm:col-span-1">
                            <label htmlFor="time" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Due Time</label>
                            <div className="relative">
                                <div className="absolute inset-y-0 end-0 top-0 flex items-center pe-3.5 pointer-events-none">
                                    <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <input type="time" name="dueTime" id="dueTime" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    {...register('time', { required: 'Due time is required' })} />
                                {errors.time && <p className='text-red-600 col-span-2' role="alert">{errors.time.message}</p>}
                            </div>
                        </div> */}
                        <div className="col-span-1 sm:col-span-1">
                            <label htmlFor="tags" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tags</label>
                            <Controller
                                name="tags"
                                control={control}
                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"

                                render={({ field: { onChange, value } }) => (
                                    <Select
                                        options={tagsData}
                                        isMulti
                                        getOptionLabel={(option) => option.name}
                                        getOptionValue={(option) => option.id.toString()}
                                        value={tagsData.filter((option) => value?.includes(option.id))}
                                        onChange={(selectedOptions) =>
                                            onChange(selectedOptions ? selectedOptions.map((option) => option.id) : [])
                                        }
                                    />
                                )}
                            />
                        </div>
                        <div className="col-span-2">
                            <label htmlFor="description" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Todo Description</label>
                            <textarea id="description" rows="4" className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Write product description here"
                                {...register('details', { required: 'Description is required' })}></textarea>
                            {errors.details && <p className='text-red-600 col-span-2' role="alert">{errors.details.message}</p>}
                        </div>
                    </div>
                    <div className="flex justify-center text-center">
                        <button type="submit" className="w-full md:w-auto flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium text-white focus:outline-none bg-primary rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-black focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                            <span className="material-symbols-rounded text-xl mr-2">add_circle_outline</span>
                            Add new Todo
                        </button>
                    </div>
                </form>
            </Modal>
        </>
    );
}

export default AddTodo;