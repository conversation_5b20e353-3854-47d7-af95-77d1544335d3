import React, { useState } from 'react';
import AddReporter from '../../pages/time-card/reporter/AddReporter';
import ReporterDataList from '../../pages/time-card/reporter/ReporterDataList';

const Reporter = () => {
  const [searchTerm, setSearchTerm] = useState('');


  // Handle search input changes
  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm);
  };

//   Passing dynamic component in modal
  const reporter = <AddReporter />

  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <ReporterDataList />
    </div>
  );
};

export default Reporter;
