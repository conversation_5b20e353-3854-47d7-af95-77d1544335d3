<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log; // Import the Log facade
use App\Models\Branch; // Use the Branch model
use App\Models\User;
use App\Models\Role;
use App\Models\Location;

class BranchController extends Controller
{
    /**
     * Show all branches with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // Retrieve all branches with their associated locations
        $branches = Branch::with(['locations'])->get();
        
        // Log the number of branches retrieved
        Log::info('All Branches Retrieved:', ['branches_count' => $branches->count()]);
        
        // Return the branches as a JSON response
        return response()->json(['branches' => $branches], 200);
    }

    // Filter logic for data table
    public function branchData(Request $request)
    {
        $query = Branch::with(['creator', 'updater', 'locations',]);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedBranchName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedLocations = $request->filled('locations') ? urldecode($request->input('locations')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedUpdatedAt = $request->filled('updated_at') ? urldecode($request->input('updated_at')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering by name
        if ($decodedBranchName) {
            $names = explode(',', $decodedBranchName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering: Filter by Locations
        if ($decodedLocations) {
        $decodedLocations = array_map('trim', explode(',', $decodedLocations));
            $query->whereHas('locations', function ($q) use ($decodedLocations) {
                $q->whereIn('locations.id', $decodedLocations);
            });        
        }

        // Filtering by created_at
        if ($decodedCreatedAt) {
            $decodedCreatedAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($decodedCreatedAts) {
                foreach ($decodedCreatedAts as $decodedCreatedAt) {
                    $q->orWhere('created_at', '=', trim($decodedCreatedAt));
                }
            });
        }
    
        // Filtering by updated_at
        if ($decodedUpdatedAt) {
            $decodedUpdatedAts = explode(',', $decodedUpdatedAt);
            $query->where(function ($q) use ($decodedUpdatedAts) {
                foreach ($decodedUpdatedAts as $decodedUpdated) {
                    $q->orWhere('updated_at', '=', trim($decodedUpdatedAt));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('locations', function ($query) use ($globalSearch) {
                        $query->where('locations_name', 'like', '%' . $globalSearch . '%');
                    });                    
            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $productTypes = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($productTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Branch::with(['creator', 'updater', 'locations']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
    
        // Grouping by related locations (show all even with 0 branches)
        if ($column === 'locations') {
            $results = \DB::table('locations')
                ->leftJoin('branch_location', 'locations.id', '=', 'branch_location.location_id')
                ->select('locations.locations_name as branch', \DB::raw('COUNT(branch_location.branch_id) as total'))
                ->groupBy('locations.locations_name')
                ->orderBy('locations.locations_name')
                ->get();
    
            return response()->json($results, 200);
        }
    
        // Default: grouping by columns on branches table
        $results = Branch::select($column, $column . ' as branch', \DB::raw('COUNT(*) as total'))
            ->groupBy($column)
            ->orderBy($column)
            ->get();
    
        return response()->json($results, 200);
    }
    
    
    
    public function show($id)
    {
        // Find the branch by ID with its associated locations
        $branch = Branch::with(['locations'])->findOrFail($id);
    
        // Log the branch retrieved
        Log::info('Branch Retrieved:', ['branch' => $branch]);
    
        return response()->json(['branch' => $branch], 200);
    }
        

    /**
     * Create a new branch by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createBranch(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'location_id' => 'required|exists:locations,id',  // Ensure the location_id exists in the locations table
        ]);

        // Log the request data
        Log::info('Create Branch Request:', ['request' => $request->all()]);

        // Check if the branch name already exists
        if (Branch::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Branch already exists.'], 409);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Create a new branch
            $branch = Branch::create([
                'name' => $request->name,
                'created_by' => $authUser->id
            ]);

            // Log the branch creation
            Log::info('Branch Created:', ['branch' => $branch]);

            // Attach the location to the branch using the pivot table
            $branch->locations()->attach($request->location_id);  // Attach the location ID to the pivot table

            // Log the successful attachment
            Log::info('Location Attached to Branch:', ['branch_id' => $branch->id, 'location_id' => $request->location_id]);

            return response()->json(['message' => 'Branch created successfully.', 'branch' => $branch], 201);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Branch Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create a branch.'], 403);
    }


    /**
     * Update an existing branch by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBranch(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'location_id' => 'nullable|exists:locations,id',  // Validate location_id if provided
        ]);

        // Log the request data
        Log::info('Update Branch Request:', ['request' => $request->all()]);

        // Find the branch by ID
        $branch = Branch::find($id);
        
        if (!$branch) {
            return response()->json(['error' => 'Branch not found.'], 404);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Check if the branch name is being updated and does not already exist
            if ($branch->name !== $request->name && Branch::where('name', $request->name)->exists()) {
                return response()->json(['error' => 'Branch name already exists.'], 409);
            }

            // Update the branch name
            $branch->name = $request->name;
            $branch->updated_by = $authUser->id; // Full name
            $branch->save();

            // Log the updated branch
            Log::info('Branch Updated:', ['branch' => $branch]);

            // Update location relationship if provided
            if ($request->has('location_id')) {
                // Detach old location(s) if any
                $branch->locations()->detach();
                
                // Attach the new location
                $branch->locations()->attach($request->location_id);

                // Log the location update
                Log::info('Branch Location Updated:', [
                    'branch_id' => $branch->id, 
                    'location_id' => $request->location_id
                ]);
            }

            return response()->json(['message' => 'Branch updated successfully.', 'branch' => $branch], 200);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Branch Update Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to update this branch.'], 403);
    }


    /**
     * Delete a branch by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteBranch($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the branch
            $branch = Branch::findOrFail($id);

            // Delete the branch
            $branch->delete();

            return response()->json(['message' => 'Branch deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this branch.'], 403);
    }
}
