{"__meta": {"id": "X6e76567304d7e53c9cff8a719d32e444", "datetime": "2025-07-11 15:33:39", "utime": **********.558914, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.098331, "end": **********.558925, "duration": 0.46059393882751465, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.098331, "relative_start": 0, "end": **********.527194, "relative_end": **********.527194, "duration": 0.4288630485534668, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.527208, "relative_start": 0.4288771152496338, "end": **********.558927, "relative_end": 2.1457672119140625e-06, "duration": 0.03171896934509277, "duration_str": "31.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20966224, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome", "param_count": null, "params": [], "start": **********.552469, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\resources\\views/welcome.blade.phpwelcome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fresources%2Fviews%2Fwelcome.blade.php&line=1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#262\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#257 …}\n  file: \"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\routes\\web.php\"\n  line: \"16 to 18\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Froutes%2Fweb.php&line=16\" onclick=\"\">routes/web.php:16-18</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "XNqeXsffkIVEcqf4pGKwddDZovZI3MSQVhDqIk4M", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1800668350 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1800668350\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1534163853 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1534163853\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1706138186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1706138186\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1515727831 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515727831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1248605606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1248605606\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2086409361 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 11 Jul 2025 09:33:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"459 characters\">XSRF-TOKEN=eyJpdiI6InJWY25xTnJ5Ti9JaTdTYkVDeUJDM3c9PSIsInZhbHVlIjoiczNLaDRTNmlsS05mWnlGV0hDQXhwcVZmemo0ZVdqYlRRcXgzaUF5RzZLcmdCZ3JIdUtWcTdsdXhDOXRvdi8xTEFsQ1k0SFZIWG9xSWdQSFpXOFdzdlFOYmFtV0pXTDc3alJ6TnhYT0VoWU9GVFZMMjRnclhjc1hDSTdNUC85MEYiLCJtYWMiOiI1MTVkMzcyNTYxNDNiMGQ3MjhhYzIxZWEwYzVhYzJlZTRkY2VhNjVhMWRkNzcxY2VkMzE4NGEwYjEyYzU2MGNhIiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:33:39 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"478 characters\">creativeapp_session=eyJpdiI6IjhRL3VLTmwvODRlTmtxVTFSVUxRUWc9PSIsInZhbHVlIjoiQlVhNk42T0hiaWtzRjA3VnIrRkxKQ05jdFNmRUdSYUN3Ylo0ZjRyRVJtb3QwaSs1SmlISE1YS095QzVHM2xVeVBzdVo1d1piNUlYUStRZjVKQnpmTHd0STFrWEI2VVFVSEwrNWswS3MvVHRxaWNza0QyTEdIV0dIUll4ODVxdnoiLCJtYWMiOiIxYWEzZDk3OGViNWRiYzMyYmZkOTk0MmYzNTlkMTcwNzQ3N2E1YThjMzcyNjg2ZWU4ODYzNDRjNmQwMTBjZmU2IiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:33:39 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"431 characters\">XSRF-TOKEN=eyJpdiI6InJWY25xTnJ5Ti9JaTdTYkVDeUJDM3c9PSIsInZhbHVlIjoiczNLaDRTNmlsS05mWnlGV0hDQXhwcVZmemo0ZVdqYlRRcXgzaUF5RzZLcmdCZ3JIdUtWcTdsdXhDOXRvdi8xTEFsQ1k0SFZIWG9xSWdQSFpXOFdzdlFOYmFtV0pXTDc3alJ6TnhYT0VoWU9GVFZMMjRnclhjc1hDSTdNUC85MEYiLCJtYWMiOiI1MTVkMzcyNTYxNDNiMGQ3MjhhYzIxZWEwYzVhYzJlZTRkY2VhNjVhMWRkNzcxY2VkMzE4NGEwYjEyYzU2MGNhIiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:33:39 GMT; domain=.creativeapp.sebpo.net; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"450 characters\">creativeapp_session=eyJpdiI6IjhRL3VLTmwvODRlTmtxVTFSVUxRUWc9PSIsInZhbHVlIjoiQlVhNk42T0hiaWtzRjA3VnIrRkxKQ05jdFNmRUdSYUN3Ylo0ZjRyRVJtb3QwaSs1SmlISE1YS095QzVHM2xVeVBzdVo1d1piNUlYUStRZjVKQnpmTHd0STFrWEI2VVFVSEwrNWswS3MvVHRxaWNza0QyTEdIV0dIUll4ODVxdnoiLCJtYWMiOiIxYWEzZDk3OGViNWRiYzMyYmZkOTk0MmYzNTlkMTcwNzQ3N2E1YThjMzcyNjg2ZWU4ODYzNDRjNmQwMTBjZmU2IiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:33:39 GMT; domain=.creativeapp.sebpo.net; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086409361\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-278173034 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XNqeXsffkIVEcqf4pGKwddDZovZI3MSQVhDqIk4M</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278173034\", {\"maxDepth\":0})</script>\n"}}