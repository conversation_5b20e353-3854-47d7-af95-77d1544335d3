<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Schedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'shift_name',
        'shift_start',
        'shift_end',
        'department_id',
        'team_id',
        'created_by',
        'updated_by',
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Schedule can be for many department
    public function departments()
    {
        return $this->belongsToMany(Department::class, 'department_schedule');
    }
    
    // Schedule can be for many team
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'schedule_team');
    }

    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function time_cards()
    {
        return $this->hasMany(TimeCard::class, 'shift_id');
    }

    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }
}