import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditTrainingTopic from './EditTrainingTopic'; // Ensure correct component is imported

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TrainingTopicList = () => {
    const [topics, setTopics] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedTopicId, setSelectedTopicId] = useState(null);
    const [error, setError] = useState(null);

    // Column names for Training Topics
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Toppic Name", key: "name" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchTopics = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/training-topics`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Topics', data);

                setTopics(data.trainingTopics.map(topic => ({
                    id: topic.id,
                    name: topic.name,
                    department: topic.department,
                    team: topic.team,
                    created_by: topic.created_by,
                    updated_by: topic.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTopics();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/training-topic/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete training topic: ' + response.statusText);
            }

            // Update the topics list after deletion
            setTopics(prevTopics => prevTopics.filter(topic => topic.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedTopicId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no topics are available
    if (topics.length === 0) {
        return <div className="text-gray-500">No data available</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={topics}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTopicId}
            />
            {modalVisible && (
                <EditTrainingTopic
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    topicId={selectedTopicId}
                />
            )}
        </div>
    );
};

export default TrainingTopicList;
