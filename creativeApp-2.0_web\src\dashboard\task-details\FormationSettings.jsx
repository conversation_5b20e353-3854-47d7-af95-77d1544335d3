import React, { useState } from 'react';
import ProductType from './ProductType';

const FormationSettings = () => {
  const [activeTab, setActiveTab] = useState('styled-product-type');

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className='bg-white dark:bg-gray-900 rounded-xl pb-10'>
      <h4 className="text-xl font-bold p-4 lg:p-6 text-left">Formation Settings</h4>
      <div className='px-4 lg:px-6 pb-6 bg-white'>
        <div className="border-b border-gray-200 dark:border-gray-700">
          <ul 
            className="
            [&::-webkit-scrollbar]:h-1
            [&::-webkit-scrollbar-track]:rounded-full
            [&::-webkit-scrollbar-track]:bg-white
            [&::-webkit-scrollbar-thumb]:rounded-full
            [&::-webkit-scrollbar-thumb]:bg-gray-300
            dark:[&::-webkit-scrollbar-track]:bg-neutral-700
            dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500
          flex -mb-px text-sm font-medium text-center overflow-x-auto w-full flex-row flex-nowrap"
             role="tablist">
            <li className='whitespace-nowrap' role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-product-type' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-product-type')}
                role="tab"
                aria-selected={activeTab === 'styled-product-type'}
              >
                Product Type
              </button>
            </li>
            <li className="whitespace-nowrap" role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-task-type' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-task-type')}
                role="tab"
                aria-selected={activeTab === 'styled-task-type'}
              >
                Task Type
              </button>
            </li>
            <li className='whitespace-nowrap' role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-revision-type' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-revision-type')}
                role="tab"
                aria-selected={activeTab === 'styled-revision-type'}
              >
                Revision Type
              </button>
            </li>
            <li className='whitespace-nowrap' role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-priority' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-priority')}
                role="tab"
                aria-selected={activeTab === 'styled-priority'}
              >
                Priority
              </button>
            </li>
            <li className='whitespace-nowrap' role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-record-type' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-record-type')}
                role="tab"
                aria-selected={activeTab === 'styled-record-type'}
              >
                Record Type
              </button>
            </li>
            <li className='whitespace-nowrap' role="presentation">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'styled-sla-achieve' ? 'bg-gray-100 text-black-400' : 'text-gray-500'}`}
                onClick={() => handleTabClick('styled-sla-achieve')}
                role="tab"
                aria-selected={activeTab === 'styled-sla-achieve'}
              >
                SLA Achieved
              </button>
            </li>
          </ul>
        </div>
        <div id="default-styled-tab-content">
          <div className={`py-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-product-type' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="product-type-tab">
            <ProductType />
          </div>
          <div className={`p-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-task-type' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="task-type-tab">
            <p>Task Type</p>
          </div>
          <div className={`p-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-revision-type' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="revision-type-tab">
            <p>Revision Type</p>
          </div>
          <div className={`p-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-priority' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="priority-tab">
            <p>Task Priority</p>
          </div>
          <div className={`p-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-record-type' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="record-type-tab">
            <p>Record Type</p>
          </div>
          <div className={`p-4 rounded-lg bg-gray-50 dark:bg-gray-800 ${activeTab === 'styled-sla-achieve' ? 'block' : 'hidden'}`} role="tabpanel" aria-labelledby="sla-achieve-tab">
            <p>SLA Achieved</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormationSettings;
