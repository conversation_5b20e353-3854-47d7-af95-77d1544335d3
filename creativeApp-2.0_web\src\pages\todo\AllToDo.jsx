import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import TablePagination from '../../common/table/TablePagination';
import EditTodo from './commonTodo/EditTodo';
import { API_URL } from '../../common/fetchData/apiConfig';
import TableContentTodo from './commonTodo/TableContentTodo';

// Check if the token is available and valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AllToDo = ({ searchTerm }) => {
    const [taskDetails, setTaskDetails] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTaskId, setSelectedTaskId] = useState(null);
    const [filteredTasks, setFilteredTasks] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 20;

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Title", key: "title" },
        { label: "Created By", key: "created_by" },
        { label: "Assignees", key: "Assignees" },
        { label: "Priority", key: "priority" },
        { label: "Due Date", key: "dueDate" },
        { label: "Countdown", key: "countdown" },
        { label: "Status", key: "status" },
    ];

    useEffect(() => {
        const fetchTaskDetails = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}task-details`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Task Details:', data);

                const mappedTasks = data.taskDetails.map(task => ({
                    id: task.id,
                    department: task.product_types.department || '',
                    team: task.product_types.team || '',
                    date: task.created_at || '',
                    //fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),
                    ticket_number: task.ticket_number || '',
                    month: task.month || '',
                    week: task.week || '',
                    received_date: task.received_date || '',
                    due_date: task.due_date || '',
                    unit: task.unit || '',
                    account_name: task.account_name || '',
                    campaign_name: task.campaign_name || '',
                    notes: task.notes || '',
                    product_type: task.product_types.name || '',
                }));

                setTaskDetails(mappedTasks);
                setFilteredTasks(mappedTasks);

            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTaskDetails();
    }, [currentPage, itemsPerPage]);

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm ? searchTerm.toLowerCase().trim() : '';

        const highlightText = (text) => {
            const strText = text ? text.toString() : ''; // Ensuring it's always a string

            const regex = new RegExp(`(${normalizedSearchTerm})`, 'gi');
            const parts = strText.split(regex);

            return parts.map((part, index) => {
                return regex.test(part) ? (
                    <span key={index} className="bg-yellow-300">{part}</span>
                ) : part;
            });
        };

        if (!normalizedSearchTerm) {
            setFilteredTasks(taskDetails);
            return;
        }

        const filtered = taskDetails.filter(task => {
            return Object.values(task).some(value => {
                const valueStr = value ? value.toString().toLowerCase() : ''; // Safeguard for undefined/null
                return valueStr.includes(normalizedSearchTerm);
            });
        }).map(task => ({
            id: task.id,
            ticket_number: highlightText(task.ticket_number),
            month: highlightText(task.month),
            week: highlightText(task.week),
            received_date: highlightText(task.received_date),
            due_date: highlightText(task.due_date),
            unit: highlightText(task.unit),
            account_name: highlightText(task.account_name),
            campaign_name: highlightText(task.campaign_name),
            notes: highlightText(task.notes),
            product_type_id: highlightText(task.product_type_id),
        }));

        setFilteredTasks(filtered);
    }, [searchTerm, taskDetails]);

    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredTasks.slice(startIndex, startIndex + itemsPerPage);

    const handleEdit = (id) => {
        setSelectedTaskId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    // Show "No data found" message if filteredTasks is empty
    if (filteredTasks.length === 0) {
        return <div className="text-center text-lg text-gray-500">No data found, please add data to see in the list.</div>;
    }
    return (
        <div>
            <TableContentTodo
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTaskId}
                hideDeleteButton={true}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredTasks.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
            {modalVisible && selectedTaskId && (
                <EditTodo
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    taskId={selectedTaskId}
                />
            )}
        </div>
    );
}

export default AllToDo;