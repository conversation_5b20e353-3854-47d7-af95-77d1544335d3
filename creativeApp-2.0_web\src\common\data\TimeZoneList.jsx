import React, { useEffect, useState } from "react";
import moment from "moment-timezone";

const TimeZoneList = () => {
  const [timeZones, setTimeZones] = useState([]);

  useEffect(() => {
    // Get all the available time zones from moment-timezone
    const timeZoneNames = moment.tz.names();

    // Map over the time zone names to format them in the required structure
    const timeZoneData = timeZoneNames.map((timezone) => {
      const city = timezone.split("/").pop().replace(/_/g, " ");
      return {
        city: city,
        timezone: timezone,
      };
    });

    // Set the formatted time zones to state
    setTimeZones(timeZoneData);
  }, []);

  return (
    <div>
      <h1>Time Zones</h1>
      <ul>
        {timeZones.map((zone, index) => (
          <li key={index}>
            <strong>{zone.city}</strong>: {zone.timezone}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TimeZoneList;
