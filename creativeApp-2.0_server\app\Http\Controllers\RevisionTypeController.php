<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RevisionType;
use Illuminate\Support\Facades\Log;

class RevisionTypeController extends Controller
{
    /**
     * Display a listing of all revision types.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $revisionTypes = RevisionType::all();

        // Log the revision types retrieved
        Log::info('All Revision Types Retrieved:', ['revision_types_count' => $revisionTypes->count()]);

        return response()->json(['revisionTypes' => $revisionTypes], 200);
    }

    /**
     * Display the specified revision type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $revisionType = RevisionType::find($id);

        if (!$revisionType) {
            return response()->json(['error' => 'Revision type not found.'], 404);
        }

        // Log the revision type retrieved
        Log::info('Revision Type Retrieved:', ['revision_type' => $revisionType]);

        return response()->json(['revisionType' => $revisionType], 200);
    }

    // Filter logic for data table
    public function revisionTypesData(Request $request)
    {
        $query = revisionType::with(['creator', 'updater', 'team', 'department']);

        // Decode all input parameters to handle URL-encoded values
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedName = $request->filled('name') ? urldecode($request->input('name')) : null;

        // Filtering by department_id
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }

        // Filtering by team_id
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }

        // Filtering by name
        if ($decodedName) {
            $names = explode(',', $decodedName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }

        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);

        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $revisionTypes = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($revisionTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = revisionType::with(['creator', 'updater', 'team', 'department']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }


    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = revisionType::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }


    /**
     * Create a new revision type.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);
        
        // Log the request data
        Log::info('Create revision type Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Create a new revision type
        try {
            $revisionType = revisionType::create([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'created_by' => $authUser->id,
            ]);
            
            // Log revision type creation
            Log::info('revision type Created:', ['revision_type' => $revisionType]);

            return response()->json([
                'message' => 'revision type created successfully.',
                'revision_type' => $revisionType
            ], 201);
        } catch (\Exception $e) {
            // Log error if revision type creation fails
            Log::error('revision type Creation Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to create revision type.'], 500);
        }
    }


    /**
     * Update an existing revision type.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
        
        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);

        // Log the request data
        Log::info('Update revision type Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Find the existing revision type by ID
        $revisionType = revisionType::find($id);

        // Check if the revision type exists
        if (!$revisionType) {
            return response()->json(['error' => 'revision type not found.'], 404);
        }

        // Update the revision type with the new data
        try {
            $revisionType->update([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'updated_by' => $authUser->id, // Track who updated the revision type
            ]);

            // Log revision type update
            Log::info('revision type Updated:', ['revision_type' => $revisionType]);

            return response()->json([
                'message' => 'revision type updated successfully.',
                'revision_type' => $revisionType
            ], 200);
        } catch (\Exception $e) {
            // Log error if revision type update fails
            Log::error('revision type Update Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to update revision type.'], 500);
        }
    }


    /**
     * Delete a revision type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $revisionType = RevisionType::findOrFail($id);

            $revisionType->delete();

            Log::info('Revision Type Deleted:', ['revision_type_id' => $id]);

            return response()->json(['message' => 'Revision type deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this revision type.'], 403);
    }
}
