<?php

namespace App\Http\Controllers;

use App\Models\AvailableStatus;
use App\Models\Billing_status;
use App\Models\Blood;
use App\Models\Branch;
use App\Models\ContactType;
use App\Models\Department;
use App\Models\Designation;
use App\Models\Location;
use App\Models\MemberStatus;
use App\Models\OnsiteStatus;
use App\Models\Resource_status;
use App\Models\Resource_type;
use App\Models\Role;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\ValidationException;
use App\Mail\VerificationMail;

use App\Notifications\MemberOnbaordNotification;


use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{

    /**
     * Show all users with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Eager load the relevant relationships
        $users = User::with([
            'roles',
            'teams',
            'departments',
            'resource_statuses',
            'resource_types',
            'billing_statuses',
            'designations',
            'bloods',
            'branches.locations',
            'contact_types',
            'member_statuses',
            'available_statuses',
            'onsite_statuses'
            ])->get();

        return response()->json($users, 200);
    }
    
    // Get Logged in users data
    public function loggedInUser(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's information for debugging (optional)
        \Log::info('Authenticated User:', ['user' => $authUser]);

        // Eager load the relationships for the authenticated user
        $userData = User::with([
            'roles',
            'teams',
            'departments',
            'resource_statuses',
            'resource_types',
            'billing_statuses',
            'designations',
            'bloods',
            'branches.locations',
            'contact_types',
            'member_statuses',
            'available_statuses',
            'onsite_statuses'
        ])
        ->where('id', $authUser->id)
        ->first();

        // Check if user data exists
        if ($userData) {
            // Return the user data with relationships
            return response()->json($userData, 200);
        } else {
            // If the user data doesn't exist, return an error
            return response()->json(['users' => $userData], 200);
            //return response()->json(['error' => 'User not found.'], 404);
        }
    }

    /**
     * Query and Filtered data for Data table
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */

     public function usersData(Request $request)
     {
         // Define the query to fetch users along with necessary relationships
         $query = User::with([
             'roles',
             'teams' => function($query) {
                // Select team_lead along with other necessary team fields
                $query->select('teams.id', 'teams.name', 'teams.team_lead');
            },
             'departments',
             'resource_statuses',
             'resource_types',
             'billing_statuses',
             'designations',
             'bloods',
             'branches.locations',
             'contact_types',
             'member_statuses',
             'available_statuses',
             'onsite_statuses',
             'creator',
             'updater',
         ])
         ->select([
             'id', 'eid', 'photo', 'fname', 'lname', 'email', 'about', 'birthday', 
             'birthday_celebration', 'birthday_celebration_date', 'gender', 
             'marital_status', 'nick_name', 'primary_contact', 'secondary_contact', 
             'emergency_contact', 'relation_contact', 'present_address', 
             'permanent_address', 'blood_donate', 'prev_designation', 
             'desk_id', 'joining_date', 'termination_date', 'employment_end', 
             'work_anniversary', 'password', 'created_at', 'updated_at', 'created_by', 'updated_by'
         ]);
     
         // Decode all input parameters to handle URL-encoded values
         $decodedEid = $request->filled('eid') ? urldecode($request->input('eid')) : null;
         $decodedEmail = $request->filled('email') ? urldecode($request->input('email')) : null;
         $decodedAbout = $request->filled('about') ? urldecode($request->input('about')) : null;
         $decodedBirthday = $request->filled('birthday') ? urldecode($request->input('birthday')) : null;
         $decodedBirthdayCelebrate = $request->filled('birthday_celebration') ? urldecode($request->input('birthday_celebration')) : null;
         $decodedBirthdayCelebrateDate = $request->filled('birthday_celebration_date') ? urldecode($request->input('birthday_celebration_date')) : null;
         $decodedGender = $request->filled('gender') ? urldecode($request->input('gender')) : null;
         $decodedMeritalStatus = $request->filled('marital_status') ? urldecode($request->input('marital_status')) : null;
         $decodedNickName = $request->filled('nick_name') ? urldecode($request->input('nick_name')) : null;
         $decodedPrimaryContact = $request->filled('primary_contact') ? urldecode($request->input('primary_contact')) : null;
         $decodedSecondaryContact = $request->filled('secondary_contact') ? urldecode($request->input('secondary_contact')) : null;
         $decodedEmergencyContact = $request->filled('emergency_contact') ? urldecode($request->input('emergency_contact')) : null;
         $decodedRelationContact = $request->filled('relation_contact') ? urldecode($request->input('relation_contact')) : null;
         $decodedPresentAddress = $request->filled('present_address') ? urldecode($request->input('present_address')) : null;
         $decodedPermanentAddress = $request->filled('permanent_address') ? urldecode($request->input('permanent_address')) : null;
         $decodedBloodDonate = $request->filled('blood_donate') ? urldecode($request->input('blood_donate')) : null;
         $decodedPrevDesignation = $request->filled('prev_designation') ? urldecode($request->input('prev_designation')) : null;
         $decodedDeskId = $request->filled('desk_id') ? urldecode($request->input('desk_id')) : null;
         $decodedJoiningDate = $request->filled('joining_date') ? urldecode($request->input('joining_date')) : null;
         $decodedTerminationDate = $request->filled('termination_date') ? urldecode($request->input('termination_date')) : null;
         $decodedEmploymentEndDate = $request->filled('employment_end') ? urldecode($request->input('employment_end')) : null;
         $decodedWorkAnniversary = $request->filled('work_anniversary') ? urldecode($request->input('work_anniversary')) : null;
         $decodedRole = $request->filled('role') ? urldecode($request->input('role')) : null;
         $decodedTeam = $request->filled('teams') ? urldecode($request->input('teams')) : null;
         $decodedTeamLead = $request->filled('team_lead') ? $request->input('team_lead') : null;
         $decodedDepartment = $request->filled('departments') ? urldecode($request->input('departments')) : null;
         $decodedLocations = $request->filled('locations') ? urldecode($request->input('locations')) : null;
         $decodedBranches = $request->filled('branches') ? urldecode($request->input('branches')) : null;
         $decodedResourceStatus = $request->filled('resource_statuses') ? urldecode($request->input('resource_statuses')) : null;
         $decodedResourceType = $request->filled('resource_types') ? urldecode($request->input('resource_types')) : null;
         $decodedContactType = $request->filled('contact_types') ? urldecode($request->input('contact_types')) : null;
         $decodedBillingStatus = $request->filled('billing_statuses') ? urldecode($request->input('billing_statuses')) : null;
         $decodedMemberStatus = $request->filled('member_statuses') ? urldecode($request->input('member_statuses')) : null;
         $decodedAvailableStatus = $request->filled('available_statuses') ? urldecode($request->input('available_statuses')) : null;
         $decodedOnsiteStatus = $request->filled('onsite_statuses') ? urldecode($request->input('onsite_statuses')) : null;
         $decodedDesignation = $request->filled('designations') ? urldecode($request->input('designations')) : null;
         $decodedBlood = $request->filled('bloods') ? urldecode($request->input('bloods')) : null;

         // Filter by Birthdayy
        if ($decodedBirthday) {
            $query->where(function ($q) use ($decodedBirthday) {
                $q->orWhere('birthday', 'like', '%' . trim($decodedBirthday) . '%');
            });
        }

        // Filter by Birthday Celebration
        if ($decodedBirthdayCelebrate) {
            $query->where(function ($q) use ($decodedBirthdayCelebrate) {
                $q->orWhere('birthday_celebration', 'like', '%' . trim($decodedBirthdayCelebrate) . '%');
            });
        }

        // Filter by Birthday Celebration Date
        if ($decodedBirthdayCelebrateDate) {
            $query->where(function ($q) use ($decodedBirthdayCelebrateDate) {
                $q->orWhere('birthday_celebration_date', 'like', '%' . trim($decodedBirthdayCelebrateDate) . '%');
            });
        }

        // Filter by Gender
        if ($decodedGender) {
            $query->where(function ($q) use ($decodedGender) {
                $q->orWhere('gender', 'like', '%' . trim($decodedGender) . '%');
            });
        }

        // Filter by Marital Status
        if ($decodedMeritalStatus) {
            $query->where(function ($q) use ($decodedMeritalStatus) {
                $q->orWhere('marital_status', 'like', '%' . trim($decodedMeritalStatus) . '%');
            });
        }

        // Filter by Nick Name
        if ($decodedNickName) {
            $query->where(function ($q) use ($decodedNickName) {
                $q->orWhere('nick_name', 'like', '%' . trim($decodedNickName) . '%');
            });
        }

        // Filter by Primary Contact
        if ($decodedPrimaryContact) {
            $query->where(function ($q) use ($decodedPrimaryContact) {
                $q->orWhere('primary_contact', 'like', '%' . trim($decodedPrimaryContact) . '%');
            });
        }

        // Filter by Secondary Contact
        if ($decodedSecondaryContact) {
            $query->where(function ($q) use ($decodedSecondaryContact) {
                $q->orWhere('secondary_contact', 'like', '%' . trim($decodedSecondaryContact) . '%');
            });
        }


        // Filter by emergency_contact
        if ($decodedEmergencyContact) {
            $emergencyContacts = explode(',', $decodedEmergencyContact);
            $query->where(function ($q) use ($emergencyContacts) {
                foreach ($emergencyContacts as $emergencyContact) {
                    $q->orWhere('emergency_contact', 'like', '%' . trim($emergencyContact) . '%');
                }
            });
        }

        // Filter by emergency_contact
        if ($decodedRelationContact) {
            $relationContacts = explode(',', $decodedRelationContact);
            $query->where(function ($q) use ($relationContacts) {
                foreach ($relationContacts as $relationContact) {
                    $q->orWhere('relation_contact', 'like', '%' . trim($relationContact) . '%');
                }
            });
        }

        // Filter by present_address
        if ($decodedPresentAddress) {
            $presentAddresses = explode(',', $decodedPresentAddress);
            $query->where(function ($q) use ($presentAddresses) {
                foreach ($presentAddresses as $presentAddress) {
                    $q->orWhere('present_address', 'like', '%' . trim($presentAddress) . '%');
                }
            });
        }

        // Filter by permanent_address
        if ($decodedPermanentAddress) {
            $permanentAddresses = explode(',', $decodedPermanentAddress);
            $query->where(function ($q) use ($permanentAddresses) {
                foreach ($permanentAddresses as $permanentAddress) {
                    $q->orWhere('permanent_address', 'like', '%' . trim($permanentAddress) . '%');
                }
            });
        }

        // Filter by blood_donate
        if ($decodedBloodDonate) {
            $bloodDonations = explode(',', $decodedBloodDonate);
            $query->where(function ($q) use ($bloodDonations) {
                foreach ($bloodDonations as $bloodDonation) {
                    $q->orWhere('blood_donate', 'like', '%' . trim($bloodDonation) . '%');
                }
            });
        }

        // Filter by prev_designation
        if ($decodedPrevDesignation) {
            $prevDesignations = explode(',', $decodedPrevDesignation);
            $query->where(function ($q) use ($prevDesignations) {
                foreach ($prevDesignations as $prevDesignation) {
                    $q->orWhere('prev_designation', 'like', '%' . trim($prevDesignation) . '%');
                }
            });
        }

        // Filter by DESK ID
        if ($decodedDeskId) {
            $deskIds = explode(',', $decodedDeskId);
            $query->where(function ($q) use ($deskIds) {
                foreach ($deskIds as $deskId) {
                    $q->orWhere('desk_id', '=', trim($deskId));
                }
            });
        }

        // Filter by desk_id
        if ($decodedJoiningDate) {
            $deskIds = explode(',', $decodedJoiningDate);
            $query->where(function ($q) use ($deskIds) {
                foreach ($deskIds as $deskId) {
                    $q->orWhere('desk_id', '=', '%' . trim($deskId) . '%');
                }
            });
        }

        // Filter by terminjoining_dateation_date
        if ($decodedJoiningDate) {
            $joiningDates = explode(',', $decodedJoiningDate);
            $query->where(function ($q) use ($joiningDates) {
                foreach ($joiningDates as $joiningDate) {
                    $q->orWhere('joining_date', 'like', '%' . trim($joiningDate) . '%');
                }
            });
        }

        // Filter by termination_date
        if ($decodedEmploymentEndDate) {
            $terminationDates = explode(',', $decodedEmploymentEndDate);
            $query->where(function ($q) use ($terminationDates) {
                foreach ($terminationDates as $terminationDate) {
                    $q->orWhere('termination_date', 'like', '%' . trim($terminationDate) . '%');
                }
            });
        }

        // Filter by employment_end
        if ($decodedEmploymentEndDate) {
            $employmentEnds = explode(',', $decodedEmploymentEndDate);
            $query->where(function ($q) use ($employmentEnds) {
                foreach ($employmentEnds as $employmentEnd) {
                    $q->orWhere('employment_end', 'like', '%' . trim($employmentEnd) . '%');
                }
            });
        }

        // Filter by Work Anniversary
        if ($decodedWorkAnniversary) {
            $workAnniversaries = explode(',', $decodedWorkAnniversary);
            $query->where(function ($q) use ($workAnniversaries) {
                foreach ($workAnniversaries as $workAnniversary) {
                    $q->orWhere('work_anniversary', 'like', '%' . trim($workAnniversary) . '%');
                }
            });
        }

        // Filter by Eid
        if ($decodedEid) {
            $eids = explode(',', $decodedEid);
            $query->where(function ($q) use ($eids) {
                foreach ($eids as $eid) {
                    $q->orWhere('eid', '=', trim($eid));
                }
            });
        }

        // Filter by Email
        if ($decodedEmail) {
            $emails = explode(',', $decodedEmail);
            $query->where(function ($q) use ($emails) {
                foreach ($emails as $email) {
                    $q->orWhere('email', 'like', '%' . trim($email) . '%');
                }
            });
        }
     
         // Filtering: Filter by role name
         if ($decodedRole) {
             $roles = explode(',', $decodedRole);
             $query->whereHas('roles', function ($q) use ($roles) {
                 foreach ($roles as $role) {
                     $q->orWhere('name', 'like', '%' . trim($role) . '%');
                 }
             });
         }
     
         // Filtering: Filter by team name
         if ($decodedLocations) {
            $decodedLocations = array_map('trim', explode(',', $decodedLocations));
            $query->whereHas('branches.locations', function ($q) use ($decodedLocations) {
                $q->whereIn('locations.id', $decodedLocations);
            });
             
         }
         
         if ($decodedBranches) {
            $decodedBranches = array_map('trim', explode(',', $decodedBranches));
            $query->whereHas('branches', function ($q) use ($decodedBranches) {
                $q->whereIn('branches.id', $decodedBranches);
            });
         }

     
         // Filtering: Filter by team name
        if ($decodedTeam) {
            if (is_string($decodedTeam)) {
                $decodedTeam = array_map('trim', explode(',', $decodedTeam));
            }
            $query->whereHas('teams', function ($q) use ($decodedTeam) {
                $q->whereIn('teams.id', $decodedTeam);
            });
        }

        // Filtering: Filter by Team Lead
        if ($decodedTeamLead) {
            if (($decodedTeamLead)) {
                $decodedTeamLead = array_map('trim', explode(',', $decodedTeamLead));
            }
            $query->whereHas('teams', function ($q) use ($decodedTeamLead) {
                $q->whereIn('teams.team_lead', $decodedTeamLead);
            });
        }
     
         // Filtering: Filter by department name
         if ($decodedDepartment) {
            if (is_string($decodedDepartment)) {
                $decodedDepartment = array_map('trim', explode(',', $decodedDepartment));
            }
            $query->whereHas('departments', function ($q) use ($decodedDepartment) {
                $q->whereIn('departments.id', $decodedDepartment);
            });
        }

         if ($decodedLocations) {
            if (is_string($decodedLocations)) {
                $decodedLocations = array_map('trim', explode(',', $decodedLocations));
            }
            $query->whereHas('branches.locations', function ($q) use ($decodedLocations) {
                $q->whereIn('locations.id', $decodedLocations);
            });
             
         }
         
         if ($decodedBranches) {
            if (is_string($decodedBranches)) {
                $decodedBranches = array_map('trim', explode(',', $decodedBranches));
            }
            $query->whereHas('branches', function ($q) use ($decodedBranches) {
                $q->whereIn('branches.id', $decodedBranches);
            });
         }

     
         // Filtering: Filter by resource status name
         if ($decodedResourceStatus) {
            if (is_string($decodedResourceStatus)) {
                $decodedResourceStatus = array_map('trim', explode(',', $decodedResourceStatus));
            }
            $query->whereHas('resource_statuses', function ($q) use ($decodedResourceStatus) {
                $q->whereIn('resource_statuses.id', $decodedResourceStatus);
            });

         }

        // Filtering: Filter by resource type name
        if ($decodedResourceType) {
            if (is_string($decodedResourceType)) {
                $decodedResourceType = array_map('trim', explode(',', $decodedResourceType));
            }
            $query->whereHas('resource_types', function ($q) use ($decodedResourceType) {
                $q->whereIn('resource_types.id', $decodedResourceType);
            });

        }
     
         // Filtering: Filter by billing status name
         if ($decodedBillingStatus) {
            if (is_string($decodedBillingStatus)) {
                $decodedBillingStatus = array_map('trim', explode(',', $decodedBillingStatus));
            }
            $query->whereHas('billing_statuses', function ($q) use ($decodedBillingStatus) {
                $q->whereIn('billing_statuses.id', $decodedBillingStatus);
            });

         }

        // Filtering: Filter by member status name
        if ($decodedMemberStatus) {
            if (is_string($decodedMemberStatus)) {
                $decodedMemberStatus = array_map('trim', explode(',', $decodedMemberStatus));
            }
            $query->whereHas('member_statuses', function ($q) use ($decodedMemberStatus) {
                $q->whereIn('member_statuses.id', $decodedMemberStatus);
            });

        }

        // Filtering: Filter by available status name
        if ($decodedAvailableStatus) {
            if (is_string($decodedAvailableStatus)) {
                $decodedAvailableStatus = array_map('trim', explode(',', $decodedAvailableStatus));
            }
            $query->whereHas('available_statuses', function ($q) use ($decodedAvailableStatus) {
                $q->whereIn('available_statuses.id', $decodedAvailableStatus);
            });

        }

        // Filtering: Filter by onsite status name
        if ($decodedOnsiteStatus) {
            if (is_string($decodedOnsiteStatus)) {
                $decodedOnsiteStatus = array_map('trim', explode(',', $decodedOnsiteStatus));
            }
            $query->whereHas('onsite_statuses', function ($q) use ($decodedOnsiteStatus) {
                $q->whereIn('onsite_statuses.id', $decodedOnsiteStatus);
            });

        }
     
         // Filtering: Filter by designation name
         if ($decodedDesignation) {
            if (is_string($decodedDesignation)) {
                $decodedDesignation = array_map('trim', explode(',', $decodedDesignation));
            }
            $query->whereHas('designations', function ($q) use ($decodedDesignation) {
                $q->whereIn('designations.id', $decodedDesignation);
            });
        }
     
         // Filtering: Filter by blood group name
         if ($decodedBlood) {
            if (is_string($decodedBlood)) {
                $decodedBlood = array_map('trim', explode(',', $decodedBlood));
            }
            $query->whereHas('bloods', function ($q) use ($decodedBlood) {
                $q->whereIn('bloods.id', $decodedBlood);
            });
        }
     
        
     
         // Global Search: Search across multiple columns and relationships
         $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
     
         if ($globalSearch) {
             $query->where(function ($q) use ($globalSearch) {
                 $q->orWhere('eid', 'like', '%' . $globalSearch . '%')
                     ->orWhere('fname', 'like', '%' . $globalSearch . '%')
                     ->orWhere('lname', 'like', '%' . $globalSearch . '%')
                     ->orWhere('email', 'like', '%' . $globalSearch . '%')
                     ->orWhere('about', 'like', '%' . $globalSearch . '%')
                     ->orWhere('birthday', 'like', '%' . $globalSearch . '%')
                     ->orWhere('birthday_celebration', 'like', '%' . $globalSearch . '%')
                     ->orWhere('birthday_celebration_date', 'like', '%' . $globalSearch . '%')
                     ->orWhere('gender', 'like', '%' . $globalSearch . '%')
                     ->orWhere('marital_status', 'like', '%' . $globalSearch . '%')
                     ->orWhere('nick_name', 'like', '%' . $globalSearch . '%')
                     ->orWhere('primary_contact', 'like', '%' . $globalSearch . '%')
                     ->orWhere('secondary_contact', 'like', '%' . $globalSearch . '%')
                     ->orWhere('emergency_contact', 'like', '%' . $globalSearch . '%')
                     ->orWhere('relation_contact', 'like', '%' . $globalSearch . '%')
                     ->orWhere('present_address', 'like', '%' . $globalSearch . '%')
                     ->orWhere('permanent_address', 'like', '%' . $globalSearch . '%')
                     ->orWhere('blood_donate', 'like', '%' . $globalSearch . '%')
                     ->orWhere('prev_designation', 'like', '%' . $globalSearch . '%')
                     ->orWhere('desk_id', 'like', '%' . $globalSearch . '%')
                     ->orWhere('joining_date', 'like', '%' . $globalSearch . '%')
                     ->orWhere('termination_date', 'like', '%' . $globalSearch . '%')
                     ->orWhere('employment_end', 'like', '%' . $globalSearch . '%')
                     ->orWhere('work_anniversary', 'like', '%' . $globalSearch . '%')
                     ->orWhere('password', 'like', '%' . $globalSearch . '%')
                     ->orWhere('created_by', 'like', '%' . $globalSearch . '%')
                     ->orWhere('updated_by', 'like', '%' . $globalSearch . '%')
                     // Search within the related tables by name
                     ->orWhereHas('roles', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('teams', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('teams', function ($query) use ($globalSearch) {
                        $query->where('team_lead', 'like', '%' . $globalSearch . '%');
                    })
                     ->orWhereHas('departments', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('resource_statuses', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('billing_statuses', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('designations', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('bloods', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     })
                     ->orWhereHas('branches.locations', function ($query) use ($globalSearch) {
                         $query->where('name', 'like', '%' . $globalSearch . '%');
                     });
             });
         }
     
         // Sorting: Sort by created_at by default, or user-defined sorting
         $sortBy = $request->query('sort_by', 'created_at');
         $order = $request->query('order', 'desc');
         $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
         $query->orderBy($sortBy, $order);
     
         // Pagination
         $perPage = $request->query('per_page', 15);
         $page = $request->query('page', 1);
         $users = $query->paginate($perPage, ['*'], 'page', $page);

        // Include role names and team names in the response
        $users->getCollection()->transform(function ($user) {
            // Accessing the role and team names using the defined accessors
            $user->role_names = $user->role_names; // Accessor for role names
            $user->team_names = $user->team_names; // Accessor for team names

            return $user;
        });
     
         return response()->json($users, 200);
     }
     
     public function group(Request $request)
     {
         // Retrieve the dynamic column name from query parameters.
         $column = $request->query('column');
     
         if (!$column) {
             return response()->json(['error' => 'The "column" parameter is required.'], 400);
         }
     
         // Optional: Get table name (used for raw table data)
         $table = $request->query('table');
         if ($table) {
             $data = DB::table($table)->get();
             return response()->json($data);
         }
     
         // Get team_lead filter if provided
         $teamLeadFilter = $request->query('team_lead');
     
         // Build base query
         $results = User::query()
             ->with([
                 'roles',
                 'teams',
                 'departments',
                 'resource_statuses',
                 'resource_types',
                 'billing_statuses',
                 'designations',
                 'bloods',
                 'branches.locations',
                 'contact_types',
                 'member_statuses',
                 'available_statuses',
                 'onsite_statuses',
                 'creator',
                 'updater',
             ])
             ->select("users.$column", "users.$column as title", \DB::raw("COUNT(*) as total"))
             ->groupBy("users.$column")
             ->orderBy("users.$column");
             
     
         // If team_lead filter is provided, join teams through pivot and filter by team_lead
         if ($teamLeadFilter) {
             $results->join('team_user', 'users.id', '=', 'team_user.user_id')
                     ->join('teams', 'teams.id', '=', 'team_user.team_id')
                     ->where('teams.team_lead', $teamLeadFilter);
         }

         $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
         
         // Modify the query to group by the column based on the team_lead relationship
         $results->groupBy($column)->orderBy($column);
     
         return response()->json($results->get(), 200);
     }
     
     
     public function searchByField(Request $request)
     {
         // Retrieve and decode the 'title' parameter from the URL
         $encodedColumn = $request->query('column');
         $encodedText = $request->query('text');
         if (!$encodedColumn) {
             return response()->json(['error' => 'The parameter is required.'], 400);
         }
         
         $column = urldecode($encodedColumn);
         $text = urldecode($encodedText);
 
         // Perform the search on the 'title' column using a partial match
         $results = User::with([
            'roles',
            'teams',
            'departments',
            'resource_statuses',
            'resource_types',
            'billing_statuses',
            'designations',
            'bloods',
            'branches.locations',
            'contact_types',
            'member_statuses',
            'available_statuses',
            'onsite_statuses',
            'creator',
            'updater',
         ]); 
 
         
 
         
         if(strpos($column, ".") !== false) {
             $columnExp = explode('.', $column);
 
             $tblName = $columnExp[0];
             $fieldName = $columnExp[1];
 
             $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                 $query->where($fieldName, 'like', '%' . $text . '%');
             });
         }else{
             $results->where($column, 'like', '%' . $text . '%');
         }
 
         // Return the search results as a JSON response
         return response()->json($results->get(), 200);
     }
 

    /**
     * Display a single user with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // Eager load relevant relationships for the user
            $user = User::with([
                'roles',
                'teams',
                'departments',
                'resource_statuses',
                'resource_types',
                'billing_statuses',
                'designations',
                'bloods',
                'branches.locations',
                'contact_types',
                'member_statuses',
                'available_statuses',
                'onsite_statuses'
            ])->findOrFail($id);

            return response()->json($user, 200);
            //return response()->json(['user' => $user], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['message' => 'User not found.'], 404);
        }
    }

    /**
     * Create users by Super Admin and Admin.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createUser(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
        
        \Log::info('Authenticated User:', ['user' => $authUser]);
        \Log::info('Roles:', ['roles' => $authUser->roles]);
        
        // Validate the request data
        $validatedData = $request->validate([
            'eid' => 'required|string|unique:users,eid',
            'email' => 'required|email|unique:users,email',
            'designations' => 'required|array|exists:designations,id',
            'resource_types' => 'required|array|exists:resource_types,id',
            'roles' => 'required|array|exists:roles,id',
            'teams' => 'required|array|exists:teams,id',
            'departments' => 'required|array|exists:departments,id',
            'resource_statuses' => 'required|array|exists:resource_statuses,id',
            'billing_statuses' => 'required|array|exists:billing_statuses,id',
            'contact_types' => 'required|array|exists:contact_types,id',
            'available_statuses' => 'required|array|exists:available_statuses,id',
            'member_statuses' => 'required|array|exists:member_statuses,id',
            'branches' => 'required|array|exists:branches,id',
            'onsite_statuses' => 'required|array|exists:onsite_statuses,id',
        ]);
        
        // Log the validated data
        \Log::info('Validated Data:', ['validatedData' => $validatedData]);
        
        // Check the authenticated user's roles
        if ($authUser->roles()->where('name', 'super-admin')->exists()) {
            \Log::info('Super Admin detected. Proceeding to create user.');
            return $this->createNewUser($request);
        }
        
        if ($authUser->roles()->where('name', 'admin')->exists()) {
            if (in_array(1, $request->roles) || in_array(2, $request->roles)) {
                \Log::warning('Admin tried to create super-admin or admin user. Access denied.');
                return response()->json(['error' => 'Admin cannot create super-admin or admin users.'], 403);
            }
        
            \Log::info('Admin detected. Proceeding to create user.');
            return $this->createNewUser($request);
        }
        
        \Log::warning('User does not have permission to create users.');
        return response()->json(['error' => 'You do not have permission to create users!'], 403);
    }
    
    
    public function createNewUser(Request $request)
    {
        \Log::info('Starting to create new user.');
    
        // Define the default password
        $defaultPassword = 'SEBPO@2024#';

        // Get the authenticated user
        $authUser = auth()->user();
    
        // Start a database transaction
        DB::beginTransaction();
        try {
            // Create the new user with the default password
            $user = User::create([
                'email' => $request->email,
                'eid' => $request->eid,
                'password' => Hash::make($defaultPassword),
                'created_by' => $authUser->id,
            ]);
    
            // Attach relationships after user creation
            \Log::info('Attaching relationships to the new user.');
    
            // Attach teams with is_default
            $teams = $request->teams;
            
            // Ensure only one team is marked as default
            if (isset($request->default_team_id)) {
                // Set the default team if provided in the request
                $defaultTeamId = $request->default_team_id;
    
                // Attach the teams to the user and mark the default team
                foreach ($teams as $teamId) {
                    if ($teamId == $defaultTeamId) {
                        $user->teams()->attach($teamId, ['is_default' => true]); // Mark the default team
                    } else {
                        $user->teams()->attach($teamId, ['is_default' => false]); // Mark non-default teams
                    }
                }
            } else {
                // If no default team is provided, assign the first team as default
                foreach ($teams as $index => $teamId) {
                    $isDefault = ($index === 0) ? true : false;
                    $user->teams()->attach($teamId, ['is_default' => $isDefault]);
                }
            }
    
            // Attach other relationships
            $user->designations()->attach($request->designations);
            $user->resource_types()->attach($request->resource_types);
            $user->roles()->attach($request->roles);
            $user->departments()->attach($request->departments);
            $user->resource_statuses()->attach($request->resource_statuses);
            $user->billing_statuses()->attach($request->billing_statuses);
            $user->contact_types()->attach($request->contact_types);
            $user->available_statuses()->attach($request->available_statuses);
            $user->member_statuses()->attach($request->member_statuses);
            $user->branches()->attach($request->branches);
            $user->onsite_statuses()->attach($request->onsite_statuses);
            
    
            \Log::info('Relationships attached successfully to user.', ['user_id' => $user->id]);
    
            // Send the email verification notification
            $user->sendEmailVerificationNotification();
    
            // Send the onboard notification (user and password are passed as arguments)
            $user->notify(new MemberOnbaordNotification($user, $defaultPassword));
    
            // Commit the transaction
            DB::commit();
    
            // Return the created user details
            return response()->json([
                'user' => $user->load('roles'),
            ], 201);
        } catch (\Exception $e) {
            // Rollback transaction if anything fails
            DB::rollBack();
    
            // Log the error
            \Log::error('Error occurred during user creation.', ['error' => $e->getMessage()]);
    
            // Re-throw the exception
            throw $e;
        }
    }
    
    public function sendVerificationEmail(User $user)
    {
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify', now()->addMinutes(60), ['id' => $user->id, 'hash' => sha1($user->email)]
        );
    
        Mail::to($user->email)->send(new VerificationMail($verificationUrl));
    }


    /**
     * Update users by super-admin and admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function update(Request $request, $id)
    {
        // Log the incoming request data for debugging purposes
        \Log::info('Received request to update user.', [
            'user_id' => $id,
            'request_data' => $request->all()
        ]);

        \Log::info('Received request data', ['request_data' => $request->all()]);  // This will log all request data
        \Log::info('Received files', ['photo' => $request->file('photo')]);  // Log the file specifically


        // Get the authenticated user
        $authUser = $request->user();
        \Log::info('Authenticated user fetched.', ['auth_user_id' => $authUser->id]);

        // Step 1: Conditional Validation for Admin/Super-Admin or Regular User
        if ($authUser->roles()->where('name', 'super-admin')->exists() || $authUser->roles()->where('name', 'admin')->exists()) {
            // Admin/Super-Admin updating their own profile
            if ($authUser->id == $id) {
                \Log::info('Admin or Super-Admin updating their own profile.', ['user_id' => $authUser->id]);
                try {
                    $validatedData = $request->validate([
                        'fname' => 'nullable|string|max:255',
                        'lname' => 'nullable|string|max:255',
                        'birthday' => 'nullable|date',
                        'birthday_celebration' => 'nullable|string|max:255',
                        'birthday_celebration_date' => 'nullable|date',
                        'gender' => 'nullable|string|max:50',
                        'marital_status' => 'nullable|string|max:50',
                        'nick_name' => 'nullable|string|max:255',
                        'primary_contact' => 'nullable|string|max:255',
                        'secondary_contact' => 'nullable|string|max:255',
                        'emergency_contact' => 'nullable|string|max:255',
                        'relation_contact' => 'nullable|string|max:255',
                        'blood_donate' => 'nullable|string|max:255', // Added blood_donate field
                        'prev_designation' => 'nullable|string|max:255', // Added prev_designation field
                        'desk_id' => 'nullable|string|max:255',
                        'joining_date' => 'nullable|date',
                        'termination_date' => 'nullable|date',
                        'employment_end' => 'nullable|date',
                        'work_anniversary' => 'nullable|date',
                        'about' => 'nullable|string|max:5000',
                        'present_address' => 'nullable|string|max:5000',
                        'permanent_address' => 'nullable|string|max:5000',
                        'bloods' => 'nullable|array|exists:bloods,id',
                        'bloods.*' => 'exists:bloods,id',
                        'photo' => 'nullable|string', // Changed to string to handle Base64 input
                        'password' => 'nullable|string',

                        // Admin-specific fields that are required for other users
                        'eid' => 'nullable|string',
                        'email' => 'nullable|email|unique:users,email,' . $authUser->id,
                        'designations' => 'nullable|array|exists:designations,id',
                        'resource_types' => 'nullable|array|exists:resource_types,id',
                        'roles' => 'nullable|array|exists:roles,id',
                        'teams' => 'nullable|array|exists:teams,id',
                        'departments' => 'nullable|array|exists:departments,id',
                        'resource_statuses' => 'nullable|array|exists:resource_statuses,id',
                        'billing_statuses' => 'nullable|array|exists:billing_statuses,id',
                        'contact_types' => 'nullable|array|exists:contact_types,id',
                        'available_statuses' => 'nullable|array|exists:available_statuses,id',
                        'member_statuses' => 'nullable|array|exists:member_statuses,id',
                        'branches' => 'nullable|array|exists:branches,id',
                        'onsite_statuses' => 'nullable|array|exists:onsite_statuses,id',
                        
                    ]);
                } catch (\Illuminate\Validation\ValidationException $e) {
                    \Log::error('Validation failed for admin/super-admin updating their own profile.', [
                        'error_messages' => $e->errors(),
                        'request_data' => $request->all()
                    ]);
                    return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
                }
                return $this->updateUserWithRegularFields($authUser, $request);
            } else {
                // Admin/Super-Admin updating another user
                \Log::info('Admin or Super-Admin updating another user.', ['user_id' => $authUser->id]);
                $userToUpdate = User::findOrFail($id);  // Ensure user exists

                try {
                    $validatedData = $request->validate([
                        'eid' => 'required|string',
                        'email' => 'required|email|unique:users,email,' . $id,
                        'designations' => 'required|array|exists:designations,id',
                        'resource_types' => 'required|array|exists:resource_types,id',
                        'roles' => 'required|array|exists:roles,id',
                        'teams' => 'required|array|exists:teams,id',
                        'departments' => 'required|array|exists:departments,id',
                        'resource_statuses' => 'required|array|exists:resource_statuses,id',
                        'billing_statuses' => 'required|array|exists:billing_statuses,id',
                        'contact_types' => 'required|array|exists:contact_types,id',
                        'available_statuses' => 'required|array|exists:available_statuses,id',
                        'member_statuses' => 'required|array|exists:member_statuses,id',
                        'branches' => 'required|array|exists:branches,id',
                        'onsite_statuses' => 'required|array|exists:onsite_statuses,id',
                    ]);
                } catch (\Illuminate\Validation\ValidationException $e) {
                    \Log::error('Validation failed for admin/super-admin updating another user.', [
                        'error_messages' => $e->errors(),
                        'request_data' => $request->all()
                    ]);
                    return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
                }

                return $this->updateUserWithAdminFields($userToUpdate, $request);
            }
        } else {
            // Regular user updating their own profile
            if ($authUser->id == $id) {
                \Log::info('Regular user updating their own profile.', ['user_id' => $authUser->id]);
                try {
                    $validatedData = $request->validate([
                        'fname' => 'nullable|string|max:255',
                        'lname' => 'nullable|string|max:255',
                        'birthday' => 'nullable|date',
                        'birthday_celebration' => 'nullable|string|max:255',
                        'birthday_celebration_date' => 'nullable|date',
                        'gender' => 'nullable|string|max:50',
                        'marital_status' => 'nullable|string|max:50',
                        'nick_name' => 'nullable|string|max:255',
                        'primary_contact' => 'nullable|string|max:255',
                        'secondary_contact' => 'nullable|string|max:255',
                        'emergency_contact' => 'nullable|string|max:255',
                        'relation_contact' => 'nullable|string|max:255',
                        'blood_donate' => 'nullable|string|max:255',
                        'prev_designation' => 'nullable|string|max:255',
                        'desk_id' => 'nullable|string|max:255',
                        'joining_date' => 'nullable|date',
                        'work_anniversary' => 'nullable|date',
                        'about' => 'nullable|string|max:500',
                        'present_address' => 'nullable|string|max:500',
                        'permanent_address' => 'nullable|string|max:500',
                        'email' => 'nullable|email|unique:users,email,' . $authUser->id,

                    ]);
                } catch (\Illuminate\Validation\ValidationException $e) {
                    \Log::error('Validation failed for regular user updating their own profile.', [
                        'error_messages' => $e->errors(),
                        'request_data' => $request->all()
                    ]);
                    return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
                }
                return $this->updateUserWithRegularFields($authUser, $request);
            } else {
                // Regular users cannot update other users' information
                \Log::warning('Unauthorized regular user trying to update another user.', ['user_id' => $authUser->id]);
                return response()->json(['error' => 'You cannot update another user\'s information.'], 403);
            }
        }
    }   

    private function updateUserWithAdminFields($user, Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Log request files and input fields
        Log::info('Request Files:', ['files' => $request->files->all()]);
        Log::info('Photo from input:', ['photo' => $request->input('photo')]);  // Base64 encoded photo

        // Decode the base64 image (if provided)
        $photoPath = $this->decodeBase64Image($request->input('photo'), 'photo');

        // Log the decoded photo path
        Log::info('Decoded Photo Path:', ['photo_path' => $photoPath]);

        // Handle photo update if a new one is provided
        if ($photoPath) {
            // If a new photo is uploaded, remove the old photo (optional)
            if ($user->photo && file_exists(storage_path('app/public/' . $user->photo))) {
                // Delete the old photo file
                unlink(storage_path('app/public/' . $user->photo));
            }

            // Update the user's photo path with the new one
            $user->photo = $photoPath;
        }

        try {

            // Setting created_by to the authenticated user's ID
            $user->updated_by = $authUser->id;

            if ($request->has('fname')) {
                $user->fname = $request->fname;
            }
    
            if ($request->has('lname')) {
                $user->lname = $request->lname;
            }
    
            if ($request->has('birthday')) {
                $user->birthday = $request->birthday;
            }
    
            if ($request->has('birthday_celebration')) {
                $user->birthday_celebration = $request->birthday_celebration;
            }
    
            if ($request->has('birthday_celebration_date')) {
                $user->birthday_celebration_date = $request->birthday_celebration_date;
            }
    
            if ($request->has('gender')) {
                $user->gender = $request->gender;
            }
    
            if ($request->has('marital_status')) {
                $user->marital_status = $request->marital_status;
            }
    
            if ($request->has('nick_name')) {
                $user->nick_name = $request->nick_name;
            }
    
            if ($request->has('primary_contact')) {
                $user->primary_contact = $request->primary_contact;
            }
    
            if ($request->has('secondary_contact')) {
                $user->secondary_contact = $request->secondary_contact;
            }
    
            if ($request->has('emergency_contact')) {
                $user->emergency_contact = $request->emergency_contact;
            }
    
            if ($request->has('relation_contact')) {
                $user->relation_contact = $request->relation_contact;
            }
    
            if ($request->has('blood_donate')) {
                $user->blood_donate = $request->blood_donate;
            }
    
            if ($request->has('prev_designation')) {
                $user->prev_designation = $request->prev_designation;
            }
    
            if ($request->has('desk_id')) {
                $user->desk_id = $request->desk_id;
            }
    
            if ($request->has('joining_date')) {
                $user->joining_date = $request->joining_date;
            }

            if ($request->has('termination_date')) {
                $user->termination_date = $request->termination_date;
            }

            if ($request->has('employment_end')) {
                $user->employment_end = $request->employment_end;
            }
    
            if ($request->has('work_anniversary')) {
                $user->work_anniversary = $request->work_anniversary;
            }
    
            if ($request->has('about')) {
                $user->about = $request->about;
            }
    
            if ($request->has('present_address')) {
                $user->present_address = $request->present_address;
            }
    
            if ($request->has('permanent_address')) {
                $user->permanent_address = $request->permanent_address;
            }

            if ($request->has('password')) {
                // Hash the password before saving it
                $user->password = bcrypt($request->password);
            }            
    
            // For the EID and Email, don't allow them to be updated by regular users.
            // They will remain as is, even if empty values are sent.
    
            if ($request->has('email')) {
                $user->email = $request->email;
            }            
    
            if ($request->has('eid')) {
                $user->eid = $user->eid; // retain current EID if provided as empty or unchanged
            }
    
            // Sync other relationships if provided in the request
            if ($request->has('designations')) {
                $user->designations()->sync($request->designations);
            }
    
            if ($request->has('resource_types')) {
                $user->resource_types()->sync($request->resource_types);
            }
    
            if ($request->has('roles')) {
                $user->roles()->sync($request->roles);
            }
    
            if ($request->has('departments')) {
                $user->departments()->sync($request->departments);
            }
    
            if ($request->has('resource_statuses')) {
                $user->resource_statuses()->sync($request->resource_statuses);
            }
    
            if ($request->has('billing_statuses')) {
                $user->billing_statuses()->sync($request->billing_statuses);
            }
    
            if ($request->has('contact_types')) {
                $user->contact_types()->sync($request->contact_types);
            }
    
            if ($request->has('available_statuses')) {
                $user->available_statuses()->sync($request->available_statuses);
            }
    
            if ($request->has('member_statuses')) {
                $user->member_statuses()->sync($request->member_statuses);
            }
    
            if ($request->has('branches')) {
                $user->branches()->sync($request->branches);
            }
    
            if ($request->has('onsite_statuses')) {
                $user->onsite_statuses()->sync($request->onsite_statuses);
            }

            // If the user has no blood groups, sync the new blood group association
            if ($user->bloods->isEmpty() && $request->has('bloods') && !empty($request->bloods)) {
                \Log::info('No blood group found for user, adding new blood group associations.', [
                    'user_id' => $user->id,
                    'bloods' => $request->bloods
                ]);
    
                // Sync the blood group associations
                $user->bloods()->sync($request->bloods);
            } elseif ($request->has('bloods') && !empty($request->bloods)) {
                \Log::info('Updating blood group associations for user.', [
                    'user_id' => $user->id,
                    'bloods' => $request->bloods
                ]);
    
                // Sync the blood group associations
                $user->bloods()->sync($request->bloods);
            }
    
            // Handle team relationships and default team
            $teams = $request->teams;  // Array of team IDs
            $defaultTeamId = $request->default_team_id;  // The team ID to be marked as default
    
            if ($teams && count($teams) > 0) {
                // Detach all current teams and re-attach them
                $user->teams()->detach();
    
                // Attach the teams and set the default team
                foreach ($teams as $teamId) {
                    // If the team is the default team, set is_default to true
                    $isDefault = ($teamId == $defaultTeamId) ? true : false;
    
                    // Attach the team with the 'is_default' flag
                    $user->teams()->attach($teamId, ['is_default' => $isDefault]);
                }
            } else {
                \Log::warning('No teams provided to update for user.', ['user_id' => $user->id]);
            }
    
            \Log::info('Admin user updated successfully.', ['user_id' => $user->id]);
    
        } catch (\Exception $e) {
            \Log::error('Error updating admin user details.', ['user_id' => $user->id, 'error' => $e->getMessage()]);
            return response()->json(['error' => 'Error updating user details.'], 500);
        }
    
        // Save changes
        if ($user->save()) {
            \Log::info('Admin user saved successfully.', ['user_id' => $user->id]);
            return response()->json(['message' => 'User updated successfully.'], 200);
        }
    
        \Log::error('Failed to save admin user.', ['user_id' => $user->id]);
        return response()->json(['error' => 'Failed to update user. Please try again.'], 500);
    }
    
    private function updateUserWithRegularFields($user, Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Log request files and input fields
        Log::info('Request Files:', ['files' => $request->files->all()]);
        Log::info('Photo from input:', ['photo' => $request->input('photo')]);  // Base64 encoded photo

        // Decode the base64 image (if provided)
        $photoPath = $this->decodeBase64Image($request->input('photo'), 'photo');

        // Log the decoded photo path
        Log::info('Decoded Photo Path:', ['photo_path' => $photoPath]);

        // Handle photo update if a new one is provided
        if ($photoPath) {
            // If a new photo is uploaded, remove the old photo (optional)
            if ($user->photo && file_exists(storage_path('app/public/' . $user->photo))) {
                // Delete the old photo file
                unlink(storage_path('app/public/' . $user->photo));
            }

            // Update the user's photo path with the new one
            $user->photo = $photoPath;
        }
    
        try {
            // Only update contact-related fields
            if ($request->has('fname')) {
                $user->fname = $request->fname;
            }
    
            if ($request->has('lname')) {
                $user->lname = $request->lname;
            }
    
            if ($request->has('birthday')) {
                $user->birthday = $request->birthday;
            }
    
            if ($request->has('birthday_celebration')) {
                $user->birthday_celebration = $request->birthday_celebration;
            }
    
            if ($request->has('birthday_celebration_date')) {
                $user->birthday_celebration_date = $request->birthday_celebration_date;
            }
    
            if ($request->has('gender')) {
                $user->gender = $request->gender;
            }
    
            if ($request->has('marital_status')) {
                $user->marital_status = $request->marital_status;
            }
    
            if ($request->has('nick_name')) {
                $user->nick_name = $request->nick_name;
            }
    
            if ($request->has('primary_contact')) {
                $user->primary_contact = $request->primary_contact;
            }
    
            if ($request->has('secondary_contact')) {
                $user->secondary_contact = $request->secondary_contact;
            }
    
            if ($request->has('emergency_contact')) {
                $user->emergency_contact = $request->emergency_contact;
            }
    
            if ($request->has('relation_contact')) {
                $user->relation_contact = $request->relation_contact;
            }
    
            if ($request->has('blood_donate')) {
                $user->blood_donate = $request->blood_donate;
            }
    
            if ($request->has('prev_designation')) {
                $user->prev_designation = $request->prev_designation;
            }
    
            if ($request->has('desk_id')) {
                $user->desk_id = $request->desk_id;
            }
    
            if ($request->has('joining_date')) {
                $user->joining_date = $request->joining_date;
            }
    
            if ($request->has('work_anniversary')) {
                $user->work_anniversary = $request->work_anniversary;
            }
    
            if ($request->has('about')) {
                $user->about = $request->about;
            }
    
            if ($request->has('present_address')) {
                $user->present_address = $request->present_address;
            }
    
            if ($request->has('permanent_address')) {
                $user->permanent_address = $request->permanent_address;
            }

            if ($request->has('password')) {
                // Hash the password before saving it
                $user->password = bcrypt($request->password);
            }            
    
            // For the EID and Email, don't allow them to be updated by regular users.
            if ($request->has('email')) {
                $user->email = $request->email;
            } 
    
            if ($request->has('eid')) {
                $user->eid = $user->eid; // retain current EID if provided as empty or unchanged
            }
    
            // Sync other relationships if provided in the request
            if ($request->has('designations')) {
                $user->designations()->sync($request->designations);
            }
    
            if ($request->has('resource_types')) {
                $user->resource_types()->sync($request->resource_types);
            }
    
            if ($request->has('roles')) {
                $user->roles()->sync($request->roles);
            }
    
            if ($request->has('departments')) {
                $user->departments()->sync($request->departments);
            }
    
            if ($request->has('resource_statuses')) {
                $user->resource_statuses()->sync($request->resource_statuses);
            }
    
            if ($request->has('billing_statuses')) {
                $user->billing_statuses()->sync($request->billing_statuses);
            }
    
            if ($request->has('contact_types')) {
                $user->contact_types()->sync($request->contact_types);
            }
    
            if ($request->has('available_statuses')) {
                $user->available_statuses()->sync($request->available_statuses);
            }
    
            if ($request->has('member_statuses')) {
                $user->member_statuses()->sync($request->member_statuses);
            }
    
            if ($request->has('branches')) {
                $user->branches()->sync($request->branches);
            }
    
            if ($request->has('onsite_statuses')) {
                $user->onsite_statuses()->sync($request->onsite_statuses);
            }

            $user->updated_by = $authUser->id;
    
            // Handle team relationships and default team
            $teams = $request->teams;  // Array of team IDs
            $defaultTeamId = $request->default_team_id;  // The team ID to be marked as default
    
            if ($teams && count($teams) > 0) {
                // Detach all current teams and re-attach them
                $user->teams()->detach();
    
                // Attach the teams and set the default team
                foreach ($teams as $teamId) {
                    // If the team is the default team, set is_default to true
                    $isDefault = ($teamId == $defaultTeamId) ? true : false;
    
                    // Attach the team with the 'is_default' flag
                    $user->teams()->attach($teamId, ['is_default' => $isDefault]);
                }
            } else {
                \Log::warning('No teams provided to update for user.', ['user_id' => $user->id]);
            }
    
            // If the user has no blood groups, sync the new blood group association
            if ($user->bloods->isEmpty() && $request->has('bloods') && !empty($request->bloods)) {
                \Log::info('No blood group found for user, adding new blood group associations.', [
                    'user_id' => $user->id,
                    'bloods' => $request->bloods
                ]);
    
                // Sync the blood group associations
                $user->bloods()->sync($request->bloods);
            } elseif ($request->has('bloods') && !empty($request->bloods)) {
                \Log::info('Updating blood group associations for user.', [
                    'user_id' => $user->id,
                    'bloods' => $request->bloods
                ]);
    
                // Sync the blood group associations
                $user->bloods()->sync($request->bloods);
            }
    
            // Save the updated user data
            $user->save();
    
            // Log the updated user details
            \Log::info('User updated contact and personal information.', [
                'user_id' => $user->id,
                'fname' => $user->fname,
                'lname' => $user->lname,
                'birthday' => $user->birthday,
                'birthday_celebration' => $user->birthday_celebration,
                'birthday_celebration_date' => $user->birthday_celebration_date,
                'gender' => $user->gender,
                'marital_status' => $user->marital_status,
                'nick_name' => $user->nick_name,
                'primary_contact' => $user->primary_contact,
                'secondary_contact' => $user->secondary_contact,
                'emergency_contact' => $user->emergency_contact,
                'relation_contact' => $user->relation_contact,
                'blood_donate' => $user->blood_donate,
                'prev_designation' => $user->prev_designation,
                'desk_id' => $user->desk_id,
                'joining_date' => $user->joining_date,
                'work_anniversary' => $user->work_anniversary,
                'about' => $user->about,
                'present_address' => $user->present_address,
                'permanent_address' => $user->permanent_address,
            ]);
    
            // Save changes
            if ($user->save()) {
                \Log::info('Regular user saved successfully.', ['user_id' => $user->id]);
                return response()->json(['message' => 'User updated successfully.'], 200);
            }
    
            \Log::error('Failed to save regular user.', ['user_id' => $user->id]);
            return response()->json(['error' => 'Failed to update user. Please try again.'], 500);
        } catch (\Exception $e) {
            \Log::error('Error updating regular user details.', ['user_id' => $user->id, 'error' => $e->getMessage()]);
            return response()->json(['error' => 'Error updating user details.'], 500);
        }
    }
    
    // Helper function to decode base64 image data and store it
    protected function decodeBase64Image($base64String, $type)
    {
        if (!$base64String) {
            return null; // No file to handle
        }
    
        // Match and extract the image data and type
        if (preg_match('#^data:image/(\w+);base64,#i', $base64String, $matches)) {
            $imageType = $matches[1]; // Extracted image type (e.g., 'jpeg', 'png')
    
            // Remove the base64 header to get raw image data
            $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64String));
    
            // Check if decoding was successful
            if ($imageData === false) {
                return null; // If base64 decoding fails, return null
            }
    
            // Generate a unique file name with the appropriate extension
            $fileName = $type . '-' . uniqid() . '.' . $imageType; // Use dynamic extension based on image type
            $filePath = storage_path('app/public/profile/' . $fileName);
    
            // Save the file to storage
            file_put_contents($filePath, $imageData);
    
            return 'profile/' . $fileName;
        }
    
        return null; // If the base64 string does not match the expected format
    }
    

    // private function updateUserWithRegularFields($user, Request $request)
    // {
    //     try {
    //         // Update personal and contact-related fields
    //         $user->fname = $request->fname;
    //         $user->lname = $request->lname;
    //         $user->about = $request->about;
    //         $user->birthday = $request->birthday;
    //         $user->birthday_celebration = $request->birthday_celebration;
    //         $user->gender = $request->gender;
    //         $user->marital_status = $request->marital_status;
    //         $user->nick_name = $request->nick_name;
    //         $user->primary_contact = $request->primary_contact;
    //         $user->secondary_contact = $request->secondary_contact;
    //         $user->emergency_contact = $request->emergency_contact;
    //         $user->relation_contact = $request->relation_contact;
    //         $user->present_address = $request->present_address;
    //         $user->permanent_address = $request->permanent_address;
    //         $user->desk_id = $request->desk_id;
    //         $user->joining_date = $request->joining_date;
    //         $user->work_anniversary = $request->work_anniversary;

    //         // Additional contact fields that might be optional
    //         $user->contact = $request->contact;
    //         $user->emergency_contact = $request->emergency_contact;  // Note: This might be the same as the earlier one, adjust as needed
    //         $user->address = $request->address;
    //         $user->permanent_address = $request->permanent_address;

    //         // Log the updated information for debugging purposes
    //         \Log::info('Regular user updated their profile.', [
    //             'user_id' => $user->id,
    //             'fname' => $user->fname,
    //             'lname' => $user->lname,
    //             'about' => $user->about,
    //             'birthday' => $user->birthday,
    //             'birthday_celebration' => $user->birthday_celebration,
    //             'gender' => $user->gender,
    //             'marital_status' => $user->marital_status,
    //             'nick_name' => $user->nick_name,
    //             'primary_contact' => $user->primary_contact,
    //             'secondary_contact' => $user->secondary_contact,
    //             'emergency_contact' => $user->emergency_contact,
    //             'relation_contact' => $user->relation_contact,
    //             'present_address' => $user->present_address,
    //             'permanent_address' => $user->permanent_address,
    //             'desk_id' => $user->desk_id,
    //             'joining_date' => $user->joining_date,
    //             'work_anniversary' => $user->work_anniversary,
    //             'contact' => $user->contact,
    //             'address' => $user->address,
    //         ]);
    //     } catch (\Exception $e) {
    //         \Log::error('Error updating regular user details.', ['user_id' => $user->id, 'error' => $e->getMessage()]);
    //         return response()->json(['error' => 'Error updating user details.'], 500);
    //     }

    //     // Save changes to the user record
    //     if ($user->save()) {
    //         \Log::info('Regular user saved successfully.', ['user_id' => $user->id]);
    //         return response()->json(['message' => 'User updated successfully.'], 200);
    //     }

    //     \Log::error('Failed to save regular user.', ['user_id' => $user->id]);
    //     return response()->json(['error' => 'Failed to update user. Please try again.'], 500);
    // }


    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the product type
            $user = User::findOrFail($id);

            // Delete the product type
            $user->delete();

            return response()->json(['message' => 'User deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete the user.'], 403);
    }

 

    /**
     * Login based on the authentication token.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */

     public function login(Request $request)
     {
         // Validate the request data
         $request->validate([
             'eid' => 'required|string', // Ensure eid is required and is a string
             'password' => 'required|string', // Ensure password is required and is a string
         ]);
     
         // Find the user by eid
         $user = User::with([
            'roles',
            'teams',
            'departments',
            'resource_statuses',
            'resource_types',
            'billing_statuses',
            'designations',
            'bloods',
            'branches.locations',
            'contact_types',
            'member_statuses',
            'available_statuses',
            'onsite_statuses'
        ])->where('eid', $request->eid)->first();
     
         // Check if user exists and the password is correct
         if (!$user || !Hash::check($request->password, $user->password)) {
             return response()->json(['error' => 'The provided credentials are incorrect.'], 401);
         }
     
         // Check if email is verified
         if (!$user->email_verified_at) {
             return response()->json(['error' => 'Please verify your identity address to login.'], 401);
         }
     
         // Check if the user has an "active" resource status
         $activeStatus = $user->resource_statuses()->where('name', 'active')->exists();
     
         if (!$activeStatus) {
             return response()->json(['error' => 'Your account is not active. Please contact your manager.'], 401);
         }
     
         // Generate an API token for the user
         $token = $user->createToken('api-token')->plainTextToken;
     
         // Return the token and user ID in the response
         return response()->json([
             'token' => $token,
             'user_id' => $user->id,  // Send the user ID
             'user' => $user,  // Send the user Data
         ], 200);
     }
        


    // Inside your AuthController.php
    public function showLoginForm()
    {
        return response()->json(['message' => 'Please login to continue.'], 200);
    }



    // public function login(Request $request)
    // {
    //     // Validate the request data
    //     $request->validate([
    //         'eid' => 'required|string', // Ensure eid is required and is a string
    //         'password' => 'required|string', // Ensure password is required and is a string
    //     ]);
    
    //     // Find the user by eid
    //     $user = User::where('eid', $request->eid)->first();

    //     // Check if user exists and the password is correct
    //     if (!$user || !Hash::check($request->password, $user->password)) {
    //         return response()->json(['error' => 'The provided credentials are incorrect.'], 401);
    //     }
    //     // Generate an API token for the user
    //     $token = $user->createToken('api-token')->plainTextToken;
    
    //     // Return the token in the response
    //     return response()->json(['token' => $token], 200);
    // }

    /**
     * Logout the authenticated user.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        // Get the authenticated user
        $user = $request->user();

        // Check if the user is authenticated
        if (!$user) {
            return response()->json(['message' => 'Unauthorized.'], 401);
        }

        // Revoke the user's current access token
        $user->currentAccessToken()->delete();

        // Return a response indicating the user has been logged out
        return response()->json(['message' => 'Logged out successfully.'], 200);
    }


    // ***Create first Super Admin User***
    // Call the below method directly from Tinker
    // $controller = new App\Http\Controllers\AuthController();
    // $controller->createSuperAdmin();

    public function createSuperAdmin()
    {
        // Check if the super admin already exists
        $existingUser = User::where('email', '<EMAIL>')->first();
        if ($existingUser) {
            return response()->json(['message' => 'Super Admin already exists.'], 409);
        }

        // Create the super admin user
        $superAdmin = User::create([
            'eid' => '4444',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'), // Hash the password
        ]);

        // Assign the super admin role (assuming the role ID is 1)
        $superAdminRole = Role::where('name', 'super-admin')->first();
        $superAdmin->roles()->attach($superAdminRole->id);

        // Generate an authentication token for the super admin user
        $token = $superAdmin->createToken('api-token')->plainTextToken;

        // Return the user details and token
        return response()->json([
            'message' => 'Super Admin created successfully.',
            'user' => $superAdmin,
            'token' => $token,
        ], 201);
    }
}