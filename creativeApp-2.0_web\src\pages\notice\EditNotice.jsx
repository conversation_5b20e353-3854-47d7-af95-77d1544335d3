import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from "./EditorToolbar";
import 'react-quill/dist/quill.snow.css';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const isTokenValid = () => !!localStorage.getItem('token');

const EditNotice = ({ isVisible, setVisible, noticeId }) => {
    const [title, setTitle] = useState('');
    const [content, setContent] = useState('');
    const [priority, setPriority] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [category, setCategory] = useState('');
    const [expiryDate, setExpiryDate] = useState('');
    const [publishedDate, setPublishedDate] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [priorities, setPriorities] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [categories, setCategories] = useState([]);

    // Fetch dropdown data (departments, priorities, categories)
    useEffect(() => {
        const fetchData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
            try {
                const token = localStorage.getItem('token');

                const [departmentsRes, prioritiesRes, categoriesRes] = await Promise.all([
                    fetch(`${API_URL}departments`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    }),
                    fetch(`${API_URL}priorities`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    }),
                    fetch(`${API_URL}notice-board-category`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    })
                ]);

                const departmentsData = await departmentsRes.json();
                const prioritiesData = await prioritiesRes.json();
                const categoriesData = await categoriesRes.json();

                console.log("Departments:", departmentsData);
                console.log("Priorities:", prioritiesData);
                console.log("Categories:", categoriesData);

                setDepartments(departmentsData.departments || []);
                setPriorities(prioritiesData.priorities || []);
                setCategories(categoriesData.categories || []);
            } catch (error) {
                setError(error.message);
            }
        };
        fetchData();
    }, []);

    // Fetch notice details
    useEffect(() => {
        const fetchNotice = async () => {
            if (!noticeId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}notice/${noticeId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) throw new Error('Failed to fetch notice details.');

                const noticeData = await response.json();
                setTitle(noticeData.title || '');
                setContent(noticeData.content || '');
                setPriority(noticeData.priority || '');
                setSelectedTeam(noticeData.team || '');
                setSelectedDepartment(noticeData.department || '');
                setCategory(noticeData.category || '');
                setExpiryDate(noticeData.expiry_date || '');
                setPublishedDate(noticeData.published_date || '');
            } catch (error) {
                setError(error.message);
            }
        };

        fetchNotice();
    }, [noticeId]);

    // Handle department change
    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam('');

        const department = departments.find(dep => dep.name === departmentName);
        setTeams(department?.teams || []);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        console.log("Submit button clicked!");
    
        if (!title || !content || !priority || !selectedTeam || !selectedDepartment || !category) {
            setError('Please fill all required fields.');
            return;
        }
    
        setError('');
        setIsLoading(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                setIsLoading(false);
                return;
            }
    
            const payload = {
              title,
              content,
              priority,
              team: selectedTeam,
              department: selectedDepartment,
              category,
              expiry_date: expiryDate ? new Date(expiryDate).toISOString().split("T")[0] : null,
              published_date: publishedDate ? new Date(publishedDate).toISOString().split("T")[0] : null,
            };
    
            console.log("Updating Notice with Data:", payload);
    
            const response = await fetch(`${API_URL}notice/${noticeId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });
    
            const responseData = await response.json();
            console.log("Update Response:", responseData);
    
            if (!response.ok) throw new Error(responseData.message || 'Failed to update notice.');
    
            setSuccessMessage('Notice updated successfully!');
        } catch (error) {
            console.error("Update Error:", error);
            setError(error.message);
        } finally {
            setIsLoading(false);
        }
    };
    

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
  <div className="bg-white p-6 rounded-lg shadow-md w-[90%] max-w-3xl max-h-[80vh] overflow-y-auto relative">
    <h4 className="text-xl font-semibold mb-4 py-4">Edit Notice</h4>
    <button
      onClick={() => setVisible(false)}
      className="absolute top-4 right-4 text-gray-400 hover:text-gray-900 text-2xl font-bold"
    >
      &times;
    </button>

    <form className="space-y-4" onSubmit={handleSubmit}>
      {/* Full-Width Fields */}
      <div className="mb-4">
        <label>Title</label>
        <input
          type="text"
          value={title || ''}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full border rounded-md p-2"
          required
        />
      </div>

      <div className="mb-4">
        <label>Content</label>
        <EditorToolbar />
        <ReactQuill
          value={content || ''}
          onChange={setContent}
          className="bg-white border border-gray-300 rounded-md shadow-sm"
          theme="snow"
          modules={modules}
          formats={formats}
        />
      </div>

      {/* Two-Column Layout for Inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label>Priority</label>
          <select
            value={priority || ''}
            onChange={(e) => setPriority(e.target.value)}
            required
            className="w-full border rounded-md p-2"
          >
            <option value="">Select Priority</option>
            {priorities.map((p) => (
              <option key={p.id} value={p.name}>
                {p.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label>Department</label>
          <select
            value={selectedDepartment || ''}
            onChange={handleDepartmentChange}
            required
            className="w-full border rounded-md p-2"
          >
            <option value="">Select Department</option>
            {departments.map((dep) => (
              <option key={dep.id} value={dep.name}>
                {dep.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label>Team</label>
          <select
            value={selectedTeam || ''}
            onChange={(e) => setSelectedTeam(e.target.value)}
            required
            className="w-full border rounded-md p-2"
          >
            <option value="">Select Team</option>
            {teams.map((team) => (
              <option key={team.id} value={team.name}>
                {team.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label>Category</label>
          <select
            value={category || ''}
            onChange={(e) => setCategory(e.target.value)}
            required
            className="w-full border rounded-md p-2"
          >
            <option value="">Select Category</option>
            {categories.map((c) => (
              <option key={c.id} value={c.name}>
                {c.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label>Expiry Date</label>
          <input
            type="date"
            value={expiryDate || ''}
            onChange={(e) => setExpiryDate(e.target.value)}
            className="w-full border rounded-md p-2"
          />
        </div>

        <div>
          <label>Published Date</label>
          <input
            type="date"
            value={publishedDate || ''}
            onChange={(e) => setPublishedDate(e.target.value)}
            className="w-full border rounded-md p-2"
          />
        </div>
      </div>

      {/* Full-Width Submit Button */}
      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
      >
        {isLoading ? 'Updating...' : 'Update Notice'}
      </button>
    </form>
  </div>
</div>


    );
};

export default EditNotice;
