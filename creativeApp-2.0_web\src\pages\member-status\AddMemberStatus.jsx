import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddMemberStatus = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [memberStatuses, setMemberStatuses] = useState([]); 
    const [memberStatusName, setMemberStatusName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch member statuses if needed
    const fetchMemberStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/member_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }

            const data = await response.json();

            // Handle the case when member_statuses is empty or missing
            if (data && Array.isArray(data.member_statuses)) {
                setMemberStatuses(data.member_statuses);  // Set fetched member statuses
            } else {
                setMemberStatuses([]);  // If no member statuses found, set as empty array
            }

        } catch (error) {
            setError(error.message);
        }
    };

    // Run fetch when the component loads
    React.useEffect(() => {
        fetchMemberStatuses();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedMemberStatusName = memberStatusName.trim();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        // Check if the member status already exists
        const memberStatusExists = memberStatuses.some((status) => {
            const statusNameLower = status.name.toLowerCase().trim();
            return statusNameLower === trimmedMemberStatusName.toLowerCase();
        });

        if (memberStatusExists) {
            setError('Member status already exists. Please add a different member status.');
            setTimeout(() => setError(''), 3000);
            return;  // Exit if member status already exists
        }

        setError('');  // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Send the new member status data along with created_by and updated_by as full name
            const response = await fetch(`${API_URL}/member_statuses`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedMemberStatusName,
                    created_by: createdBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save member status: ' + response.statusText);
            }

            const result = await response.json();
            //setSuccessMessage(`Member status "${result.name || trimmedMemberStatusName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Team member status added successfully.',
            });

            setMemberStatusName('');  // Clear the member status input field

            // Refetch the member statuses list after adding the new status
            await fetchMemberStatuses();

        } catch (error) {
            alertMessage('error');
        }
    };

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
           
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Member Status</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="memberStatusName" className="block text-sm font-medium text-gray-700 pb-4">
                                Member Status Name
                            </label>
                            <input
                                type="text"
                                id="memberStatusName"
                                value={memberStatusName}
                                onChange={(e) => setMemberStatusName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Member Status
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddMemberStatus;
