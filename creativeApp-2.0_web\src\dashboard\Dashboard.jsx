import React, { useEffect, useState } from "react";
import WeatherData from '../pages/weatherAndTime/WeatherData';
import { API_URL } from './../common/fetchData/apiConfig';
import { useNavigate } from "react-router-dom";
import Loading from "../common/Loading";

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null && token !== ''; // Ensuring that the token is not empty
};

const Dashboard = () => {

  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  
  // Fetch logged-in user's data from the API
  useEffect(() => {
    const token = localStorage.getItem('token');
  
    if (!isTokenValid()) {
      setError('No valid authentication token found.');
      setLoading(false);
      navigate('/login');
      return; // Return early if no valid token exists
    }

    const user = localStorage.getItem('user');

    if(user) {
      setUserData(JSON.parse(user));
      setLoading(false);
      return; // Return early if user data is already available
    }
  
    const fetchUserData = async () => {

      setFilterOptionLoading(true);

      try {
        const response = await fetch(`${API_URL}logged-users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,  // Pass token in the header
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
  
        const data = await response.json();
  
        setUserData(data); // Set the user data
  
      } catch (error) {
        setError(error.message);
      } finally {
        setFilterOptionLoading(false);
      }
    };
  
    fetchUserData();
  }, []);

  // If loading users or filter options, show loading component
  if (filterOptionLoading) {
    return <Loading />;
  }

  return (
    <div className='rounded-xl'>
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-7 bg-white flex flex-auto border border-gray-300 dark:border-gray-600 p-12 rounded-2xl dark:bg-gray-800">
          <div className="flex flex-row justify-center gap-8 items-center">
            <div className="w-36 h-36 overflow-hidden rounded-full flex items-center justify-center bg-gray-100">
              {loading ? (
                  <div>...</div>
                ) : error ? (
                  <div>{error}</div>
                ) : userData ? (
                  userData.photo ? (
                    <img
                      className="w-36 h-36 rounded-full"
                      src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${userData.photo}`}
                      alt="Avatar"
                    />
                  ) : (
                    <div className="max-w-24">No Photo Available</div>
                  )
                ) : (
                  <div>No User Data</div>
                )}
            </div>
            <div className="text-left">
              <h3 className="text-2xl font-medium pb-4 dark:text-gray-300">
                {userData ? (userData.fname || "Name") : "Name"} {userData ? (userData.lname || "Not Found") : "Not Found"}
              </h3>
              <p className="text-xl pb-4 dark:text-gray-300">
                {userData && userData.designations?.[0]?.name ? userData.designations[0].name : "No Designation"}
              </p>

              <span className="bg-gray-200 py-3 px-4 text-primary text-base rounded-full max-w-24 block text-center dark:text-gray-400 dark:bg-gray-600">{userData?.member_statuses?.[0]?.name || "Not found"}</span>
            </div>
          </div>
        </div>
        <div className="col-span-5 flex flex-wrap border border-gray-300 dark:border-gray-600 rounded-2xl">
          <WeatherData />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
