<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TaskDetails;
use App\Models\TaskType;
use App\Models\ProductType;
use App\Models\RevisionType;
use App\Models\Region;
use App\Models\RecordType;
use App\Models\Priority;
use App\Models\Reporter;
use App\Models\Team;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

use Illuminate\Support\Facades\Log;

class TaskDetailsController extends Controller
{
    /**
     * Show all Task Details with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    //Query and Filtered data for Data table
    public function taskRecordsData(Request $request)
    {
        // $query = TaskDetails::query();

        $query = TaskDetails::with([
            'product_types',
            'task_types',
            'record_types',
            'revision_types',
            'regions',
            'priorities',
            'reporters',
            'creator',
            'updater', 
        ]);

        // return response()->json($query, 200);

        // Decode all input parameters to handle URL-encoded values
        $decodedDepartment = $request->filled('department') ? urldecode($request->input('department')) : null;
        $decodedTeam = $request->filled('team') ? urldecode($request->input('team')) : null;
        $decodedDate = $request->filled('date') ? urldecode($request->input('date')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedTicketNumber = $request->filled('ticket_number') ? urldecode($request->input('ticket_number')) : null;
        $decodedReceivedDate = $request->filled('received_date') ? urldecode($request->input('received_date')) : null;
        $decodedDueDate = $request->filled('due_date') ? urldecode($request->input('due_date')) : null;
        $decodedPriority = $request->filled('priority_id') ? urldecode($request->input('priority_id')) : null;
        $decodedTaskType = $request->filled('task_type_id') ? urldecode($request->input('task_type_id')) : null;
        $decodedRevisionType = $request->filled('revision_type_id') ? urldecode($request->input('revision_type_id')) : null;
        $decodedProductType = $request->filled('product_type_id') ? urldecode($request->input('product_type_id')) : null;
        $decodedUnit = $request->filled('hour') ? urldecode($request->input('hour')) : null;
        $decodedReporter = $request->filled('reporter_id') ? urldecode($request->input('reporter_id')) : null;
        $decodedRegion = $request->filled('region_id') ? urldecode($request->input('region_id')) : null;
        $decodedAccountName = $request->filled('account_name') ? urldecode($request->input('account_name')) : null;
        $decodedCampaignName = $request->filled('campaign_name') ? urldecode($request->input('campaign_name')) : null;
        $decodedNotes = $request->filled('notes') ? urldecode($request->input('notes')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;

        
        // Filtering: Support multiple values for ticket_number, shift, teams and departments
        
        if ($decodedDepartment) {
            $departments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($departments) {
                foreach ($departments as $department) {
                    $q->orWhere('department', '=', trim($department));
                }
            });
        }
        
        if ($decodedTeam) {
            $teams = explode(',', $decodedTeam);
            $query->where(function ($q) use ($teams) {
                foreach ($teams as $team) {
                    $q->orWhere('team', '=', trim($team));
                }
            });
        }

        if ($decodedDate) {
            $dates = explode(',', $decodedDate);
            $query->where(function ($q) use ($dates) {
                foreach ($dates as $date) {
                    $q->orWhere('created_at', 'like', '%' .trim($date). '%');
                }
            });
        }
        
        if ($decodedCreatedAt) {
            $createdAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($createdAts) {
                foreach ($createdAts as $createdAt) {
                    $q->orWhere('created_at', 'like', '%' .trim($date). '%');
                }
            });
        }

        if ($decodedTicketNumber) {
            $ticketNumbers = explode(',', $decodedTicketNumber);
            $query->where(function ($q) use ($ticketNumbers) {
                foreach ($ticketNumbers as $ticketNumber) {
                    $q->orWhere('ticket_number', 'like', '%' .trim($ticketNumber). '%');
                }
            });
        }

        if ($decodedReceivedDate) {
            $receivedDates = explode(',', $decodedReceivedDate);
            $query->where(function ($q) use ($receivedDates) {
                foreach ($receivedDates as $receivedDate) {
                    $q->orWhere('recieved_date', 'like', '%' .trim($receivedDate). '%');
                }
            });
        }

        if ($decodedDueDate) {
            $dueDates = explode(',', $decodedDueDate);
            $query->where(function ($q) use ($dueDates) {
                foreach ($dueDates as $dueDate) {
                    $q->orWhere('due_date', 'like', '%' .trim($dueDate). '%');
                }
            });
        }

        if ($decodedPriority) {
            $priorities = explode(',', $decodedPriority);
            $query->where(function ($q) use ($priorities) {
                foreach ($priorities as $priority) {
                    $q->orWhere('priority_id', '=', trim($priority));
                }
            });
        }

        if ($decodedTaskType) {
            $taskTypes = explode(',', $decodedTaskType);
            $query->where(function ($q) use ($taskTypes) {
                foreach ($taskTypes as $taskType) {
                    $q->orWhere('task_type_id', '=', trim($taskType));
                }
            });
        }

        if ($decodedRevisionType) {
            $revisionTypes = explode(',', $decodedRevisionType);
            $query->where(function ($q) use ($revisionTypes) {
                foreach ($revisionTypes as $revisionType) {
                    $q->orWhere('revision_type_id', '=', trim($revisionType));
                }
            });
        }

        if ($decodedProductType) {
            $productTypes = explode(',', $decodedProductType);
            $query->where(function ($q) use ($productTypes) {
                foreach ($productTypes as $productType) {
                    $q->orWhere('product_type_id', '=', trim($productType));
                }
            });
        }

        if ($decodedUnit) {
            $units = explode(',', $decodedUnit);
            $query->where(function ($q) use ($units) {
                foreach ($units as $unit) {
                    $q->orWhere('unit', 'like', '%' .trim($unit). '%');
                }
            });
        }

        if ($decodedReporter) {
            $reporters = explode(',', $decodedReporter);
            $query->where(function ($q) use ($reporters) {
                foreach ($reporters as $reporter) {
                    $q->orWhere('reporter_id', '=', trim($reporter));
                }
            });
        }

        if ($decodedRegion) {
            $regions = explode(',', $decodedRegion);
            $query->where(function ($q) use ($regions) {
                foreach ($regions as $region) {
                    $q->orWhere('region_id', '=', trim($region));
                }
            });
        }

        if ($decodedAccountName) {
            $accountNames = explode(',', $decodedAccountName);
            $query->where(function ($q) use ($accountNames) {
                foreach ($accountNames as $accountName) {
                    $q->orWhere('account_name', 'like', '%' .trim($accountName). '%');
                }
            });
        }

        if ($decodedCampaignName) {
            $campaignNames = explode(',', $decodedCampaignName);
            $query->where(function ($q) use ($campaignNames) {
                foreach ($campaignNames as $campaignName) {
                    $q->orWhere('campaign_name', 'like', '%' .trim($campaignName). '%');
                }
            });
        }
        
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }


        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;

        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('ticket_number', 'like', '%' . $globalSearch . '%')
                    // ->orWhere('date', 'like', '%' . $globalSearch . '%')
                    ->orWhere('department', 'like', '%' . $globalSearch . '%')
                    ->orWhere('team', 'like', '%' . $globalSearch . '%')
                    ->orWhere('created_at', 'like', '%' . $globalSearch . '%')
                    // ->orWhere('recieved_date', 'like', '%' . $globalSearch . '%')
                    // ->orWhere('due_date', 'like', '%' . $globalSearch . '%')
                    // Corrected the relation names for 'priority_id', 'task_type_id', etc.
                    ->orWhereHas('priorities', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('task_types', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('revision_types', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('product_types', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhere('unit', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('reporters', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('regions', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhere('account_name', 'like', '%' . $globalSearch . '%')
                    ->orWhere('campaign_name', 'like', '%' . $globalSearch . '%')
                    ->orWhere('notes', 'like', '%' . $globalSearch . '%')
                    // Corrected 'creator' and 'updater' relationships
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }



        // Sorting: Use query parameters 'sort_by' and 'order'
        // Default sorting: sort by 'created_at' in descending order
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);
        

        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $taskrecords = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($taskrecords, 200);
    }

    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        
        // Build the query: Select the group column and the count of records in each group.
        $results = TaskDetails::with([
            'creator','updater', 'teams', 'departments', 'product_types', 'task_types', 'record_types', 'revision_types', 'regions', 'priorities', 'reporters',
        ]);              

        
        $results->select($column, $column. ' as ticket_number', \DB::raw("COUNT(*) as total"));

        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }
        
        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = TaskDetails::with([
            'creator','updater', 'teams', 'departments', 'product_types', 'task_types', 'record_types', 'revision_types', 'regions', 'priorities', 'reporters',
        ]); 

        

        
        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        }else{
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    //Previous Mthods for regular data
    public function index()
    {
        $task_details = TaskDetails::with([
            'product_types',
            'task_types',
            'record_types',
            'revision_types',
            'regions',
            'priorities',
            'reporters',
        ])->get();
    
        Log::info('All Task Details Retrieved:', ['task_count' => $task_details->count()]);
    
        return response()->json(['taskDetails' => $task_details], 200);
    }

    // Filter Task based on due date and the task which due date expired in previous 3 days
    public function filterDueDate()
    {
        // Get the current date
        $currentDate = Carbon::now();
    
        // Retrieve task details with related data
        $task_details = TaskDetails::with([
            'product_types',
            'task_types',
            'record_types',
            'revision_types',
            'regions',
            'priorities',
            'reporters',
        ])
        ->get();
    
        // Filter tasks where due_date is either in the future or expired before 3 days
        $filtered_task_details = $task_details->filter(function ($task) use ($currentDate) {
            // Convert the due_date to a Carbon instance
            $dueDate = Carbon::parse($task->due_date);
    
            // Check if the task's due date is in the future or expired before 3 days
            return $dueDate->isFuture() || $dueDate->diffInDays($currentDate) <= 10;
        });
    
        // Return filtered task details in the same format as the index method (JSON response)
        return response()->json(['taskDetails' => $filtered_task_details->values()]);
    }
    

    /**
     * Display the specified Task Details.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the Task Details by ID
        $taskDetail = TaskDetails::with([
            'product_types',
            'task_types',
            'record_types',
            'revision_types',
            'regions',
            'priorities',
            'reporters',
        ])->findOrFail($id);

        if (!$taskDetail) {
            return response()->json(['error' => 'Task Details not found.'], 404);
        }

        // Log the Task Details retrieved
        Log::info('Task Details Retrieved:', ['taskDetail' => $taskDetail]);

        return response()->json(['taskDetail' => $taskDetail], 200);
    }


        /**
     * Create a new Task Details by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Ensure the user is authenticated
        if (!auth()->check()) {
            Log::warning('Unauthorized access attempt: No authenticated user.');
            return response()->json(['error' => 'Unauthorized'], 401); // Or handle accordingly
        }
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
    
        // Validate the request data
        try {
            $validatedData = $request->validate([
                'ticket_number' => 'required|string|max:255',
                'received_date' => 'required|date',
                'due_date' => 'required|date',
                'unit' => 'required|integer',
                'account_name' => 'required|string|max:255',
                'campaign_name' => 'required|string|max:255',
                'notes' => 'nullable|string',
                'department' => 'nullable|string',
                'team' => 'nullable|string',
                'product_type_id' => 'nullable|exists:product_types,id',
                'task_type_id' => 'nullable|exists:task_types,id',
                'revision_type_id' => 'nullable|exists:revision_types,id',
                'region_id' => 'nullable|exists:regions,id',
                'priority_id' => 'nullable|exists:priorities,id',
                'reporter_id' => 'nullable|exists:reporters,id',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation error
            Log::error('Validation failed:', ['errors' => $e->errors()]);
            return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
        }
    
        // Log the request data after validation
        Log::info('Create Task Details Request:', ['request' => $validatedData]);
    
        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'])->exists()) {
            // Log role-based access check success
            Log::info('User has required role:', ['user_id' => $authUser->id, 'role' => 'super-admin/admin']);
    
            // Create a new TaskDetails record
            try {
                $taskDetail = TaskDetails::create([
                    'ticket_number' => $validatedData['ticket_number'],
                    'received_date' => $validatedData['received_date'],
                    'due_date' => $validatedData['due_date'],
                    'unit' => $validatedData['unit'],
                    'account_name' => $validatedData['account_name'],
                    'campaign_name' => $validatedData['campaign_name'],
                    'notes' => $validatedData['notes'],
                    'department' => $validatedData['department'],
                    'team' => $validatedData['team'],
                    'product_type_id' => $validatedData['product_type_id'],
                    'task_type_id' => $validatedData['task_type_id'],
                    'revision_type_id' => $validatedData['revision_type_id'],
                    'region_id' => $validatedData['region_id'],
                    'priority_id' => $validatedData['priority_id'],
                    'reporter_id' => $validatedData['reporter_id'],
                    'created_by' => $authUser->id,
                ]);

                $taskDetail->updated_at = null;
                $taskDetail->saveQuietly(); 
    
                // Log the TaskDetails creation
                Log::info('Task Details Created:', ['taskDetail' => $taskDetail]);
    
                // Return the success response with TaskDetails data
                return response()->json([
                    'message' => 'Task Details created successfully.',
                    'taskDetail' => $taskDetail
                ], 201);
            } catch (\Exception $e) {
                // Log any error during task creation
                Log::error('Error creating TaskDetails:', ['error' => $e->getMessage()]);
                return response()->json(['error' => 'Failed to create Task Details. Please try again later.'], 500);
            }
        }
    
        // Deny access for users without the proper role
        Log::warning('Unauthorized Task Details Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create Task Details.'], 403);
    }
    
    
    /**
     * Update an existing Task Details by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Ensure the user is authenticated
        if (!auth()->check()) {
            Log::warning('Unauthorized access attempt: No authenticated user.');
            return response()->json(['error' => 'Unauthorized'], 401); // Or handle accordingly
        }

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        try {
            $validatedData = $request->validate([
                'ticket_number' => 'required|string|max:255',
                'received_date' => 'required|date',
                'due_date' => 'required|date',
                'unit' => 'required|integer',
                'account_name' => 'required|string|max:255',
                'campaign_name' => 'required|string|max:255',
                'notes' => 'nullable|string',
                'department' => 'nullable|string',
                'team' => 'nullable|string',
                'product_type_id' => 'nullable|exists:product_types,id',
                'task_type_id' => 'nullable|exists:task_types,id',
                'revision_type_id' => 'nullable|exists:revision_types,id',
                'region_id' => 'nullable|exists:regions,id',
                'priority_id' => 'nullable|exists:priorities,id',
                'reporter_id' => 'nullable|exists:reporters,id',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Log validation error
            Log::error('Validation failed:', ['errors' => $e->errors()]);
            return response()->json(['error' => 'Validation failed', 'details' => $e->errors()], 422);
        }

        // Log the request data after validation
        Log::info('Update Task Details Request:', ['request' => $validatedData]);

        // Find the TaskDetails record with the given id
        $taskDetail = TaskDetails::find($id);

        // Check if task details exist
        if (!$taskDetail) {
            Log::warning('Task Details not found:', ['id' => $id]);
            return response()->json(['error' => 'Task Details not found.'], 404);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'])->exists()) {
            // Log role-based access check success
            Log::info('User has required role:', ['user_id' => $authUser->id, 'role' => 'super-admin/admin']);

            // Update the TaskDetails record
            try {
                $taskDetail->update([
                    'ticket_number' => $validatedData['ticket_number'],
                    'received_date' => $validatedData['received_date'],
                    'due_date' => $validatedData['due_date'],
                    'unit' => $validatedData['unit'],
                    'account_name' => $validatedData['account_name'],
                    'campaign_name' => $validatedData['campaign_name'],
                    'notes' => $validatedData['notes'],
                    'department' => $validatedData['department'],
                    'team' => $validatedData['team'],
                    'product_type_id' => $validatedData['product_type_id'],
                    'task_type_id' => $validatedData['task_type_id'],
                    'revision_type_id' => $validatedData['revision_type_id'],
                    'region_id' => $validatedData['region_id'],
                    'priority_id' => $validatedData['priority_id'],
                    'reporter_id' => $validatedData['reporter_id'],
                    'updated_by' => $authUser->id,
                ]);

                // Log the TaskDetails update
                Log::info('Task Details Updated:', ['taskDetail' => $taskDetail]);

                // Return the success response with updated TaskDetails data
                return response()->json([
                    'message' => 'Task Details updated successfully.',
                    'taskDetail' => $taskDetail
                ], 200);
            } catch (\Exception $e) {
                // Log any error during task update
                Log::error('Error updating TaskDetails:', ['error' => $e->getMessage()]);
                return response()->json(['error' => 'Failed to update Task Details. Please try again later.'], 500);
            }
        }

        // Deny access for users without the proper role
        Log::warning('Unauthorized Task Details Update Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to update Task Details.'], 403);
    }

    /**
     * Delete a Task Details by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the Task Details
            $taskDetail = TaskDetails::findOrFail($id);

            // Delete the Task Details
            $taskDetail->delete();

            return response()->json(['message' => 'Task Details deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this Task Details.'], 403);
    }
}
