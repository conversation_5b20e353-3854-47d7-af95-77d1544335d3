import React from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const ViewNotice = ({ isVisible, setVisible, notice }) => {
    if (!isVisible || !notice) return null; // Hide modal if not visible or no notice selected

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 p-4">
      <div className="bg-white w-full md:w-3/4 lg:w-1/2 p-6 rounded-lg shadow-lg relative">
        {/* Title */}
        <h2 className="text-2xl font-bold mb-4 text-2xl font-semibold">{notice.title}</h2>

        {/* Description Section */}
        <div className="border rounded-lg p-4 bg-gray-100">
          <ReactQuill
            value={notice.content || ""}
            theme="snow"
            readOnly={true}
            modules={{ toolbar: false }}
            className="custom-quill"
          />
        </div>

        {/* Details Section */}
        <div className="flex flex-wrap gap-4 mt-6 border-t pt-4 text-gray-700 text-sm">
          <p className="font-bold text-base text-2xl"><strong>Category:</strong> {notice.category}</p>
          <p className="font-bold text-base text-2xl"><strong>Department:</strong> {notice.department}</p>
          <p className="font-bold text-base text-2xl"><strong>Team:</strong> {notice.team}</p>
          <p className="font-bold text-base text-2xl"><strong>Priority:</strong> <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs">{notice.priority}</span></p>
          <p className="font-bold text-base text-2xl"><strong>Expiry Date:</strong><span className="border-4 border-dotted border-orange-600 bg-indigo-100 rounded-full text-xs"> {notice.expiry_date}</span></p>
          <p className="font-bold text-base text-2xl"><strong>Published Date:</strong><span className="bg-orange-300  text-white px-2 py-1 border-dotted rounded-full text-xs"> {notice.published_date}</span></p>
        </div>

        {/* Close Button */}
        <button
          onClick={() => setVisible(false)}
          className="absolute top-2 right-2 bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-full"
        >
          ✖
        </button>
      </div>
    </div>
    );
};

export default ViewNotice;
