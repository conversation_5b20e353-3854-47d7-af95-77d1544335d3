<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Team;
use App\Models\User;

class AttendanceSeeder extends Seeder
{
    public function run(): void
    {
        // Ensure required related data exists
        // if (Department::count() == 0) {
        //     Department::factory(5)->create();
        // }

        // if (Team::count() == 0) {
        //     Team::factory(5)->create();
        // }

        // if (User::count() == 0) {
        //     User::factory(10)->create();
        // }

        // Create 50 attendance entries with real relational data
        Attendance::factory(500)->create();
    }
}
