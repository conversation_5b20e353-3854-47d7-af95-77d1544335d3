import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { setToken, logout } from './authSlice';

const baseApiURL = process.env.REACT_APP_BASE_API_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: baseApiURL,
  prepareHeaders: (headers, { getState }) => {
    // const token = getState().auth.token;
    const token = localStorage.getItem("token");
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
    let result = await baseQuery(args, api, extraOptions);
  
    if (result.error && result.error.status === 401) {
      // Try to refresh token
      const refreshResult = await baseQuery('/refresh-token', api, extraOptions);
      if (refreshResult.data) {
        api.dispatch(setToken(refreshResult.data.token)); // Store new token
        result = await baseQuery(args, api, extraOptions); // Retry the original request
      } else {
        api.dispatch(logout());
      }
    }
    return result;
  };
  

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  keepUnusedDataFor: 0,
});
