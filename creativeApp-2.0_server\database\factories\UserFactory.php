<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'eid' => $this->faker->unique()->randomNumber(4),
            'photo' => "https://picsum.photos/640/480",
            'fname' => $this->faker->firstName(),
            'lname' => $this->faker->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
            'about' => $this->faker->paragraph(),
            'birthday' => $this->faker->date(),
            'birthday_celebration' => $this->faker->boolean(),
            'birthday_celebration_date' => $this->faker->optional()->date(),
            'gender' => $this->faker->randomElement(['male', 'female', 'other']),
            'marital_status' => $this->faker->randomElement(['single', 'married', 'divorced']),
            'nick_name' => $this->faker->userName(),
            'primary_contact' => $this->faker->phoneNumber(),
            'secondary_contact' => $this->faker->optional()->phoneNumber(),
            'emergency_contact' => $this->faker->phoneNumber(),
            'relation_contact' => $this->faker->word(),
            'present_address' => $this->faker->address(),
            'permanent_address' => $this->faker->address(),
            'blood_donate' => $this->faker->boolean(),
            'prev_designation' => $this->faker->optional()->jobTitle(),
            'report_to' => null,
            'desk_id' => $this->faker->optional()->randomNumber(4),
            'joining_date' => $this->faker->date(),
            'termination_date' => null,
            'employment_end' => null,
            'work_anniversary' => $this->faker->optional()->date(),
            'password' => Hash::make('password'),
            'created_by'  => User::inRandomOrder()->value('id') ?? "",
            'updated_by'  => User::inRandomOrder()->value('id') ?? "",
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
        ];
    }
}
