import React, { useEffect, useState } from "react";
import { fetchWeather<PERSON><PERSON> } from "openmeteo";
import CustomClock from "./CustomClock";
import weatherBackground from "../../assets/images/weather-background.png";
import weatherIcon from "../../assets/images/icon-sun-cloud.png";

const WeatherData = () => {
    const [weatherData, setWeatherData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [ipData, setIpData] = useState(false);
    const [data5Days, set5DaysData] = useState(false);
    const background = {
        backgroundImage: `url(${weatherBackground})`,
        backgroundPosition: 'center',
        backgroundSize: 'cover',
    }
    // Fetch IP data with multiple API fallbacks
    useEffect(() => {
        const fetchLocationData = async () => {
            // For development, use a fixed location to avoid CORS issues
            if (process.env.NODE_ENV === 'development') {
                console.log('Development mode: Using fixed location');
                setIpData({
                    lat: 23.8103,
                    lon: 90.4125,
                    city: 'Dhaka',
                    country: 'Bangladesh',
                    timezone: 'Asia/Dhaka'
                });
                return;
            }

            // For production, add a timeout to ensure we don't wait too long
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('API timeout')), 10000); // 10 second timeout
            });

           
            const geoApis = [
                {
                    url: 'https://reallyfreegeoip.org/json/',
                    transform: (data) => ({
                        lat: data.latitude,
                        lon: data.longitude,
                        city: data.city,
                        country: data.country_name,
                        timezone: data.time_zone
                    })
                },
                {
                    url: 'https://freegeoip.tech/json/',
                    transform: (data) => ({
                        lat: data.latitude,
                        lon: data.longitude,
                        city: data.city,
                        country: data.country_name,
                        timezone: data.time_zone
                    })
                },
                {
                    url: 'https://api.ipgeolocation.io/ipgeo?apiKey=free',
                    transform: (data) => ({
                        lat: parseFloat(data.latitude),
                        lon: parseFloat(data.longitude),
                        city: data.city,
                        country: data.country_name,
                        timezone: data.time_zone?.name || data.timezone
                    })
                },
                {
                    url: 'https://freeipapi.com/api/json',
                    transform: (data) => ({
                        lat: data.latitude,
                        lon: data.longitude,
                        city: data.cityName,
                        country: data.countryName,
                        timezone: data.timeZone
                    })
                }
            ];

            // First try browser geolocation if available
            if (navigator.geolocation) {
                try {
                    const position = await new Promise((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(resolve, reject, {
                            timeout: 5000,
                            enableHighAccuracy: false
                        });
                    });

                    console.log('Using browser geolocation');
                    setIpData({
                        lat: position.coords.latitude,
                        lon: position.coords.longitude,
                        city: 'Current Location',
                        country: 'Current Location',
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                    });
                    return;
                } catch (geoError) {
                    console.log('Browser geolocation failed, trying IP APIs:', geoError.message);
                }
            }

            // Try each API until one works
            for (let i = 0; i < geoApis.length; i++) {
                const api = geoApis[i];
                try {
                    console.log(`Trying API ${i + 1}/${geoApis.length}: ${api.url}`);

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout per API

                    const response = await fetch(api.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (compatible; WeatherApp/1.0)',
                        },
                        mode: 'cors',
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`API ${i + 1} Response:`, data);

                    // Transform data to consistent format
                    const transformedData = api.transform(data);

                    // Validate required fields
                    if (transformedData.lat && transformedData.lon &&
                        !isNaN(transformedData.lat) && !isNaN(transformedData.lon)) {
                        console.log(`✅ Successfully fetched location data from API ${i + 1}:`, transformedData);
                        setIpData(transformedData);
                        return; // Success, exit the loop
                    } else {
                        throw new Error('Invalid or incomplete location data received');
                    }
                } catch (err) {
                    console.warn(`❌ API ${i + 1} failed (${api.url}):`, err.message);

                    // Special handling for the lead's recommended APIs
                    if (i < 2) { 
                        console.warn(`⚠️  Lead's recommended API ${i + 1} failed. This might need attention.`);
                    }

                    // Continue to next API
                }
            }

            // If all APIs fail, try a simple approach with user's timezone
            console.error('All geolocation APIs failed, using timezone-based location');

            // Get user's timezone and set approximate location
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            let defaultLocation = {
                lat: 40.7128,
                lon: -74.0060,
                city: 'New York',
                country: 'United States',
                timezone: 'America/New_York'
            };

            // Set location based on common timezones
            if (userTimezone.includes('Asia/Dhaka')) {
                defaultLocation = {
                    lat: 23.8103,
                    lon: 90.4125,
                    city: 'Dhaka',
                    country: 'Bangladesh',
                    timezone: 'Asia/Dhaka'
                };
            } else if (userTimezone.includes('Europe/London')) {
                defaultLocation = {
                    lat: 51.5074,
                    lon: -0.1278,
                    city: 'London',
                    country: 'United Kingdom',
                    timezone: 'Europe/London'
                };
            } else if (userTimezone.includes('America/Los_Angeles')) {
                defaultLocation = {
                    lat: 34.0522,
                    lon: -118.2437,
                    city: 'Los Angeles',
                    country: 'United States',
                    timezone: 'America/Los_Angeles'
                };
            }

            console.log('Using timezone-based location:', defaultLocation);
            setIpData(defaultLocation);
        };

        fetchLocationData();
    }, []);
    // traffic url generate
    const trafficUrl = `https://www.google.com/maps/@${ipData?.lat},${ipData?.lon}`;
    const params = {
        latitude: ipData?.lat,
        longitude: ipData?.lon,
        hourly: [
            "temperature_2m", "relative_humidity_2m", "apparent_temperature", "precipitation_probability", "precipitation", "rain", "visibility", "wind_speed_10m", "uv_index",
        ],
        daily: ["weather_code", "sunrise", "sunset"],
        timeformat: "unixtime",
        timezone: "auto",
        past_days: 0,
        forecast_days: 6,
    };
    function getRoundedHour(unixTimestamp) {
        // Create a date object from the Unix timestamp (in seconds)
        const date = new Date(unixTimestamp * 1000);

        // Get hours, minutes, and seconds
        const hours = date.getUTCHours() + 6;
        const minutes = date.getUTCMinutes();
        const seconds = date.getUTCSeconds();

        // Determine the rounded hour
        let roundedHour;
        if (minutes > 31 || seconds > 31) {
            roundedHour = hours + 1; // Round up to the next hour
        } else {
            roundedHour = hours; // Keep the same hour
        }

        // Return the rounded hour (in 24-hour format)
        return roundedHour % 24; // Ensure it wraps around at 24
    }

    // Example usage
    const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp
    const roundedHour = getRoundedHour(unixTime);

    const currentTime = Math.floor(Date.now() / 1000);
    const isoString = new Date(currentTime * 1000).toISOString();

    useEffect(() => {
        if (!ipData || !ipData.lat || !ipData.lon) {
            console.log('Waiting for location data...');
            return;
        }

        const fetchWeather = async () => {
            try {
                console.log('Fetching weather data for:', ipData);
                const url = "https://api.open-meteo.com/v1/forecast";
                const responses = await fetchWeatherApi(url, params);

                if (!responses || responses.length === 0) {
                    throw new Error('No weather data received');
                }

                const range = (start, stop, step) =>
                    Array.from({ length: (stop - start) / step }, (_, i) => start + i * step);
                const response = responses[0];

                const utcOffsetSeconds = response.utcOffsetSeconds();
                const hourly = response.hourly();
                const daily = response.daily();

                if (!hourly || !daily) {
                    throw new Error('Invalid weather data structure');
                }

                const weather = {
                    hourly: {
                        time: range(
                            Number(hourly.time()),
                            Number(hourly.timeEnd()),
                            hourly.interval()
                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                        temperature2m: hourly.variables(0).valuesArray(),
                        relativeHumidity2m: hourly.variables(1).valuesArray(),
                        apparentTemperature: hourly.variables(2).valuesArray(),
                        precipitationProbability: hourly.variables(3).valuesArray(),
                        precipitation: hourly.variables(4).valuesArray(),
                        rain: hourly.variables(5).valuesArray(),
                        visibility: hourly.variables(6).valuesArray(),
                        windSpeed10m: hourly.variables(7).valuesArray(),
                        uvIndex: hourly.variables(8).valuesArray(),
                    },
                    daily: {
                        time: range(
                            Number(daily.time()),
                            Number(daily.timeEnd()),
                            daily.interval()
                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                        weatherCode: daily.variables(0).valuesArray(),
                        sunrise: daily.variables(1).valuesArray(),
                        sunset: daily.variables(2).valuesArray(),
                    },
                };

                setWeatherData(weather);
                console.log('Weather data successfully loaded');
            } catch (err) {
                console.error('Weather fetch error:', err);
                setError(err);
            } finally {
                setLoading(false);
            }
        };
        fetchWeather();
    }, [ipData]);

    useEffect(() => {
        // filter 5days data
        if (!weatherData) return;
        const getFivePMData = (data) => {
            const result = [];

            // 5 PM index is 17, calculate the indices for 6 days (6 * 24 = 144)
            const fivePMIndex = roundedHour;

            // Set here day 1 to avoid today's data
            for (let day = 1; day < 6; day++) {
                const index = day * 24 + fivePMIndex;
                result.push({
                    dayName: data.daily.time[day].toLocaleString('default', { weekday: 'short' }), // Get day name
                    date: data.daily.time[day].toLocaleString('default', { day: 'numeric', month: 'long' }), // Get date month
                    temperature: data.hourly.temperature2m[index], // Temperature
                    feelTemperature: data.hourly.apparentTemperature[index], // Feels-like temperature
                });
            }

            return result;
        };
        // set 5days Temperature
        set5DaysData(getFivePMData(weatherData))
    }, [ipData, weatherData]);

    if (loading) {
        return (
            <div className="w-full rounded-2xl bg-gray-200 animate-pulse">
                <div className="px-3 py-6 rounded-2xl bg-gradient-to-b from-gray-300 to-gray-400">
                    <div className="grid grid-cols-12 gap-4 w-100">
                        <div className="col-span-8 flex flex-col content-between justify-between">
                            <div className="flex flex-row justify-evenly">
                                <div className="mt-8 w-16 h-16 bg-gray-300 rounded"></div>
                                <div className="text-left pl-3 space-y-2">
                                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                                    <div className="h-6 bg-gray-300 rounded w-48"></div>
                                    <div className="h-12 bg-gray-300 rounded w-24"></div>
                                    <div className="h-4 bg-gray-300 rounded w-36"></div>
                                    <div className="h-4 bg-gray-300 rounded w-28"></div>
                                </div>
                            </div>
                        </div>
                        <div className="col-span-4 w-full pl-6 space-y-3">
                            {[...Array(5)].map((_, i) => (
                                <div key={i} className="h-4 bg-gray-300 rounded"></div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        const selectLocation = (location) => {
            setIpData(location);
            setError(null);
            setLoading(true);
        };

        const locations = [
            { name: 'Dhaka, Bangladesh', lat: 23.8103, lon: 90.4125, city: 'Dhaka', country: 'Bangladesh', timezone: 'Asia/Dhaka' },
            { name: 'New York, USA', lat: 40.7128, lon: -74.0060, city: 'New York', country: 'United States', timezone: 'America/New_York' },
            { name: 'London, UK', lat: 51.5074, lon: -0.1278, city: 'London', country: 'United Kingdom', timezone: 'Europe/London' },
            { name: 'Los Angeles, USA', lat: 34.0522, lon: -118.2437, city: 'Los Angeles', country: 'United States', timezone: 'America/Los_Angeles' },
        ];

        return (
            <div className="w-full rounded-2xl bg-red-100 border border-red-300">
                <div className="px-3 py-6 rounded-2xl">
                    <div className="text-center text-red-700">
                        <h3 className="text-lg font-semibold mb-2">Weather Data Unavailable</h3>
                        <p className="text-sm mb-4">Unable to load weather information: {error.message}</p>
                        <p className="text-sm mb-4">Please select a location manually:</p>
                        <div className="grid grid-cols-2 gap-2 mb-4">
                            {locations.map((location, index) => (
                                <button
                                    key={index}
                                    onClick={() => selectLocation(location)}
                                    className="bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
                                >
                                    {location.name}
                                </button>
                            ))}
                        </div>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
                        >
                            Retry Auto-Detection
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    // Safety check for weather data
    if (!weatherData || !weatherData.hourly || !weatherData.hourly.temperature2m) {
        return (
            <div className="w-full rounded-2xl bg-yellow-100 border border-yellow-300">
                <div className="px-3 py-6 rounded-2xl">
                    <div className="text-center text-yellow-700">
                        <h3 className="text-lg font-semibold mb-2">Loading Weather Data...</h3>
                        <p className="text-sm">Please wait while we fetch the latest weather information.</p>
                    </div>
                </div>
            </div>
        );
    }

    // Generate hourly data with safety checks
    const hourlyData = [];
    for (let i = roundedHour; i < roundedHour + 12; i++) {
        const temperature = weatherData.hourly.temperature2m[i];
        if (temperature !== undefined && temperature !== null) {
            hourlyData.push(
                <div key={i} className={`flex flex-col text-white ${hourlyData.length < 5 ? 'pb-2' : ''}`}>
                    <p className="text-xs -mb-1">{i > 12 && i < 25 ? `${(i - 12)}PM ` : `${i > 24 ? (i - 24) : i}AM `}</p>
                    <p className="font-bold">{Math.round(temperature)}°C</p>
                </div>
            );
        }
    }
    return (
        <div className="w-full rounded-2xl" style={background}>
            <div className="px-3 py-6 rounded-2xl" style={{ background: "linear-gradient(-180deg, #01010110, #010101)" }}>
                <div className="grid grid-cols-12 gap-4 w-100" >
                    <div className="col-span-8 flex flex-col content-between justify-between">
                        <div className="flex flex-row justify-evenly">
                            <div className="mt-8">
                                <img className="w-full" src={weatherIcon} alt="weather icon for sun and cloud" />
                            </div>
                            <div className="text-white text-left pl-3">
                                <CustomClock wLong={ipData} />
                                <h5 className="text-white">{`${ipData?.city || 'Unknown'}, ${ipData?.country || 'Unknown'}`}</h5>
                                <h1 className="text-white text-6xl font-bold">
                                    {weatherData.hourly.temperature2m[roundedHour] !== undefined
                                        ? Math.round(weatherData.hourly.temperature2m[roundedHour])
                                        : '--'}° C
                                </h1>
                                <p>
                                    Feels like:{" "}
                                    {weatherData.hourly.apparentTemperature[roundedHour] !== undefined
                                        ? Math.round(weatherData.hourly.apparentTemperature[roundedHour])
                                        : '--'}° C
                                </p>
                                <p>
                                    Humidity: {weatherData.hourly.relativeHumidity2m[roundedHour] !== undefined
                                        ? weatherData.hourly.relativeHumidity2m[roundedHour]
                                        : '--'}%
                                </p>
                            </div>
                        </div>
                        <div className="pt-6 w-full overflow-x-auto">
                            <div className="grid grid-cols-6">
                                {hourlyData}
                            </div>
                        </div>
                    </div>
                    <div className="col-span-4 w-full pl-6" style={{ gap: '0px', borderLeft: "5px solid #ffffff30", }}>
                        {data5Days && data5Days?.map((item, index) => {
                            return (
                                <div className="grid grid-cols-12 text-white text-left" key={index} style={{ marginTop: "10px", alignItems: 'center' }}>
                                    <div className="col-span-6">

                                        {/* {item.date}{" "} */}

                                        <p className="font-bold">
                                            {item.dayName}
                                        </p>
                                    </div>
                                    <div className="col-span-6" style={{ textAlign: "center" }}>
                                        <p>
                                            {Math.round(item.temperature)}°C
                                        </p>
                                    </div>
                                </div>
                            )
                        })
                        }
                        <p className="text-white text-left mt-8">
                            Wind Speed:{" "}
                            {weatherData.hourly.windSpeed10m[roundedHour] !== undefined
                                ? Math.round(weatherData.hourly.windSpeed10m[roundedHour])
                                : '--'} km/h
                        </p>
                        <p className="text-white text-left mb-2">
                            Visibility: {weatherData.hourly.visibility[roundedHour] !== undefined
                                ? (weatherData.hourly.visibility[roundedHour] / 1000).toFixed(1)
                                : '--'} km
                        </p>
                        <a className="text-red-700 font-bold text-left bg-white px-6 pt-1 pb-2 border-0 rounded-full" href={trafficUrl} target="__blank">Live Traffic</a>
                    </div>
                </div>
                {/* <div className="pt-6 w-full overflow-x-auto">
                    <div className="grid grid-cols-12">
                        {hourlyData}
                    </div>
                </div> */}
            </div>
        </div>
    );
};

export default WeatherData;
