{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\team-member\\\\AddMember.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { alertMessage } from '../../common/coreui';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isTokenValid = () => {\n  const token = localStorage.getItem('token');\n  return token !== null;\n};\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\nconst AddMemberTest = ({\n  isVisible,\n  setVisible\n}) => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [roles, setRoles] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const [departments, setDepartments] = useState([]);\n  const [designations, setDesignations] = useState([]);\n  const [resourceTypes, setResourceTypes] = useState([]);\n  const [resourceStatuses, setResourceStatuses] = useState([]);\n  const [billingStatuses, setBillingStatuses] = useState([]);\n  const [contactTypes, setContactTypes] = useState([]);\n  const [availableStatuses, setAvailableStatuses] = useState([]);\n  const [memberStatuses, setMemberStatuses] = useState([]);\n  const [branches, setBranches] = useState([]);\n  const [onsiteStatuses, setOnsiteStatuses] = useState([]);\n  const [eid, setEid] = useState('');\n  const [email, setEmail] = useState('');\n  const [selectedRoles, setSelectedRoles] = useState({});\n  const [selectedTeams, setSelectedTeams] = useState({});\n  const [defaultTeamId, setDefaultTeamId] = useState(null);\n  const [hoveredTeamId, setHoveredTeamId] = useState(null);\n  const [selectedDepartments, setSelectedDepartments] = useState([]);\n  const [selectedDesignations, setSelectedDesignations] = useState('');\n  const [selectedResourceTypes, setSelectedResourceTypes] = useState('');\n  const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);\n  const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);\n  const [selectedContactTypes, setSelectedContactTypes] = useState([]);\n  const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);\n  const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);\n  const [selectedBranches, setSelectedBranches] = useState([]);\n  const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);\n  const [loggedInUser, setLoggedInUser] = useState(null);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const fetchUsers = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/users`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      setUsers(data);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the roles to select for users\n  const fetchRoles = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/roles`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n\n      // Access the roles array from the data object\n      const rolesData = data.roles; // Get the roles array\n\n      const rolesMap = rolesData.reduce((acc, role) => {\n        acc[role.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setRoles(rolesData);\n      setSelectedRoles(rolesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the teams to select for users\n  const fetchTeams = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/teams`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n\n      // Access the teams array from the data object\n      const teamsData = data.teams; // Get the teams array\n\n      const teamsMap = teamsData.reduce((acc, team) => {\n        acc[team.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setTeams(teamsData);\n      setSelectedTeams(teamsMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the departments to select for users\n  const fetchDepartments = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/departments`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const departmentsData = data.departments;\n\n      // Ensure `selectedDepartments` is an array, initialized to empty\n      const initialSelectedDepartments = [];\n\n      // Set both departments and an empty selectedDepartments array\n      setDepartments(departmentsData);\n      setSelectedDepartments(initialSelectedDepartments); // Initialize as an empty array\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the designations to select for users\n  const fetchDesignations = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/designations`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n\n      // Access the teams array from the data object\n      const designationsData = data.designations; // Get the teams array\n\n      const designationsMap = designationsData.reduce((acc, designation) => {\n        acc[designation.id] = false;\n        return acc;\n      }, {});\n      setDesignations(designationsData);\n      setSelectedDesignations('');\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the Resource Type (Responsibility Level) to select for users\n  const fetchResourceTypes = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/resource_types`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const resourceTypesData = data['Resource Types'];\n      const resourceTypesMap = resourceTypesData.reduce((acc, resourceType) => {\n        acc[resourceType.id] = false;\n        return acc;\n      }, {});\n      setResourceTypes(resourceTypesData);\n      setSelectedResourceTypes('');\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the resourceStatuses to select for users\n  const fetchResourceStatuses = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/resource_statuses`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n\n      // Access the resourceStatuses array from the data object\n      const resourceStatusesData = data.resource_status; // Get the resourceStatuses array\n\n      const resourceStatusesMap = resourceStatusesData.reduce((acc, resourceStatus) => {\n        acc[resourceStatus.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setResourceStatuses(resourceStatusesData);\n      setSelectedResourceStatuses(resourceStatusesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the billing statuses to select for users\n  const fetchBillingStatuses = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/billing_statuses`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const billingStatusesData = data['billing statuses'];\n      const billingStatusesMap = billingStatusesData.reduce((acc, billingStatus) => {\n        acc[billingStatus.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setBillingStatuses(billingStatusesData);\n      setSelectedBillingStatuses(billingStatusesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the contact types to select for users\n  const fetchContactTypes = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/contact_types`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const contactTypesData = data.contact_types;\n      const contactTypesMap = contactTypesData.reduce((acc, contactType) => {\n        acc[contactType.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setContactTypes(contactTypesData);\n      setSelectedContactTypes(contactTypesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the available status to select for users\n  const fetchAvailableStatuses = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/available_statuses`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const availableStatusesData = data.available_statuses;\n      const availableStatusesMap = availableStatusesData.reduce((acc, availableStatus) => {\n        acc[availableStatus.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setAvailableStatuses(availableStatusesData);\n      setSelectedAvailableStatuses(availableStatusesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the team member status to select for users\n  const fetchMemberStatuses = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/member_statuses`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const memberStatusesData = data.member_statuses;\n      const memberStatusesMap = memberStatusesData.reduce((acc, memberStatus) => {\n        acc[memberStatus.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setMemberStatuses(memberStatusesData);\n      setSelectedMemberStatuses(memberStatusesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the branch to select for users\n  const fetchBranches = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/branches`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const branchesData = data.branches;\n      const branchesMap = branchesData.reduce((acc, branch) => {\n        acc[branch.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setBranches(branchesData);\n      setSelectedBranches(branchesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n\n  // Fetching all the branch to select for users\n  const fetchOnsiteStatuses = async () => {\n    if (!isTokenValid()) {\n      setError('No authentication token found.');\n      return;\n    }\n    const token = localStorage.getItem('token');\n    try {\n      const response = await fetch(`${API_URL}/onsite_statuses`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\n      }\n      const data = await response.json();\n      const onsiteStatusesData = data.onsite_statuses;\n      const onsiteStatusesMap = onsiteStatusesData.reduce((acc, onsiteStatus) => {\n        acc[onsiteStatus.id] = false; // Initialize all roles as unchecked\n        return acc;\n      }, {});\n      setOnsiteStatuses(onsiteStatusesData);\n      setSelectedOnsiteStatuses(onsiteStatusesMap);\n    } catch (error) {\n      setError(error.message);\n    }\n  };\n  useEffect(() => {\n    fetchUsers();\n    fetchRoles();\n    fetchTeams();\n    fetchDepartments();\n    fetchDesignations();\n    fetchResourceTypes();\n    fetchResourceStatuses();\n    fetchBillingStatuses();\n    fetchContactTypes();\n    fetchAvailableStatuses();\n    fetchMemberStatuses();\n    fetchBranches();\n    fetchOnsiteStatuses();\n  }, []);\n\n  // Retrieving Logged In Users id\n  useEffect(() => {\n    const userId = localStorage.getItem('user_id');\n    if (userId) {\n      setLoggedInUser(userId);\n    }\n  }, []);\n\n  // Filter teams based on selected departments\n  const filterTeamsByDepartments = () => {\n    if (!selectedDepartments) return teams;\n    return teams.filter(team => {\n      var _team$departments;\n      return (_team$departments = team.departments) === null || _team$departments === void 0 ? void 0 : _team$departments.some(department => department.id === selectedDepartments);\n    });\n  };\n\n  // Handle team selection change\n  const handleTeamChange = teamId => {\n    setSelectedTeams(prevSelectedTeams => {\n      const updatedTeams = {\n        ...prevSelectedTeams,\n        [teamId]: !prevSelectedTeams[teamId]\n      };\n      return updatedTeams;\n    });\n  };\n  const handleDefaultTeamChange = teamId => {\n    setDefaultTeamId(teamId); // Set selected team as the default\n  };\n  const handleMouseEnter = teamId => {\n    setHoveredTeamId(teamId); // Track hovered team\n  };\n  const handleMouseLeave = () => {\n    setHoveredTeamId(null); // Reset hover state\n  };\n  const filteredTeams = filterTeamsByDepartments();\n  const handleSubmit = async event => {\n    event.preventDefault();\n    setLoading(true);\n    const trimmedEid = eid.trim();\n    const trimmedEmail = email.trim();\n    if (!trimmedEid || !trimmedEmail) {\n      setError('EID and Email are required.');\n      return;\n    }\n    const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());\n    const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());\n    if (eidExists || emailExists) {\n      let message = 'The ';\n      if (eidExists) message += 'EID ';\n      if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';\n      message += 'already exists. Please add a new EID and/or Email.';\n      setError(message);\n      setTimeout(() => setError(''), 1000);\n      return;\n    }\n    setError('');\n\n    // Prepare roles array based on selected roles\n    const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);\n    const selectedTeamIds = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);\n    // Always passed as an array (even if one department is selected)\n    const selectedDepartmentIds = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];\n    // Single select: directly pass the selected value, not in an array\n    const selectedDesignationIds = selectedDesignations ? [selectedDesignations] : [];\n    const selectedResourceTypeIds = selectedResourceTypes ? [selectedResourceTypes] : [];\n    const selectedResourceStatusIds = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];\n    const selectedBillingStatusIds = Array.isArray(selectedBillingStatuses) ? selectedBillingStatuses : [selectedBillingStatuses];\n    const selectedContactTypeIds = Array.isArray(selectedContactTypes) ? selectedContactTypes : [selectedContactTypes];\n    const selectedAvailableStatusIds = Array.isArray(selectedAvailableStatuses) ? selectedAvailableStatuses : [selectedAvailableStatuses];\n    const selectedMemberStatusIds = Array.isArray(selectedMemberStatuses) ? selectedMemberStatuses : [selectedMemberStatuses];\n    const selectedBranchIds = Array.isArray(selectedBranches) ? selectedBranches : [selectedBranches];\n    const selectedOnsiteStatusIds = Array.isArray(selectedOnsiteStatuses) ? selectedOnsiteStatuses : [selectedOnsiteStatuses];\n    try {\n      const token = localStorage.getItem('token');\n\n      // Get user_id from localStorage for 'created_by'\n      const createdBy = loggedInUser;\n      if (!createdBy) {\n        setError('User is not logged in.');\n        return;\n      }\n      const response = await fetch(`${API_URL}/users`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          eid: trimmedEid,\n          email: trimmedEmail,\n          roles: selectedRoleIds.map(Number),\n          // Send the roles array as numbers\n          teams: selectedTeamIds.map(Number),\n          // Send the teams array as numbers\n          default_team_id: defaultTeamId,\n          departments: selectedDepartmentIds.map(Number),\n          // Send the departments as numbers\n          designations: selectedDesignationIds.map(Number),\n          resource_types: selectedResourceTypeIds.map(Number),\n          resource_statuses: selectedResourceStatusIds.map(Number),\n          billing_statuses: selectedBillingStatusIds.map(Number),\n          contact_types: selectedContactTypeIds.map(Number),\n          available_statuses: selectedAvailableStatusIds.map(Number),\n          member_statuses: selectedMemberStatusIds.map(Number),\n          branches: selectedBranchIds.map(Number),\n          onsite_statuses: selectedOnsiteStatusIds.map(Number),\n          created_by: createdBy\n        })\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Error response:', errorText);\n        throw new Error('Failed to save user: ' + response.statusText);\n      }\n      const result = await response.json();\n      //setSuccessMessage(`User with EID \"${trimmedEid}\" added successfully!`);\n      alertMessage('success');\n      setEid('');\n      setEmail('');\n      setSelectedRoles({});\n      setSelectedTeams({});\n      setSelectedDepartments([]);\n      setSelectedDesignations('');\n      setSelectedResourceTypes('');\n      setSelectedResourceStatuses([]);\n      setSelectedBillingStatuses([]);\n      setSelectedContactTypes([]);\n      setSelectedAvailableStatuses([]);\n      setSelectedMemberStatuses([]);\n      setSelectedBranches([]);\n      setSelectedOnsiteStatuses([]);\n      fetchUsers();\n    } catch (error) {\n      //setError(error.message);\n      alertMessage('error');\n      setLoading(false);\n    }\n  };\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md w-full max-w-6xl relative bg-neutral-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-4 bg-gray-100 px-4 py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-base font-medium text-left text-gray-800\",\n            children: \"Add New Team \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-3xl text-gray-500 hover:text-gray-800\",\n            onClick: () => setVisible(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"eid\",\n                className: \"block pb-2 text-base text-gray-600\",\n                children: [\"Employee ID \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"eid\",\n                value: eid,\n                onChange: e => setEid(e.target.value),\n                placeholder: \"Add Team Member ID\",\n                required: true,\n                className: \"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"block pb-2 text-base text-gray-600\",\n                children: [\"Email \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                id: \"email\",\n                value: email,\n                onChange: e => setEmail(e.target.value),\n                placeholder: \"Add Team Member Email\",\n                required: true,\n                className: \"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"designation\",\n                className: \"block pb-2 text-base text-gray-600\",\n                children: [\"Designation \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedDesignations || '' // Handle single value\n                  ,\n                  onChange: e => setSelectedDesignations(e.target.value) // Update with selected value\n                  ,\n                  required: true,\n                  className: \"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    disabled: true,\n                    children: \"Select a designation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 41\n                  }, this), designations.map(designation => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: designation.id,\n                    children: designation.name\n                  }, designation.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 45\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"resource-type\",\n                className: \"block pb-2 text-base text-gray-600\",\n                children: [\"Responsibility Level \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 58\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedResourceTypes || '',\n                  onChange: e => setSelectedResourceTypes(e.target.value) // This updates selectedResourceTypes state\n                  ,\n                  required: true,\n                  className: \"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    disabled: true,\n                    children: \"Select responsibility Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 41\n                  }, this), resourceTypes.map(resourceType => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: resourceType.id,\n                    children: resourceType.name\n                  }, resourceType.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 45\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Roles \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: roles.map(role => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: selectedRoles[role.id] || false,\n                    onChange: () => setSelectedRoles(prev => ({\n                      ...prev,\n                      [role.id]: !prev[role.id]\n                    })),\n                    className: \"form-checkbox my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: role.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 824,\n                    columnNumber: 45\n                  }, this), \" \"]\n                }, role.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Department \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: departments.map(department => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"department\",\n                    value: department.id,\n                    checked: selectedDepartments === department.id,\n                    onChange: () => setSelectedDepartments(department.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 37\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: department.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 37\n                  }, this)]\n                }, department.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 33\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Teams \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: filteredTeams.length > 0 ? filteredTeams.map(team => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  onMouseEnter: () => handleMouseEnter(team.id),\n                  onMouseLeave: handleMouseLeave,\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"inline-flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: selectedTeams[team.id] || false,\n                      onChange: () => handleTeamChange(team.id),\n                      className: \"form-checkbox my-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2\",\n                      children: team.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 49\n                  }, this), defaultTeamId !== team.id && hoveredTeamId === team.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-[10px] text-gray-500 whitespace-nowrap\",\n                      children: \"Make Default\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      checked: defaultTeamId === team.id,\n                      onChange: () => handleDefaultTeamChange(team.id),\n                      className: \"form-radio\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 53\n                  }, this), defaultTeamId === team.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-[10px] text-gray-500 whitespace-nowrap\",\n                      children: \"Default Team\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"radio\",\n                      checked: true,\n                      disabled: true,\n                      className: \"form-radio\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 896,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 53\n                  }, this)]\n                }, team.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 45\n                }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Select a department to show teams\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Resource Statuses \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 55\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 914,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: resourceStatuses.map(resourceStatus => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"resourceStatus\",\n                    value: resourceStatus.id,\n                    checked: selectedResourceStatuses === resourceStatus.id,\n                    onChange: () => setSelectedResourceStatuses(resourceStatus.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: resourceStatus.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 45\n                  }, this)]\n                }, resourceStatus.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Billing Statuses \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 54\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: billingStatuses.map(billingStatus => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"billingStatus\",\n                    value: billingStatus.id,\n                    checked: selectedBillingStatuses === billingStatus.id,\n                    onChange: () => setSelectedBillingStatuses(billingStatus.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: billingStatus.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 951,\n                    columnNumber: 45\n                  }, this)]\n                }, billingStatus.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Contact Types \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 51\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: contactTypes.map(contactType => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"contactType\",\n                    value: contactType.id,\n                    checked: selectedContactTypes === contactType.id,\n                    onChange: () => setSelectedContactTypes(contactType.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: contactType.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 45\n                  }, this)]\n                }, contactType.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Available Statuses \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 56\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: availableStatuses.map(availableStatus => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"availableStatus\",\n                    value: availableStatus.id,\n                    checked: selectedAvailableStatuses === availableStatus.id,\n                    onChange: () => setSelectedAvailableStatuses(availableStatus.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: availableStatus.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 45\n                  }, this)]\n                }, availableStatus.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Team Member Statuses \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1003,\n                  columnNumber: 58\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: memberStatuses.map(memberStatus => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"memberStatus\",\n                    value: memberStatus.id,\n                    checked: selectedMemberStatuses === memberStatus.id,\n                    onChange: () => setSelectedMemberStatuses(memberStatus.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: memberStatus.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 45\n                  }, this)]\n                }, memberStatus.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"Branch \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: branches.map(branch => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"branch\",\n                    value: branch.id,\n                    checked: selectedBranches === branch.id,\n                    onChange: () => setSelectedBranches(branch.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: branch.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 45\n                  }, this)]\n                }, branch.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\",\n                children: [\"On-Site Status \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-600 text-base\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 52\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col p-4\",\n                children: onsiteStatuses.map(onsiteStatus => /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"inline-flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    name: \"onsiteStatus\",\n                    value: onsiteStatus.id,\n                    checked: selectedOnsiteStatuses === onsiteStatus.id,\n                    onChange: () => setSelectedOnsiteStatuses(onsiteStatus.id),\n                    required: true,\n                    className: \"form-radio my-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2\",\n                    children: onsiteStatus.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 45\n                  }, this)]\n                }, onsiteStatus.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 41\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 25\n          }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm pt-4\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 35\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-left p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                class: \"material-symbols-rounded text-white text-xl font-regular\",\n                children: \"add_circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 33\n              }, this), loading ? 'Onboarding...' : 'Send Invitation']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1068,\n            columnNumber: 25\n          }, this), successMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-500 text-sm\",\n            children: successMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1077,\n            columnNumber: 44\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(AddMemberTest, \"r7HHfpgS0eXA2kqa+SidzAyzZNE=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = AddMemberTest;\nexport default AddMemberTest;\nvar _c;\n$RefreshReg$(_c, \"AddMemberTest\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useLocation", "useNavigate", "alertMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isTokenValid", "token", "localStorage", "getItem", "API_URL", "process", "env", "REACT_APP_BASE_API_URL", "AddMemberTest", "isVisible", "setVisible", "_s", "location", "navigate", "users", "setUsers", "roles", "setRoles", "teams", "setTeams", "departments", "setDepartments", "designations", "setDesignations", "resourceTypes", "setResourceTypes", "resourceStatuses", "setResourceStatuses", "billingStatuses", "setBillingStatuses", "contactTypes", "setContactTypes", "availableStatuses", "setAvailableStatuses", "memberStatuses", "setMemberStatuses", "branches", "setBranches", "onsiteStatuses", "setOnsiteStatuses", "eid", "setEid", "email", "setEmail", "selectedRoles", "setSelectedRoles", "selectedTeams", "setSelectedTeams", "defaultTeamId", "setDefaultTeamId", "hoveredTeamId", "setHoveredTeamId", "selectedDepartments", "setSelectedDepartments", "selectedDesignations", "setSelectedDesignations", "selectedResourceTypes", "setSelectedResourceTypes", "selectedResourceStatuses", "setSelectedResourceStatuses", "selectedBillingStatuses", "setSelectedBillingStatuses", "selectedContactTypes", "setSelectedContactTypes", "selectedAvailableStatuses", "setSelectedAvailableStatuses", "selectedMemberStatuses", "setSelectedMemberStatuses", "selectedBranches", "setSelectedBranches", "selectedOnsiteStatuses", "setSelectedOnsiteStatuses", "loggedInUser", "setLoggedInUser", "error", "setError", "loading", "setLoading", "successMessage", "setSuccessMessage", "fetchUsers", "response", "fetch", "method", "headers", "ok", "Error", "statusText", "data", "json", "message", "fetchRoles", "rolesData", "rolesMap", "reduce", "acc", "role", "id", "fetchTeams", "teamsData", "teamsMap", "team", "fetchDepartments", "departmentsData", "initialSelectedDepartments", "fetchDesignations", "designationsData", "designationsMap", "designation", "fetchResourceTypes", "resourceTypesData", "resourceTypesMap", "resourceType", "fetchResourceStatuses", "resourceStatusesData", "resource_status", "resourceStatusesMap", "resourceStatus", "fetchBillingStatuses", "billingStatusesData", "billingStatusesMap", "billingStatus", "fetchContactTypes", "contactTypesData", "contact_types", "contactTypesMap", "contactType", "fetchAvailableStatuses", "availableStatusesData", "available_statuses", "availableStatusesMap", "availableStatus", "fetchMemberStatuses", "memberStatusesData", "member_statuses", "memberStatusesMap", "memberStatus", "fetchBranches", "branchesData", "branchesMap", "branch", "fetchOnsiteStatuses", "onsiteStatusesData", "onsite_statuses", "onsiteStatusesMap", "onsiteStatus", "userId", "filterTeamsByDepartments", "filter", "_team$departments", "some", "department", "handleTeamChange", "teamId", "prevSelectedTeams", "updatedTeams", "handleDefaultTeamChange", "handleMouseEnter", "handleMouseLeave", "filteredTeams", "handleSubmit", "event", "preventDefault", "trimmedEid", "trim", "trimmedEmail", "eidExists", "user", "toLowerCase", "emailExists", "endsWith", "setTimeout", "selectedRoleIds", "Object", "keys", "roleId", "selectedTeamIds", "selectedDepartmentIds", "Array", "isArray", "selectedDesignationIds", "selectedResourceTypeIds", "selectedResourceStatusIds", "selectedBillingStatusIds", "selectedContactTypeIds", "selectedAvailableStatusIds", "selectedMemberStatusIds", "selectedBranchIds", "selectedOnsiteStatusIds", "created<PERSON>y", "body", "JSON", "stringify", "map", "Number", "default_team_id", "resource_types", "resource_statuses", "billing_statuses", "created_by", "errorText", "text", "console", "result", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "value", "onChange", "e", "target", "placeholder", "required", "disabled", "name", "checked", "prev", "length", "onMouseEnter", "onMouseLeave", "class", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/team-member/AddMember.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport { alertMessage } from '../../common/coreui';\r\n\r\nconst isTokenValid = () => {\r\n    const token = localStorage.getItem('token');\r\n    return token !== null;\r\n};\r\n\r\nconst API_URL = process.env.REACT_APP_BASE_API_URL;\r\n\r\nconst AddMemberTest = ({isVisible, setVisible}) => {\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const [users, setUsers] = useState([]);\r\n    const [roles, setRoles] = useState([]);\r\n    const [teams, setTeams] = useState([]);\r\n    const [departments, setDepartments] = useState([]);\r\n    const [designations, setDesignations] = useState([]);\r\n    const [resourceTypes, setResourceTypes] = useState([]);\r\n    const [resourceStatuses, setResourceStatuses] = useState([]);\r\n    const [billingStatuses, setBillingStatuses] = useState([]);\r\n    const [contactTypes, setContactTypes] = useState([]);\r\n    const [availableStatuses, setAvailableStatuses] = useState([]);\r\n    const [memberStatuses, setMemberStatuses] = useState([]);\r\n    const [branches, setBranches] = useState([]);\r\n    const [onsiteStatuses, setOnsiteStatuses] = useState([]);\r\n    const [eid, setEid] = useState('');\r\n    const [email, setEmail] = useState('');\r\n    const [selectedRoles, setSelectedRoles] = useState({});\r\n    const [selectedTeams, setSelectedTeams] = useState({});\r\n    const [defaultTeamId, setDefaultTeamId] = useState(null);\r\n    const [hoveredTeamId, setHoveredTeamId] = useState(null);\r\n    const [selectedDepartments, setSelectedDepartments] = useState([]);\r\n    const [selectedDesignations, setSelectedDesignations] = useState('');\r\n    const [selectedResourceTypes, setSelectedResourceTypes] = useState('');\r\n    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);\r\n    const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);\r\n    const [selectedContactTypes, setSelectedContactTypes] = useState([]);\r\n    const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);\r\n    const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);\r\n    const [selectedBranches, setSelectedBranches] = useState([]);\r\n    const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);\r\n    const [loggedInUser, setLoggedInUser] = useState(null);\r\n\r\n    const [error, setError] = useState('');\r\n    const [loading, setLoading] = useState(false);\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n\r\n    const fetchUsers = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n\r\n        const token = localStorage.getItem('token');\r\n\r\n        try {\r\n            const response = await fetch(`${API_URL}/users`, {\r\n                method: 'GET',\r\n\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n\r\n            const data = await response.json();\r\n\r\n            setUsers(data);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the roles to select for users\r\n    const fetchRoles = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/roles`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            // Access the roles array from the data object\r\n            const rolesData = data.roles; // Get the roles array\r\n    \r\n            const rolesMap = rolesData.reduce((acc, role) => {\r\n                acc[role.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setRoles(rolesData);\r\n            setSelectedRoles(rolesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };   \r\n\r\n    // Fetching all the teams to select for users\r\n    const fetchTeams = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/teams`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            // Access the teams array from the data object\r\n            const teamsData = data.teams; // Get the teams array\r\n    \r\n            const teamsMap = teamsData.reduce((acc, team) => {\r\n                acc[team.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setTeams(teamsData);\r\n            setSelectedTeams(teamsMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the departments to select for users\r\n    const fetchDepartments = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/departments`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n            const departmentsData = data.departments;\r\n    \r\n            // Ensure `selectedDepartments` is an array, initialized to empty\r\n            const initialSelectedDepartments = [];\r\n            \r\n            // Set both departments and an empty selectedDepartments array\r\n            setDepartments(departmentsData);\r\n            setSelectedDepartments(initialSelectedDepartments); // Initialize as an empty array\r\n    \r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n    \r\n\r\n    // Fetching all the designations to select for users\r\n    const fetchDesignations = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/designations`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            // Access the teams array from the data object\r\n            const designationsData = data.designations; // Get the teams array\r\n    \r\n            const designationsMap = designationsData.reduce((acc, designation) => {\r\n                acc[designation.id] = false;\r\n                return acc;\r\n            }, {});\r\n    \r\n            setDesignations(designationsData);\r\n            setSelectedDesignations('');\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the Resource Type (Responsibility Level) to select for users\r\n    const fetchResourceTypes = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/resource_types`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const resourceTypesData = data['Resource Types']; \r\n    \r\n            const resourceTypesMap = resourceTypesData.reduce((acc, resourceType) => {\r\n                acc[resourceType.id] = false;\r\n                return acc;\r\n            }, {});\r\n    \r\n            setResourceTypes(resourceTypesData);\r\n            setSelectedResourceTypes('');\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the resourceStatuses to select for users\r\n    const fetchResourceStatuses = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/resource_statuses`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n\r\n    \r\n            // Access the resourceStatuses array from the data object\r\n            const resourceStatusesData = data.resource_status; // Get the resourceStatuses array\r\n    \r\n            const resourceStatusesMap = resourceStatusesData.reduce((acc, resourceStatus) => {\r\n                acc[resourceStatus.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setResourceStatuses(resourceStatusesData);\r\n            setSelectedResourceStatuses(resourceStatusesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the billing statuses to select for users\r\n    const fetchBillingStatuses = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/billing_statuses`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const billingStatusesData = data['billing statuses']; \r\n    \r\n            const billingStatusesMap = billingStatusesData.reduce((acc, billingStatus) => {\r\n                acc[billingStatus.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setBillingStatuses(billingStatusesData);\r\n            setSelectedBillingStatuses(billingStatusesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the contact types to select for users\r\n    const fetchContactTypes = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/contact_types`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const contactTypesData = data.contact_types;\r\n    \r\n            const contactTypesMap = contactTypesData.reduce((acc, contactType) => {\r\n                acc[contactType.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setContactTypes(contactTypesData);\r\n            setSelectedContactTypes(contactTypesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the available status to select for users\r\n    const fetchAvailableStatuses = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/available_statuses`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const availableStatusesData = data.available_statuses;\r\n    \r\n            const availableStatusesMap = availableStatusesData.reduce((acc, availableStatus) => {\r\n                acc[availableStatus.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setAvailableStatuses(availableStatusesData);\r\n            setSelectedAvailableStatuses(availableStatusesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the team member status to select for users\r\n    const fetchMemberStatuses = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/member_statuses`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const memberStatusesData = data.member_statuses; \r\n    \r\n            const memberStatusesMap = memberStatusesData.reduce((acc, memberStatus) => {\r\n                acc[memberStatus.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setMemberStatuses(memberStatusesData);\r\n            setSelectedMemberStatuses(memberStatusesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n    \r\n    // Fetching all the branch to select for users\r\n    const fetchBranches = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/branches`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const branchesData = data.branches; \r\n    \r\n            const branchesMap = branchesData.reduce((acc, branch) => {\r\n                acc[branch.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setBranches(branchesData);\r\n            setSelectedBranches(branchesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n\r\n    // Fetching all the branch to select for users\r\n    const fetchOnsiteStatuses = async () => {\r\n        if (!isTokenValid()) {\r\n            setError('No authentication token found.');\r\n            return;\r\n        }\r\n    \r\n        const token = localStorage.getItem('token');\r\n    \r\n        try {\r\n            const response = await fetch(`${API_URL}/onsite_statuses`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);\r\n            }\r\n    \r\n            const data = await response.json();\r\n    \r\n            const onsiteStatusesData = data.onsite_statuses;\r\n    \r\n            const onsiteStatusesMap = onsiteStatusesData.reduce((acc, onsiteStatus) => {\r\n                acc[onsiteStatus.id] = false; // Initialize all roles as unchecked\r\n                return acc;\r\n            }, {});\r\n    \r\n            setOnsiteStatuses(onsiteStatusesData);\r\n            setSelectedOnsiteStatuses(onsiteStatusesMap);\r\n        } catch (error) {\r\n            setError(error.message);\r\n        }\r\n    };\r\n    \r\n    \r\n    \r\n\r\n    useEffect(() => {\r\n        fetchUsers();\r\n        fetchRoles();\r\n        fetchTeams();\r\n        fetchDepartments();\r\n        fetchDesignations();\r\n        fetchResourceTypes();\r\n        fetchResourceStatuses();\r\n        fetchBillingStatuses();\r\n        fetchContactTypes();\r\n        fetchAvailableStatuses();\r\n        fetchMemberStatuses();\r\n        fetchBranches();\r\n        fetchOnsiteStatuses();\r\n    }, []);\r\n\r\n    // Retrieving Logged In Users id\r\n    useEffect(() => {\r\n        const userId = localStorage.getItem('user_id');\r\n        if (userId) {\r\n            setLoggedInUser(userId);\r\n        }\r\n    }, []);\r\n\r\n    \r\n    // Filter teams based on selected departments\r\n    const filterTeamsByDepartments = () => {\r\n        if (!selectedDepartments) return teams;\r\n    \r\n        return teams.filter(team =>\r\n        team.departments?.some(department => department.id === selectedDepartments)\r\n        );\r\n    };\r\n    \r\n    // Handle team selection change\r\n    const handleTeamChange = (teamId) => {\r\n        setSelectedTeams((prevSelectedTeams) => {\r\n            const updatedTeams = { ...prevSelectedTeams, [teamId]: !prevSelectedTeams[teamId] };\r\n            return updatedTeams;\r\n        });\r\n    };\r\n    \r\n    const handleDefaultTeamChange = (teamId) => {\r\n        setDefaultTeamId(teamId); // Set selected team as the default\r\n    };\r\n\r\n    const handleMouseEnter = (teamId) => {\r\n        setHoveredTeamId(teamId);  // Track hovered team\r\n    };\r\n    \r\n    const handleMouseLeave = () => {\r\n        setHoveredTeamId(null);  // Reset hover state\r\n    };\r\n    \r\n\r\n    const filteredTeams = filterTeamsByDepartments();\r\n    \r\n  \r\n    \r\n\r\n    const handleSubmit = async (event) => {\r\n        event.preventDefault();\r\n\r\n        setLoading(true);\r\n\r\n        const trimmedEid = eid.trim();\r\n        const trimmedEmail = email.trim();\r\n    \r\n        if (!trimmedEid || !trimmedEmail) {\r\n            setError('EID and Email are required.');\r\n            return;\r\n        }\r\n    \r\n        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());\r\n        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());\r\n    \r\n        if (eidExists || emailExists) {\r\n            let message = 'The ';\r\n            if (eidExists) message += 'EID ';\r\n            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';\r\n            message += 'already exists. Please add a new EID and/or Email.';\r\n            setError(message);\r\n            setTimeout(() => setError(''), 1000);\r\n            return;\r\n        }\r\n    \r\n        setError('');\r\n\r\n        // Prepare roles array based on selected roles\r\n        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);\r\n        const selectedTeamIds = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);\r\n        // Always passed as an array (even if one department is selected)\r\n        const selectedDepartmentIds = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];\r\n        // Single select: directly pass the selected value, not in an array\r\n        const selectedDesignationIds = selectedDesignations ? [selectedDesignations] : [];\r\n        const selectedResourceTypeIds = selectedResourceTypes ? [selectedResourceTypes] : [];\r\n        const selectedResourceStatusIds = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];\r\n        const selectedBillingStatusIds = Array.isArray(selectedBillingStatuses) ? selectedBillingStatuses : [selectedBillingStatuses];\r\n        const selectedContactTypeIds = Array.isArray(selectedContactTypes) ? selectedContactTypes : [selectedContactTypes];\r\n        const selectedAvailableStatusIds = Array.isArray(selectedAvailableStatuses) ? selectedAvailableStatuses : [selectedAvailableStatuses];\r\n        const selectedMemberStatusIds = Array.isArray(selectedMemberStatuses) ? selectedMemberStatuses : [selectedMemberStatuses];\r\n        const selectedBranchIds = Array.isArray(selectedBranches) ? selectedBranches : [selectedBranches];\r\n        const selectedOnsiteStatusIds = Array.isArray(selectedOnsiteStatuses) ? selectedOnsiteStatuses : [selectedOnsiteStatuses];\r\n\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n\r\n            // Get user_id from localStorage for 'created_by'\r\n            const createdBy = loggedInUser;\r\n\r\n            if (!createdBy) {\r\n                setError('User is not logged in.');\r\n                return;\r\n            }\r\n            \r\n            const response = await fetch(`${API_URL}/users`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    eid: trimmedEid,\r\n                    email: trimmedEmail,\r\n                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers\r\n                    teams: selectedTeamIds.map(Number), // Send the teams array as numbers\r\n                    default_team_id: defaultTeamId,\r\n                    departments: selectedDepartmentIds.map(Number), // Send the departments as numbers\r\n                    designations: selectedDesignationIds.map(Number),\r\n                    resource_types: selectedResourceTypeIds.map(Number),\r\n                    resource_statuses: selectedResourceStatusIds.map(Number),\r\n                    billing_statuses: selectedBillingStatusIds.map(Number),\r\n                    contact_types: selectedContactTypeIds.map(Number),\r\n                    available_statuses: selectedAvailableStatusIds.map(Number),\r\n                    member_statuses: selectedMemberStatusIds.map(Number),\r\n                    branches: selectedBranchIds.map(Number),\r\n                    onsite_statuses: selectedOnsiteStatusIds.map(Number),\r\n                    created_by: createdBy,\r\n                }),\r\n            });\r\n    \r\n            if (!response.ok) {\r\n                const errorText = await response.text();\r\n                console.error('Error response:', errorText);\r\n                throw new Error('Failed to save user: ' + response.statusText);\r\n            }\r\n            \r\n    \r\n            const result = await response.json();\r\n            //setSuccessMessage(`User with EID \"${trimmedEid}\" added successfully!`);\r\n            alertMessage('success')\r\n            setEid('');\r\n            setEmail('');\r\n            setSelectedRoles({});\r\n            setSelectedTeams({});\r\n            setSelectedDepartments([]);\r\n            setSelectedDesignations('');\r\n            setSelectedResourceTypes('');\r\n            setSelectedResourceStatuses([]);\r\n            setSelectedBillingStatuses([]);\r\n            setSelectedContactTypes([]);\r\n            setSelectedAvailableStatuses([]);\r\n            setSelectedMemberStatuses([]);\r\n            setSelectedBranches([]);\r\n            setSelectedOnsiteStatuses([]);\r\n    \r\n            fetchUsers();\r\n\r\n\r\n        } catch (error) {\r\n            //setError(error.message);\r\n            alertMessage('error');\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    if (!isVisible) return null;\r\n\r\n    return (\r\n        <>\r\n            \r\n            <div className=\"fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden\">\r\n                <div className=\"bg-white rounded-lg shadow-md w-full max-w-6xl relative bg-neutral-50\">\r\n                    <div className=\"flex justify-between items-center mb-4 bg-gray-100 px-4 py-2\">\r\n                        <h4 className=\"text-base font-medium text-left text-gray-800\">Add New Team </h4>\r\n                        <button\r\n                            className=\"text-3xl text-gray-500 hover:text-gray-800\"\r\n                            onClick={() => setVisible(false)}\r\n                        >\r\n                            &times;\r\n                        </button>\r\n                    </div>\r\n\r\n                    <form onSubmit={handleSubmit}>\r\n                        <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left\">\r\n                                <label htmlFor=\"eid\" className=\"block pb-2 text-base text-gray-600\">\r\n                                    Employee ID <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <input\r\n                                    type=\"number\"\r\n                                    id=\"eid\"\r\n                                    value={eid}\r\n                                    onChange={(e) => setEid(e.target.value)}\r\n                                    placeholder='Add Team Member ID'\r\n                                    required\r\n                                    className=\"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left\">\r\n                                <label htmlFor=\"email\" className=\"block pb-2 text-base text-gray-600\">\r\n                                    Email <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <input\r\n                                    type=\"email\"\r\n                                    id=\"email\"\r\n                                    value={email}\r\n                                    onChange={(e) => setEmail(e.target.value)}\r\n                                    placeholder='Add Team Member Email'\r\n                                    required\r\n                                    className=\"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                />\r\n                            </div>\r\n                            {/* Select Designations */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left\">\r\n                                <label htmlFor=\"designation\" className=\"block pb-2 text-base text-gray-600\">\r\n                                    Designation <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"\">\r\n                                    <select\r\n                                        value={selectedDesignations || ''} // Handle single value\r\n                                        onChange={(e) => setSelectedDesignations(e.target.value)} // Update with selected value\r\n                                        required\r\n                                        className=\"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    >\r\n                                        <option value=\"\" disabled>Select a designation</option>\r\n                                        {designations.map((designation) => (\r\n                                            <option key={designation.id} value={designation.id}>\r\n                                                {designation.name}\r\n                                            </option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Responsibility Level (Resource Types) */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left\">\r\n                                <label htmlFor=\"resource-type\" className=\"block pb-2 text-base text-gray-600\">\r\n                                    Responsibility Level <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"\">\r\n                                    <select\r\n                                        value={selectedResourceTypes || ''}\r\n                                        onChange={(e) => setSelectedResourceTypes(e.target.value)} // This updates selectedResourceTypes state\r\n                                        required\r\n                                        className=\"py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50\"\r\n                                    >\r\n                                        <option value=\"\" disabled>Select responsibility Level</option>\r\n                                        {resourceTypes.map((resourceType) => (\r\n                                            <option key={resourceType.id} value={resourceType.id}>\r\n                                                {resourceType.name}\r\n                                            </option>\r\n                                        ))}\r\n                                    </select>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Select Roles */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Roles <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {roles.map(role => (\r\n                                        <label className=\"inline-flex items-center\" key={role.id}>\r\n                                            <input\r\n                                                type=\"checkbox\"\r\n                                                checked={selectedRoles[role.id] || false}\r\n                                                onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}\r\n                                                className=\"form-checkbox my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{role.name}</span> {/* Assuming role has a 'name' property */}\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            {/* Department Selection */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                            <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                Department <span className='text-red-600'>*</span>\r\n                            </label>\r\n                            <div className=\"flex flex-col p-4\">\r\n                                {departments.map(department => (\r\n                                <label className=\"inline-flex items-center\" key={department.id}>\r\n                                    <input\r\n                                    type=\"radio\"\r\n                                    name=\"department\"\r\n                                    value={department.id}\r\n                                    checked={selectedDepartments === department.id}\r\n                                    onChange={() => setSelectedDepartments(department.id)}\r\n                                    required\r\n                                    className=\"form-radio my-2\"\r\n                                    />\r\n                                    <span className=\"ml-2\">{department.name}</span>\r\n                                </label>\r\n                                ))}\r\n                            </div>\r\n                            </div>\r\n\r\n                            {/* Teams Selection */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Teams <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {filteredTeams.length > 0 ? (\r\n                                        filteredTeams.map((team) => (\r\n                                            <div\r\n                                                className=\"flex items-center justify-between\"\r\n                                                key={team.id}\r\n                                                onMouseEnter={() => handleMouseEnter(team.id)}\r\n                                                onMouseLeave={handleMouseLeave}\r\n                                            >\r\n                                                <label className=\"inline-flex items-center\">\r\n                                                    <input\r\n                                                        type=\"checkbox\"\r\n                                                        checked={selectedTeams[team.id] || false}\r\n                                                        onChange={() => handleTeamChange(team.id)}\r\n                                                        className=\"form-checkbox my-2\"\r\n                                                    />\r\n                                                    <span className=\"ml-2\">{team.name}</span>\r\n                                                </label>\r\n\r\n                                                {/* Show the default team checkbox and hide the popup for default team */}\r\n                                                {defaultTeamId !== team.id && hoveredTeamId === team.id && (\r\n                                                    <div className=\"relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center\">                                          \r\n                                                        <span className=\"text-[10px] text-gray-500 whitespace-nowrap\">Make Default</span>\r\n                                                        <input\r\n                                                            type=\"radio\"\r\n                                                            checked={defaultTeamId === team.id}\r\n                                                            onChange={() => handleDefaultTeamChange(team.id)}\r\n                                                            className=\"form-radio\"\r\n                                                        />\r\n                                                    </div>\r\n                                                )}\r\n\r\n                                                {/* Always show checkbox for the default team */}\r\n                                                {defaultTeamId === team.id && (\r\n                                                    <div className=\"relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center\">\r\n                                                        <span className=\"text-[10px] text-gray-500 whitespace-nowrap\">\r\n                                                            Default Team\r\n                                                        </span>\r\n                                                        <input\r\n                                                            type=\"radio\"\r\n                                                            checked={true}\r\n                                                            disabled\r\n                                                            className=\"form-radio\"\r\n                                                        />\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                        ))\r\n                                    ) : (\r\n                                        <p>Select a department to show teams</p>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Select Resource Statuses */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Resource Statuses <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {resourceStatuses.map(resourceStatus => (\r\n                                        <label className=\"inline-flex items-center\" key={resourceStatus.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"resourceStatus\"\r\n                                                value={resourceStatus.id}\r\n                                                checked={selectedResourceStatuses === resourceStatus.id}\r\n                                                onChange={() => setSelectedResourceStatuses(resourceStatus.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{resourceStatus.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Billing Statuses */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Billing Statuses <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {billingStatuses.map(billingStatus => (\r\n                                        <label className=\"inline-flex items-center\" key={billingStatus.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"billingStatus\"\r\n                                                value={billingStatus.id}\r\n                                                checked={selectedBillingStatuses === billingStatus.id}\r\n                                                onChange={() => setSelectedBillingStatuses(billingStatus.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{billingStatus.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Contact Types */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Contact Types <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {contactTypes.map(contactType => (\r\n                                        <label className=\"inline-flex items-center\" key={contactType.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"contactType\"\r\n                                                value={contactType.id}\r\n                                                checked={selectedContactTypes === contactType.id}\r\n                                                onChange={() => setSelectedContactTypes(contactType.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{contactType.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Available Statuses */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Available Statuses <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {availableStatuses.map(availableStatus => (\r\n                                        <label className=\"inline-flex items-center\" key={availableStatus.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"availableStatus\"\r\n                                                value={availableStatus.id}\r\n                                                checked={selectedAvailableStatuses === availableStatus.id}\r\n                                                onChange={() => setSelectedAvailableStatuses(availableStatus.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{availableStatus.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Team Member Statuses */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Team Member Statuses <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {memberStatuses.map(memberStatus => (\r\n                                        <label className=\"inline-flex items-center\" key={memberStatus.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"memberStatus\"\r\n                                                value={memberStatus.id}\r\n                                                checked={selectedMemberStatuses === memberStatus.id}\r\n                                                onChange={() => setSelectedMemberStatuses(memberStatus.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{memberStatus.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select Branch */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    Branch <span className='text-red-600'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {branches.map(branch => (\r\n                                        <label className=\"inline-flex items-center\" key={branch.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"branch\"\r\n                                                value={branch.id}\r\n                                                checked={selectedBranches === branch.id}\r\n                                                onChange={() => setSelectedBranches(branch.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{branch.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                            {/* Select On-Site Status */}\r\n                            <div className=\"mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 p-4 border-b border-gray-200\">\r\n                                    On-Site Status <span className='text-red-600 text-base'>*</span>\r\n                                </label>\r\n                                <div className=\"flex flex-col p-4\">\r\n                                    {onsiteStatuses.map(onsiteStatus => (\r\n                                        <label className=\"inline-flex items-center\" key={onsiteStatus.id}>\r\n                                            <input\r\n                                                type=\"radio\"\r\n                                                name=\"onsiteStatus\"\r\n                                                value={onsiteStatus.id}\r\n                                                checked={selectedOnsiteStatuses === onsiteStatus.id}\r\n                                                onChange={() => setSelectedOnsiteStatuses(onsiteStatus.id)}\r\n                                                required\r\n                                                className=\"form-radio my-2\"\r\n                                            />\r\n                                            <span className=\"ml-2\">{onsiteStatus.name}</span>\r\n                                        </label>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        {error && <p className=\"text-red-500 text-sm pt-4\">{error}</p>}\r\n                        <div className='text-left p-6'>\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4\"\r\n                            >\r\n                                <span class=\"material-symbols-rounded text-white text-xl font-regular\">add_circle</span>\r\n                                {loading ? 'Onboarding...' : 'Send Invitation'}\r\n                            </button>\r\n                        </div>\r\n                        {successMessage && <p className=\"text-green-500 text-sm\">{successMessage}</p>}\r\n                    </form>\r\n                </div>\r\n            </div>\r\n           \r\n        </>\r\n    );\r\n};\r\n\r\nexport default AddMemberTest;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,OAAOF,KAAK,KAAK,IAAI;AACzB,CAAC;AAED,MAAMG,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB;AAElD,MAAMC,aAAa,GAAGA,CAAC;EAACC,SAAS;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgD,GAAG,EAAEC,MAAM,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACgE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACkE,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5E,MAAM,CAACoE,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAACsE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACwE,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC9E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoF,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAMwF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAChF,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,QAAQ,EAAE;QAC7C+E,MAAM,EAAE,KAAK;QAEbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC1E,QAAQ,CAACyE,IAAI,CAAC;IAClB,CAAC,CAAC,OAAOd,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAC3F,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,QAAQ,EAAE;QAC7C+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMG,SAAS,GAAGJ,IAAI,CAACxE,KAAK,CAAC,CAAC;;MAE9B,MAAM6E,QAAQ,GAAGD,SAAS,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;QAC7CD,GAAG,CAACC,IAAI,CAACC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QACtB,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN9E,QAAQ,CAAC2E,SAAS,CAAC;MACnB/C,gBAAgB,CAACgD,QAAQ,CAAC;IAC9B,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMQ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAAClG,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,QAAQ,EAAE;QAC7C+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMU,SAAS,GAAGX,IAAI,CAACtE,KAAK,CAAC,CAAC;;MAE9B,MAAMkF,QAAQ,GAAGD,SAAS,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEM,IAAI,KAAK;QAC7CN,GAAG,CAACM,IAAI,CAACJ,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QACtB,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN5E,QAAQ,CAACgF,SAAS,CAAC;MACnBpD,gBAAgB,CAACqD,QAAQ,CAAC;IAC9B,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACtG,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,cAAc,EAAE;QACnD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClC,MAAMc,eAAe,GAAGf,IAAI,CAACpE,WAAW;;MAExC;MACA,MAAMoF,0BAA0B,GAAG,EAAE;;MAErC;MACAnF,cAAc,CAACkF,eAAe,CAAC;MAC/BlD,sBAAsB,CAACmD,0BAA0B,CAAC,CAAC,CAAC;IAExD,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAGD;EACA,MAAMe,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACzG,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,eAAe,EAAE;QACpD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;MAElC;MACA,MAAMiB,gBAAgB,GAAGlB,IAAI,CAAClE,YAAY,CAAC,CAAC;;MAE5C,MAAMqF,eAAe,GAAGD,gBAAgB,CAACZ,MAAM,CAAC,CAACC,GAAG,EAAEa,WAAW,KAAK;QAClEb,GAAG,CAACa,WAAW,CAACX,EAAE,CAAC,GAAG,KAAK;QAC3B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENxE,eAAe,CAACmF,gBAAgB,CAAC;MACjCnD,uBAAuB,CAAC,EAAE,CAAC;IAC/B,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMmB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC7G,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,iBAAiB,EAAE;QACtD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAMqB,iBAAiB,GAAGtB,IAAI,CAAC,gBAAgB,CAAC;MAEhD,MAAMuB,gBAAgB,GAAGD,iBAAiB,CAAChB,MAAM,CAAC,CAACC,GAAG,EAAEiB,YAAY,KAAK;QACrEjB,GAAG,CAACiB,YAAY,CAACf,EAAE,CAAC,GAAG,KAAK;QAC5B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENtE,gBAAgB,CAACqF,iBAAiB,CAAC;MACnCrD,wBAAwB,CAAC,EAAE,CAAC;IAChC,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMuB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACjH,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,oBAAoB,EAAE;QACzD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;;MAGlC;MACA,MAAMyB,oBAAoB,GAAG1B,IAAI,CAAC2B,eAAe,CAAC,CAAC;;MAEnD,MAAMC,mBAAmB,GAAGF,oBAAoB,CAACpB,MAAM,CAAC,CAACC,GAAG,EAAEsB,cAAc,KAAK;QAC7EtB,GAAG,CAACsB,cAAc,CAACpB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAChC,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENpE,mBAAmB,CAACuF,oBAAoB,CAAC;MACzCvD,2BAA2B,CAACyD,mBAAmB,CAAC;IACpD,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAM4B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACtH,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,mBAAmB,EAAE;QACxD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAM8B,mBAAmB,GAAG/B,IAAI,CAAC,kBAAkB,CAAC;MAEpD,MAAMgC,kBAAkB,GAAGD,mBAAmB,CAACzB,MAAM,CAAC,CAACC,GAAG,EAAE0B,aAAa,KAAK;QAC1E1B,GAAG,CAAC0B,aAAa,CAACxB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAC/B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENlE,kBAAkB,CAAC0F,mBAAmB,CAAC;MACvC1D,0BAA0B,CAAC2D,kBAAkB,CAAC;IAClD,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMgC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC1H,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,gBAAgB,EAAE;QACrD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAMkC,gBAAgB,GAAGnC,IAAI,CAACoC,aAAa;MAE3C,MAAMC,eAAe,GAAGF,gBAAgB,CAAC7B,MAAM,CAAC,CAACC,GAAG,EAAE+B,WAAW,KAAK;QAClE/B,GAAG,CAAC+B,WAAW,CAAC7B,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENhE,eAAe,CAAC4F,gBAAgB,CAAC;MACjC5D,uBAAuB,CAAC8D,eAAe,CAAC;IAC5C,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMqC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC/H,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,qBAAqB,EAAE;QAC1D+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAMuC,qBAAqB,GAAGxC,IAAI,CAACyC,kBAAkB;MAErD,MAAMC,oBAAoB,GAAGF,qBAAqB,CAAClC,MAAM,CAAC,CAACC,GAAG,EAAEoC,eAAe,KAAK;QAChFpC,GAAG,CAACoC,eAAe,CAAClC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QACjC,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN9D,oBAAoB,CAAC+F,qBAAqB,CAAC;MAC3C/D,4BAA4B,CAACiE,oBAAoB,CAAC;IACtD,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAM0C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpI,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,kBAAkB,EAAE;QACvD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAM4C,kBAAkB,GAAG7C,IAAI,CAAC8C,eAAe;MAE/C,MAAMC,iBAAiB,GAAGF,kBAAkB,CAACvC,MAAM,CAAC,CAACC,GAAG,EAAEyC,YAAY,KAAK;QACvEzC,GAAG,CAACyC,YAAY,CAACvC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAC9B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN5D,iBAAiB,CAACkG,kBAAkB,CAAC;MACrClE,yBAAyB,CAACoE,iBAAiB,CAAC;IAChD,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAM+C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACzI,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,WAAW,EAAE;QAChD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAMiD,YAAY,GAAGlD,IAAI,CAACpD,QAAQ;MAElC,MAAMuG,WAAW,GAAGD,YAAY,CAAC5C,MAAM,CAAC,CAACC,GAAG,EAAE6C,MAAM,KAAK;QACrD7C,GAAG,CAAC6C,MAAM,CAAC3C,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QACxB,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN1D,WAAW,CAACqG,YAAY,CAAC;MACzBrE,mBAAmB,CAACsE,WAAW,CAAC;IACpC,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;;EAED;EACA,MAAMmD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC7I,YAAY,CAAC,CAAC,EAAE;MACjB2E,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACJ;IAEA,MAAM1E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI;MACA,MAAM8E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,kBAAkB,EAAE;QACvD+E,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAI,CAACgF,QAAQ,CAACI,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,+DAA+D,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAC1G;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAElC,MAAMqD,kBAAkB,GAAGtD,IAAI,CAACuD,eAAe;MAE/C,MAAMC,iBAAiB,GAAGF,kBAAkB,CAAChD,MAAM,CAAC,CAACC,GAAG,EAAEkD,YAAY,KAAK;QACvElD,GAAG,CAACkD,YAAY,CAAChD,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAC9B,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MAENxD,iBAAiB,CAACuG,kBAAkB,CAAC;MACrCvE,yBAAyB,CAACyE,iBAAiB,CAAC;IAChD,CAAC,CAAC,OAAOtE,KAAK,EAAE;MACZC,QAAQ,CAACD,KAAK,CAACgB,OAAO,CAAC;IAC3B;EACJ,CAAC;EAKDnG,SAAS,CAAC,MAAM;IACZyF,UAAU,CAAC,CAAC;IACZW,UAAU,CAAC,CAAC;IACZO,UAAU,CAAC,CAAC;IACZI,gBAAgB,CAAC,CAAC;IAClBG,iBAAiB,CAAC,CAAC;IACnBI,kBAAkB,CAAC,CAAC;IACpBI,qBAAqB,CAAC,CAAC;IACvBK,oBAAoB,CAAC,CAAC;IACtBI,iBAAiB,CAAC,CAAC;IACnBK,sBAAsB,CAAC,CAAC;IACxBK,mBAAmB,CAAC,CAAC;IACrBK,aAAa,CAAC,CAAC;IACfI,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtJ,SAAS,CAAC,MAAM;IACZ,MAAM2J,MAAM,GAAGhJ,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;IAC9C,IAAI+I,MAAM,EAAE;MACRzE,eAAe,CAACyE,MAAM,CAAC;IAC3B;EACJ,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC/F,mBAAmB,EAAE,OAAOlC,KAAK;IAEtC,OAAOA,KAAK,CAACkI,MAAM,CAAC/C,IAAI;MAAA,IAAAgD,iBAAA;MAAA,QAAAA,iBAAA,GACxBhD,IAAI,CAACjF,WAAW,cAAAiI,iBAAA,uBAAhBA,iBAAA,CAAkBC,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACtD,EAAE,KAAK7C,mBAAmB,CAAC;IAAA,CAC3E,CAAC;EACL,CAAC;;EAED;EACA,MAAMoG,gBAAgB,GAAIC,MAAM,IAAK;IACjC1G,gBAAgB,CAAE2G,iBAAiB,IAAK;MACpC,MAAMC,YAAY,GAAG;QAAE,GAAGD,iBAAiB;QAAE,CAACD,MAAM,GAAG,CAACC,iBAAiB,CAACD,MAAM;MAAE,CAAC;MACnF,OAAOE,YAAY;IACvB,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,uBAAuB,GAAIH,MAAM,IAAK;IACxCxG,gBAAgB,CAACwG,MAAM,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMI,gBAAgB,GAAIJ,MAAM,IAAK;IACjCtG,gBAAgB,CAACsG,MAAM,CAAC,CAAC,CAAE;EAC/B,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC3B3G,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAE;EAC7B,CAAC;EAGD,MAAM4G,aAAa,GAAGZ,wBAAwB,CAAC,CAAC;EAKhD,MAAMa,YAAY,GAAG,MAAOC,KAAK,IAAK;IAClCA,KAAK,CAACC,cAAc,CAAC,CAAC;IAEtBrF,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMsF,UAAU,GAAG3H,GAAG,CAAC4H,IAAI,CAAC,CAAC;IAC7B,MAAMC,YAAY,GAAG3H,KAAK,CAAC0H,IAAI,CAAC,CAAC;IAEjC,IAAI,CAACD,UAAU,IAAI,CAACE,YAAY,EAAE;MAC9B1F,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACJ;IAEA,MAAM2F,SAAS,GAAGxJ,KAAK,CAACwI,IAAI,CAACiB,IAAI,IAAI,OAAOA,IAAI,CAAC/H,GAAG,KAAK,QAAQ,IAAI+H,IAAI,CAAC/H,GAAG,CAACgI,WAAW,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC,KAAKD,UAAU,CAACK,WAAW,CAAC,CAAC,CAAC;IAChI,MAAMC,WAAW,GAAG3J,KAAK,CAACwI,IAAI,CAACiB,IAAI,IAAI,OAAOA,IAAI,CAAC7H,KAAK,KAAK,QAAQ,IAAI6H,IAAI,CAAC7H,KAAK,CAAC8H,WAAW,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC,KAAKC,YAAY,CAACG,WAAW,CAAC,CAAC,CAAC;IAExI,IAAIF,SAAS,IAAIG,WAAW,EAAE;MAC1B,IAAI/E,OAAO,GAAG,MAAM;MACpB,IAAI4E,SAAS,EAAE5E,OAAO,IAAI,MAAM;MAChC,IAAI+E,WAAW,EAAE/E,OAAO,IAAI,CAACA,OAAO,CAACgF,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,QAAQ;MAC9EhF,OAAO,IAAI,oDAAoD;MAC/Df,QAAQ,CAACe,OAAO,CAAC;MACjBiF,UAAU,CAAC,MAAMhG,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACpC;IACJ;IAEAA,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,MAAMiG,eAAe,GAAGC,MAAM,CAACC,IAAI,CAAClI,aAAa,CAAC,CAACwG,MAAM,CAAC2B,MAAM,IAAInI,aAAa,CAACmI,MAAM,CAAC,CAAC;IAC1F,MAAMC,eAAe,GAAGH,MAAM,CAACC,IAAI,CAAChI,aAAa,CAAC,CAACsG,MAAM,CAACK,MAAM,IAAI3G,aAAa,CAAC2G,MAAM,CAAC,CAAC;IAC1F;IACA,MAAMwB,qBAAqB,GAAGC,KAAK,CAACC,OAAO,CAAC/H,mBAAmB,CAAC,GAAGA,mBAAmB,GAAG,CAACA,mBAAmB,CAAC;IAC9G;IACA,MAAMgI,sBAAsB,GAAG9H,oBAAoB,GAAG,CAACA,oBAAoB,CAAC,GAAG,EAAE;IACjF,MAAM+H,uBAAuB,GAAG7H,qBAAqB,GAAG,CAACA,qBAAqB,CAAC,GAAG,EAAE;IACpF,MAAM8H,yBAAyB,GAAGJ,KAAK,CAACC,OAAO,CAACzH,wBAAwB,CAAC,GAAGA,wBAAwB,GAAG,CAACA,wBAAwB,CAAC;IACjI,MAAM6H,wBAAwB,GAAGL,KAAK,CAACC,OAAO,CAACvH,uBAAuB,CAAC,GAAGA,uBAAuB,GAAG,CAACA,uBAAuB,CAAC;IAC7H,MAAM4H,sBAAsB,GAAGN,KAAK,CAACC,OAAO,CAACrH,oBAAoB,CAAC,GAAGA,oBAAoB,GAAG,CAACA,oBAAoB,CAAC;IAClH,MAAM2H,0BAA0B,GAAGP,KAAK,CAACC,OAAO,CAACnH,yBAAyB,CAAC,GAAGA,yBAAyB,GAAG,CAACA,yBAAyB,CAAC;IACrI,MAAM0H,uBAAuB,GAAGR,KAAK,CAACC,OAAO,CAACjH,sBAAsB,CAAC,GAAGA,sBAAsB,GAAG,CAACA,sBAAsB,CAAC;IACzH,MAAMyH,iBAAiB,GAAGT,KAAK,CAACC,OAAO,CAAC/G,gBAAgB,CAAC,GAAGA,gBAAgB,GAAG,CAACA,gBAAgB,CAAC;IACjG,MAAMwH,uBAAuB,GAAGV,KAAK,CAACC,OAAO,CAAC7G,sBAAsB,CAAC,GAAGA,sBAAsB,GAAG,CAACA,sBAAsB,CAAC;IAEzH,IAAI;MACA,MAAMrE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAM0L,SAAS,GAAGrH,YAAY;MAE9B,IAAI,CAACqH,SAAS,EAAE;QACZlH,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACJ;MAEA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9E,OAAO,QAAQ,EAAE;QAC7C+E,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACL,eAAe,EAAE,UAAUnF,KAAK,EAAE;UAClC,cAAc,EAAE;QACpB,CAAC;QACD6L,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACjBxJ,GAAG,EAAE2H,UAAU;UACfzH,KAAK,EAAE2H,YAAY;UACnBrJ,KAAK,EAAE4J,eAAe,CAACqB,GAAG,CAACC,MAAM,CAAC;UAAE;UACpChL,KAAK,EAAE8J,eAAe,CAACiB,GAAG,CAACC,MAAM,CAAC;UAAE;UACpCC,eAAe,EAAEnJ,aAAa;UAC9B5B,WAAW,EAAE6J,qBAAqB,CAACgB,GAAG,CAACC,MAAM,CAAC;UAAE;UAChD5K,YAAY,EAAE8J,sBAAsB,CAACa,GAAG,CAACC,MAAM,CAAC;UAChDE,cAAc,EAAEf,uBAAuB,CAACY,GAAG,CAACC,MAAM,CAAC;UACnDG,iBAAiB,EAAEf,yBAAyB,CAACW,GAAG,CAACC,MAAM,CAAC;UACxDI,gBAAgB,EAAEf,wBAAwB,CAACU,GAAG,CAACC,MAAM,CAAC;UACtDtE,aAAa,EAAE4D,sBAAsB,CAACS,GAAG,CAACC,MAAM,CAAC;UACjDjE,kBAAkB,EAAEwD,0BAA0B,CAACQ,GAAG,CAACC,MAAM,CAAC;UAC1D5D,eAAe,EAAEoD,uBAAuB,CAACO,GAAG,CAACC,MAAM,CAAC;UACpD9J,QAAQ,EAAEuJ,iBAAiB,CAACM,GAAG,CAACC,MAAM,CAAC;UACvCnD,eAAe,EAAE6C,uBAAuB,CAACK,GAAG,CAACC,MAAM,CAAC;UACpDK,UAAU,EAAEV;QAChB,CAAC;MACL,CAAC,CAAC;MAEF,IAAI,CAAC5G,QAAQ,CAACI,EAAE,EAAE;QACd,MAAMmH,SAAS,GAAG,MAAMvH,QAAQ,CAACwH,IAAI,CAAC,CAAC;QACvCC,OAAO,CAAChI,KAAK,CAAC,iBAAiB,EAAE8H,SAAS,CAAC;QAC3C,MAAM,IAAIlH,KAAK,CAAC,uBAAuB,GAAGL,QAAQ,CAACM,UAAU,CAAC;MAClE;MAGA,MAAMoH,MAAM,GAAG,MAAM1H,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpC;MACA9F,YAAY,CAAC,SAAS,CAAC;MACvB8C,MAAM,CAAC,EAAE,CAAC;MACVE,QAAQ,CAAC,EAAE,CAAC;MACZE,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpBM,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,uBAAuB,CAAC,EAAE,CAAC;MAC3BE,wBAAwB,CAAC,EAAE,CAAC;MAC5BE,2BAA2B,CAAC,EAAE,CAAC;MAC/BE,0BAA0B,CAAC,EAAE,CAAC;MAC9BE,uBAAuB,CAAC,EAAE,CAAC;MAC3BE,4BAA4B,CAAC,EAAE,CAAC;MAChCE,yBAAyB,CAAC,EAAE,CAAC;MAC7BE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,yBAAyB,CAAC,EAAE,CAAC;MAE7BS,UAAU,CAAC,CAAC;IAGhB,CAAC,CAAC,OAAON,KAAK,EAAE;MACZ;MACA/E,YAAY,CAAC,OAAO,CAAC;MACrBkF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,IAAI,CAACpE,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACIZ,OAAA,CAAAE,SAAA;IAAA6M,QAAA,eAEI/M,OAAA;MAAKgN,SAAS,EAAC,kHAAkH;MAAAD,QAAA,eAC7H/M,OAAA;QAAKgN,SAAS,EAAC,uEAAuE;QAAAD,QAAA,gBAClF/M,OAAA;UAAKgN,SAAS,EAAC,8DAA8D;UAAAD,QAAA,gBACzE/M,OAAA;YAAIgN,SAAS,EAAC,+CAA+C;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpN,OAAA;YACIgN,SAAS,EAAC,4CAA4C;YACtDK,OAAO,EAAEA,CAAA,KAAMxM,UAAU,CAAC,KAAK,CAAE;YAAAkM,QAAA,EACpC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENpN,OAAA;UAAMsN,QAAQ,EAAEnD,YAAa;UAAA4C,QAAA,gBACzB/M,OAAA;YAAKgN,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACrF/M,OAAA;cAAKgN,SAAS,EAAC,sCAAsC;cAAAD,QAAA,gBACjD/M,OAAA;gBAAOuN,OAAO,EAAC,KAAK;gBAACP,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,GAAC,cACpD,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACRpN,OAAA;gBACIwN,IAAI,EAAC,QAAQ;gBACbpH,EAAE,EAAC,KAAK;gBACRqH,KAAK,EAAE9K,GAAI;gBACX+K,QAAQ,EAAGC,CAAC,IAAK/K,MAAM,CAAC+K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACxCI,WAAW,EAAC,oBAAoB;gBAChCC,QAAQ;gBACRd,SAAS,EAAC;cAAwI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpN,OAAA;cAAKgN,SAAS,EAAC,sCAAsC;cAAAD,QAAA,gBACjD/M,OAAA;gBAAOuN,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,GAAC,QAC5D,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACRpN,OAAA;gBACIwN,IAAI,EAAC,OAAO;gBACZpH,EAAE,EAAC,OAAO;gBACVqH,KAAK,EAAE5K,KAAM;gBACb6K,QAAQ,EAAGC,CAAC,IAAK7K,QAAQ,CAAC6K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC1CI,WAAW,EAAC,uBAAuB;gBACnCC,QAAQ;gBACRd,SAAS,EAAC;cAAwI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,sCAAsC;cAAAD,QAAA,gBACjD/M,OAAA;gBAAOuN,OAAO,EAAC,aAAa;gBAACP,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,GAAC,cAC5D,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACb/M,OAAA;kBACIyN,KAAK,EAAEhK,oBAAoB,IAAI,EAAG,CAAC;kBAAA;kBACnCiK,QAAQ,EAAGC,CAAC,IAAKjK,uBAAuB,CAACiK,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC;kBAAA;kBAC1DK,QAAQ;kBACRd,SAAS,EAAC,wIAAwI;kBAAAD,QAAA,gBAElJ/M,OAAA;oBAAQyN,KAAK,EAAC,EAAE;oBAACM,QAAQ;oBAAAhB,QAAA,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACtD3L,YAAY,CAAC2K,GAAG,CAAErF,WAAW,iBAC1B/G,OAAA;oBAA6ByN,KAAK,EAAE1G,WAAW,CAACX,EAAG;oBAAA2G,QAAA,EAC9ChG,WAAW,CAACiH;kBAAI,GADRjH,WAAW,CAACX,EAAE;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEnB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,sCAAsC;cAAAD,QAAA,gBACjD/M,OAAA;gBAAOuN,OAAO,EAAC,eAAe;gBAACP,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,GAAC,uBACrD,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACb/M,OAAA;kBACIyN,KAAK,EAAE9J,qBAAqB,IAAI,EAAG;kBACnC+J,QAAQ,EAAGC,CAAC,IAAK/J,wBAAwB,CAAC+J,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC;kBAAA;kBAC3DK,QAAQ;kBACRd,SAAS,EAAC,wIAAwI;kBAAAD,QAAA,gBAElJ/M,OAAA;oBAAQyN,KAAK,EAAC,EAAE;oBAACM,QAAQ;oBAAAhB,QAAA,EAAC;kBAA2B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC7DzL,aAAa,CAACyK,GAAG,CAAEjF,YAAY,iBAC5BnH,OAAA;oBAA8ByN,KAAK,EAAEtG,YAAY,CAACf,EAAG;oBAAA2G,QAAA,EAChD5F,YAAY,CAAC6G;kBAAI,GADT7G,YAAY,CAACf,EAAE;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,QAC9E,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7B5L,KAAK,CAACiL,GAAG,CAACjG,IAAI,iBACXnG,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,UAAU;oBACfS,OAAO,EAAElL,aAAa,CAACoD,IAAI,CAACC,EAAE,CAAC,IAAI,KAAM;oBACzCsH,QAAQ,EAAEA,CAAA,KAAM1K,gBAAgB,CAACkL,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE,CAAC/H,IAAI,CAACC,EAAE,GAAG,CAAC8H,IAAI,CAAC/H,IAAI,CAACC,EAAE;oBAAE,CAAC,CAAC,CAAE;oBACnF4G,SAAS,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE5G,IAAI,CAAC6H;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,KAAC;gBAAA,GAPGjH,IAAI,CAACC,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQjD,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBAC5F/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,aACzE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7BxL,WAAW,CAAC6K,GAAG,CAAC1C,UAAU,iBAC3B1J,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACAwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,YAAY;oBACjBP,KAAK,EAAE/D,UAAU,CAACtD,EAAG;oBACrB6H,OAAO,EAAE1K,mBAAmB,KAAKmG,UAAU,CAACtD,EAAG;oBAC/CsH,QAAQ,EAAEA,CAAA,KAAMlK,sBAAsB,CAACkG,UAAU,CAACtD,EAAE,CAAE;oBACtD0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAErD,UAAU,CAACsE;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVF1D,UAAU,CAACtD,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWvD,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,QAC9E,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7B7C,aAAa,CAACiE,MAAM,GAAG,CAAC,GACrBjE,aAAa,CAACkC,GAAG,CAAE5F,IAAI,iBACnBxG,OAAA;kBACIgN,SAAS,EAAC,mCAAmC;kBAE7CoB,YAAY,EAAEA,CAAA,KAAMpE,gBAAgB,CAACxD,IAAI,CAACJ,EAAE,CAAE;kBAC9CiI,YAAY,EAAEpE,gBAAiB;kBAAA8C,QAAA,gBAE/B/M,OAAA;oBAAOgN,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACvC/M,OAAA;sBACIwN,IAAI,EAAC,UAAU;sBACfS,OAAO,EAAEhL,aAAa,CAACuD,IAAI,CAACJ,EAAE,CAAC,IAAI,KAAM;sBACzCsH,QAAQ,EAAEA,CAAA,KAAM/D,gBAAgB,CAACnD,IAAI,CAACJ,EAAE,CAAE;sBAC1C4G,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACFpN,OAAA;sBAAMgN,SAAS,EAAC,MAAM;sBAAAD,QAAA,EAAEvG,IAAI,CAACwH;oBAAI;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EAGPjK,aAAa,KAAKqD,IAAI,CAACJ,EAAE,IAAI/C,aAAa,KAAKmD,IAAI,CAACJ,EAAE,iBACnDpG,OAAA;oBAAKgN,SAAS,EAAC,kGAAkG;oBAAAD,QAAA,gBAC7G/M,OAAA;sBAAMgN,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjFpN,OAAA;sBACIwN,IAAI,EAAC,OAAO;sBACZS,OAAO,EAAE9K,aAAa,KAAKqD,IAAI,CAACJ,EAAG;sBACnCsH,QAAQ,EAAEA,CAAA,KAAM3D,uBAAuB,CAACvD,IAAI,CAACJ,EAAE,CAAE;sBACjD4G,SAAS,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR,EAGAjK,aAAa,KAAKqD,IAAI,CAACJ,EAAE,iBACtBpG,OAAA;oBAAKgN,SAAS,EAAC,kGAAkG;oBAAAD,QAAA,gBAC7G/M,OAAA;sBAAMgN,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAE9D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPpN,OAAA;sBACIwN,IAAI,EAAC,OAAO;sBACZS,OAAO,EAAE,IAAK;sBACdF,QAAQ;sBACRf,SAAS,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACR;gBAAA,GAxCI5G,IAAI,CAACJ,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCX,CACR,CAAC,gBAEFpN,OAAA;kBAAA+M,QAAA,EAAG;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAC1C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,oBAClE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7BlL,gBAAgB,CAACuK,GAAG,CAAC5E,cAAc,iBAChCxH,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,gBAAgB;oBACrBP,KAAK,EAAEjG,cAAc,CAACpB,EAAG;oBACzB6H,OAAO,EAAEpK,wBAAwB,KAAK2D,cAAc,CAACpB,EAAG;oBACxDsH,QAAQ,EAAEA,CAAA,KAAM5J,2BAA2B,CAAC0D,cAAc,CAACpB,EAAE,CAAE;oBAC/D0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEvF,cAAc,CAACwG;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVN5F,cAAc,CAACpB,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW3D,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,mBACnE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7BhL,eAAe,CAACqK,GAAG,CAACxE,aAAa,iBAC9B5H,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,eAAe;oBACpBP,KAAK,EAAE7F,aAAa,CAACxB,EAAG;oBACxB6H,OAAO,EAAElK,uBAAuB,KAAK6D,aAAa,CAACxB,EAAG;oBACtDsH,QAAQ,EAAEA,CAAA,KAAM1J,0BAA0B,CAAC4D,aAAa,CAACxB,EAAE,CAAE;oBAC7D0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEnF,aAAa,CAACoG;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVLxF,aAAa,CAACxB,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW1D,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,gBACtE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7B9K,YAAY,CAACmK,GAAG,CAACnE,WAAW,iBACzBjI,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,aAAa;oBAClBP,KAAK,EAAExF,WAAW,CAAC7B,EAAG;oBACtB6H,OAAO,EAAEhK,oBAAoB,KAAKgE,WAAW,CAAC7B,EAAG;oBACjDsH,QAAQ,EAAEA,CAAA,KAAMxJ,uBAAuB,CAAC+D,WAAW,CAAC7B,EAAE,CAAE;oBACxD0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE9E,WAAW,CAAC+F;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVHnF,WAAW,CAAC7B,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWxD,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,qBACjE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7B5K,iBAAiB,CAACiK,GAAG,CAAC9D,eAAe,iBAClCtI,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,iBAAiB;oBACtBP,KAAK,EAAEnF,eAAe,CAAClC,EAAG;oBAC1B6H,OAAO,EAAE9J,yBAAyB,KAAKmE,eAAe,CAAClC,EAAG;oBAC1DsH,QAAQ,EAAEA,CAAA,KAAMtJ,4BAA4B,CAACkE,eAAe,CAAClC,EAAE,CAAE;oBACjE0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEzE,eAAe,CAAC0F;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVP9E,eAAe,CAAClC,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW5D,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,uBAC/D,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7B1K,cAAc,CAAC+J,GAAG,CAACzD,YAAY,iBAC5B3I,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,cAAc;oBACnBP,KAAK,EAAE9E,YAAY,CAACvC,EAAG;oBACvB6H,OAAO,EAAE5J,sBAAsB,KAAKsE,YAAY,CAACvC,EAAG;oBACpDsH,QAAQ,EAAEA,CAAA,KAAMpJ,yBAAyB,CAACqE,YAAY,CAACvC,EAAE,CAAE;oBAC3D0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEpE,YAAY,CAACqF;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVJzE,YAAY,CAACvC,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWzD,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,SAC7E,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7BxK,QAAQ,CAAC6J,GAAG,CAACrD,MAAM,iBAChB/I,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,QAAQ;oBACbP,KAAK,EAAE1E,MAAM,CAAC3C,EAAG;oBACjB6H,OAAO,EAAE1J,gBAAgB,KAAKwE,MAAM,CAAC3C,EAAG;oBACxCsH,QAAQ,EAAEA,CAAA,KAAMlJ,mBAAmB,CAACuE,MAAM,CAAC3C,EAAE,CAAE;oBAC/C0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEhE,MAAM,CAACiF;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVErE,MAAM,CAAC3C,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWnD,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENpN,OAAA;cAAKgN,SAAS,EAAC,6EAA6E;cAAAD,QAAA,gBACxF/M,OAAA;gBAAOgN,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,GAAC,iBACrE,eAAA/M,OAAA;kBAAMgN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACRpN,OAAA;gBAAKgN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAC7BtK,cAAc,CAAC2J,GAAG,CAAChD,YAAY,iBAC5BpJ,OAAA;kBAAOgN,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,gBACvC/M,OAAA;oBACIwN,IAAI,EAAC,OAAO;oBACZQ,IAAI,EAAC,cAAc;oBACnBP,KAAK,EAAErE,YAAY,CAAChD,EAAG;oBACvB6H,OAAO,EAAExJ,sBAAsB,KAAK2E,YAAY,CAAChD,EAAG;oBACpDsH,QAAQ,EAAEA,CAAA,KAAMhJ,yBAAyB,CAAC0E,YAAY,CAAChD,EAAE,CAAE;oBAC3D0H,QAAQ;oBACRd,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACFpN,OAAA;oBAAMgN,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE3D,YAAY,CAAC4E;kBAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAVJhE,YAAY,CAAChD,EAAE;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWzD,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACLvI,KAAK,iBAAI7E,OAAA;YAAGgN,SAAS,EAAC,2BAA2B;YAAAD,QAAA,EAAElI;UAAK;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9DpN,OAAA;YAAKgN,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC1B/M,OAAA;cACIwN,IAAI,EAAC,QAAQ;cACbR,SAAS,EAAC,6HAA6H;cAAAD,QAAA,gBAEvI/M,OAAA;gBAAMsO,KAAK,EAAC,0DAA0D;gBAAAvB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACvFrI,OAAO,GAAG,eAAe,GAAG,iBAAiB;YAAA;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EACLnI,cAAc,iBAAIjF,OAAA;YAAGgN,SAAS,EAAC,wBAAwB;YAAAD,QAAA,EAAE9H;UAAc;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBAER,CAAC;AAEX,CAAC;AAACtM,EAAA,CAhjCIH,aAAa;EAAA,QACEf,WAAW,EACXC,WAAW;AAAA;AAAA0O,EAAA,GAF1B5N,aAAa;AAkjCnB,eAAeA,aAAa;AAAC,IAAA4N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}