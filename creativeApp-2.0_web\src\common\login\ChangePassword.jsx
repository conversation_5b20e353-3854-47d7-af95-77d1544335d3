import React, { useState, useEffect } from 'react';
import { alertMessage } from '../coreui'; // Assuming you have this utility
const API_URL = process.env.REACT_APP_BASE_API_URL;

export default function ChangePassword() {
    const [user, setUser] = useState(null);
    const [password, setPassword] = useState('');
    const [passwordConfirmation, setPasswordConfirmation] = useState('');
    const [isPasswordVisible, setIsPasswordVisible] = useState(false);
    const [isConfirmationVisible, setIsConfirmationVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [errorMessage, setErrorMessage] = useState('');
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    // Fetch user data from the API
    const fetchUserData = async () => {
        if (!userId) {
            setError('User ID not found in local storage');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch user data');
            }

            const data = await response.json();
            setUser(data.user);
        } catch (error) {
            setError(error.message);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []); // Empty dependency array to run on mount

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (password !== passwordConfirmation) {
            setError('Passwords do not match');
            return;
        }

        setLoading(true);
        setError('');
        setShowSuccessMessage(false);

        try {
            if (!userId) {
                setError('User ID not found in local storage');
                return;
            }

            // Prepare form data
            const requestData = {
                password,
            };

            // Make the PUT request to update the user's password
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });

            if (!response.ok) {
                throw new Error('Failed to update password');
            }

            const updatedUser = await response.json();

            alertMessage('success'); // Assuming this triggers a success alert
            setShowSuccessMessage(true); // Show success message
            setPassword('');
            setPasswordConfirmation('');
            setLoading(false);
        } catch (error) {
            alertMessage('error'); // Assuming this triggers an error alert
            setError(error.message);
            setLoading(false);
        }
    };

    return (
        <section className="bg-white dark:bg-gray-900 w-full md:w-1/2">
            <div className="flex flex-col items-start justify-start px-6 py-8 mx-auto md:h-screen lg:py-0">
                <div className="w-full bg-white rounded-3xl dark:border md:mt-0 xl:p-0 dark:bg-gray-800 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-center gap-12">
                    <div className="p-6 space-y-4 md:space-y-6 sm:p-8 w-full text-left">
                        <h6 className='text-base text-gray-600 pb-1'>
                            {showSuccessMessage ? 'Your password has been reset!' : 'Reset Your Password'}
                        </h6>
                        <h1 className="text-2xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white mt-0">
                            {showSuccessMessage ? 'Your password has been reset!' : 'Update Your Password'}
                        </h1>

                        {error && <p className="text-red-500">{error}</p>}

                        {showSuccessMessage && (
                            <p className="text-green-500 font-regular">
                                Your password has been successfully updated! Please logout and login for confirmation
                            </p>
                        )}

                        <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
                            <div className='relative'>
                                <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">New Password</label>
                                <input
                                    type={isPasswordVisible ? "text" : "password"}
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    placeholder="Enter your new password"
                                    required
                                />
                                <span
                                    onClick={() => setIsPasswordVisible(!isPasswordVisible)}
                                    className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400 cursor-pointer"
                                >
                                    {isPasswordVisible ? 'visibility_off' : 'visibility'}
                                </span>
                            </div>

                            <div className='relative'>
                                <label htmlFor="password_confirmation" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirm Password</label>
                                <input
                                    type={isConfirmationVisible ? "text" : "password"}
                                    id="password_confirmation"
                                    value={passwordConfirmation}
                                    onChange={(e) => setPasswordConfirmation(e.target.value)}
                                    className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    placeholder="Confirm your new password"
                                    required
                                />
                                <span
                                    onClick={() => setIsConfirmationVisible(!isConfirmationVisible)}
                                    className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400 cursor-pointer"
                                >
                                    {isConfirmationVisible ? 'visibility_off' : 'visibility'}
                                </span>
                            </div>

                            <button
                                type="submit"
                                className="w-full text-white bg-primary hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-xl text-sm px-5 py-3 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            >
                                {loading ? 'Reseting...' : 'Reset Password'}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    );
}
