import React, { forwardRef } from "react"; 
import { Quill } from "react-quill";

// Custom Undo button component
const CustomUndo = () => (
  <svg viewBox="0 0 18 18">
    <polygon className="ql-fill ql-stroke" points="6 10 4 12 2 10 6 10" />
    <path className="ql-stroke" d="M8.09,13.91A4.6,4.6,0,0,0,9,14A5,5,0,1,0,4,9" />
  </svg>
);

// Custom Redo button component
const CustomRedo = () => (
  <svg viewBox="0 0 18 18">
    <polygon className="ql-fill ql-stroke" points="12 10 14 12 16 10 12 10" />
    <path className="ql-stroke" d="M9.91,13.91A4.6,4.6,0,0,1,9,14A5,5,0,1,1,5,9" />
  </svg>
);

// Undo and redo functions
const undoChange = () => {
  const editor = document.querySelector(".ql-editor");
  if (editor) {
    const quillInstance = Quill.find(editor);
    quillInstance.history.undo();
  }
};

const redoChange = () => {
  const editor = document.querySelector(".ql-editor");
  if (editor) {
    const quillInstance = Quill.find(editor);
    quillInstance.history.redo();
  }
};

// Register fonts and sizes
const Size = Quill.import("formats/size");
Size.whitelist = ["extra-small", "small", "medium", "large"];
Quill.register(Size, true);

const Font = Quill.import("formats/font");
Font.whitelist = [
  "arial", "verdana", "tahoma", "trebuchet-ms", "times-new-roman", "georgia", 
  "garamond", "courier-new", "brush-script-mt", "cursive", "comic-sans", "helvetica", "lucida", "monospace"
];
Quill.register(Font, true);

// Editor Modules
export const modules = {
  toolbar: {
    container: "#toolbar",
    handlers: {
      undo: undoChange,
      redo: redoChange
    }
  },
  history: {
    delay: 500,
    maxStack: 100,
    userOnly: true
  }
};

// Editor Formats
export const formats = [
  "header",   // Enables H1-H6
  "font",
  "size",
  "bold",
  "italic",
  "underline",
  "align",
  "strike",
  "script",
  "blockquote",
  "background",
  "list",
  "bullet",
  "indent",
  "link",
  "image",
  "color",
  "code-block"
];

// Custom Quill Toolbar
const QuillToolbar = forwardRef((props, ref) => (
  <div id="toolbar">
    {/* Font and Size */}
    <span className="ql-formats">
      <select className="ql-font" defaultValue="arial">
        {Font.whitelist.map(font => (
          <option key={font} value={font}>
            {font.replace("-", " ").toUpperCase()}
          </option>
        ))}
      </select>
      <select className="ql-size" defaultValue="medium">
        <option value="extra-small">Size 1</option>
        <option value="small">Size 2</option>
        <option value="medium">Size 3</option>
        <option value="large">Size 4</option>
      </select>
      <select className="ql-header" defaultValue="">
        <option value="1">H1</option>
        <option value="2">H2</option>
        <option value="3">H3</option>
        <option value="4">H4</option>
        <option value="5">H5</option>
        <option value="6">H6</option>
        <option value="">Normal</option>
      </select>
    </span>

    {/* Formatting Options */}
    <span className="ql-formats">
      <button className="ql-bold" aria-label="Bold" />
      <button className="ql-italic" aria-label="Italic" />
      <button className="ql-underline" aria-label="Underline" />
      <button className="ql-strike" aria-label="Strikethrough" />
    </span>

    {/* Lists & Indentation */}
    <span className="ql-formats">
      <button className="ql-list" value="ordered" aria-label="Ordered List" />
      <button className="ql-list" value="bullet" aria-label="Bullet List" />
      <button className="ql-indent" value="-1" aria-label="Decrease Indent" />
      <button className="ql-indent" value="+1" aria-label="Increase Indent" />
    </span>

    {/* Align, Colors, Background */}
    <span className="ql-formats">
      <select className="ql-align" aria-label="Text Align" />
      <select className="ql-color" aria-label="Text Color" />
      <select className="ql-background" aria-label="Text Background" />
    </span>

    {/* Links, Images, Video */}
    {/* <span className="ql-formats">
      <button className="ql-link" aria-label="Insert Link" />
      <button className="ql-image" aria-label="Insert Image" />
      <button className="ql-video" aria-label="Insert Video" />
    </span> */}

    {/* Code & Cleanup */}
    {/* <span className="ql-formats">
      <button className="ql-code-block" aria-label="Insert Code Block" />
      <button className="ql-clean" aria-label="Clear Formatting" />
    </span> */}

    {/* Undo / Redo */}
    {/* <span className="ql-formats">
      <button className="ql-undo" onClick={undoChange} aria-label="Undo">
        <CustomUndo />
      </button>
      <button className="ql-redo" onClick={redoChange} aria-label="Redo">
        <CustomRedo />
      </button>
    </span> */}
  </div>
));

export default QuillToolbar;
