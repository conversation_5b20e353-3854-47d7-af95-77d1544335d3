-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jan 06, 2025 at 01:38 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `creative-app`
--

-- --------------------------------------------------------

--
-- Table structure for table `available_statuses`
--

CREATE TABLE `available_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `available_statuses`
--

INSERT INTO `available_statuses` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Full Time', ' ', ' ', '2024-12-12 06:54:07', '2024-12-12 06:54:07'),
(2, 'Part-Time', ' ', ' ', '2024-12-12 06:54:10', '2024-12-12 06:54:10'),
(3, 'Contractor', ' ', ' ', '2024-12-12 06:54:16', '2024-12-12 06:54:16');

-- --------------------------------------------------------

--
-- Table structure for table `available_status_user`
--

CREATE TABLE `available_status_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `available_status_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `available_status_user`
--

INSERT INTO `available_status_user` (`id`, `user_id`, `available_status_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `billing_statuses`
--

CREATE TABLE `billing_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `billing_statuses`
--

INSERT INTO `billing_statuses` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Billable', ' ', ' ', '2024-12-12 06:49:53', '2024-12-12 06:49:53'),
(2, 'Non-Billable', ' ', ' ', '2024-12-12 06:50:21', '2024-12-12 06:50:21');

-- --------------------------------------------------------

--
-- Table structure for table `billing_status_user`
--

CREATE TABLE `billing_status_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `billing_status_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `billing_status_user`
--

INSERT INTO `billing_status_user` (`id`, `user_id`, `billing_status_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `bloods`
--

CREATE TABLE `bloods` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bloods`
--

INSERT INTO `bloods` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'A+', ' ', ' ', '2024-12-12 06:59:53', '2024-12-12 06:59:53'),
(2, 'A-', ' ', ' ', '2024-12-12 07:00:06', '2024-12-12 07:00:06'),
(3, 'B+', ' ', ' ', '2024-12-12 07:00:11', '2024-12-12 07:00:11'),
(4, 'B-', ' ', ' ', '2024-12-12 07:00:14', '2024-12-12 07:00:14'),
(5, 'AB+', ' ', ' ', '2024-12-12 07:00:21', '2024-12-12 07:00:21'),
(6, 'AB-', ' ', ' ', '2024-12-12 07:00:24', '2024-12-12 07:00:24'),
(7, 'O+', ' ', ' ', '2024-12-12 07:00:28', '2024-12-12 07:00:28'),
(8, 'O-', ' ', ' ', '2024-12-12 07:00:32', '2024-12-12 07:00:32'),
(9, 'Bommm', ' ', ' ', '2024-12-12 07:00:35', '2024-12-12 07:00:35');

-- --------------------------------------------------------

--
-- Table structure for table `blood_user`
--

CREATE TABLE `blood_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `blood_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(6, 'MBD', ' ', ' ', '2024-12-12 06:55:11', '2024-12-12 06:55:11'),
(7, 'MHK', ' ', ' ', '2024-12-12 06:55:18', '2024-12-12 06:55:18'),
(8, 'Raowa', ' ', ' ', '2024-12-12 06:55:24', '2024-12-12 06:55:24'),
(9, 'Jessore', ' ', ' ', '2024-12-12 06:55:29', '2024-12-12 06:55:29');

-- --------------------------------------------------------

--
-- Table structure for table `branch_location`
--

CREATE TABLE `branch_location` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `branch_id` bigint(20) UNSIGNED NOT NULL,
  `location_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branch_location`
--

INSERT INTO `branch_location` (`id`, `branch_id`, `location_id`, `created_at`, `updated_at`) VALUES
(1, 6, 1, NULL, NULL),
(2, 7, 1, NULL, NULL),
(3, 8, 1, NULL, NULL),
(4, 9, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `branch_user`
--

CREATE TABLE `branch_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `branch_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branch_user`
--

INSERT INTO `branch_user` (`id`, `user_id`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 2, 6, NULL, NULL),
(2, 3, 6, NULL, NULL),
(3, 4, 6, NULL, NULL),
(4, 5, 6, NULL, NULL),
(5, 6, 6, NULL, NULL),
(6, 7, 6, NULL, NULL),
(7, 8, 6, NULL, NULL),
(8, 9, 6, NULL, NULL),
(9, 10, 6, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `contact_types`
--

CREATE TABLE `contact_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_types`
--

INSERT INTO `contact_types` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Permanent', ' ', ' ', '2024-12-12 06:53:13', '2024-12-12 06:53:13'),
(2, 'Probation Period', ' ', ' ', '2024-12-12 06:53:18', '2024-12-12 06:53:44'),
(3, 'Temporary', ' ', ' ', '2024-12-12 06:53:30', '2024-12-12 06:53:30');

-- --------------------------------------------------------

--
-- Table structure for table `contact_type_user`
--

CREATE TABLE `contact_type_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `contact_type_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_type_user`
--

INSERT INTO `contact_type_user` (`id`, `user_id`, `contact_type_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `departments`
--

CREATE TABLE `departments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `departments`
--

INSERT INTO `departments` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Creative Development', ' ', ' ', '2024-12-12 06:59:18', '2024-12-12 06:59:18'),
(2, 'HR', ' ', ' ', '2024-12-12 06:59:22', '2024-12-12 06:59:22'),
(3, 'Finance', ' ', ' ', '2024-12-12 06:59:28', '2024-12-12 06:59:28'),
(4, 'Admin', ' ', ' ', '2024-12-12 06:59:31', '2024-12-12 06:59:31');

-- --------------------------------------------------------

--
-- Table structure for table `department_schedule`
--

CREATE TABLE `department_schedule` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `department_id` bigint(20) UNSIGNED NOT NULL,
  `schedule_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `department_team`
--

CREATE TABLE `department_team` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `department_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `department_team`
--

INSERT INTO `department_team` (`id`, `department_id`, `team_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 1, 2, NULL, NULL),
(3, 1, 3, NULL, NULL),
(4, 1, 4, NULL, NULL),
(5, 1, 5, NULL, NULL),
(6, 1, 6, NULL, NULL),
(7, 1, 7, NULL, NULL),
(8, 2, 8, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `department_user`
--

CREATE TABLE `department_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `department_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `department_user`
--

INSERT INTO `department_user` (`id`, `user_id`, `department_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `designations`
--

CREATE TABLE `designations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `designations`
--

INSERT INTO `designations` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Creative Designer', ' ', ' ', '2024-12-12 06:57:30', '2024-12-12 06:57:30'),
(2, 'Creative Developer', ' ', ' ', '2024-12-12 06:57:36', '2024-12-12 06:57:36'),
(3, 'Web developer', ' ', ' ', '2024-12-12 06:57:52', '2024-12-12 06:57:52'),
(4, 'Creative Development Manager', ' ', ' ', '2024-12-12 06:58:02', '2024-12-12 06:58:02'),
(5, 'Operations Manager', ' ', ' ', '2024-12-12 06:58:11', '2024-12-12 06:58:11');

-- --------------------------------------------------------

--
-- Table structure for table `designation_user`
--

CREATE TABLE `designation_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `designation_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `designation_user`
--

INSERT INTO `designation_user` (`id`, `user_id`, `designation_id`, `created_at`, `updated_at`) VALUES
(1, 2, 5, NULL, NULL),
(2, 3, 4, NULL, NULL),
(3, 4, 4, NULL, NULL),
(4, 5, 4, NULL, NULL),
(5, 6, 3, NULL, NULL),
(6, 7, 2, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 4, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `holiday_calenders`
--

CREATE TABLE `holiday_calenders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `office_location` varchar(255) NOT NULL,
  `holiday_name` varchar(255) NOT NULL,
  `holiday_department` varchar(255) NOT NULL,
  `holiday_start_date` date NOT NULL,
  `holiday_end_date` date NOT NULL,
  `day_of_week` varchar(255) NOT NULL,
  `days` int(11) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `holiday_calenders`
--

INSERT INTO `holiday_calenders` (`id`, `office_location`, `holiday_name`, `holiday_department`, `holiday_start_date`, `holiday_end_date`, `day_of_week`, `days`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Bangladesh', 'Test', 'Creative Development', '2024-12-13', '2024-12-16', 'Friday, Monday', 2, 'Admin User', 'Admin User', '2024-12-12 07:42:28', '2024-12-12 07:42:28');

-- --------------------------------------------------------

--
-- Table structure for table `holiday_calender_user`
--

CREATE TABLE `holiday_calender_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `holiday_calender_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `locations`
--

CREATE TABLE `locations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `locations_name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `locations`
--

INSERT INTO `locations` (`id`, `locations_name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Bangladesh', ' ', ' ', '2024-12-12 06:54:37', '2024-12-12 06:54:37'),
(2, 'USA', ' ', ' ', '2024-12-12 06:54:42', '2024-12-12 06:54:42'),
(3, 'El Salvador', ' ', ' ', '2024-12-12 06:54:49', '2024-12-12 06:54:49');

-- --------------------------------------------------------

--
-- Table structure for table `member_statuses`
--

CREATE TABLE `member_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `member_statuses`
--

INSERT INTO `member_statuses` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Live', ' ', ' ', '2024-12-12 06:56:08', '2024-12-12 06:56:08'),
(2, 'Bench', ' ', ' ', '2024-12-12 06:56:18', '2024-12-12 06:56:18'),
(3, 'Trainee', ' ', ' ', '2024-12-12 06:56:22', '2024-12-12 06:56:22');

-- --------------------------------------------------------

--
-- Table structure for table `member_status_user`
--

CREATE TABLE `member_status_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `member_status_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `member_status_user`
--

INSERT INTO `member_status_user` (`id`, `user_id`, `member_status_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(201, '2014_10_12_000000_create_users_table', 1),
(202, '2014_10_12_100000_create_password_resets_table', 1),
(203, '2019_08_19_000000_create_failed_jobs_table', 1),
(204, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(205, '2024_10_08_151517_create_departments_table', 1),
(206, '2024_10_08_151529_create_teams_table', 1),
(207, '2024_10_08_151626_create_resource_statuses_table', 1),
(208, '2024_10_08_151646_create_billing_statuses_table', 1),
(209, '2024_10_08_151700_create_roles_table', 1),
(210, '2024_10_08_151751_create_department_user_table', 1),
(211, '2024_10_08_151805_create_team_user_table', 1),
(212, '2024_10_08_151846_create_resource_status_user_table', 1),
(213, '2024_10_08_151900_create_billing_status_user_table', 1),
(214, '2024_10_09_141642_create_resource_types_table', 1),
(215, '2024_10_14_091023_create_role_user_table', 1),
(216, '2024_10_15_115508_create_holiday_calenders_table', 1),
(217, '2024_10_15_124403_create_holiday_calender_user_table', 1),
(218, '2024_11_04_092237_create_quick_access_hubs_table', 1),
(219, '2024_11_04_102801_create_quick_access_hub_user_table', 1),
(220, '2024_11_06_091112_create_resource_type_user_table', 1),
(221, '2024_11_11_143931_create_locations_table', 1),
(222, '2024_11_12_125747_create_designations_table', 2),
(223, '2024_11_12_125813_create_designation_user_table', 2),
(224, '2024_11_18_110805_create_bloods_table', 2),
(225, '2024_11_18_110858_create_blood_user_table', 2),
(226, '2024_11_18_111003_create_onsite_statuses_table', 2),
(227, '2024_11_18_111029_create_onsite_status_user_table', 2),
(228, '2024_11_18_111201_create_member_statuses_table', 2),
(229, '2024_11_18_111300_create_contact_types_table', 2),
(230, '2024_11_18_111311_create_contact_type_user_table', 2),
(231, '2024_11_18_111450_create_available_statuses_table', 2),
(232, '2024_11_18_111511_create_available_status_user_table', 2),
(233, '2024_11_18_113733_create_member_status_user_table', 2),
(234, '2024_11_19_121753_create_trainings_table', 2),
(235, '2024_11_19_140854_create_training_user_table', 2),
(236, '2024_11_20_094149_create_branch_location_table', 2),
(237, '2024_11_20_134032_create_sessions_table', 3),
(238, '2024_11_21_103603_create_schedules_table', 4),
(239, '2024_11_25_150458_create_training_topics_table', 5),
(240, '2024_11_25_150532_create_training_categories_table', 5),
(241, '2024_11_27_114453_create_branch_user_table', 5),
(242, '2024_12_02_131810_create_department_team_table', 5),
(245, '2024_12_23_130828_create_product_types_table', 6),
(247, '2024_12_23_130854_create_record_types_table', 6),
(249, '2024_12_23_130942_create_sla_achieves_table', 6),
(250, '2024_12_23_104751_create_task_details_table', 7),
(251, '2024_12_24_105813_create_product_types_table', 8),
(253, '2024_12_30_105206_create_task_details_table', 9),
(254, '2024_12_31_092403_create_time_cards_table', 10),
(256, '2024_12_18_095331_create_revision_types_table', 12),
(257, '2024_12_18_103050_create_regions_table', 12),
(258, '2025_01_01_161323_create_priorities_table', 12),
(259, '2024_12_18_094721_create_task_types_table', 13),
(260, '2025_01_03_131829_create_reporters_table', 14),
(261, '2025_01_03_143646_create_sla_achieves_table', 15),
(262, '2025_01_03_152320_create_time_cards_table', 16),
(263, '2025_01_03_153627_create_time_cards_table', 17);

-- --------------------------------------------------------

--
-- Table structure for table `onsite_statuses`
--

CREATE TABLE `onsite_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `onsite_statuses`
--

INSERT INTO `onsite_statuses` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Home', ' ', ' ', '2024-12-12 06:55:48', '2024-12-12 06:55:48'),
(2, 'Office', ' ', ' ', '2024-12-12 06:55:51', '2024-12-12 06:55:51');

-- --------------------------------------------------------

--
-- Table structure for table `onsite_status_user`
--

CREATE TABLE `onsite_status_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `onsite_status_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `onsite_status_user`
--

INSERT INTO `onsite_status_user` (`id`, `user_id`, `onsite_status_id`, `created_at`, `updated_at`) VALUES
(1, 2, 2, NULL, NULL),
(2, 3, 2, NULL, NULL),
(3, 4, 2, NULL, NULL),
(4, 5, 2, NULL, NULL),
(5, 6, 2, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 2, NULL, NULL),
(8, 9, 2, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `personal_access_tokens`
--

INSERT INTO `personal_access_tokens` (`id`, `tokenable_type`, `tokenable_id`, `name`, `token`, `abilities`, `last_used_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\User', 1, 'api-token', '41136c3aac53fb6d9085bcc9eaba2c5e5e461d82322afdab3bd93ddb556c2a2c', '[\"*\"]', NULL, NULL, '2024-12-12 05:54:24', '2024-12-12 05:54:24'),
(2, 'App\\Models\\User', 1, 'api-token', 'e49074d83d5c596eecf514e372475b68a6fd2c644e3154b4f7ee46c6361f6979', '[\"*\"]', '2024-12-12 07:38:40', NULL, '2024-12-12 05:54:31', '2024-12-12 07:38:40'),
(3, 'App\\Models\\User', 1, 'api-token', '0aed40054530cc4b228a3cc4e8fd347b1ff038be4dde3a531a389fd6aa4022f6', '[\"*\"]', NULL, NULL, '2024-12-12 07:15:30', '2024-12-12 07:15:30'),
(4, 'App\\Models\\User', 1, 'api-token', '82aff0680efae646e40f47c5deb3af62ba243f5bd3879effd1e60dd4c3ec9e99', '[\"*\"]', NULL, NULL, '2024-12-12 07:16:05', '2024-12-12 07:16:05'),
(5, 'App\\Models\\User', 1, 'api-token', '80df40612b7fff1e8d97a20456e55800afc00bcff1729e9fccfc5e03d0e54a1c', '[\"*\"]', NULL, NULL, '2024-12-12 07:16:28', '2024-12-12 07:16:28'),
(6, 'App\\Models\\User', 1, 'api-token', 'aef7c72fadc84027ea44ad02268a0859f3338209fd3f531ca45fce08c5786afc', '[\"*\"]', NULL, NULL, '2024-12-12 07:16:54', '2024-12-12 07:16:54'),
(7, 'App\\Models\\User', 1, 'api-token', '7e6c300ff35dc1ecc8a8c9c8f629f682a4df819067d73a601e7f99d4efe4019d', '[\"*\"]', NULL, NULL, '2024-12-12 07:17:34', '2024-12-12 07:17:34'),
(8, 'App\\Models\\User', 1, 'api-token', 'b9ff082894f42e7e0461ade490f000d2cdd3e1d2991f14f0c9ed93c40f80a0b4', '[\"*\"]', NULL, NULL, '2024-12-12 07:18:01', '2024-12-12 07:18:01'),
(9, 'App\\Models\\User', 1, 'api-token', 'a421545ab9bea1b1b90c17902a48cc5e9640cb933f6d77e4340a89e1e556161e', '[\"*\"]', NULL, NULL, '2024-12-12 07:18:50', '2024-12-12 07:18:50'),
(10, 'App\\Models\\User', 1, 'api-token', 'b6b97f715d9422a6c73d9d990a6778d114a20bec04e457b8da9a81cb39641fe5', '[\"*\"]', NULL, NULL, '2024-12-12 07:19:21', '2024-12-12 07:19:21'),
(11, 'App\\Models\\User', 1, 'api-token', '1275c1ce36b0c1f3c204af2ab65e5af78d2a93044137a0642fc1a054ded7b869', '[\"*\"]', NULL, NULL, '2024-12-12 07:19:56', '2024-12-12 07:19:56'),
(12, 'App\\Models\\User', 1, 'api-token', '250c9102477302421aa665fd6eacd6c4980308dbb2fc20098cfc915eeb4b4d8d', '[\"*\"]', NULL, NULL, '2024-12-12 07:20:36', '2024-12-12 07:20:36'),
(13, 'App\\Models\\User', 1, 'api-token', '7f9d1ff41beff7e161dc0c015585d0501bc795d6b9eb02936d6bd22b9d79821e', '[\"*\"]', NULL, NULL, '2024-12-12 07:21:14', '2024-12-12 07:21:14'),
(14, 'App\\Models\\User', 1, 'api-token', '4780ad42fbdc88aaa5e132a6af1b84fe4f3cb63c09e1b7eb3a8d8eb93581cd12', '[\"*\"]', NULL, NULL, '2024-12-12 07:21:39', '2024-12-12 07:21:39'),
(15, 'App\\Models\\User', 1, 'api-token', '9cdc72988450bd1d64942b7ea24561da4a3a9c775c807b54ca65025caffa1110', '[\"*\"]', NULL, NULL, '2024-12-12 07:22:05', '2024-12-12 07:22:05'),
(16, 'App\\Models\\User', 1, 'api-token', 'b271b556c5e983a862cd935a8c682d58a89f6d3d1c0e5caade460edd61634761', '[\"*\"]', NULL, NULL, '2024-12-12 07:22:35', '2024-12-12 07:22:35'),
(17, 'App\\Models\\User', 1, 'api-token', '928879ada99dcf1a5b71c7eb585ad43caa064f88623b7a25244a9a2dabdf3a8e', '[\"*\"]', NULL, NULL, '2024-12-12 07:22:55', '2024-12-12 07:22:55'),
(18, 'App\\Models\\User', 1, 'api-token', 'f648df79b79423b59355941365787f5f7f1af9673003ca64027d738198203321', '[\"*\"]', NULL, NULL, '2024-12-12 07:23:22', '2024-12-12 07:23:22'),
(19, 'App\\Models\\User', 2, 'api-token', 'a40ec0db485d9971005e828be5b40e93d72a84b5d2f2325d231a973559814b58', '[\"*\"]', NULL, NULL, '2024-12-12 07:25:55', '2024-12-12 07:25:55'),
(20, 'App\\Models\\User', 3, 'api-token', '7f380b226abc611d53973031003f0daac305839f3288f0dcfa731a4960f61c02', '[\"*\"]', NULL, NULL, '2024-12-12 07:31:07', '2024-12-12 07:31:07'),
(21, 'App\\Models\\User', 4, 'api-token', '217c68a1ebb05a7b82df27bb0c45fbdafcb82b598678b4719c7ea0134786dabd', '[\"*\"]', NULL, NULL, '2024-12-12 07:33:42', '2024-12-12 07:33:42'),
(22, 'App\\Models\\User', 5, 'api-token', '5da3b6333ea88a31ceed5854e5802cddb8b9edd5dedecd91e4c6b1680a68fbcc', '[\"*\"]', NULL, NULL, '2024-12-12 07:34:46', '2024-12-12 07:34:46'),
(23, 'App\\Models\\User', 6, 'api-token', '4f7c4c7babb55b97716045b9b87be9f725bc61252c9eaecadbee2e81ed034686', '[\"*\"]', NULL, NULL, '2024-12-12 07:35:26', '2024-12-12 07:35:26'),
(24, 'App\\Models\\User', 7, 'api-token', 'eda28131a5bb6916affb1034fc06bfce34a2998bfa8634595d5e64122237dedf', '[\"*\"]', NULL, NULL, '2024-12-12 07:36:11', '2024-12-12 07:36:11'),
(25, 'App\\Models\\User', 8, 'api-token', 'fcd26758f491a1e4e443fdb4fd71b6fc66a37b67b52e0fe1b631acd3af1c6c48', '[\"*\"]', NULL, NULL, '2024-12-12 07:37:03', '2024-12-12 07:37:03'),
(26, 'App\\Models\\User', 9, 'api-token', '4fb2e54ddffc666a6327e2ecd245caf64dbd748d885780cef1c86a6da942295f', '[\"*\"]', NULL, NULL, '2024-12-12 07:37:47', '2024-12-12 07:37:47'),
(27, 'App\\Models\\User', 2, 'api-token', 'aa27cf24b871efca99f9b3d683544a7a9a2af87ce07a3d8a5975a438c2e26d76', '[\"*\"]', '2024-12-12 09:33:43', NULL, '2024-12-12 07:39:04', '2024-12-12 09:33:43'),
(28, 'App\\Models\\User', 2, 'api-token', '6e3c995dac1c95d23debd16ae62cffb45f132b5bd3f368ab4c9ed80f68164e7f', '[\"*\"]', '2024-12-24 10:10:08', NULL, '2024-12-12 09:48:20', '2024-12-24 10:10:08'),
(29, 'App\\Models\\User', 1, 'api-token', '7ddb3a8bb806293e88cd92ad99d9588a8e0523c4c99a1c0721ca1667676c8ace', '[\"*\"]', '2024-12-23 09:20:55', NULL, '2024-12-23 07:54:35', '2024-12-23 09:20:55'),
(30, 'App\\Models\\User', 1, 'api-token', '638c7fc62ecf789be205a0a5cd31f055bae3a38d647a1be3eec011f3c2ef893d', '[\"*\"]', '2024-12-24 10:36:24', NULL, '2024-12-24 04:48:57', '2024-12-24 10:36:24'),
(31, 'App\\Models\\User', 2, 'api-token', '76aa320ac7bb9baa84b112bb552135642739a532ca73c00c8f6e6ac153c3dfdd', '[\"*\"]', '2024-12-24 10:58:03', NULL, '2024-12-24 10:11:50', '2024-12-24 10:58:03'),
(32, 'App\\Models\\User', 2, 'api-token', 'fbe33e15992786045050b9d198296e1d86f1b7459993b81a630ae301996ab817', '[\"*\"]', '2024-12-26 09:34:21', NULL, '2024-12-24 10:59:19', '2024-12-26 09:34:21'),
(33, 'App\\Models\\User', 2, 'api-token', 'c3689875d536220e2f624ff1b6f28a9b4acc5524173a209c9491ce3ce09f4362', '[\"*\"]', '2024-12-26 09:40:55', NULL, '2024-12-26 09:34:29', '2024-12-26 09:40:55'),
(34, 'App\\Models\\User', 2, 'api-token', 'e1376d8f70eab4210fa507f73ba50e466925fa02e7757ad09d6f169b1288662e', '[\"*\"]', '2025-01-06 06:05:47', NULL, '2024-12-26 09:41:09', '2025-01-06 06:05:47'),
(35, 'App\\Models\\User', 1, 'api-token', 'e564e56b98f6140b03dc5e3a2a488296f4567cbcba55411c816e950d32428712', '[\"*\"]', '2024-12-27 10:44:27', NULL, '2024-12-27 08:39:46', '2024-12-27 10:44:27'),
(36, 'App\\Models\\User', 1, 'api-token', 'b6a61349e63684ed0395737e26a11530d212ef1b80c707b0efb11dc50cbbe8b6', '[\"*\"]', '2024-12-30 05:04:13', NULL, '2024-12-30 04:41:54', '2024-12-30 05:04:13'),
(37, 'App\\Models\\User', 10, 'api-token', '7bdbf91284512203171ae80329945bb5af3ac3fadbac8d42476d24a49d27f2e4', '[\"*\"]', NULL, NULL, '2024-12-31 03:56:02', '2024-12-31 03:56:02'),
(38, 'App\\Models\\User', 1, 'api-token', '6db77d4b8db0db559aa5b9eb6680704584b1b9f1bef8d147d9ffc43ae412c886', '[\"*\"]', '2024-12-31 06:44:34', NULL, '2024-12-31 04:42:51', '2024-12-31 06:44:34'),
(39, 'App\\Models\\User', 1, 'api-token', '4875ca29c6fc4fbbaf05d23fb7b6e18196815fbd32d585eb75b3ea72bb2ffa33', '[\"*\"]', '2025-01-03 07:36:11', NULL, '2025-01-03 07:33:09', '2025-01-03 07:36:11');

-- --------------------------------------------------------

--
-- Table structure for table `priorities`
--

CREATE TABLE `priorities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `priorities`
--

INSERT INTO `priorities` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Priority 1', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-02 10:17:24', '2025-01-02 10:17:24'),
(2, 'Priority 2', 'Creative Development', 'Boats Group', 'Admin User', 'Admin User', '2025-01-02 10:17:34', '2025-01-02 10:17:34');

-- --------------------------------------------------------

--
-- Table structure for table `product_types`
--

CREATE TABLE `product_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_types`
--

INSERT INTO `product_types` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'HTML5 + Video 2', 'Creative Development', 'Boats Group', ' ', 'Admin User', '2024-12-24 05:07:51', '2024-12-24 08:02:58'),
(2, 'Build From Site', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2024-12-24 06:36:52', '2024-12-24 06:36:52'),
(3, 'Build From Site2', 'HR', 'HR-Test', 'Admin User', 'Admin User', '2024-12-24 06:39:58', '2024-12-24 06:39:58'),
(4, 'HTML5 + Video 3', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2024-12-24 10:15:05', '2024-12-24 10:15:05'),
(5, 'Build From Site 2222', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-01 04:07:20', '2025-01-01 04:07:20');

-- --------------------------------------------------------

--
-- Table structure for table `quick_access_hubs`
--

CREATE TABLE `quick_access_hubs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `hubs_icon` varchar(255) NOT NULL,
  `hubs_image` varchar(255) NOT NULL,
  `hubs_title` varchar(255) NOT NULL,
  `hubs_details` varchar(255) NOT NULL,
  `hubs_url` varchar(255) NOT NULL,
  `hubs_cta` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `quick_access_hubs`
--

INSERT INTO `quick_access_hubs` (`id`, `hubs_icon`, `hubs_image`, `hubs_title`, `hubs_details`, `hubs_url`, `hubs_cta`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'flag', 'test', 'test', 'test', 'test.com', 'test', 'Admin User', 'Admin User', '2024-12-12 07:43:14', '2024-12-12 07:43:14');

-- --------------------------------------------------------

--
-- Table structure for table `quick_access_hub_user`
--

CREATE TABLE `quick_access_hub_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `hubs_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `record_types`
--

CREATE TABLE `record_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `record_types`
--

INSERT INTO `record_types` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Work', '', NULL, NULL, NULL),
(2, 'QA', '', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `regions`
--

CREATE TABLE `regions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `regions`
--

INSERT INTO `regions` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'APAC', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-02 10:16:42', '2025-01-02 10:16:42'),
(2, 'EMEA', 'Creative Development', 'Boats Group', 'Admin User', 'Admin User', '2025-01-02 10:16:53', '2025-01-02 10:16:53');

-- --------------------------------------------------------

--
-- Table structure for table `reporters`
--

CREATE TABLE `reporters` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `reporters`
--

INSERT INTO `reporters` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Test Reporter', 'Creative Development', 'Bloomberg', ' ', ' ', '2025-01-03 07:35:54', '2025-01-03 07:35:54');

-- --------------------------------------------------------

--
-- Table structure for table `resource_statuses`
--

CREATE TABLE `resource_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `resource_statuses`
--

INSERT INTO `resource_statuses` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Active', ' ', ' ', '2024-12-12 06:51:59', '2024-12-12 06:51:59'),
(2, 'Inactive', ' ', ' ', '2024-12-12 06:52:04', '2024-12-12 06:52:04'),
(3, 'Pending', ' ', ' ', '2024-12-12 06:52:08', '2024-12-12 06:52:08'),
(4, 'Terminated', ' ', ' ', '2024-12-12 06:52:26', '2024-12-12 06:52:26'),
(5, 'Resigned', ' ', ' ', '2024-12-12 06:52:32', '2024-12-12 06:52:32');

-- --------------------------------------------------------

--
-- Table structure for table `resource_status_user`
--

CREATE TABLE `resource_status_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `resource_status_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `resource_status_user`
--

INSERT INTO `resource_status_user` (`id`, `user_id`, `resource_status_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 1, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 1, NULL, NULL),
(5, 6, 1, NULL, NULL),
(6, 7, 1, NULL, NULL),
(7, 8, 1, NULL, NULL),
(8, 9, 1, NULL, NULL),
(9, 10, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `resource_types`
--

CREATE TABLE `resource_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `resource_types`
--

INSERT INTO `resource_types` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'HOD', ' ', ' ', '2024-12-12 06:56:57', '2024-12-12 06:56:57'),
(2, 'Manager', ' ', ' ', '2024-12-12 07:01:04', '2024-12-12 07:01:04'),
(3, 'Team Lead', ' ', ' ', '2024-12-12 07:01:09', '2024-12-12 07:01:09'),
(4, 'Coordinator', ' ', ' ', '2024-12-12 07:01:21', '2024-12-12 07:01:21'),
(5, 'Designer', ' ', ' ', '2024-12-12 07:01:26', '2024-12-12 07:01:26'),
(6, 'Developer', ' ', ' ', '2024-12-12 07:01:30', '2024-12-12 07:01:30'),
(7, 'QA', ' ', ' ', '2024-12-12 07:01:40', '2024-12-12 07:01:40'),
(8, 'Guest', ' ', ' ', '2024-12-12 07:01:43', '2024-12-12 07:01:43');

-- --------------------------------------------------------

--
-- Table structure for table `resource_type_user`
--

CREATE TABLE `resource_type_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `resource_type_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `resource_type_user`
--

INSERT INTO `resource_type_user` (`id`, `user_id`, `resource_type_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 2, NULL, NULL),
(3, 4, 1, NULL, NULL),
(4, 5, 2, NULL, NULL),
(5, 6, 3, NULL, NULL),
(6, 7, 4, NULL, NULL),
(7, 8, 4, NULL, NULL),
(8, 9, 5, NULL, NULL),
(9, 10, 6, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `revision_types`
--

CREATE TABLE `revision_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `revision_types`
--

INSERT INTO `revision_types` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Layout Update', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-02 10:15:30', '2025-01-02 10:15:30'),
(2, 'Image Manipulation', 'Creative Development', 'Boats Group', 'Admin User', 'Admin User', '2025-01-02 10:15:55', '2025-01-02 10:15:55');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'super-admin', '', NULL, NULL, NULL),
(2, 'admin', '', NULL, NULL, NULL),
(3, 'hod', '', NULL, NULL, NULL),
(4, 'Manager', '', NULL, NULL, NULL),
(5, 'team-lead', '', NULL, NULL, NULL),
(6, 'coordinator', '', NULL, NULL, NULL),
(7, 'shift-lead', '', NULL, NULL, NULL),
(8, 'team-member', '', NULL, NULL, NULL),
(9, 'guest', '', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `role_user`
--

CREATE TABLE `role_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_user`
--

INSERT INTO `role_user` (`id`, `user_id`, `role_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 2, 1, NULL, NULL),
(3, 3, 2, NULL, NULL),
(4, 4, 3, NULL, NULL),
(5, 5, 4, NULL, NULL),
(6, 6, 5, NULL, NULL),
(7, 7, 6, NULL, NULL),
(8, 8, 7, NULL, NULL),
(9, 9, 8, NULL, NULL),
(10, 10, 5, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `schedules`
--

CREATE TABLE `schedules` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `shift_name` varchar(255) NOT NULL,
  `shift_start` time NOT NULL,
  `shift_end` time NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `schedules`
--

INSERT INTO `schedules` (`id`, `shift_name`, `shift_start`, `shift_end`, `created_at`, `updated_at`) VALUES
(1, 'Morning Shift', '08:00:00', '17:00:00', '2024-12-12 07:15:30', '2024-12-12 07:15:30'),
(2, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:16:05', '2024-12-12 07:16:05'),
(3, 'Morning Shift', '08:00:00', '17:00:00', '2024-12-12 07:16:28', '2024-12-12 07:16:28'),
(4, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:16:54', '2024-12-12 07:16:54'),
(5, 'Morning Shift', '08:00:00', '17:00:00', '2024-12-12 07:17:34', '2024-12-12 07:17:34'),
(6, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:18:00', '2024-12-12 07:18:00'),
(7, 'Custom', '19:00:00', '04:00:00', '2024-12-12 07:18:50', '2024-12-12 07:18:50'),
(8, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:19:21', '2024-12-12 07:19:21'),
(9, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:19:56', '2024-12-12 07:19:56'),
(10, 'Night Shift', '22:00:00', '07:00:00', '2024-12-12 07:20:36', '2024-12-12 07:20:36'),
(11, 'Night Shift', '22:00:00', '07:00:00', '2024-12-12 07:21:13', '2024-12-12 07:21:13'),
(12, 'Night Shift', '22:00:00', '07:59:00', '2024-12-12 07:21:39', '2024-12-12 07:21:39'),
(13, 'Night Shift', '22:00:00', '07:00:00', '2024-12-12 07:22:05', '2024-12-12 07:22:05'),
(14, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:22:34', '2024-12-12 07:22:34'),
(15, 'Night Shift', '22:00:00', '07:00:00', '2024-12-12 07:22:55', '2024-12-12 07:22:55'),
(16, 'Evening Shift', '14:00:00', '23:00:00', '2024-12-12 07:23:22', '2024-12-12 07:23:22');

-- --------------------------------------------------------

--
-- Table structure for table `schedule_team`
--

CREATE TABLE `schedule_team` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `schedule_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `schedule_team`
--

INSERT INTO `schedule_team` (`id`, `schedule_id`, `team_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 2, 1, NULL, NULL),
(3, 3, 2, NULL, NULL),
(4, 4, 2, NULL, NULL),
(5, 5, 3, NULL, NULL),
(6, 6, 3, NULL, NULL),
(7, 7, 4, NULL, NULL),
(8, 8, 4, NULL, NULL),
(9, 9, 5, NULL, NULL),
(10, 10, 5, NULL, NULL),
(11, 11, 1, NULL, NULL),
(12, 12, 2, NULL, NULL),
(13, 13, 3, NULL, NULL),
(14, 14, 6, NULL, NULL),
(15, 15, 6, NULL, NULL),
(16, 16, 7, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `schedule_user`
--

CREATE TABLE `schedule_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `schedule_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `schedule_user`
--

INSERT INTO `schedule_user` (`id`, `schedule_id`, `user_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, NULL),
(2, 2, 1, NULL, NULL),
(3, 3, 1, NULL, NULL),
(4, 4, 1, NULL, NULL),
(5, 5, 1, NULL, NULL),
(6, 6, 1, NULL, NULL),
(7, 7, 1, NULL, NULL),
(8, 8, 1, NULL, NULL),
(9, 9, 1, NULL, NULL),
(10, 10, 1, NULL, NULL),
(11, 11, 1, NULL, NULL),
(12, 12, 1, NULL, NULL),
(13, 13, 1, NULL, NULL),
(14, 14, 1, NULL, NULL),
(15, 15, 1, NULL, NULL),
(16, 16, 1, NULL, NULL),
(17, 2, 2, NULL, NULL),
(18, 4, 3, NULL, NULL),
(19, 8, 4, NULL, NULL),
(20, 9, 5, NULL, NULL),
(21, 14, 6, NULL, NULL),
(22, 14, 7, NULL, NULL),
(23, 9, 8, NULL, NULL),
(24, 16, 9, NULL, NULL),
(25, 7, 10, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sla_achieves`
--

CREATE TABLE `sla_achieves` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sla_achieves`
--

INSERT INTO `sla_achieves` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Yes', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-03 08:37:46', '2025-01-03 08:37:46'),
(2, 'No', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-03 08:37:53', '2025-01-03 08:37:53');

-- --------------------------------------------------------

--
-- Table structure for table `task_details`
--

CREATE TABLE `task_details` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ticket_number` varchar(255) NOT NULL,
  `month` date NOT NULL,
  `week` date NOT NULL,
  `received_date` date NOT NULL,
  `due_date` date NOT NULL,
  `unit` int(11) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `campaign_name` varchar(255) NOT NULL,
  `notes` text NOT NULL,
  `department` varchar(255) DEFAULT NULL,
  `team` varchar(255) DEFAULT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `priority_id` bigint(20) UNSIGNED DEFAULT NULL,
  `task_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `revision_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `product_type_id` bigint(20) UNSIGNED NOT NULL,
  `record_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `region_id` bigint(20) UNSIGNED DEFAULT NULL,
  `sla_achieve_id` bigint(20) UNSIGNED DEFAULT NULL,
  `reporter_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `task_details`
--

INSERT INTO `task_details` (`id`, `ticket_number`, `month`, `week`, `received_date`, `due_date`, `unit`, `account_name`, `campaign_name`, `notes`, `department`, `team`, `created_by`, `updated_by`, `priority_id`, `task_type_id`, `revision_type_id`, `product_type_id`, `record_type_id`, `region_id`, `sla_achieve_id`, `reporter_id`, `created_at`, `updated_at`) VALUES
(1, 'DD-2020', '2024-12-27', '2024-12-27', '2024-12-27', '2024-12-31', 20, 'BMW', 'Winter BMW', 'Test 2', 'Test department', 'Test team', ' ', ' ', NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, '2024-12-30 05:03:43', '2024-12-30 05:04:13'),
(2, 'DD-2021', '2024-12-30', '2024-12-30', '2024-12-30', '2024-12-31', 2, 'BMW', 'Winter BMW', 'Test', NULL, NULL, 'Admin User', 'Admin User', NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, '2024-12-30 05:44:22', '2024-12-30 05:44:22'),
(3, 'DD-2022', '2024-12-30', '2024-12-30', '2024-12-30', '2024-12-31', 2, 'BMW2', 'Winter BMW2', 'Test', NULL, NULL, 'Admin User', 'Admin User', NULL, NULL, NULL, 4, NULL, NULL, NULL, NULL, '2024-12-30 05:51:35', '2024-12-30 05:51:35'),
(4, 'DD-2023', '2024-12-30', '2024-12-30', '2024-12-30', '2025-01-01', 2, 'BMW', 'Winter BMW', 'test for boats group', 'Creative Development', 'Boats Group', 'Admin User', 'Admin User', NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, '2024-12-30 05:57:13', '2024-12-30 09:50:17'),
(5, 'DD-2024', '2024-12-30', '2024-12-30', '2024-12-30', '2024-12-31', 2, 'BMW', 'Winter BMW', 'Test for Bloomberg updated', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, '2024-12-30 09:54:28', '2024-12-30 10:18:17'),
(6, 'DD-2026', '2025-01-03', '2025-01-03', '2025-01-03', '2025-01-04', 3, 'Account DD-26', 'Campaign DD-26', 'Notes for DD-26', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', NULL, NULL, NULL, 4, NULL, NULL, NULL, NULL, '2025-01-03 04:22:53', '2025-01-03 04:22:53'),
(7, 'DD-2027', '2025-01-03', '2025-01-03', '2025-01-03', '2025-01-07', 4, 'Account DD-27', 'Campaign DD-27', 'DD-27', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', NULL, 3, NULL, 5, NULL, NULL, NULL, NULL, '2025-01-03 05:11:55', '2025-01-03 05:11:55'),
(8, 'DD-2028', '2025-01-03', '2025-01-03', '2025-01-03', '2025-01-10', 2, 'Account DD-28', 'Campaign DD-28', 'DD-28', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', NULL, 3, NULL, 2, NULL, 1, NULL, 1, '2025-01-03 09:04:52', '2025-01-03 09:04:52');

-- --------------------------------------------------------

--
-- Table structure for table `task_types`
--

CREATE TABLE `task_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `department` varchar(255) NOT NULL,
  `team` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `task_types`
--

INSERT INTO `task_types` (`id`, `name`, `department`, `team`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Initial Build', 'Creative Development', 'Clipcentric', 'Admin User', 'Admin User', '2025-01-02 10:10:59', '2025-01-02 10:10:59'),
(2, 'Revision', 'Creative Development', 'Clipcentric', 'Admin User', 'Admin User', '2025-01-02 10:11:14', '2025-01-02 10:11:14'),
(3, 'Task Management', 'Creative Development', 'Bloomberg', 'Admin User', 'Admin User', '2025-01-02 10:14:16', '2025-01-02 10:14:16'),
(4, 'Training', 'Creative Development', 'Boats Group', 'Admin User', 'Admin User', '2025-01-02 10:14:29', '2025-01-02 10:14:29');

-- --------------------------------------------------------

--
-- Table structure for table `teams`
--

CREATE TABLE `teams` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `icon` varchar(255) NOT NULL,
  `logo` varchar(255) NOT NULL,
  `poc` varchar(255) NOT NULL,
  `manager` varchar(255) NOT NULL,
  `team_lead` varchar(255) NOT NULL,
  `launch` date NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `teams`
--

INSERT INTO `teams` (`id`, `name`, `icon`, `logo`, `poc`, `manager`, `team_lead`, `launch`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Bloomberg', 'images/icon-6772779b9ab12.png', 'images/logo-6772779b9a9bc.png', 'Admin User', 'Admin User', 'Admin User', '2024-12-15', 'Admin User', 'Admin User', '2024-12-12 07:05:04', '2024-12-30 04:36:11'),
(2, 'Clipcentric', 'images/R3OnwAwMMeduhuQrDbfrh8mf4UrcAh0tA70uoaPL.jpg', 'images/dzaFnC7jfIlB9vTggT7fS2UPFA9x480KWU2BkZ0K.webp', 'Admin User', 'Admin User', 'Admin User', '2024-12-31', 'Admin User', 'Admin User', '2024-12-12 07:06:32', '2024-12-12 07:06:32'),
(3, 'Accuweather-', 'images/Oy9lp***********************************.png', 'images/FE9dOGEnYkocJcUc37yMAYZZp1BthGj8eQ4w3J29.png', 'Admin User', 'Admin User', 'Admin User', '2025-01-01', 'Admin User', 'Admin User', '2024-12-12 07:08:03', '2024-12-12 07:08:03'),
(4, 'Bigtincan', 'images/icon-67727757955e0.png', 'images/logo-67727757954bf.png', 'Admin User', 'Admin User', 'Admin User', '2025-01-02', 'Admin User', 'Admin User', '2024-12-12 07:09:13', '2024-12-30 04:35:03'),
(5, 'Boats Group', 'images/lQMsOrGuBAb6MjwufdfpqUgCqHTTrMWjeWiui2zI.jpg', 'images/GQgydG4Lj4GXnELESWG1t5K9DyhcKGeEHpjA3f8S.jpg', 'Admin User', 'Admin User', 'Admin User', '2024-12-13', 'Admin User', 'Admin User', '2024-12-12 07:10:24', '2024-12-12 07:10:24'),
(6, 'Multiview', 'images/qxRYzmzIzMuMte1dtyVsDiIGZgFmjw40sb6ef90x.jpg', 'images/TMZCjnJqAgXgiQwA7bgZt2x5XLcdgQEm0kMPnzvt.webp', 'Admin User', 'Admin User', 'Admin User', '2024-12-20', 'Admin User', 'Admin User', '2024-12-12 07:11:05', '2024-12-12 07:11:05'),
(7, 'Creative Reserve', 'images/VBsjDAOtCJ61erHF3PLQ8NL3GVYKNjNbkcOsbN7S.jpg', 'images/zhVVzsdT6uYsCl4AkT6x0CRSguDFG5Oep8kqyjHr.webp', 'Admin User', 'Admin User', 'Admin User', '2024-12-20', 'Admin User', 'Admin User', '2024-12-12 07:14:00', '2024-12-12 07:14:00'),
(8, 'HR-Test', 'images/rFJMthh8IpLomc62zRmRfkNVNkBuco26NInLr5DC.png', 'images/LeRw0iZNhoVNE36JuK4syXEDRLafTooOWYP3FNU7.png', 'Admin User', 'Admin User', 'Admin User', '2024-12-25', 'Admin User', 'Admin User', '2024-12-23 04:37:03', '2024-12-23 04:37:03');

-- --------------------------------------------------------

--
-- Table structure for table `team_user`
--

CREATE TABLE `team_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `team_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `team_user`
--

INSERT INTO `team_user` (`id`, `user_id`, `team_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 3, 2, NULL, NULL),
(3, 4, 4, NULL, NULL),
(4, 5, 5, NULL, NULL),
(5, 6, 6, NULL, NULL),
(6, 7, 6, NULL, NULL),
(7, 8, 5, NULL, NULL),
(8, 9, 7, NULL, NULL),
(9, 10, 4, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `time_cards`
--

CREATE TABLE `time_cards` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `team` int(11) NOT NULL,
  `date` date NOT NULL,
  `shift` int(11) NOT NULL,
  `user` int(11) NOT NULL,
  `ticket` varchar(255) NOT NULL,
  `product_type` int(11) DEFAULT NULL,
  `task_type` int(11) DEFAULT NULL,
  `record_type` int(11) DEFAULT NULL,
  `revision_type` int(11) DEFAULT NULL,
  `priority` int(11) DEFAULT NULL,
  `unit` int(11) NOT NULL,
  `hour` time NOT NULL,
  `reporter` int(11) DEFAULT NULL,
  `region` int(11) DEFAULT NULL,
  `account` varchar(255) NOT NULL,
  `campaign` varchar(255) NOT NULL,
  `sla` varchar(255) DEFAULT NULL,
  `client_error` int(11) NOT NULL,
  `notes` text NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `time_cards`
--

INSERT INTO `time_cards` (`id`, `team`, `date`, `shift`, `user`, `ticket`, `product_type`, `task_type`, `record_type`, `revision_type`, `priority`, `unit`, `hour`, `reporter`, `region`, `account`, `campaign`, `sla`, `client_error`, `notes`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 1, '2025-01-06', 1, 2, 'DD-2028', 2, 3, NULL, NULL, NULL, 2, '04:00:00', 1, 1, 'Account DD-28', 'Campaign DD-28', NULL, 0, 'Test', 'Admin User', 'Admin User', '2025-01-06 03:26:13', '2025-01-06 03:26:13');

-- --------------------------------------------------------

--
-- Table structure for table `trainings`
--

CREATE TABLE `trainings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `training_sl` int(11) NOT NULL,
  `training_department` varchar(255) NOT NULL,
  `training_title` varchar(255) NOT NULL,
  `training_arrange` varchar(255) NOT NULL,
  `training_category` varchar(255) NOT NULL,
  `training_topic` varchar(255) NOT NULL,
  `training_date` date NOT NULL,
  `training_time` time NOT NULL,
  `training_duration` varchar(255) NOT NULL,
  `training_presentation_url` varchar(255) NOT NULL,
  `training_record_url` varchar(255) NOT NULL,
  `training_access_passcode` varchar(255) NOT NULL,
  `training_location` varchar(255) NOT NULL,
  `training_tags` varchar(255) NOT NULL,
  `training_evaluation_form` varchar(255) NOT NULL,
  `training_response` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `training_categories`
--

CREATE TABLE `training_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `training_topics`
--

CREATE TABLE `training_topics` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `training_training_category`
--

CREATE TABLE `training_training_category` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `training_id` bigint(20) UNSIGNED NOT NULL,
  `training_category_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `training_user`
--

CREATE TABLE `training_user` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `training_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `eid` bigint(20) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `fname` varchar(255) DEFAULT NULL,
  `lname` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `about` text DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `birthday_celebration` varchar(255) DEFAULT NULL,
  `birthday_celebration_date` date DEFAULT NULL,
  `gender` varchar(255) DEFAULT NULL,
  `marital_status` varchar(255) DEFAULT NULL,
  `nick_name` varchar(255) DEFAULT NULL,
  `primary_contact` varchar(255) DEFAULT NULL,
  `secondary_contact` varchar(255) DEFAULT NULL,
  `emergency_contact` varchar(255) DEFAULT NULL,
  `relation_contact` varchar(255) DEFAULT NULL,
  `present_address` varchar(255) DEFAULT NULL,
  `permanent_address` varchar(255) DEFAULT NULL,
  `blood_donate` varchar(255) DEFAULT NULL,
  `prev_designation` varchar(255) DEFAULT NULL,
  `report_to` varchar(255) DEFAULT NULL,
  `desk_id` varchar(255) DEFAULT NULL,
  `joining_date` date DEFAULT NULL,
  `termination_date` date DEFAULT NULL,
  `employment_end` date DEFAULT NULL,
  `work_anniversary` date DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `eid`, `photo`, `fname`, `lname`, `email`, `about`, `birthday`, `birthday_celebration`, `birthday_celebration_date`, `gender`, `marital_status`, `nick_name`, `primary_contact`, `secondary_contact`, `emergency_contact`, `relation_contact`, `present_address`, `permanent_address`, `blood_donate`, `prev_designation`, `report_to`, `desk_id`, `joining_date`, `termination_date`, `employment_end`, `work_anniversary`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 4444, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$byV44UylN/nDH2k1nDg/g.prjrVQBZPJ5YIUxUD/vgSBw6pONhFgG', NULL, '2024-12-12 05:54:23', '2024-12-12 07:38:40', NULL, NULL),
(2, 1001, NULL, 'Admin', 'User', '<EMAIL>', 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s,', '1990-11-21', 'No', '2024-12-27', 'male', 'married', 'Admin', '01630608854', '01923539156', '01795187572', 'Mother', 'Test address, Dhaka-1212', 'Test address, Dhaka-1207', 'Yes', 'Creative Developer', NULL, '7255', '2019-12-27', NULL, NULL, '2019-12-27', NULL, '$2y$10$mlMjVkscuJLB6hmlSm3KuuNdQw.k6dQccNpc1XaDYPflBfjShbUbi', NULL, '2024-12-12 07:25:54', '2024-12-12 07:41:46', NULL, NULL),
(3, 1002, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$xhEpHB2AC4D8rq1/IW1N5./.pV2QEe92XEvd3BaF708QjLZUwFOu.', NULL, '2024-12-12 07:31:05', '2024-12-12 07:31:05', NULL, NULL),
(4, 1003, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$uS1xjbmf9XQDCIIXW9JZBO5rZPVWEnKL31My/WcNcOytySpiMe09q', NULL, '2024-12-12 07:33:41', '2024-12-12 07:33:41', NULL, NULL),
(5, 1004, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$HTGJ.P3VoM8Qi2aUirME8Ovn/MT3WdvA.q805zJJE79he8/DJQr1q', NULL, '2024-12-12 07:34:44', '2024-12-12 07:34:44', NULL, NULL),
(6, 1005, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$QNVBbP5HdWXcIGDxFc0A0uEzc8ccjYnAnAjbi/zFCVdLY9cVA8Jni', NULL, '2024-12-12 07:35:25', '2024-12-12 07:35:25', NULL, NULL),
(7, 1006, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$FtWFT8dOTfbcxVoLh5VnDue3GLLok0c/2LmPK2DKTVTYCCHDOPwhG', NULL, '2024-12-12 07:36:10', '2024-12-12 07:36:10', NULL, NULL),
(8, 1007, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$8o.P273ERLVVuTEuOZf9M.qCi1SthKs6NJX7vyEDVxqK2Qcg20j2y', NULL, '2024-12-12 07:37:02', '2024-12-12 07:37:02', NULL, NULL),
(9, 1008, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$TeOs1xyCFnSnnisBP0ovPO/XjqDPLmaUP/uiq5pvn2U0Qw.RRIDAC', NULL, '2024-12-12 07:37:45', '2024-12-12 07:37:45', NULL, NULL),
(10, 5555, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '$2y$10$wOeo37MjzKgh3P8LWuL/fuvtqHe2CEd6k4rHaKCrp.IhzOdGHFNd6', NULL, '2024-12-31 03:56:02', '2024-12-31 03:56:02', NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `available_statuses`
--
ALTER TABLE `available_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `available_statuses_name_unique` (`name`);

--
-- Indexes for table `available_status_user`
--
ALTER TABLE `available_status_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `available_status_user_user_id_available_status_id_unique` (`user_id`,`available_status_id`),
  ADD KEY `available_status_user_available_status_id_foreign` (`available_status_id`);

--
-- Indexes for table `billing_statuses`
--
ALTER TABLE `billing_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `billing_statuses_name_unique` (`name`);

--
-- Indexes for table `billing_status_user`
--
ALTER TABLE `billing_status_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `billing_status_user_user_id_billing_status_id_unique` (`user_id`,`billing_status_id`),
  ADD KEY `billing_status_user_billing_status_id_foreign` (`billing_status_id`);

--
-- Indexes for table `bloods`
--
ALTER TABLE `bloods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `bloods_name_unique` (`name`);

--
-- Indexes for table `blood_user`
--
ALTER TABLE `blood_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `blood_user_user_id_blood_id_unique` (`user_id`,`blood_id`),
  ADD KEY `blood_user_blood_id_foreign` (`blood_id`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `branches_name_unique` (`name`);

--
-- Indexes for table `branch_location`
--
ALTER TABLE `branch_location`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `branch_location_branch_id_location_id_unique` (`branch_id`,`location_id`),
  ADD KEY `branch_location_location_id_foreign` (`location_id`);

--
-- Indexes for table `branch_user`
--
ALTER TABLE `branch_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `branch_user_user_id_branch_id_unique` (`user_id`,`branch_id`),
  ADD KEY `branch_user_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `contact_types`
--
ALTER TABLE `contact_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `contact_types_name_unique` (`name`);

--
-- Indexes for table `contact_type_user`
--
ALTER TABLE `contact_type_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `contact_type_user_user_id_contact_type_id_unique` (`user_id`,`contact_type_id`),
  ADD KEY `contact_type_user_contact_type_id_foreign` (`contact_type_id`);

--
-- Indexes for table `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `departments_name_unique` (`name`);

--
-- Indexes for table `department_schedule`
--
ALTER TABLE `department_schedule`
  ADD PRIMARY KEY (`id`),
  ADD KEY `department_schedule_department_id_foreign` (`department_id`);

--
-- Indexes for table `department_team`
--
ALTER TABLE `department_team`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `department_team_department_id_team_id_unique` (`department_id`,`team_id`),
  ADD KEY `department_team_team_id_foreign` (`team_id`);

--
-- Indexes for table `department_user`
--
ALTER TABLE `department_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `department_user_user_id_department_id_unique` (`user_id`,`department_id`),
  ADD KEY `department_user_department_id_foreign` (`department_id`);

--
-- Indexes for table `designations`
--
ALTER TABLE `designations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `designations_name_unique` (`name`);

--
-- Indexes for table `designation_user`
--
ALTER TABLE `designation_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `designation_user_user_id_designation_id_unique` (`user_id`,`designation_id`),
  ADD KEY `designation_user_designation_id_foreign` (`designation_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `holiday_calenders`
--
ALTER TABLE `holiday_calenders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `holiday_calender_user`
--
ALTER TABLE `holiday_calender_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `holiday_calender_user_user_id_holiday_calender_id_unique` (`user_id`,`holiday_calender_id`),
  ADD KEY `holiday_calender_user_holiday_calender_id_foreign` (`holiday_calender_id`);

--
-- Indexes for table `locations`
--
ALTER TABLE `locations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `locations_locations_name_unique` (`locations_name`);

--
-- Indexes for table `member_statuses`
--
ALTER TABLE `member_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `member_statuses_name_unique` (`name`);

--
-- Indexes for table `member_status_user`
--
ALTER TABLE `member_status_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `member_status_user_user_id_member_status_id_unique` (`user_id`,`member_status_id`),
  ADD KEY `member_status_user_member_status_id_foreign` (`member_status_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `onsite_statuses`
--
ALTER TABLE `onsite_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `onsite_statuses_name_unique` (`name`);

--
-- Indexes for table `onsite_status_user`
--
ALTER TABLE `onsite_status_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `onsite_status_user_user_id_onsite_status_id_unique` (`user_id`,`onsite_status_id`),
  ADD KEY `onsite_status_user_onsite_status_id_foreign` (`onsite_status_id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `priorities`
--
ALTER TABLE `priorities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `priorities_name_unique` (`name`);

--
-- Indexes for table `product_types`
--
ALTER TABLE `product_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `product_types_name_unique` (`name`);

--
-- Indexes for table `quick_access_hubs`
--
ALTER TABLE `quick_access_hubs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `quick_access_hub_user`
--
ALTER TABLE `quick_access_hub_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `quick_access_hub_user_user_id_hubs_id_unique` (`user_id`,`hubs_id`),
  ADD KEY `quick_access_hub_user_hubs_id_foreign` (`hubs_id`);

--
-- Indexes for table `record_types`
--
ALTER TABLE `record_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `record_types_name_unique` (`name`);

--
-- Indexes for table `regions`
--
ALTER TABLE `regions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `regions_name_unique` (`name`);

--
-- Indexes for table `reporters`
--
ALTER TABLE `reporters`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `resource_statuses`
--
ALTER TABLE `resource_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resource_statuses_name_unique` (`name`);

--
-- Indexes for table `resource_status_user`
--
ALTER TABLE `resource_status_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resource_status_user_user_id_resource_status_id_unique` (`user_id`,`resource_status_id`),
  ADD KEY `resource_status_user_resource_status_id_foreign` (`resource_status_id`);

--
-- Indexes for table `resource_types`
--
ALTER TABLE `resource_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resource_types_name_unique` (`name`);

--
-- Indexes for table `resource_type_user`
--
ALTER TABLE `resource_type_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resource_type_user_user_id_resource_type_id_unique` (`user_id`,`resource_type_id`),
  ADD KEY `resource_type_user_resource_type_id_foreign` (`resource_type_id`);

--
-- Indexes for table `revision_types`
--
ALTER TABLE `revision_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `revision_types_name_unique` (`name`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `role_user`
--
ALTER TABLE `role_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `role_user_user_id_role_id_unique` (`user_id`,`role_id`),
  ADD KEY `role_user_role_id_foreign` (`role_id`);

--
-- Indexes for table `schedules`
--
ALTER TABLE `schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `schedule_team`
--
ALTER TABLE `schedule_team`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `schedule_user`
--
ALTER TABLE `schedule_user`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `sla_achieves`
--
ALTER TABLE `sla_achieves`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `task_details`
--
ALTER TABLE `task_details`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_details_ticket_number_unique` (`ticket_number`),
  ADD KEY `task_details_priority_id_foreign` (`priority_id`),
  ADD KEY `task_details_task_type_id_foreign` (`task_type_id`),
  ADD KEY `task_details_revision_type_id_foreign` (`revision_type_id`),
  ADD KEY `task_details_product_type_id_foreign` (`product_type_id`),
  ADD KEY `task_details_record_type_id_foreign` (`record_type_id`),
  ADD KEY `task_details_region_id_foreign` (`region_id`),
  ADD KEY `task_details_sla_achieve_id_foreign` (`sla_achieve_id`);

--
-- Indexes for table `task_types`
--
ALTER TABLE `task_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `task_types_name_unique` (`name`);

--
-- Indexes for table `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `teams_name_unique` (`name`);

--
-- Indexes for table `team_user`
--
ALTER TABLE `team_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_user_user_id_team_id_unique` (`user_id`,`team_id`),
  ADD KEY `team_user_team_id_foreign` (`team_id`);

--
-- Indexes for table `time_cards`
--
ALTER TABLE `time_cards`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `trainings`
--
ALTER TABLE `trainings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `trainings_training_title_unique` (`training_title`);

--
-- Indexes for table `training_categories`
--
ALTER TABLE `training_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `training_categories_name_unique` (`name`);

--
-- Indexes for table `training_topics`
--
ALTER TABLE `training_topics`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `training_topics_name_unique` (`name`);

--
-- Indexes for table `training_training_category`
--
ALTER TABLE `training_training_category`
  ADD PRIMARY KEY (`id`),
  ADD KEY `training_training_category_training_id_foreign` (`training_id`),
  ADD KEY `training_training_category_training_category_id_foreign` (`training_category_id`);

--
-- Indexes for table `training_user`
--
ALTER TABLE `training_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `training_user_user_id_training_id_unique` (`user_id`,`training_id`),
  ADD KEY `training_user_training_id_foreign` (`training_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_eid_unique` (`eid`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `available_statuses`
--
ALTER TABLE `available_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `available_status_user`
--
ALTER TABLE `available_status_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `billing_statuses`
--
ALTER TABLE `billing_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `billing_status_user`
--
ALTER TABLE `billing_status_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `bloods`
--
ALTER TABLE `bloods`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `blood_user`
--
ALTER TABLE `blood_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `branch_location`
--
ALTER TABLE `branch_location`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `branch_user`
--
ALTER TABLE `branch_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `contact_types`
--
ALTER TABLE `contact_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `contact_type_user`
--
ALTER TABLE `contact_type_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `departments`
--
ALTER TABLE `departments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `department_schedule`
--
ALTER TABLE `department_schedule`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `department_team`
--
ALTER TABLE `department_team`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `department_user`
--
ALTER TABLE `department_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `designations`
--
ALTER TABLE `designations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `designation_user`
--
ALTER TABLE `designation_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `holiday_calenders`
--
ALTER TABLE `holiday_calenders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `holiday_calender_user`
--
ALTER TABLE `holiday_calender_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `locations`
--
ALTER TABLE `locations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `member_statuses`
--
ALTER TABLE `member_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `member_status_user`
--
ALTER TABLE `member_status_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=264;

--
-- AUTO_INCREMENT for table `onsite_statuses`
--
ALTER TABLE `onsite_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `onsite_status_user`
--
ALTER TABLE `onsite_status_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `priorities`
--
ALTER TABLE `priorities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `product_types`
--
ALTER TABLE `product_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `quick_access_hubs`
--
ALTER TABLE `quick_access_hubs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `quick_access_hub_user`
--
ALTER TABLE `quick_access_hub_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `record_types`
--
ALTER TABLE `record_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `regions`
--
ALTER TABLE `regions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `reporters`
--
ALTER TABLE `reporters`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `resource_statuses`
--
ALTER TABLE `resource_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `resource_status_user`
--
ALTER TABLE `resource_status_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `resource_types`
--
ALTER TABLE `resource_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `resource_type_user`
--
ALTER TABLE `resource_type_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `revision_types`
--
ALTER TABLE `revision_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `role_user`
--
ALTER TABLE `role_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `schedules`
--
ALTER TABLE `schedules`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `schedule_team`
--
ALTER TABLE `schedule_team`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `schedule_user`
--
ALTER TABLE `schedule_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `sla_achieves`
--
ALTER TABLE `sla_achieves`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `task_details`
--
ALTER TABLE `task_details`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `task_types`
--
ALTER TABLE `task_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `teams`
--
ALTER TABLE `teams`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `team_user`
--
ALTER TABLE `team_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `time_cards`
--
ALTER TABLE `time_cards`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `trainings`
--
ALTER TABLE `trainings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `training_categories`
--
ALTER TABLE `training_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `training_topics`
--
ALTER TABLE `training_topics`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `training_training_category`
--
ALTER TABLE `training_training_category`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `training_user`
--
ALTER TABLE `training_user`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `available_status_user`
--
ALTER TABLE `available_status_user`
  ADD CONSTRAINT `available_status_user_available_status_id_foreign` FOREIGN KEY (`available_status_id`) REFERENCES `available_statuses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `available_status_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `billing_status_user`
--
ALTER TABLE `billing_status_user`
  ADD CONSTRAINT `billing_status_user_billing_status_id_foreign` FOREIGN KEY (`billing_status_id`) REFERENCES `billing_statuses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `billing_status_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blood_user`
--
ALTER TABLE `blood_user`
  ADD CONSTRAINT `blood_user_blood_id_foreign` FOREIGN KEY (`blood_id`) REFERENCES `bloods` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `blood_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `branch_location`
--
ALTER TABLE `branch_location`
  ADD CONSTRAINT `branch_location_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `branch_location_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `branch_user`
--
ALTER TABLE `branch_user`
  ADD CONSTRAINT `branch_user_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `branch_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contact_type_user`
--
ALTER TABLE `contact_type_user`
  ADD CONSTRAINT `contact_type_user_contact_type_id_foreign` FOREIGN KEY (`contact_type_id`) REFERENCES `contact_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `contact_type_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `department_schedule`
--
ALTER TABLE `department_schedule`
  ADD CONSTRAINT `department_schedule_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `department_team`
--
ALTER TABLE `department_team`
  ADD CONSTRAINT `department_team_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `department_team_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `department_user`
--
ALTER TABLE `department_user`
  ADD CONSTRAINT `department_user_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `department_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `designation_user`
--
ALTER TABLE `designation_user`
  ADD CONSTRAINT `designation_user_designation_id_foreign` FOREIGN KEY (`designation_id`) REFERENCES `designations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `designation_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `holiday_calender_user`
--
ALTER TABLE `holiday_calender_user`
  ADD CONSTRAINT `holiday_calender_user_holiday_calender_id_foreign` FOREIGN KEY (`holiday_calender_id`) REFERENCES `holiday_calenders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `holiday_calender_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `member_status_user`
--
ALTER TABLE `member_status_user`
  ADD CONSTRAINT `member_status_user_member_status_id_foreign` FOREIGN KEY (`member_status_id`) REFERENCES `member_statuses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `member_status_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `onsite_status_user`
--
ALTER TABLE `onsite_status_user`
  ADD CONSTRAINT `onsite_status_user_onsite_status_id_foreign` FOREIGN KEY (`onsite_status_id`) REFERENCES `onsite_statuses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `onsite_status_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `quick_access_hub_user`
--
ALTER TABLE `quick_access_hub_user`
  ADD CONSTRAINT `quick_access_hub_user_hubs_id_foreign` FOREIGN KEY (`hubs_id`) REFERENCES `quick_access_hubs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `quick_access_hub_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `resource_status_user`
--
ALTER TABLE `resource_status_user`
  ADD CONSTRAINT `resource_status_user_resource_status_id_foreign` FOREIGN KEY (`resource_status_id`) REFERENCES `resource_statuses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `resource_status_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `resource_type_user`
--
ALTER TABLE `resource_type_user`
  ADD CONSTRAINT `resource_type_user_resource_type_id_foreign` FOREIGN KEY (`resource_type_id`) REFERENCES `resource_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `resource_type_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_user`
--
ALTER TABLE `role_user`
  ADD CONSTRAINT `role_user_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `task_details`
--
ALTER TABLE `task_details`
  ADD CONSTRAINT `task_details_priority_id_foreign` FOREIGN KEY (`priority_id`) REFERENCES `priorities` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_product_type_id_foreign` FOREIGN KEY (`product_type_id`) REFERENCES `product_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_record_type_id_foreign` FOREIGN KEY (`record_type_id`) REFERENCES `record_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_revision_type_id_foreign` FOREIGN KEY (`revision_type_id`) REFERENCES `revision_types` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_sla_achieve_id_foreign` FOREIGN KEY (`sla_achieve_id`) REFERENCES `sla_achieves` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_details_task_type_id_foreign` FOREIGN KEY (`task_type_id`) REFERENCES `task_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `team_user`
--
ALTER TABLE `team_user`
  ADD CONSTRAINT `team_user_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `training_training_category`
--
ALTER TABLE `training_training_category`
  ADD CONSTRAINT `training_training_category_training_category_id_foreign` FOREIGN KEY (`training_category_id`) REFERENCES `training_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `training_training_category_training_id_foreign` FOREIGN KEY (`training_id`) REFERENCES `trainings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `training_user`
--
ALTER TABLE `training_user`
  ADD CONSTRAINT `training_user_training_id_foreign` FOREIGN KEY (`training_id`) REFERENCES `trainings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `training_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
