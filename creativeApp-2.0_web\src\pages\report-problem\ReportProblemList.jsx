import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditReportProblem from './EditReportProblem';

const isTokenValid = () => localStorage.getItem('token') !== null;

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ReportProblemList = () => {
    const [reportProblems, setReportProblems] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true);
    const [selectedReportProblemId, setSelectedReportProblemId] = useState(null);
    const [error, setError] = useState(null);
    const [statusList, setStatusList] = useState({});

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Subject", key: "subject" },
        { label: "Message", key: "message" },
        // { label: "Created By", key: "created_by" },
        // { label: "Updated By", key: "updated_by" },
        { label: "Status", key: "status" },  // Added Status Column
    ];

    useEffect(() => {
        const fetchStatuses = async () => {
            try {
                const response = await fetch(`${API_URL}/status`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch status list: ${response.statusText}`);
                }

                const data = await response.json();
                const statusArray = Array.isArray(data) ? data : data[Object.keys(data).find(key => Array.isArray(data[key]))] || [];

                const statusMap = {};
                statusArray.forEach(status => {
                    if (status.id && status.name) {
                        statusMap[status.id] = status.name;
                    }
                });

                setStatusList(statusMap);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchStatuses();
    }, []);

    useEffect(() => {
        const fetchReportProblems = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/report-problems/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                setReportProblems(data.reports.map(report => ({
                    ...report,
                    status: statusList[report.status] || "New", // Default status to "New"
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        if (Object.keys(statusList).length > 0) {
            fetchReportProblems();
        }
    }, [statusList]);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/report-problem/${id}/`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to delete report: ${response.statusText}`);
            }

            setReportProblems(prevReports => prevReports.filter(report => report.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedReportProblemId(id);
        setModalVisible(true);
    };

    return (
        <div>
            {error && <div className="text-red-500">{error}</div>}
            {loading ? (
                <div className="text-gray-500">Loading...</div>
            ) : reportProblems.length === 0 ? (
                <div className="text-gray-500">No data available</div>
            ) : (
                <TableContent
                    tableContent={reportProblems}
                    columnNames={columnNames}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    setModalVisible={setModalVisible}
                    setSelectedServiceId={setSelectedReportProblemId}
                />
            )}
            {modalVisible && (
                <EditReportProblem
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    reportProblemId={selectedReportProblemId}
                    statusList={statusList} // Pass status list to modal
                />
            )}
        </div>
    );
};

export default ReportProblemList;
