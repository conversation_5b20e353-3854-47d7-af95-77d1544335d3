import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const EditTaskType = ({ isVisible, setVisible, dataItemsId }) => {
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [taskTypeName, setTaskTypeName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user ID (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        if (!isVisible) return;

        const fetchDepartments = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const data = await response.json();

                setDepartments(data.departments);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, [isVisible]);

    // Fetch the Task Type Details to Edit
    useEffect(() => {
        if (!dataItemsId || !departments.length) return;

        const fetchTaskType = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}task-type/${dataItemsId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch task type details');
                }

                const data = await response.json();
                const taskType = data.taskType;

                // Set the values from the fetched task type
                setTaskTypeName(taskType.name);
                setSelectedDepartment(taskType.department_id); // Set department ID, not name
                setSelectedTeam(taskType.team_id); // Set team ID, not name

                // Fetch teams for the selected department
                const department = departments.find(dep => dep.id === taskType.department_id); // Match by ID

                if (department && department.teams) {
                    setTeams(department.teams);

                    if (!taskType.team_id && department.teams.length > 0) {
                        setSelectedTeam(department.teams[0].id); // Set default team by ID
                    }
                } else {
                    setTeams([]);
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchTaskType();
    }, [dataItemsId, departments]);

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentId = parseInt(e.target.value);
        setSelectedDepartment(departmentId);
        setSelectedTeam(''); // Clear selected team when department changes

        if (departmentId) {
            const department = departments.find(dep => dep.id === departmentId);

            if (department && department.teams && department.teams.length > 0) {
                setTeams(department.teams);
                setSelectedTeam(department.teams[0].id); // Set the default team by ID
            } else {
                setTeams([]);
                setSelectedTeam('');
            }
        } else {
            setTeams([]);
            setSelectedTeam('');
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!selectedDepartment || !selectedTeam || !taskTypeName) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Ensure loggedInUser is set and not null
            if (!loggedInUser) {
                setError('User is not logged in.');
                return;
            }

            const response = await fetch(`${API_URL}task-type/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department_id: selectedDepartment, // Send department ID
                    team_id: selectedTeam, // Send team ID
                    name: taskTypeName,
                    updated_by: loggedInUser,  // Include updated_by with logged-in user ID
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update task type.');
            }

            const result = await response.json();
            //setSuccessMessage(`Task Type "${result.task_type.name}" updated successfully!`);
            alertMessage('success');
            setVisible(false);
        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit Task Type</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.length === 0 ? (
                                        <option disabled>No departments available</option>
                                    ) : (
                                        departments.map((department) => (
                                            <option key={department.id} value={department.id}> {/* Changed to use ID */}
                                                {department.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(parseInt(e.target.value))}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.length === 0 ? (
                                        <option disabled>No teams available</option>
                                    ) : (
                                        teams.map((team) => (
                                            <option key={team.id} value={team.id}> {/* Changed to use ID */}
                                                {team.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="taskTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Task Type Name
                                </label>
                                <input
                                    id="taskTypeName"
                                    type="text"
                                    value={taskTypeName}
                                    onChange={(e) => setTaskTypeName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Update Task Type
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditTaskType;
