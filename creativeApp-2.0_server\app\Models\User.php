<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Auth\Notifications\VerifyEmail;
use App\Models\Resource_type;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

use App\Notifications\CustomVerifyEmail;


class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'eid',
        'photo',
        'fname',
        'lname',
        'email',
        'about',
        'birthday',
        'birthday_celebration',
        'birthday_celebration_date',
        'gender',
        'marital_status',
        'nick_name',
        'primary_contact',
        'secondary_contact',
        'emergency_contact',
        'relation_contact',
        'present_address',
        'permanent_address',
        'blood_donate',
        'prev_designation',
        'report_to',
        'desk_id',
        'joining_date',
        'termination_date',
        'employment_end',
        'work_anniversary',
        'password',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'joining_date' => 'date',
        'termination_date' => 'date',
        'birthday' => 'date',
        'work_anniversary' => 'date',
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    
    // Relation with Billing_status
    public function billing_statuses()
    {
        return $this->belongsToMany(Billing_status::class);
    }

    // Relation with Departments
    public function departments()
    {
        return $this->belongsToMany(Department::class);
    }
    
    // Relation with Resource_status
    public function resource_statuses()
    {
        return $this->belongsToMany(Resource_status::class);
    }
    
    // Relation with Roles
    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    // Relation with Team
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_user', 'user_id', 'team_id')->withPivot('is_default');
        // return $this->belongsToMany(Team::class)->withPivot('is_default');
    }

    // Relation with Resource Type
    public function resource_types()
    {
        return $this->belongsToMany(Resource_type::class);
    }

    // Relation with Designation Type
    public function designations()
    {
        return $this->belongsToMany(Designation::class);
    }


    // Relation with Holiday_calender
    public function holiday_calenders()
    {
        return $this->belongsToMany(Holiday_calender::class);
    }

    // Relation with Quick Access Hub
    public function quick_access_hubs()
    {
        return $this->belongsToMany(Quick_access_hub::class);
    }

    // Relation with Bloods Group
    public function bloods()
    {
        return $this->belongsToMany(Blood::class);
    }

    
    // Relation with Bloods Group
    public function branches()
    {
        return $this->belongsToMany(Branch::class);
    }

    // Relation with Onsite Statues (Home/Office)
    public function onsite_statuses()
    {
        return $this->belongsToMany(OnsiteStatus::class);
    }

    // Relation with Team Member Statuses (Live/Bench/Trainee)
    public function member_statuses()
    {
        return $this->belongsToMany(MemberStatus::class);
    }

    // Relation with Contact Types (Permanent/Probation/Temp)
    public function contact_types()
    {
        return $this->belongsToMany(ContactType::class);
    }

    // Relation with Availability Status (Part time/Full/Contructor)
    public function available_statuses()
    {
        return $this->belongsToMany(AvailableStatus::class);
    }

    // Relation with Schedules (Shift)
    public function schedules()
    {
        return $this->belongsToMany(Schedule::class);
    }

    public function users()
    {
        return $this->belongsToMany(TimeCard::class, 'user_id');
    }

    public function resourceTypes(): BelongsToMany
    {
        return $this->belongsToMany(Resource_type::class);
    }

    // Adding accessores to retrive relational names
    public function getRoleNamesAttribute()
    {
        return $this->roles->pluck('name')->toArray(); // Get all role names as an array
    }

    public function getTeamNamesAttribute()
    {
        return $this->teams->pluck('name')->toArray(); // Get all team names as an array
    }

    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmail);
    }


}
