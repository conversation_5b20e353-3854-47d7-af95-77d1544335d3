import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const EditTaskType = ({ isVisible, setVisible, productTypeId }) => {
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [productTypeName, setProductTypeName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch Departments and Teams when modal is visible
    useEffect(() => {
        if (!isVisible) return;  // Only fetch when modal is visible

        const fetchDepartments = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const data = await response.json();

                setDepartments(data.departments);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, [isVisible]); // Re-fetch if modal visibility changes

    // Fetch the Product Type Details to Edit
    useEffect(() => {
        if (!productTypeId || !departments.length) return; // Ensure departments are loaded first

        const fetchProductType = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}product-type/${productTypeId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch product type details');
                }

                const data = await response.json();
                const productType = data.productType;


                // Set the values from the fetched product type
                setProductTypeName(productType.name);
                setSelectedDepartment(productType.department); // Pre-select department
                setSelectedTeam(productType.team); // Pre-select team

                // Fetch teams for the selected department
                const department = departments.find(dep => dep.name === productType.department);


                if (department && department.teams) {
                    setTeams(department.teams);
                    // Automatically select the relevant team (if exists)
                    if (!productType.team && department.teams.length > 0) {
                        setSelectedTeam(department.teams[0].name); // Set the first team as default
                    }
                } else {
                    setTeams([]);  // In case the department is not found or has no teams
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchProductType();
    }, [productTypeId, departments]); // Trigger fetch when productTypeId or departments change

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam(''); // Reset team when department changes

        if (departmentName) {
            // Find the department object using the selected name
            const department = departments.find(dep => dep.name === departmentName);
            console.log('Department selected:', department); // Debugging log

            if (department && department.teams && department.teams.length > 0) {
                setTeams(department.teams); // Set teams related to the selected department
                setSelectedTeam(department.teams[0].name); // Set the first team as default
            } else {
                setTeams([]); // Clear teams if no teams available
                setSelectedTeam(''); // Reset the selected team
            }
        } else {
            setTeams([]); // Clear teams if no department is selected
            setSelectedTeam(''); // Reset the selected team
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!selectedDepartment || !selectedTeam || !productTypeName) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            const response = await fetch(`${API_URL}product-type/${productTypeId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department: selectedDepartment,
                    team: selectedTeam,
                    name: productTypeName,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update product type.');
            }

            const result = await response.json();
            setSuccessMessage(`Product Type "${result.product_type.name}" updated successfully!`);
            setVisible(false); // Close the modal after success
        } catch (error) {
            setError(error.message);
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit Product Type</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.length === 0 ? (
                                        <option disabled>No departments available</option>
                                    ) : (
                                        departments.map((department) => (
                                            <option key={department.id} value={department.name}>
                                                {department.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.length === 0 ? (
                                        <option disabled>No teams available</option>
                                    ) : (
                                        teams.map((team) => (
                                            <option key={team.id} value={team.name}>
                                                {team.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="productTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                    Product Type Name
                                </label>
                                <input
                                    id="productTypeName"
                                    type="text"
                                    value={productTypeName}
                                    onChange={(e) => setProductTypeName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Update Product Type
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditTaskType;
