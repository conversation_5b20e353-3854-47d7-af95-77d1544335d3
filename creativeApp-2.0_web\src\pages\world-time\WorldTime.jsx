import React, { useEffect, useState } from "react";
import moment from "moment";
import "moment-timezone";
import { fetchWeather<PERSON><PERSON> } from "openmeteo";
import Select from "react-select";
import DynamicTimeCard from "./DynamicTimeCard";
import { useNavigate } from "react-router-dom";
import Calendar from "date-bengali-revised";
import uq from '@umalqura/core';
import timezonebg from "../../assets/images/timezonebg.png";

const timeZones = {
  Dubai: "Asia/Dubai",
  Kabul: "Asia/Kabul",
  Yerevan: "Asia/Yerevan",
  Baku: "Asia/Baku",
  Dhaka: "Asia/Dhaka",
  Brunei: "Asia/Brunei",
  Thimphu: "Asia/Thimphu",
  Shanghai: "Asia/Shanghai",
  Urumqi: "Asia/Urumqi",
  Nicosia: "Asia/Nicosia",
  Famagusta: "Asia/Famagusta",
  Tbilisi: "Asia/Tbilisi",
  Hong_Kong: "Asia/Hong_Kong",
  Jakarta: "Asia/Jakarta",
  Pontianak: "Asia/Pontianak",
  Makassar: "Asia/Makassar",
  Jayapura: "Asia/Jayapura",
  Jerusalem: "Asia/Jerusalem",
  Kolkata: "Asia/Kolkata",
  Baghdad: "Asia/Baghdad",
  Tehran: "Asia/Tehran",
  Amman: "Asia/Amman",
  Tokyo: "Asia/Tokyo",
  Bishkek: "Asia/Bishkek",
  Pyongyang: "Asia/Pyongyang",
  Seoul: "Asia/Seoul",
  Almaty: "Asia/Almaty",
  Qyzylorda: "Asia/Qyzylorda",
  Qostanay: "Asia/Qostanay",
  Aqtobe: "Asia/Aqtobe",
  Aqtau: "Asia/Aqtau",
  Atyrau: "Asia/Atyrau",
  Oral: "Asia/Oral",
  Beirut: "Asia/Beirut",
  Colombo: "Asia/Colombo",
  Yangon: "Asia/Yangon",
  Ulaanbaatar: "Asia/Ulaanbaatar",
  Hovd: "Asia/Hovd",
  Choibalsan: "Asia/Choibalsan",
  Macau: "Asia/Macau",
  Kuala_Lumpur: "Asia/Kuala_Lumpur",
  Kuching: "Asia/Kuching",
  Karachi: "Asia/Karachi",
  Gaza: "Asia/Gaza",
  Hebron: "Asia/Hebron",
  Kathmandu: "Asia/Kathmandu",
  Yekaterinburg: "Asia/Yekaterinburg",
  Qatar: "Asia/Qatar",
  Omsk: "Asia/Omsk",
  Novosibirsk: "Asia/Novosibirsk",
  Barnaul: "Asia/Barnaul",
  Tomsk: "Asia/Tomsk",
  Novokuznetsk: "Asia/Novokuznetsk",
  Krasnoyarsk: "Asia/Krasnoyarsk",
  Irkutsk: "Asia/Irkutsk",
  Chita: "Asia/Chita",
  Yakutsk: "Asia/Yakutsk",
  Khandyga: "Asia/Khandyga",
  Vladivostok: "Asia/Vladivostok",
  Ust_Nera: "Asia/Ust-Nera",
  Singapore: "Asia/Singapore",
  Magadan: "Asia/Magadan",
  Sakhalin: "Asia/Sakhalin",
  Srednekolymsk: "Asia/Srednekolymsk",
  Kamchatka: "Asia/Kamchatka",
  Anadyr: "Asia/Anadyr",
  Bangkok: "Asia/Bangkok",
  Dushanbe: "Asia/Dushanbe",
  Taipei: "Asia/Taipei",
  Dili: "Asia/Dili",
  Ashgabat: "Asia/Ashgabat",
  Damascus: "Asia/Damascus",
  Riyadh: "Asia/Riyadh",
  Samarkand: "Asia/Samarkand",
  Tashkent: "Asia/Tashkent",
  Ho_Chi_Minh: "Asia/Ho_Chi_Minh",
  Andorra: "Europe/Andorra",
  Tirane: "Europe/Tirane",
  Vienna: "Europe/Vienna",
  Brussels: "Europe/Brussels",
  Sofia: "Europe/Sofia",
  Minsk: "Europe/Minsk",
  Zurich: "Europe/Zurich",
  Prague: "Europe/Prague",
  Berlin: "Europe/Berlin",
  Copenhagen: "Europe/Copenhagen",
  Tallinn: "Europe/Tallinn",
  Madrid: "Europe/Madrid",
  Helsinki: "Europe/Helsinki",
  Paris: "Europe/Paris",
  London: "Europe/London",
  Gibraltar: "Europe/Gibraltar",
  Athens: "Europe/Athens",
  Budapest: "Europe/Budapest",
  Dublin: "Europe/Dublin",
  Rome: "Europe/Rome",
  Vilnius: "Europe/Vilnius",
  Luxembourg: "Europe/Luxembourg",
  Riga: "Europe/Riga",
  Monaco: "Europe/Monaco",
  Chisinau: "Europe/Chisinau",
  Malta: "Europe/Malta",
  Amsterdam: "Europe/Amsterdam",
  Oslo: "Europe/Oslo",
  Warsaw: "Europe/Warsaw",
  Lisbon: "Europe/Lisbon",
  Bucharest: "Europe/Bucharest",
  Belgrade: "Europe/Belgrade",
  Kaliningrad: "Europe/Kaliningrad",
  Moscow: "Europe/Moscow",
  Simferopol: "Europe/Simferopol",
  Kirov: "Europe/Kirov",
  Astrakhan: "Europe/Astrakhan",
  Volgograd: "Europe/Volgograd",
  Saratov: "Europe/Saratov",
  Ulyanovsk: "Europe/Ulyanovsk",
  Samara: "Europe/Samara",
  Stockholm: "Europe/Stockholm",
  Istanbul: "Europe/Istanbul",
  Kiev: "Europe/Kiev",
  Uzhgorod: "Europe/Uzhgorod",
  Zaporozhye: "Europe/Zaporozhye",
  Casey: "Antarctica/Casey",
  Davis: "Antarctica/Davis",
  DumontDUrville: "Antarctica/DumontDUrville",
  Mawson: "Antarctica/Mawson",
  Palmer: "Antarctica/Palmer",
  Rothera: "Antarctica/Rothera",
  Syowa: "Antarctica/Syowa",
  Troll: "Antarctica/Troll",
  Vostok: "Antarctica/Vostok",
  Macquarie: "Antarctica/Macquarie",
  Buenos_Aires: "America/Argentina/Buenos_Aires",
  Cordoba: "America/Argentina/Cordoba",
  Salta: "America/Argentina/Salta",
  Jujuy: "America/Argentina/Jujuy",
  Tucuman: "America/Argentina/Tucuman",
  Catamarca: "America/Argentina/Catamarca",
  La_Rioja: "America/Argentina/La_Rioja",
  San_Juan: "America/Argentina/San_Juan",
  Mendoza: "America/Argentina/Mendoza",
  San_Luis: "America/Argentina/San_Luis",
  Rio_Gallegos: "America/Argentina/Rio_Gallegos",
  Ushuaia: "America/Argentina/Ushuaia",
  Barbados: "America/Barbados",
  La_Paz: "America/La_Paz",
  Belem: "America/Belem",
  Fortaleza: "America/Fortaleza",
  Recife: "America/Recife",
  Araguaina: "America/Araguaina",
  Maceio: "America/Maceio",
  Bahia: "America/Bahia",
  Sao_Paulo: "America/Sao_Paulo",
  Campo_Grande: "America/Campo_Grande",
  Cuiaba: "America/Cuiaba",
  Porto_Velho: "America/Porto_Velho",
  Boa_Vista: "America/Boa_Vista",
  Manaus: "America/Manaus",
  Eirunepe: "America/Eirunepe",
  Rio_Branco: "America/Rio_Branco",
  Nassau: "America/Nassau",
  Belize: "America/Belize",
  St_Johns: "America/St_Johns",
  Halifax: "America/Halifax",
  Glace_Bay: "America/Glace_Bay",
  Moncton: "America/Moncton",
  Goose_Bay: "America/Goose_Bay",
  Blanc_Sablon: "America/Blanc-Sablon",
  Toronto: "America/Toronto",
  Nipigon: "America/Nipigon",
  Thunder_Bay: "America/Thunder_Bay",
  Iqaluit: "America/Iqaluit",
  Pangnirtung: "America/Pangnirtung",
  Atikokan: "America/Atikokan",
  Winnipeg: "America/Winnipeg",
  Rainy_River: "America/Rainy_River",
  Resolute: "America/Resolute",
  Rankin_Inlet: "America/Rankin_Inlet",
  Regina: "America/Regina",
  Swift_Current: "America/Swift_Current",
  Edmonton: "America/Edmonton",
  Cambridge_Bay: "America/Cambridge_Bay",
  Yellowknife: "America/Yellowknife",
  Inuvik: "America/Inuvik",
  Creston: "America/Creston",
  Dawson_Creek: "America/Dawson_Creek",
  Fort_Nelson: "America/Fort_Nelson",
  Vancouver: "America/Vancouver",
  Whitehorse: "America/Whitehorse",
  Dawson: "America/Dawson",
  Santiago: "America/Santiago",
  Punta_Arenas: "America/Punta_Arenas",
  Bogota: "America/Bogota",
  Costa_Rica: "America/Costa_Rica",
  Havana: "America/Havana",
  Curacao: "America/Curacao",
  Santo_Domingo: "America/Santo_Domingo",
  Guayaquil: "America/Guayaquil",
  Cayenne: "America/Cayenne",
  Godthab: "America/Godthab",
  Danmarkshavn: "America/Danmarkshavn",
  Scoresbysund: "America/Scoresbysund",
  Thule: "America/Thule",
  Guatemala: "America/Guatemala",
  Guyana: "America/Guyana",
  Tegucigalpa: "America/Tegucigalpa",
  Port_au_Prince: "America/Port-au-Prince",
  Jamaica: "America/Jamaica",
  Martinique: "America/Martinique",
  Mexico_City: "America/Mexico_City",
  Cancun: "America/Cancun",
  Merida: "America/Merida",
  Monterrey: "America/Monterrey",
  Matamoros: "America/Matamoros",
  Caracas: "America/Caracas",
  Mazatlan: "America/Mazatlan",
  Chihuahua: "America/Chihuahua",
  Ojinaga: "America/Ojinaga",
  Hermosillo: "America/Hermosillo",
  Tijuana: "America/Tijuana",
  Bahia_Banderas: "America/Bahia_Banderas",
  Managua: "America/Managua",
  Panama: "America/Panama",
  Lima: "America/Lima",
  Miquelon: "America/Miquelon",
  Puerto_Rico: "America/Puerto_Rico",
  El_Salvador: "America/El_Salvador",
  Grand_Turk: "America/Grand_Turk",
  Paramaribo: "America/Paramaribo",
  Asuncion: "America/Asuncion",
  Port_of_Spain: "America/Port_of_Spain",
  New_York: "America/New_York",
  New_Jersey: "America/New_York",
  Detroit: "America/Detroit",
  Louisville: "America/Kentucky/Louisville",
  Monticello: "America/Kentucky/Monticello",
  Indianapolis: "America/Indiana/Indianapolis",
  Vincennes: "America/Indiana/Vincennes",
  Winamac: "America/Indiana/Winamac",
  Marengo: "America/Indiana/Marengo",
  Petersburg: "America/Indiana/Petersburg",
  Vevay: "America/Indiana/Vevay",
  Tell_City: "America/Indiana/Tell_City",
  Knox: "America/Indiana/Knox",
  Chicago: "America/Chicago",
  Menominee: "America/Menominee",
  Denver: "America/Denver",
  Boise: "America/Boise",
  Phoenix: "America/Phoenix",
  Center: "America/North_Dakota/Center",
  New_Salem: "America/North_Dakota/New_Salem",
  Beulah: "America/North_Dakota/Beulah",
  Los_Angeles: "America/Los_Angeles",
  Anchorage: "America/Anchorage",
  Alaska: "America/Anchorage",
  Juneau: "America/Juneau",
  Sitka: "America/Sitka",
  Metlakatla: "America/Metlakatla",
  Yakutat: "America/Yakutat",
  Nome: "America/Nome",
  Adak: "America/Adak",
  Montevideo: "America/Montevideo",
  Pago_Pago: "Pacific/Pago_Pago",
  Rarotonga: "Pacific/Rarotonga",
  Easter: "Pacific/Easter",
  Galapagos: "Pacific/Galapagos",
  Fiji: "Pacific/Fiji",
  Chuuk: "Pacific/Chuuk",
  Pohnpei: "Pacific/Pohnpei",
  Kosrae: "Pacific/Kosrae",
  Guam: "Pacific/Guam",
  Majuro: "Pacific/Majuro",
  Kwajalein: "Pacific/Kwajalein",
  Tarawa: "Pacific/Tarawa",
  Enderbury: "Pacific/Enderbury",
  Kiritimati: "Pacific/Kiritimati",
  Noumea: "Pacific/Noumea",
  Norfolk: "Pacific/Norfolk",
  Nauru: "Pacific/Nauru",
  Niue: "Pacific/Niue",
  Auckland: "Pacific/Auckland",
  Chatham: "Pacific/Chatham",
  Tahiti: "Pacific/Tahiti",
  Marquesas: "Pacific/Marquesas",
  Gambier: "Pacific/Gambier",
  Port_Moresby: "Pacific/Port_Moresby",
  Bougainville: "Pacific/Bougainville",
  Pitcairn: "Pacific/Pitcairn",
  Palau: "Pacific/Palau",
  Guadalcanal: "Pacific/Guadalcanal",
  Fakaofo: "Pacific/Fakaofo",
  Tongatapu: "Pacific/Tongatapu",
  Funafuti: "Pacific/Funafuti",
  Wake: "Pacific/Wake",
  Honolulu: "Pacific/Honolulu",
  Efate: "Pacific/Efate",
  Wallis: "Pacific/Wallis",
  Apia: "Pacific/Apia",
  Lord_Howe: "Australia/Lord_Howe",
  Hobart: "Australia/Hobart",
  Currie: "Australia/Currie",
  Melbourne: "Australia/Melbourne",
  Sydney: "Australia/Sydney",
  Broken_Hill: "Australia/Broken_Hill",
  Brisbane: "Australia/Brisbane",
  Lindeman: "Australia/Lindeman",
  Adelaide: "Australia/Adelaide",
  Darwin: "Australia/Darwin",
  Perth: "Australia/Perth",
  Eucla: "Australia/Eucla",
  Abidjan: "Africa/Abidjan",
  Algiers: "Africa/Algiers",
  Cairo: "Africa/Cairo",
  El_Aaiun: "Africa/El_Aaiun",
  Ceuta: "Africa/Ceuta",
  Accra: "Africa/Accra",
  Bissau: "Africa/Bissau",
  Nairobi: "Africa/Nairobi",
  Monrovia: "Africa/Monrovia",
  Tripoli: "Africa/Tripoli",
  Casablanca: "Africa/Casablanca",
  Maputo: "Africa/Maputo",
  Windhoek: "Africa/Windhoek",
  Lagos: "Africa/Lagos",
  Khartoum: "Africa/Khartoum",
  Juba: "Africa/Juba",
  Sao_Tome: "Africa/Sao_Tome",
  Ndjamena: "Africa/Ndjamena",
  Tunis: "Africa/Tunis",
  Johannesburg: "Africa/Johannesburg",
  Azores: "Atlantic/Azores",
  Bermuda: "Atlantic/Bermuda",
  Madeira: "Atlantic/Madeira",
  Cape_Verde: "Atlantic/Cape_Verde",
  Canary: "Atlantic/Canary",
  Stanley: "Atlantic/Stanley",
  Faroe: "Atlantic/Faroe",
  South_Georgia: "Atlantic/South_Georgia",
  Reykjavik: "Atlantic/Reykjavik",
  Cocos: "Indian/Cocos",
  Christmas: "Indian/Christmas",
  Chagos: "Indian/Chagos",
  Mauritius: "Indian/Mauritius",
  Maldives: "Indian/Maldives",
  Mahe: "Indian/Mahe",
  Reunion: "Indian/Reunion",
  Kerguelen: "Indian/Kerguelen",
};

function WorldTime() {
  const defaultDateFormat = "dddd, LL";
  const defaultTimeFormat = "hh:mm A";
  const background = {
    backgroundImage: `url(${timezonebg})`,
    backgroundPosition: 'center center',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat'
  }

  let bnCalendar = new Calendar();
  const arabicCalendar = uq(); 

  

  const queryParameters = new URLSearchParams(window.location.search);
  const getDateTime = queryParameters.get("datetime");
  const getDefaultTimeZones = queryParameters.get("defaulttimezone");
  const getTimeZones = queryParameters.get("timezones");

  const [defaultTimeZone, setDefaultTimeZone] = useState({
    label: "Dhaka",
    value: "Asia/Dhaka",
  }); // Store team data
  const [currentDateTime, setCurrentDateTime] = useState(
    moment().tz(defaultTimeZone["value"])
  ); // Store team data
  const [error, setError] = useState(null); // Handle errors
  const [loading, setLoading] = useState(true); // Loading state
  const [weatherData, setWeatherData] = useState(null);
  const [ipData, setIpData] = useState(false);
  const [fromDateTime, setFromDateTime] = useState("");
  const [fromtimezone, setFromtimezone] = useState({
    label: "Dhaka",
    value: "Asia/Dhaka",
  });
  const [totimezone, setTotimezone] = useState({
    label: "New York",
    value: "America/New_York",
  });
  const [showCityList, setShowCityList] = useState([
    {
      label: "Dhaka",
      value: "Asia/Dhaka",
      isFixed: true,
    },
    {
      label: "Alaska",
      value: "America/Anchorage",
    },
   
    {
      label: "New York",
      value: "America/New_York",
      isFixed: true,
    },
    {
      label: "New Jersey",
      value: "America/New_York",
      isFixed: true,
    },
    {
      label: "Los Angeles",
      value: "America/Los_Angeles",
      isFixed: true,
    },
    {
      label: "Chicago",
      value: "America/Chicago",
    },
    {
      label: "El Salvador",
      value: "America/El_Salvador",
    },

    {
      label: "London",
      value: "Europe/London",
    },
   
    {
      label: "Honolulu",
      value: "Pacific/Honolulu",
    },

    {
      label: "Adelaide",
      value: "Australia/Adelaide",
    },
    {
      label: "Sydney",
      value: "Australia/Sydney",
    },
    
    {
      label: "Perth",
      value: "Australia/Perth",
    },
    
    {
      label: "Singapore",
      value: "Asia/Singapore",
    },
   
    {
      label: "Dubai",
      value: "Asia/Dubai",
    },
   
    {
      label: "Tokyo",
      value: "Asia/Tokyo",
    },
   
    
   
    
  ]);

  
  const [fixedCityList, setFixedCityList] = useState([
    {
      label: "Dhaka",
      value: "Asia/Dhaka",
      isFixed: true,
    },
    {
      label: "Alaska",
      value: "America/Anchorage",
    },
   
    {
      label: "New York",
      value: "America/New_York",
      isFixed: true,
    },
    {
      label: "New Jersey",
      value: "America/New_York",
      isFixed: true,
    },
    {
      label: "Los Angeles",
      value: "America/Los_Angeles",
      isFixed: true,
    },
    {
      label: "Chicago",
      value: "America/Chicago",
    },
    {
      label: "El Salvador",
      value: "America/El_Salvador",
    },

    {
      label: "London",
      value: "Europe/London",
    },
   
    {
      label: "Honolulu",
      value: "Pacific/Honolulu",
    },

    {
      label: "Adelaide",
      value: "Australia/Adelaide",
    },
    {
      label: "Sydney",
      value: "Australia/Sydney",
    },
    
    {
      label: "Perth",
      value: "Australia/Perth",
    },
    
    {
      label: "Singapore",
      value: "Asia/Singapore",
    },
   
    {
      label: "Dubai",
      value: "Asia/Dubai",
    },
   
    {
      label: "Tokyo",
      value: "Asia/Tokyo",
    },
   
    
   
    
  ]);


  const [favCityList, setFavCityList] = useState([
    {
      label: "New York",
      value: "America/New_York",
    },

    {
      label: "London",
      value: "Europe/London",
    },

    {
      label: "El Salvador",
      value: "America/El_Salvador",
    },
  ]);

  const params = {
    latitude: ipData?.lat,
    longitude: ipData?.lon,
    hourly: [
      "temperature_2m",
      "relative_humidity_2m",
      "apparent_temperature",
      "precipitation_probability",
      "precipitation",
      "rain",
      "visibility",
      "wind_speed_10m",
      "uv_index",
    ],
    daily: ["weather_code", "sunrise", "sunset"],
    timeformat: "unixtime",
    timezone: "auto",
    past_days: 0,
    forecast_days: 6,
  };

  const datetimeToISOString = (datetime) => {
    return new Date(datetime).valueOf();
  };

  const generateShareUrl = () => {
    let url = window.location.href.split("?")[0];
    let params = new URLSearchParams();

    let isoDateTime = fromDateTime
      ? new Date(fromDateTime).toISOString()
      : new Date(currentDateTime).toISOString();
    let dtmz = fromDateTime ? fromtimezone["label"] : defaultTimeZone["label"];
    let timezones = showCityList.map((item) =>
      item["label"].replaceAll(" ", "_")
    );

    params.append("datetime", datetimeToISOString(isoDateTime));
    params.append("defaulttimezone", dtmz);
    params.append("timezones", timezones.join(","));
    url += "?" + params.toString(",");
    return url;
  };

  const getTimeDifferenceBetweenTimezones = (
    timezone1,
    timezone2,
    time1,
    time2
  ) => {
    // time1 and time2 are optional. If not provided, current time in respective timezones is used.

    const moment1 = time1 ? moment.tz(time1, timezone1) : moment.tz(timezone1);
    const moment2 = time2 ? moment.tz(time2, timezone2) : moment.tz(timezone2);

    const diffInHours = moment2.diff(moment1, "hours");

    let result = {
      differenceInHours: diffInHours,
      timeInTz1: moment1.format("HH:mm"),
      timeInTz2: moment2.format("HH:mm"),
    };

    // console.log(timezone1, timezone2, time1, time2)
    // console.log(result)

    return result.differenceInHours;
  };

  let singleLayoutClasses = "";

  if (window.location.pathname === "/world-time-share") {
    singleLayoutClasses = " max-w-[1642px] mx-auto ";
  }

  useEffect(() => {
    if (getDateTime) {
      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();
      setFromDateTime(getDateTimeParseData);
    }

    if (getDefaultTimeZones) {
      setDefaultTimeZone({
        label: getDefaultTimeZones,
        value: timeZones[getDefaultTimeZones],
      });
      setFromtimezone({
        label: getDefaultTimeZones,
        value: timeZones[getDefaultTimeZones],
      });
    }

    if (getTimeZones) {
      let timezones = getTimeZones.split(",");
      let timezonesData = timezones.map((item) => {
        return { label: item, value: timeZones[item] };
      });
      setShowCityList(timezonesData);
    }
  }, [getDateTime, getDefaultTimeZones, getTimeZones]);

  // Fetch IP data with timeout and retry logic
  useEffect(() => {
    const fetchWithTimeout = async (url, options = {}, timeout = 10000) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    };

    const fetchIP = async (retryCount = 0) => {
      try {
        const response = await fetchWithTimeout(`//ip-api.com/json/`, { method: "GET" }, 8000);

        if (!response.ok) {
          throw new Error("Failed to fetch IP data: " + response.statusText);
        }

        const data = await response.json();
        setIpData(data);
        fetchCurrentDateTimeByIP(data["query"]);
      } catch (error) {
        console.warn("IP fetch attempt failed:", error.message);

        // Retry logic - try up to 2 more times
        if (retryCount < 2) {
          console.log(`Retrying IP fetch... Attempt ${retryCount + 2}/3`);
          setTimeout(() => fetchIP(retryCount + 1), 2000 * (retryCount + 1));
          return;
        }

        // If all retries fail, use fallback
        console.log("All IP fetch attempts failed, using fallback");
        setError("Unable to fetch location data, using default timezone");
        setIpData({ query: "fallback", timezone: "UTC" });
        setLoading(false);
      }
    };

    fetchIP();
  }, []);

  const fetchCurrentDateTimeByIP = async (ip = "", retryCount = 0) => {
    try {
      if (ip && ip !== "fallback") {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        try {
          const response = await fetch(
            `https://timeapi.io/api/time/current/ip?ipAddress=${ip}`,
            {
              method: "GET",
              signal: controller.signal
            }
          );
          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error("Failed to fetch time data: " + response.statusText);
          }

          const responseData = await response.json();

          if (responseData) {
            setDefaultTimeZone({ ...defaultTimeZone, responseData });
            setCurrentDateTime(moment(responseData["dateTime"]));
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          throw fetchError;
        }
      } else {
        // Fallback to local time
        console.log("Using local time as fallback");
        setCurrentDateTime(moment());
      }
    } catch (error) {
      console.warn("Time fetch attempt failed:", error.message);

      // Retry logic - try up to 2 more times
      if (retryCount < 2 && ip !== "fallback") {
        console.log(`Retrying time fetch... Attempt ${retryCount + 2}/3`);
        setTimeout(() => fetchCurrentDateTimeByIP(ip, retryCount + 1), 2000 * (retryCount + 1));
        return;
      }

      // If all retries fail, use local time
      console.log("All time fetch attempts failed, using local time");
      setCurrentDateTime(moment());
      setError("Unable to fetch accurate time, using local time");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!ipData) return;

    const fetchWeather = async (retryCount = 0) => {
      try {
        const url = "https://api.open-meteo.com/v1/forecast";

        // Add timeout to weather API call
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Weather API timeout')), 10000)
        );

        const weatherPromise = fetchWeatherApi(url, params);
        const responses = await Promise.race([weatherPromise, timeoutPromise]);

        const range = (start, stop, step) =>
          Array.from(
            { length: (stop - start) / step },
            (_, i) => start + i * step
          );
        const response = responses[0];

        const utcOffsetSeconds = response.utcOffsetSeconds();
        const hourly = response.hourly();
        const daily = response.daily();

        const weather = {
          hourly: {
            time: range(
              Number(hourly.time()),
              Number(hourly.timeEnd()),
              hourly.interval()
            ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
            temperature2m: hourly.variables(0).valuesArray(),
            relativeHumidity2m: hourly.variables(1).valuesArray(),
            apparentTemperature: hourly.variables(2).valuesArray(),
            precipitationProbability: hourly.variables(3).valuesArray(),
            precipitation: hourly.variables(4).valuesArray(),
            rain: hourly.variables(5).valuesArray(),
            visibility: hourly.variables(6).valuesArray(),
            windSpeed10m: hourly.variables(7).valuesArray(),
            uvIndex: hourly.variables(8).valuesArray(),
          },
          daily: {
            time: range(
              Number(daily.time()),
              Number(daily.timeEnd()),
              daily.interval()
            ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
            weatherCode: daily.variables(0).valuesArray(),
            sunrise: daily.variables(1).valuesArray(),
            sunset: daily.variables(2).valuesArray(),
          },
        };

        setWeatherData(weather);
      } catch (err) {
        console.warn("Weather fetch attempt failed:", err.message);

        // Retry logic - try up to 2 more times
        if (retryCount < 2) {
          console.log(`Retrying weather fetch... Attempt ${retryCount + 2}/3`);
          setTimeout(() => fetchWeather(retryCount + 1), 3000 * (retryCount + 1));
          return;
        }

        // If all retries fail, set empty weather data
        console.log("All weather fetch attempts failed, using fallback");
        setWeatherData({
          hourly: { time: [], temperature2m: [], relativeHumidity2m: [] },
          daily: { time: [], weatherCode: [], sunrise: [], sunset: [] }
        });
        setError("Unable to fetch weather data");
      } finally {
        setLoading(false);
      }
    };

    fetchWeather();
  }, [ipData]);

  // Periodic refresh to prevent stale connections
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      // Refresh current time every 5 minutes to prevent stale data
      if (currentDateTime) {
        setCurrentDateTime(moment());
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Connection health check every 10 minutes
    const healthCheckInterval = setInterval(async () => {
      try {
        // Simple ping to check if network is still available
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        await fetch('//ip-api.com/json/', {
          method: 'HEAD',
          signal: controller.signal
        });
        clearTimeout(timeoutId);

        // If successful, clear any previous errors
        if (error) {
          setError(null);
        }
      } catch (healthError) {
        console.warn('Connection health check failed:', healthError.message);
        // Don't set error for health check failures to avoid UI disruption
      }
    }, 10 * 60 * 1000); // 10 minutes

    // Cleanup intervals on component unmount
    return () => {
      clearInterval(refreshInterval);
      clearInterval(healthCheckInterval);
    };
  }, [currentDateTime, error]);


  const convertDateTime = (totimezonevalue = "") => {
    let fromtimezonevalue = fromtimezone.value || defaultTimeZone["value"];
    var fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);
    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);

    if (moment(retDateTime).isValid()) {
      return retDateTime;
    }

    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);
    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);

    if (moment(retDateTime).isValid()) {
      return retDateTime;
    }
    return false;
  };

  const getTimeZoneSelectOptions = () => {
    let data = [];
    Object.keys(timeZones)
      .sort()
      .map((label) =>
        data.push({
          label: `${label.replaceAll("_", " ")}`,
          value: timeZones[label],
        })
      );

    return data;
  };

  const getBnDateTime = (dateTime) => {
    let date = new Date(dateTime);
    let bnDateTime = bnCalendar.fromDate(date).format("dddd D MMMM, Y Q");
    return bnDateTime;
  };

  return (
    <div
      className={
        "bg-white dark:bg-gray-900 rounded-xl p-4 " + singleLayoutClasses
      }
    >
      <div className="flex justify-start items-start flex-row mb-[20px]">
        <div className="text-left w-1/2 ">
          <h4 className="text-xl font-medium ">World Time</h4>
        </div>
      </div>

      <div className="flex justify-start items-start flex-row mb-[50px] gap-3">
        {/* Current Date Time */}
        <div className="border border-gray-200 rounded-xl  px-[15px] w-5/12 min-h-[210px] bg-[#0b333f] text-[#fff]">
        <DynamicTimeCard />
        </div>

        {favCityList.length > 0 &&
          favCityList.map((item) => {
            // let label = key, timezone = timeZoneList[key];
            let label = item["label"];
            let timezone = item["value"];

            return (
              <div
                className="border border-gray-200 rounded-xl p-[10px] w-3/12 min-h-[210px] bg-[#57B6B2] text-[#fff] "
                key={"timezone-" + timezone}
              >
                <div className="justify-center text-2xl inline-block align-text-middle mt-[20px]">
                  <p className="text-[20px] font-normal">
                    {getTimeDifferenceBetweenTimezones(
                      defaultTimeZone["value"],
                      timezone,
                      currentDateTime.format("lll"),
                      convertDateTime(timezone).format("lll")
                    )}{" "}
                    <small>Hours</small> (
                    {convertDateTime(timezone).format("z")})
                  </p>
                  <h1 className="text-4xl font-bold mb-1 text-[#0B333F]">
                    {label.replaceAll("_", " ")
                      ? label.replaceAll("_", " ")
                      : label}
                  </h1>
                  <p className=" text-[24px] font-bold ">
                    {/* {moment(fromDateTime).tz(timezone).format(defaultDateFormat)} */}
                    {convertDateTime(timezone) &&
                      convertDateTime(timezone).format("LL")}
                  </p>
                  <p className=" text-[16px] font-normal ">
                    {/* {moment(fromDateTime).tz(timezone).format(defaultTimeFormat)} */}
                    {convertDateTime(timezone) &&
                      convertDateTime(timezone).format("dddd, hh:mm A")}
                  </p>
                </div>
              </div>
            );
          })}

        <div className="border border-gray-200   rounded-xl   p-0 py-3 w-4/12 min-h-[210px]  bg-[#DFECF1] text-[#0B333F]">
          {currentDateTime && moment(currentDateTime).isValid() && (
            <div className="justify-center justify-items-center">
              <h1 className="text-[24px]  font-semibold">Today is</h1>
              <hr className="border border-[#0B333F] my-[10px] h-[1px] w-[100%]" />

              {/* <h1 className="text-[64px] font-bold">
              {moment(fromDateTime).isValid() && moment(fromDateTime).format(defaultTimeFormat)}
              {!moment(fromDateTime).isValid() && moment(currentDateTime).isValid() && moment(currentDateTime).format(defaultTimeFormat)}
              </h1> */}
              <p className="text-[20px] font-medium ">
                {moment(fromDateTime).isValid() && (
                  <>
                    <p>{moment(fromDateTime).format(defaultDateFormat)}</p>
                    <p>{getBnDateTime(currentDateTime)}</p>
                    <p>{arabicCalendar.format('fullDate', 'en')}</p>
                  </>
                )}
                {!moment(fromDateTime).isValid() &&
                  moment(currentDateTime).isValid() && (
                    <>
                      <p>{moment(currentDateTime).format(defaultDateFormat)}</p>
                      <p>{getBnDateTime(currentDateTime)}</p>
                      <p>{arabicCalendar.format('fullDate', 'en')}</p>
                    </>
                  )}
              </p>

              {/* <h1 className="text-[64px] font-bold">
                {convertDateTime(totimezone.value).format("hh:mm A")}
              </h1>
              <p className="text-[20px] font-normal ">
                {convertDateTime(totimezone.value).format(defaultDateFormat)}
              </p> */}
            </div>
          )}
        </div>
      </div>

      <div className="w-full flex justify-start items-start flex-wrap  mb-10">
        <div className="text-start w-full ">
          <div className="flex items-center rounded-xl gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
            <div className="grid shrink-0 grid-cols-1 focus-within:relative w-full ">
              <Select
                closeMenuOnSelect={false}
                isClearable={true}
                // isClearable={fixedCityList}
                isSearchable={true}
                isDisabled={false}
                isMulti={true}
                required={false}
                placeholder="Choose your city"
                name="showCityList"
                value={showCityList.length <= 0? setShowCityList(fixedCityList) : showCityList}
                options={getTimeZoneSelectOptions()}
                onChange={(item) => {
                  setShowCityList(item);
                }}
              />
            </div>
            {/* <button onClick={() => navigator.clipboard.writeText(generateShareUrl())} target="_blank" className="block min-w-0 rounded-xl text-center grow py-1.5 pr-3 pl-1 border border-gray-200 bg-[#0B333F] hover:bg-[#2d9abb] text-base text-[#fff] placeholder:text-gray-400 focus:outline-none sm:text-sm/6" >Share the link</button> */}
          </div>
        </div>
      </div>
      <div className="w-full flex justify-start items-start flex-wrap  " style={background}>
        {/* Contacts Navigation */}

        {/* All Contacts timeZone */}

        {/* {Object.keys(showCityList).sort().map((label) => { */}
        {showCityList.length > 0 &&
          showCityList.map((item) => {
            // let label = key, timezone = timeZoneList[key];
            let label = item["label"];
            let timezone = item["value"];

            return (
              <div
                key={"timezone-" + timezone}
                className="mb-6  w-1/3 px-[20px] "
              >
                <div className="border-b-2 border-gray-400 pb-4   flex  justify-start items-start text-start ">
                  <div className="w-1/2 text-start">
                    <p className="font-bold mb-1">
                      {label.replaceAll("_", " ")
                        ? label.replaceAll("_", " ")
                        : label}
                    </p>
                    <p className="font-normal">
                      {timezone} (
                      {moment(currentDateTime).tz(timezone).format("z")})
                    </p>
                  </div>
                  <div className="w-1/2 text-end leading-tight">
                    <div className=" text-[30px] font-bold ">
                      {moment(currentDateTime)
                        .tz(timezone)
                        .format(defaultTimeFormat)}
                      {/* {convertDateTime(timezone) && convertDateTime(timezone).format(defaultTimeFormat)} */}
                    </div>

                    <div className=" text-[14px] font-normal ">
                      {moment(currentDateTime)
                        .tz(timezone)
                        .format(defaultDateFormat)}
                      {/* {convertDateTime(timezone) && convertDateTime(timezone).format(defaultDateFormat)} */}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
      </div>
    </div>
  );
}

export default WorldTime;
