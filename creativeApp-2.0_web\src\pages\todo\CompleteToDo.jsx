import React, { useEffect, useState } from 'react';

// Check if the token is available and valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const CompleteToDo = () => {
    return (
        <>
            <h1>This is CompleteToDo Dashboard</h1>
        </>
    );
}

export default CompleteToDo;