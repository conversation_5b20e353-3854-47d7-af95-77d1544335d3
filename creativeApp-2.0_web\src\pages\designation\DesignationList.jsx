import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditDesignation from './EditDesignation';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const DesignationList = () => {
    const [designations, setDesignations] = useState([]); // Changed variable name to designations
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedDesignationId, setSelectedDesignationId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for designations
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Designation", key: "name" }, // Updated to include fullName
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchDesignations = async () => { // Updated function name for clarity
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/designations`, { // Updated the API endpoint
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                
                setDesignations(data.designations.map(designation => ({
                    id: designation.id,
                    name: designation.name,
                    created_by: designation.created_by,
                    updated_by: designation.updated_by,
                }))); 
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDesignations();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/designations/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete team: ' + response.statusText);
            }

            // Update the designations list after deletion
            setDesignations(prevDesignations => prevDesignations.filter(department => department.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedDesignationId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={designations}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedDesignationId}
            />
            {modalVisible && (
                <EditDesignation
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    designationId={selectedDesignationId}
                />
            )}
        </div>
    );
};

export default DesignationList;
