import React, { useEffect, useState } from 'react';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const getDayOfWeek = (date) => {
    const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return daysOfWeek[date.getDay()];
};

const EditTeamShiftPlan = ({ isVisible, setVisible, holidayId }) => {
    const [holidayDetails, setHolidayDetails] = useState({
        office_location: '',
        holiday_name: '',
        holiday_start_date: '',
        holiday_end_date: '',
        day_of_week: '',
        days: 0,
        holiday_department: '',
    });
    const [locations, setLocations] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const getToken = () => localStorage.getItem('token');

    const isTokenValid = () => {
        const token = getToken();
        return token !== null;
    };

    useEffect(() => {
        const fetchHoliday = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/holidaycalenders/${holidayId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.holidaycalender) {
                    const holiday = data.holidaycalender;
                    setHolidayDetails({
                        office_location: holiday.office_location || '',
                        holiday_name: holiday.holiday_name || '',
                        holiday_start_date: holiday.holiday_start_date || '',
                        holiday_end_date: holiday.holiday_end_date || '',
                        day_of_week: holiday.day_of_week || '',
                        days: holiday.days || 0,
                        holiday_department: holiday.holiday_department || '',
                    });
                } else {
                    setError('No holiday data found.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchLocations = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/locations`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.locations && Array.isArray(data.locations)) {
                    setLocations(data.locations);
                } else {
                    setError('Failed to load locations.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }

                const data = await response.json();
                if (data.departments && Array.isArray(data.departments)) {
                    setDepartments(data.departments);
                } else {
                    setError('Failed to load departments.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchHoliday();
        fetchLocations();
        fetchDepartments();
    }, [holidayId]);

    useEffect(() => {
        if (holidayDetails.holiday_start_date && holidayDetails.holiday_end_date) {
            const start = new Date(holidayDetails.holiday_start_date);
            const end = new Date(holidayDetails.holiday_end_date);
            if (end >= start) {
                const daysList = [];
                let currentDate = new Date(start);

                while (currentDate <= end) {
                    const dayOfWeek = getDayOfWeek(currentDate);
                    daysList.push(dayOfWeek);
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                setHolidayDetails((prevDetails) => ({
                    ...prevDetails,
                    days: daysList.length,
                    day_of_week: daysList.join(', '),
                }));
            } else {
                setHolidayDetails((prevDetails) => ({
                    ...prevDetails,
                    days: 0,
                    day_of_week: '',
                }));
            }
        }
    }, [holidayDetails.holiday_start_date, holidayDetails.holiday_end_date]);

    const handleSubmit = async (event) => {
        event.preventDefault();

        try {
            const response = await fetch(`${API_URL}/holidaycalenders/${holidayId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(holidayDetails),
            });

            if (!response.ok) {
                throw new Error('Failed to update holiday: ' + response.statusText);
            }

            const result = await response.json();
            setSuccessMessage(`Holiday "${result.holiday_name}" updated successfully!`);
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 1000);
        } catch (error) {
            setError(error.message);
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setHolidayDetails((prevDetails) => ({
            ...prevDetails,
            [name]: value,
        }));
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Holiday</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="office_location" className="block mb-2">Office Location</label>
                        <select
                            id="office_location"
                            name="office_location"
                            value={holidayDetails.office_location}
                            onChange={handleChange}
                            className="border rounded w-full p-2"
                            required
                        >
                            <option value="">Select Office Location</option>
                            {locations.map((location) => (
                                <option key={location.id} value={location.locations_name}>
                                    {location.locations_name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="holiday_department" className="block mb-2">Holiday Department</label>
                        <select
                            id="holiday_department"
                            name="holiday_department"
                            value={holidayDetails.holiday_department}
                            onChange={handleChange}
                            className="border rounded w-full p-2"
                            required
                        >
                            <option value="">Select Department</option>
                            {departments.map((department) => (
                                <option key={department.id} value={department.name}>
                                    {department.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <div className="mb-4">
                        <label htmlFor="holiday_name" className="block mb-2">Holiday Name</label>
                        <input
                            type="text"
                            id="holiday_name"
                            name="holiday_name"
                            value={holidayDetails.holiday_name}
                            onChange={handleChange}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="holiday_start_date" className="block mb-2">Holiday Start Date</label>
                        <input
                            type="date"
                            id="holiday_start_date"
                            name="holiday_start_date"
                            value={holidayDetails.holiday_start_date}
                            onChange={handleChange}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="holiday_end_date" className="block mb-2">Holiday End Date</label>
                        <input
                            type="date"
                            id="holiday_end_date"
                            name="holiday_end_date"
                            value={holidayDetails.holiday_end_date}
                            onChange={handleChange}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="day_of_week" className="block mb-2">Day of Week</label>
                        <input
                            type="text"
                            id="day_of_week"
                            name="day_of_week"
                            value={holidayDetails.day_of_week}
                            className="border rounded w-full p-2"
                            readOnly
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="days" className="block mb-2">Days</label>
                        <input
                            type="text"
                            id="days"
                            name="days"
                            value={holidayDetails.days}
                            className="border rounded w-full p-2"
                            readOnly
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-blue-700 text-white rounded-lg px-4 py-2"
                    >
                        Update Holiday
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditTeamShiftPlan;
