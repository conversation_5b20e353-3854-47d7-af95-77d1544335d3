import React, { useEffect, useState } from 'react';

const CustomClock = ({ wLong }) => {
    const [ipData, setIpData] = useState(wLong);
    const [time, setTime] = useState(null); // Initially, time is null before API data arrives
    const [error, setError] = useState(null);
    const latitude = ipData?.lat;
    const longitude = ipData?.lon;
    // Fetch IP data
    useEffect(() => {

        fetch("https://reallyfreegeoip.org/json/")
            .then((response) => response.json())
            .then((data) => {
                setIpData(data);
            })
            .catch((err) => setError(err));
    }, []);
    // Function to fetch time data from the API
    const fetchTime = async () => {
        try {

            const response = await fetch(`https://timeapi.io/api/time/current/coordinate?latitude=${latitude}&longitude=${longitude}`);
            const data = await response.json();
            setTime({
                year: data.year,
                month: data.month,
                day: data.day,
                hour: data.hour,
                minute: data.minute,
                seconds: data.seconds,
                timeZone: data.timeZone,
            });
        } catch (error) {
            console.error("Error fetching time data:", error);
        }
    };

    useEffect(() => {
        // Fetch the initial time when the component mounts
        fetchTime();

        // Set an interval to fetch the time every minute (60000 milliseconds)
        const apiInterval = setInterval(fetchTime, 3600000);

        // Clean up the interval when the component unmounts
        return () => clearInterval(apiInterval);
    }, [latitude, longitude]);

    useEffect(() => {
        if (!time) return; // Do nothing if time hasn't been fetched yet

        const tick = () => {
            setTime((prevTime) => {
                let { year, month, day, hour, minute, seconds } = prevTime;

                seconds += 1;
                if (seconds >= 60) {
                    seconds = 0;
                    minute += 1;
                }
                if (minute >= 60) {
                    minute = 0;
                    hour += 1;
                }
                if (hour >= 24) {
                    hour = 0;
                    day += 1;
                }

                // Add logic to handle month and year boundary if needed

                return { ...prevTime, hour, minute, seconds };
            });
        };

        const localInterval = setInterval(tick, 1000); // Update every second locally
        return () => clearInterval(localInterval); // Clean up the interval on unmount
    }, [time]);

    // Helper function to format time
    const formatTime = (num) => (num < 10 ? `0${num}` : num);

    // Helper function to convert 24-hour format to 12-hour with AM/PM
    const getAmPmHour = (hour) => {
        const period = hour >= 12 ? 'PM' : 'AM';
        const adjustedHour = hour % 12 || 12;  // Convert 0 and 12 to 12, and keep the rest in 12-hour format
        return { adjustedHour, period };
    };

    // Helper function to get the full month name
    const getMonthName = (monthNumber) => {
        const months = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ];
        return months[monthNumber - 1]; // Month number is 1-based, array index is 0-based
    };

    if (!time) {
        // Show a loading state while the time data is being fetched
        return <div className='text-white'>Loading...</div>;
    }

    // Destructure the time data for easy use
    const { adjustedHour, period } = getAmPmHour(time.hour);
    return (
        <div className='text-white text-left'>
            {`${formatTime(time.day)} ${getMonthName(time.month)}, ${time.year} `} -
            {` ${formatTime(adjustedHour)}:${formatTime(time.minute)}:${formatTime(time.seconds)} ${period}`}
        </div>
    );
};

export default CustomClock;