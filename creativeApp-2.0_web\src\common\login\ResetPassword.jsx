import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FetchResetLink, FetchResetPassword } from './../../common/fetchData/FetchLogin';
import BgImg from './../../assets/images/login-bg.png';

export default function ResetPassword() {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [passwordConfirmation, setPasswordConfirmation] = useState('');
    const [error, setError] = useState('');
    const [isPasswordVisible, setIsPasswordVisible] = useState(false);
    const [isResetLinkSent, setIsResetLinkSent] = useState(false);
    const [isPasswordReset, setIsPasswordReset] = useState(false); // For reset password state
    const [token, setToken] = useState(''); // For storing the reset token from the URL
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);

    const handleSendResetLink = async (e) => {
        e.preventDefault();

        setLoading(true);

        setError('');

        try {
            const data = await FetchResetLink(email);
            setIsResetLinkSent(true);
            setLoading(false);
        } catch (err) {
            setError('Failed to send reset link. Please try again.');
            setLoading(false);
        }
    };

    const handleResetPassword = async (e) => {
        e.preventDefault();

        setLoading(true);

        setError('');

        if (password !== passwordConfirmation) {
            setError('Passwords do not match!');
            return;
        }

        try {
            const data = await FetchResetPassword(email, password, passwordConfirmation, token);
            setIsPasswordReset(true);
            navigate('/login'); // Redirect to login after successful password reset
            setLoading(false);
        } catch (err) {
            setError('Failed to reset password. Please try again.');
            setLoading(false);
        }
    };

    return (
        <section className="bg-gray-50 dark:bg-gray-900">
            <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
                <div className="w-full bg-white rounded-3xl shadow dark:border md:mt-0 sm:max-w-6xl xl:p-0 dark:bg-gray-800 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-center gap-12">
                    <div className="p-6 space-y-4 md:space-y-6 sm:p-8 w-full sm:w-1/2 text-left">
                        <h6 className='text-base text-gray-600 pb-1'>{isResetLinkSent ? 'Check your inbox!' : 'Create or Reset Password 🛠️'}</h6>
                        <h1 className="text-2xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white mt-0">
                            {isPasswordReset ? 'Your password has been reset!' : 'Enter email to create or reset password'}
                        </h1>

                        {error && <p className="text-red-500">{error}</p>}

                        {isResetLinkSent ? (
                            <p className="text-green-500">A reset link has been sent to your email. Please check your inbox.</p>
                        ) : (
                            <form className="space-y-4 md:space-y-6" onSubmit={handleSendResetLink}>
                                <div className='relative'>
                                    <label htmlFor="email" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        placeholder="Enter your email"
                                        required
                                    />
                                </div>
                                <button
                                    type="submit"
                                    className="w-full text-white bg-secondaryOrange hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-xl text-sm px-5 py-3 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                                >
                                    {loading ? 'Sending...' : 'Send Reset Link'}
                                </button>
                                <p className="text-sm font-light text-gray-500 dark:text-gray-400">
                                    Already have your password? <Link to="/login" className="font-medium text-primary hover:underline dark:text-primary-500">Login</Link>
                                </p>
                            </form>
                        )}
                    </div>
                    <div className='bg-primary rounded-3xl w-full sm:w-1/2 my-6 mr-6'>
                        <h2 className='text-white text-4xl font-bold pt-16 px-12 pb-8 leading-normal text-left'>Get back to work with a fresh start!</h2>
                        <img src={BgImg} alt="Background" />
                    </div>
                </div>
            </div>
        </section>
    );
}
