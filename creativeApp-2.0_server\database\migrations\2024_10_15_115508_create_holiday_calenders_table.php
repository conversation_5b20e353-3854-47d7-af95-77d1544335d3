<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('holiday_calenders', function (Blueprint $table) {
            $table->id();
            $table->string('office_location');
            $table->string('holiday_name');
            $table->string('holiday_department');
            $table->date('holiday_start_date');
            $table->date('holiday_end_date');
            $table->string('day_of_week');
            $table->integer('days');
            $table->string('created_by');
            $table->string('updated_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('holiday_calenders');
    }
};
