import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const billingStatusApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getBillingStatusData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `billing-status-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['BillingStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForBillingStatus: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `billing-status-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['BillingStatusData'],
    }),

    getBillingStatusById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `billing_statuses/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'BillingStatusData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createBillingStatus: builder.mutation({
      query: (newFormationType) => ({
        url: 'billing-status-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['BillingStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateBillingStatus: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `billing_statuses/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'BillingStatusData', id }, 'BillingStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteBillingStatus: builder.mutation({
      query: (id) => ({
        url: `billing_statuses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BillingStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetBillingStatusDataQuery,
  useLazyFetchDataOptionsForBillingStatusQuery,
  useGetBillingStatusByIdQuery,
  useLazyGetBillingStatusByIdQuery,
  useCreateBillingStatusMutation,
  useUpdateBillingStatusMutation,
  useDeleteBillingStatusMutation,
} = billingStatusApi;
