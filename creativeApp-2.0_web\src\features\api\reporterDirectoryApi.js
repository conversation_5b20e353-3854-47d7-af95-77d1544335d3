import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const reporterDirectoryApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getReporterDirectoryData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `reporter-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ReporterDirectoryData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForReporterDirectory: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `reporter-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ReporterDirectoryData'],
    }),

    getReporterDirectoryById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `reporter/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ReporterDirectoryData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createReporterDirectory: builder.mutation({
      query: (newFormationType) => ({
        url: 'reporter-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ReporterDirectoryData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateReporterDirectory: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `reporter/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ReporterDirectoryData', id }, 'ReporterDirectoryData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteReporterDirectory: builder.mutation({
      query: (id) => ({
        url: `reporter/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ReporterDirectoryData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetReporterDirectoryDataQuery,
  useLazyFetchDataOptionsForReporterDirectoryQuery,
  useGetReporterDirectoryByIdQuery,
  useLazyGetReporterDirectoryByIdQuery,
  useCreateReporterDirectoryMutation,
  useUpdateReporterDirectoryMutation,
  useDeleteReporterDirectoryMutation,
} = reporterDirectoryApi;
