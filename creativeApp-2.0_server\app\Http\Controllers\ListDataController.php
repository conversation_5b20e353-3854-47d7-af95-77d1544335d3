<?php

namespace App\Http\Controllers;
use App\Models\Department;
use App\Models\Team;
use App\Models\User;
use App\Models\Designation;
use App\Models\Schedule;
use App\Models\Location;
use Illuminate\Http\JsonResponse;

use Illuminate\Http\Request;

class ListDataController extends Controller
{
    /**
     * Get list of departments.
     */
    public function getDepartments(): JsonResponse
    {
        $departments = Department::with(['teams:id,name'])->orderBy('name', 'asc')->get();

        return response()->json($departments);
    }
    /**
     * Get list of locations.
     */
    public function getOfficelocation(): JsonResponse
    {
        $locations = Location::orderBy('locations_name', 'asc')->get();

        return response()->json($locations);
    }

    /**
     * Get list of teams.
     */
    public function getTeams(): JsonResponse
    {
        $teams = Team::with('departments:id')
        ->select('teams.id', 'name',)
        ->orderBy('name', 'asc')
        ->get()
        ->map(function ($team) {
            $team->department_ids = $team->departments->pluck('id')->toArray();
            return $team;
        });

        return response()->json($teams);
    }
    
    /**
     * Get list of shifts.
     */
    public function getShifts(): JsonResponse
    {
        $teams = Schedule::with('teams')
        ->select('schedules.id', 'shift_name','shift_start','shift_end')
        ->orderBy('shift_name', 'asc')
        ->get()
        ->map(function ($team) {
            $team->team_ids = $team->teams->pluck('id')->toArray();
            return $team;
        });

        return response()->json($teams);
    }

    
    /**
     * Get list of users.
     */
    public function getUsersByDefaultTeam(): JsonResponse
    {
        $user = User::with(['teams' => function ($query) {
            $query->select('teams.id', 'team_user.is_default'); // Select is_default column
        }])
        ->select('users.*') // Explicitly select columns
        ->orderBy('users.fname', 'asc')
        ->get()
        ->map(function ($user) {
            $teams = $user->teams;
    
            if ($teams->count() > 1) {
                // If multiple teams, pick the one where is_default = 1
                $user->team_id = optional($teams->where('is_default', 1)->first())->id;
                if (!$user->team_id) {
                    $user->team_id = optional($teams->first())->id;
                }
            } else {
                // If only one team, return that team's ID
                $user->team_id = optional($teams->first())->id;
            }
    
            unset($user->teams); // Remove full teams list if not needed
            return $user;
        });
    


        return response()->json($user);
    }


    /**
     * Get list of designations.
     */
    public function getDesignations(): JsonResponse
    {
        $designations = Designation::select('id', 'name')->orderBy('name', 'asc')->get();

        return response()->json($designations);
    }
}
