import React, { useState, useEffect } from 'react';

const AddPasswordForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {
  const [formData, setFormData] = useState({
    title: '',
    username: '',
    password: '',
    team: '',
    department: '',
    strength: 'Weak Password'
  });

  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});

  // Update form when generated password changes
  useEffect(() => {
    if (generatedPassword) {
      setFormData(prev => ({
        ...prev,
        password: generatedPassword,
        strength: passwordStrength
      }));
    }
  }, [generatedPassword, passwordStrength]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Auto-calculate password strength when password changes
    if (name === 'password') {
      const strength = calculatePasswordStrength(value);
      setFormData(prev => ({
        ...prev,
        strength: strength
      }));
    }
  };

  const calculatePasswordStrength = (password) => {
    if (!password) return 'Weak Password';
    
    let score = 0;
    
    // Length check
    if (password.length >= 12) score += 2;
    else if (password.length >= 8) score += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    // Additional complexity
    if (password.length >= 16) score += 1;
    
    if (score >= 6) return 'Strong Password';
    if (score >= 4) return 'Moderate Password';
    return 'Weak Password';
  };

  const getStrengthColor = (strength) => {
    switch (strength) {
      case 'Strong Password':
        return 'bg-green-100 text-green-600 border-green-300';
      case 'Moderate Password':
        return 'bg-yellow-100 text-yellow-600 border-yellow-300';
      case 'Weak Password':
        return 'bg-red-100 text-red-600 border-red-300';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-300';
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }
    
    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    }
    
    if (!formData.team.trim()) {
      newErrors.team = 'Team is required';
    }
    
    if (!formData.department.trim()) {
      newErrors.department = 'Department is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Add strength color for display
      const strengthColor = getStrengthColor(formData.strength);
      const cardData = {
        ...formData,
        strengthColor
      };
      
      onSubmit(cardData);
      
      // Reset form
      setFormData({
        title: '',
        username: '',
        password: '',
        team: '',
        department: '',
        strength: 'Weak Password'
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const useGeneratedPassword = () => {
    if (generatedPassword) {
      setFormData(prev => ({
        ...prev,
        password: generatedPassword,
        strength: passwordStrength
      }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Title *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
            errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          }`}
          placeholder="Enter platform or service name"
        />
        {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
      </div>

      {/* Username */}
      <div>
        <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          User Name *
        </label>
        <input
          type="text"
          id="username"
          name="username"
          value={formData.username}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
            errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          }`}
          placeholder="Enter username or email"
        />
        {errors.username && <p className="mt-1 text-sm text-red-600">{errors.username}</p>}
      </div>

      {/* Password */}
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Password *
        </label>
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            id="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="Enter password"
          />
          <div className="absolute inset-y-0 right-0 flex items-center">
            <button
              type="button"
              onClick={useGeneratedPassword}
              className="px-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              title="Use generated password"
            >
              Use Gen
            </button>
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="pr-3 pl-1 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <span className="material-symbols-rounded text-sm">
                {showPassword ? 'visibility_off' : 'visibility'}
              </span>
            </button>
          </div>
        </div>
        {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
        
        {/* Password Strength Indicator */}
        {formData.password && (
          <div className="mt-2">
            <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>
              {formData.strength}
            </span>
          </div>
        )}
      </div>

      {/* Team */}
      <div>
        <label htmlFor="team" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Team *
        </label>
        <input
          type="text"
          id="team"
          name="team"
          value={formData.team}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
            errors.team ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          }`}
          placeholder="Enter team name"
        />
        {errors.team && <p className="mt-1 text-sm text-red-600">{errors.team}</p>}
      </div>

      {/* Department */}
      <div>
        <label htmlFor="department" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Department *
        </label>
        <input
          type="text"
          id="department"
          name="department"
          value={formData.department}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
            errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          }`}
          placeholder="Enter department name"
        />
        {errors.department && <p className="mt-1 text-sm text-red-600">{errors.department}</p>}
      </div>

      {/* Strength Level (Read-only, auto-calculated) */}
      <div>
        <label htmlFor="strength" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Strength Level
        </label>
        <div className={`w-full px-3 py-2 border rounded-md ${getStrengthColor(formData.strength)} cursor-not-allowed`}>
          {formData.strength}
        </div>
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Strength is automatically calculated based on your password
        </p>
      </div>

      {/* Submit Buttons */}
      <div className="md:col-span-2 flex space-x-4">
        <button
          type="submit"
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Save Password Card
        </button>
        <button
          type="button"
          onClick={onCancel}
          className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Cancel
        </button>
      </div>
    </form>
  );
};

export default AddPasswordForm;
