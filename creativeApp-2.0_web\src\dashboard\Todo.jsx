import React, { useEffect, useState } from 'react';
import TodoNav from '../pages/todo/ToDoNav';
import AllToDo from '../pages/todo/AllToDo';
import ToDayToDo from '../pages/todo/ToDayToDo';
import TomorrowToDo from '../pages/todo/TomorrowToDo';
import ThisWeekToDo from '../pages/todo/ThisWeekToDo';
import ThisMonthToDo from '../pages/todo/ThisMonthToDo';
import CompleteToDo from '../pages/todo/CompleteToDo';
import FailedToDo from '../pages/todo/FailedToDo';
import TodoHeader from '../pages/todo/commonTodo/TodoHeader';
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;
const Todo = () => {
    const [userData, setUserData] = useState([]); // Store all user data
    const [teamData, setTeamData] = useState([]); // Store team data
    const [error, setError] = useState(null); // Handle errors
    const [loading, setLoading] = useState(true); // Loading state
    const [searchQuery, setSearchQuery] = useState(''); // Search query state
    const [selectedTeam, setSelectedTeam] = useState(null); // Store selected team
    const [selectedTeamUsers, setSelectedTeamUsers] = useState([]);
    const [selectedUser, setSelectedUser] = useState(null);
    const [todoComponent, setTodoComponent] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [modalVisible, setModalVisible] = useState(false);

    // for fetch data from api conditional
    const [fetchData, setFetchData] = useState(false);

    // Fetch teams data
    useEffect(() => {
        const fetchTeams = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/teams`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch teams: ' + response.statusText);
                }

                const data = await response.json();
                setTeamData(data.teams); // Update team data
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTeams();
    }, []);
    // Fetch all users data (for "All Contacts" functionality)
    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();
                setUserData(data); // Update user data (all users)
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUsers();
    }, []);

    // Handle team selection (passed from ContactNav)
    const onSelectToDoGroup = (toDoGroupName) => {

        /* -- Temporary for showing component --- */
        setTodoComponent(toDoGroupName);
        /*-------*/
        const team = teamData.find((team) => team.id === toDoGroupName);
        if (team) {
            setSelectedTeam(team); // Update selected todo category
            // Match users by their IDs
            const usersInTeam = userData.filter((user) => team.users.some((teamUser) => teamUser.id === user.id));
            setSelectedTeamUsers(usersInTeam); // Set the users of selected team
            console.log(`Selected Group: ${toDoGroupName}`); // Log selected team ID
        }
    };

    // Handle "All Contacts" click - reset to show all contacts
    const onShowAllToDo = () => {
        setSelectedTeam(null);
        setSelectedTeamUsers([]);
    };

    // Handle user selection for detailed profile
    const handleTodoSelect = (user) => {
        setSelectedUser(user); // Set the selected user to show their details
    };

    // Filter users based on search query
    const filteredUsers = selectedTeam ?
        selectedTeamUsers.filter(user =>
            `${user.fname} ${user.lname}`.toLowerCase().includes(searchQuery.toLowerCase())
        ) :
        userData.filter(user =>
            `${user.fname} ${user.lname}`.toLowerCase().includes(searchQuery.toLowerCase())
        );

    // If loading, show loading message
    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // If an error occurs, show error message
    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div className='bg-white dark:bg-gray-900 rounded-xl'>
            <div className='rounded-xl grid grid-cols-12 justify-start items-start'>
                {/* Todo Navigation */}
                <div className='text-left col-span-2 p-6'>
                    <h6 className='text-2xl font-medium pb-8'>Achieve+</h6>
                    <TodoNav
                        fetchData={fetchData}
                        setFetchData={setFetchData}
                        teams={teamData} // Pass the toDo to ToDoNav
                        onSelectToDoGroup={onSelectToDoGroup} // Handle todo selection
                        onShowAllToDo={onShowAllToDo} // Pass handler to filter tag data
                    />
                </div>
                {/* All ToDo */}
                <div className=' col-span-10 py-6 pr-6 w-full h-[80vh] relative overflow-hidden'>
                    {/* ToDo List */}
                    <TodoHeader title={todoComponent} setFetchData={setFetchData} />
                    <div className="rounded-xl">
                        {/* showing todos as per user selection from toDoNav */}
                        {todoComponent === 'all' && <AllToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'Today' && <ToDayToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'Tomorrow' && <TomorrowToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'This Week' && <ThisWeekToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'This Month' && <ThisMonthToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'Completed Task' && <CompleteToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                        {todoComponent === 'Failed Task' && <FailedToDo users={filteredUsers} handleTodoSelect={handleTodoSelect} />}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Todo;