<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

use Illuminate\Auth\Notifications\VerifyEmail as BaseVerifyEmail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\URL;

class CustomVerifyEmail extends BaseVerifyEmail
{
    public function toMail($notifiable)
    {
        $verificationUrl = $this->verificationUrl($notifiable);

        return (new MailMessage)
            ->subject('Verify Your Email Address')
            ->greeting('Hello! 👋')
            ->line('To activate your account and verify your email address, please click the button below:')
            ->action('👉 Verify Your Email', $verificationUrl)
            ->line('If your account is not working after verification, please contact with your team lead or buddy.')
            ->line('Regards,')
            ->salutation('The SEBPO Creative Team');
    }
}