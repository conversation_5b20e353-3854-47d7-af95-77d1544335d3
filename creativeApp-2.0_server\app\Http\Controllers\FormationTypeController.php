<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\FormationType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FormationTypeController extends Controller
{
    public function index(Request $request)
    {
        // $query = FormationType::query();

        $query = FormationType::with(['creator','updater',  'team', 'department']);

        // return response()->json($query, 200);

        // Decode all input parameters to handle URL-encoded values
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedTitle = $request->filled('title') ? urldecode($request->input('title')) : null;
        $decodedType = $request->filled('type') ? urldecode($request->input('type')) : null;
        $decodedIsActive = $request->filled('is_active') ? urldecode($request->input('is_active')) : null;
        $decodedSlug = $request->filled('short_title') ? urldecode($request->input('short_title')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
         
        // Filtering: Support multiple values for title, type, and slug
        if ($decodedTitle) {
            $titles = explode(',', $decodedTitle);
            $query->where(function ($q) use ($titles) {
                foreach ($titles as $title) {
                    $q->orWhere('title', 'like', '%' .trim($title). '%');
                }
            });
        }
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }
        
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }
        
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        if ($decodedType) {
            $types = explode(',', $decodedType);
            $query->where(function ($q) use ($types) {
                foreach ($types as $type) {
                    $q->orWhere('type', '=', trim($type));
                }
            });
        }

        if ($decodedIsActive) {
            $isActives = explode(',', $decodedIsActive);
            $query->where(function ($q) use ($isActives) {
                foreach ($isActives as $isActive) {
                    $q->orWhere('is_active', '=',  trim($isActive) );
                }
            });
        }

        if ($decodedSlug) {
            $slugs = explode(',', $decodedSlug);
            $query->where(function ($q) use ($slugs) {
                foreach ($slugs as $slug) {
                    $q->orWhere('short_title', 'like', '%' .trim($slug). '%');
                }
            });
        }

        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('title', 'like', '%' . $globalSearch . '%')
                  ->orWhere('short_title', 'like', '%' . $globalSearch . '%')
                  ->orWhere('type', 'like', '%' . $globalSearch . '%')
                  ->orWhere('slug', 'like', '%' . $globalSearch . '%')
                  ->orWhere('details', 'like', '%' . $globalSearch . '%')
                  ->orWhere('is_active', 'like', '%' . $globalSearch . '%')
                  ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

                //   ->orWhere('updated_by', 'like', '%' . $globalSearch . '%')
                //   ->orWhere('created_by', 'like', '%' . $globalSearch . '%');
            });
        }


        // Sorting: Use query parameters 'sort_by' and 'order'
        // Default sorting: sort by 'created_at' in descending order
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        // Validate allowed sort columns to prevent unexpected queries
        // $allowedSortFields = ['id', 'title', 'type', 'slug', 'created_at', 'updated_at'];
        // if (!in_array($sortBy, $allowedSortFields)) {
        //     $sortBy = 'created_at';
        // }

        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);

        

        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $formationTypes = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($formationTypes, 200);
    }

    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = FormationType::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }
        
        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = FormationType::with(['creator','updater', 'team', 'department']);

        

        
        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        }else{
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }


    public function store(Request $request)
    {

        $validated = $request->validate([
                'title' => 'required|string|max:255',
                'short_title' => 'required|string|max:255',
                'type' => 'required|string|max:255',
                'is_active' => 'required',
                'department_id' => 'required',
                'team_id' => 'required',
        ]);

        // if($validated->fails()){
        //     return response()->json($validated->errors(), 403);
        // }

        try {
            $authUser = $request->user();

            if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {

            $formationType = FormationType::create( $validated);

            return response()->json([
                'status' => 'success',
                'message' => 'Created successfully',
                'data' => $formationType
            ], 201);

            }
        } catch (\Throwable $th) {
            Log::warning('Unauthorized Formation Type Data Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to create a Formation Type Data.'], 403);
        }
        
    }

    public function show(FormationType $formationType)
    {
        return $formationType;
    }

    public function update(Request $request, FormationType $formationType)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'short_title' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'department_id' => 'required',
            'team_id' => 'required',
            'is_active' => 'required',
            // Add other validation rules as needed
        ]);

        

        $formationType->update($request->all());

        return response()->json([
            'status' => 'success',
            'message' => 'Update successfully',
            'data' => $formationType
        ], 200);
    }

    public function destroy(FormationType $formationType)
    {
        $formationType->delete();
        return response()->json(null, 204);
    }
}