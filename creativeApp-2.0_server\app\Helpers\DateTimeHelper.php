<?php
namespace App\Helpers;

use Carbon\Carbon;

class DateTimeHelper
{
    public static function getCurrentDateTime()
    {
        return Carbon::now()->format('Y-m-d H:i:s');
    }

    public static function getCurrentTimezone()
    {
        return Carbon::now()->timezoneName;
    }

    public static function convertTimezone($datetime, $timezone)
    {
        return Carbon::parse($datetime)->setTimezone($timezone);
    }

    public static function calculateDuration($start, $end)
    {
        $start = Carbon::parse($start);
        $end = Carbon::parse($end);

        return $start->diff($end)->format('%H:%I:%S');
    }
}
