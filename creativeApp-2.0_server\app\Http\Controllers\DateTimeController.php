<?php

namespace App\Http\Controllers;

use App\Helpers\DateTimeHelper;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class DateTimeController extends Controller
{
    public function getCurrentDateTime()
    {
        return response()->json([
            'datetime' => DateTimeHelper::getCurrentDateTime(),
            'timezone' => DateTimeHelper::getCurrentTimezone()
        ]);
    }

    public function convertTimezone(Request $request)
    {
        $request->validate([
            'datetime' => 'required|date_format:Y-m-d H:i:s',
            'timezone' => 'required|string|timezone',
        ]);

        return response()->json([
            'converted' => DateTimeHelper::convertTimezone($request->datetime, $request->timezone)
        ]);
    }

    public function calculateDuration(Request $request)
    {
        $request->validate([
            'start' => 'required|date_format:Y-m-d H:i:s',
            'end' => 'required|date_format:Y-m-d H:i:s|after_or_equal:start',
        ]);

        return response()->json([
            'duration' => DateTimeHelper::calculateDuration($request->start, $request->end)
        ]);
    }
}
