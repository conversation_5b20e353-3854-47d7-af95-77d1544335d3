import React, { useState } from 'react';
import TableLayoutWrapper2 from '../common/table/TableLayoutWrapper2';
import TableHeader from '../common/table/TableHeader';
import TablePagination from '../common/table/TablePagination';
import TeamMemberList from '../pages/team-member/TeamMemberList';

const TeamMember = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 2;

  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm);
  };
  
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader onSearch={handleSearch} />
        <TeamMemberList
          searchTerm={searchTerm}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          setTotalCount={setTotalCount}
        />
        <TablePagination
          currentPage={currentPage}
          totalItems={totalCount}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
        />
      </TableLayoutWrapper2>
    </div>
  );
};

export default TeamMember;
