<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\App_support;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AppSupportController extends Controller
{
    /**
     * Display a listing of all "About the App" records.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $appSupports = App_support::all();

        // Log the entries retrieved
        Log::info('All About The App entries retrieved:', ['entries_count' => $appSupports->count()]);

        return response()->json(['appSupport' => $appSupports], 200);
    }

    /**
     * Display the specified "App Support" entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the entry by ID
        $appSupportEntry = App_support::find($id);

        if (!$appSupportEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        // Log the entry retrieved
        Log::info('App Support entry retrieved:', ['entry' => $appSupportEntry]);

        return response()->json(['appSupport' => $appSupportEntry], 200);
    }

    

    /**
     * Create a new "App Support" entry.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $authUser = $request->user();

        // Log the incoming request data and authenticated user details
        Log::info('Create App Support entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        // Validate the request
        $request->validate([
            'name' => 'required|string',
        ]);

        // Create the new entry
        $appSupportEntry = App_support::create([
            'name' => $request->name,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        // Log the created entry
        Log::info('App Support entry created:', ['entry' => $appSupportEntry]);

        return response()->json([
            'message' => 'Entry created successfully.',
            'appSupport' => $appSupportEntry
        ], 201);
    }

    /**
     * Update an existing "App Support" entry.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        // Log the request data and authenticated user details
        Log::info('Update App Support entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        // Validate the request
        $request->validate([
            'name' => 'required|string',
        ]);

        // Find the entry by ID
        $appSupportEntry = App_support::find($id);

        if (!$appSupportEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        // Update the entry with the new definition and update the "updated_by" field
        $appSupportEntry->update([
            'name' => $request->name,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,
        ]);

        Log::info('App Support entry updated:', ['entry' => $appSupportEntry]);

        return response()->json([
            'message' => 'Entry updated successfully.',
            'appSupport' => $appSupportEntry
        ], 200);
    }

    /**
     * Delete an App Support entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $authUser = request()->user();

        // Check if the authenticated user has the 'super-admin' or 'admin' role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the entry or fail if it doesn't exist
            $appSupportEntry = App_support::findOrFail($id);

            // Delete the entry
            $appSupportEntry->delete();

            Log::info('App Support entry deleted:', ['entry_id' => $id]);

            return response()->json(['message' => 'Entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this entry.'], 403);
    }
}
