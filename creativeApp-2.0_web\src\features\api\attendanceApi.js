import { useDispatch } from 'react-redux';
import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';


export const attendanceApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAttendance: builder.query({
      query: ({ entry_type='', sort_by = 'date', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `attendance?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}&entry_type=${entry_type}`;
        if (query) queryString += `&${query}`;
        return queryString;
      },
      providesTags: ['Attendance'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForAttendanceFilterBy: builder.query({
      query: ({ type = 'group', column = 'department_id', text = '' }) => {
        let queryString = `attendance-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['Attendance'],
    }),

    getAttendanceById: builder.query({
      query: (id) => {
        if (id == null || id == undefined) {
          id = "";
        }
        return `attendance/${id}`;
      },
      providesTags: (result, error, id) => [{ type: 'Attendance', id }],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createAttendanceRecord: builder.mutation({
      query: (newRecord) => ({
        url: 'attendance',
        method: 'POST',
        body: newRecord,
      }),
      async onQueryStarted(args, { queryFulfilled, dispatch }) {
        try {
          await queryFulfilled;
          alertMessage('created');
          dispatch(baseApi.util.invalidateTags(['Attendance', 'AttendanceByUser'])); // Invalidate all Attendance queries
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateAttendanceRecord: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `attendance/${id}`,
        method: 'PUT',
        body: data,
      }),
      // invalidatesTags: (result, error, { id }) => [{ type: 'Attendance', id }, 'Attendance','AttendanceByUser'],
      async onQueryStarted(args, { queryFulfilled, dispatch }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
          dispatch(baseApi.util.invalidateTags(['Attendance', 'AttendanceByUser'])); // Invalidate all Attendance queries
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteAttendanceRecord: builder.mutation({
      query: (id) => ({
        url: `attendance/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Attendance','AttendanceByUser'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),


    getAttendanceByUserId: builder.query({
      query: ({user_id="",date="", entry_type="", weeknum=""}) => {
        if (user_id == null || user_id == undefined) {
          user_id = "";
        }
        return `attendance-by-user?user_id=${user_id}&date=${date}&entry_type=${entry_type}&weeknum=${weeknum}`;
      },
      // providesTags: (result, error, id) => [{ type: 'Attendance', id }],
      providesTags: ['AttendanceByUser'],
      invalidatesTags: ['Attendance'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),


    getUserAttendanceByDate: builder.query({
      query: ({user_id="",date=""}) => {
        if (user_id == null || user_id == undefined) {
          user_id = "";
        }
        return `attendance-by-user-date?user_id=${user_id}&date=${date}`;
      },
      // providesTags: (result, error, id) => [{ type: 'Attendance', id }],
      providesTags: ['AttendanceByUser'],
      invalidatesTags: ['Attendance'],
      refetchOnMountOrArgChange: true, 
      keepUnusedDataFor: 0,
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    getUserAttendanceByID: builder.query({
      query: ({user_id="",date=""}) => {
        if (user_id == null || user_id == undefined) {
          user_id = "";
        }
        return `attendance-by-user-id?user_id=${user_id}&date=${date}`;
      },
      // providesTags: (result, error, id) => [{ type: 'Attendance', id }],
      providesTags: ['AttendanceByUser'],
      invalidatesTags: ['Attendance'],
      refetchOnMountOrArgChange: true, 
      keepUnusedDataFor: 0,
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),


  }),
});

export const {
  useGetAttendanceQuery,
  useLazyFetchDataOptionsForAttendanceFilterByQuery, //useLazyFetchDataOptionsForFilterByQuery
  useGetAttendanceByIdQuery,
  useGetAttendanceByUserIdQuery,
  useLazyGetAttendanceByIdQuery,
  useGetUserAttendanceByDateQuery,
  useGetUserAttendanceByIDQuery,
  useCreateAttendanceRecordMutation,
  useUpdateAttendanceRecordMutation,
  useDeleteAttendanceRecordMutation,
} = attendanceApi;
