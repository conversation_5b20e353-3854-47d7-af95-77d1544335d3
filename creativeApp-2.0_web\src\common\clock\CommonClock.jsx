import React, { useEffect, useState } from 'react';

const CommonClock = ({ onTimeData }) => {
    const [ipData, setIpData] = useState(null);
    const [time, setTime] = useState(null);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetch("http://ip-api.com/json/")
            .then((response) => response.json())
            .then((data) => {
                setIpData(data);
            })
            .catch((err) => setError(err));
    }, []);

    // Fetch time data based on latitude and longitude
    const fetchTime = async (latitude, longitude) => {
        try {
            const response = await fetch(`https://timeapi.io/api/time/current/coordinate?latitude=${latitude}&longitude=${longitude}`);
            const data = await response.json();
            setTime({
                year: data.year,
                month: data.month,
                day: data.day,
                hour: data.hour,
                minute: data.minute,
                seconds: data.seconds,
                timeZone: data.timeZone,
            });
        } catch (error) {
            console.error("Error fetching time data:", error);
        }
    };

    useEffect(() => {
        if (ipData) {
            const { lat, lon } = ipData;
            fetchTime(lat, lon);
        }
    }, [ipData]);

    useEffect(() => {
        if (!time) return; // Do nothing if time hasn't been fetched yet

        // Handle ticking of the clock every second
        const tick = () => {
            setTime((prevTime) => {
                let { year, month, day, hour, minute, seconds } = prevTime;

                seconds += 1;
                if (seconds >= 60) {
                    seconds = 0;
                    minute += 1;
                }
                if (minute >= 60) {
                    minute = 0;
                    hour += 1;
                }
                if (hour >= 24) {
                    hour = 0;
                    day += 1;
                }

                return { ...prevTime, hour, minute, seconds };
            });
        };

        const localInterval = setInterval(tick, 1000); // Update every second locally
        return () => clearInterval(localInterval); // Clean up the interval on unmount
    }, [time]);

    // Send time updates to parent via the callback
    useEffect(() => {
        if (time && onTimeData) {
            const formattedTime = `${time.hour}:${time.minute}:${time.seconds} ${time.timeZone}`;
            onTimeData({ formattedTime, timeZone: time.timeZone });
        }
    }, [time, onTimeData]);

    return null; // Don't render anything
};

export default CommonClock;
