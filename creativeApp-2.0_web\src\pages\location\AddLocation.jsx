import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddLocation = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [locations, setLocations] = useState([]); // Updated from billingStatuses
    const [locationName, setLocationName] = useState(''); // Updated from billingStatusName
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const fetchUsers = async () => {
        const token = localStorage.getItem('token');
        if (!token) {
            return;
        }
    
        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Failed to fetch user details');
            }
    
            const userData = await response.json();
            localStorage.setItem('fname', userData.first_name);
            localStorage.setItem('lname', userData.last_name);
    
        } catch (error) {
            console.error('Error fetching user details:', error);
        }
    };
    
    // Call fetchUsers after login or page load
    fetchUsers();
    

    useEffect(() => {
        const fetchLocations = async () => {  // Updated from fetchBillingStatuses
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');
    
            try {
                const response = await fetch(`${API_URL}/locations`, {  // Updated from location
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
                setLocations(data['locations'] || []);  // Updated from billing statuses
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchLocations();  // Updated from fetchBillingStatuses
    }, []);
    

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedLocationName = locationName.trim();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        // Check if the location already exists
        if (Array.isArray(locations)) {
            const locationExists = locations.some(location => {
                const locationNameLower = location.locations_name.toLowerCase().trim();
                return locationNameLower === trimmedLocationName.toLowerCase();
            });
    
            if (locationExists) {
                setError('Location already exists. Please add a different location.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
    
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Create the new location
            const response = await fetch(`${API_URL}/locations`, {  // Updated from billing_statuses endpoint
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    locations_name: trimmedLocationName,
                    created_by: createdBy,
                }),
            });
    
            if (!response.ok) {
                console.error('Failed to save location:', response.statusText);
                throw new Error('Failed to save location: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Location "${result.locations_name || trimmedLocationName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Location added successfully.',
            });

            setLocationName('');
    
            // Refetch locations after the new one is added
            const newLocationsResponse = await fetch(`${API_URL}/locations`, {  // Updated from billing_statuses
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newLocationsResponse.ok) {
                throw new Error('Failed to fetch locations: ' + newLocationsResponse.statusText);
            }
    
            const newLocationsData = await newLocationsResponse.json();
            setLocations(newLocationsData['locations'] || []);  // Updated from billing statuses
    
        } catch (error) {
            alertMessage('error');
            console.error('Error during submission:', error); // Debugging line
        }
    };
    
    
    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Location</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="locationName" className="block text-sm font-medium text-gray-700 pb-4">
                                Location Name {/* Updated label */}
                            </label>
                            <input
                                type="text"
                                id="locationName"
                                value={locationName}
                                onChange={(e) => setLocationName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3 hover:bg-blue-800"
                            >
                                Add Location {/* Updated button text */}
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddLocation;
