<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Todo extends Model
{
    use HasFactory;
    protected $fillable = [
        'title',
        'creator_id',
        'assignees',
        'priority_id',
        'due_date',
        'status_id',
        'details',
        'tag_id',        
    ];
    

    // Cast specific attributes to their appropriate data types
    protected $casts = [
        'assignees' => 'array', // Store as JSON array
        'tag_id' => 'array',    // Store as JSON array
    ];

    /**
     * Relationship: A Todo belongs to a Priority
     */
    public function priority()
    {
        return $this->belongsTo(Priority::class, 'priority_id')->select('name', 'id');
    }

    /**
     * Relationship: A Todo belongs to a Status
     */
    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id')->select('name', 'id');
    }
    /**
     * Relationship: A Todo belongs to a tags
     */
    public function tags()
    {
        // Decode the 'tag_id' array and fetch matching tags
        return TodoTag::whereIn('id', $this->tag_id)->get(['id', 'name']);
    }
    /**
     * Relationship: A Todo belongs to a assignees
     */
    public function assignees()
    {
        // Decode the 'assignees' array and fetch matching tags
        return User::whereIn('id', $this->assignees)->get(['id', 'photo']);
    }

    /**
     * Relationship: A Todo belongs to a Creator (User)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id')->select('id', 'fname', 'photo');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updater_id');
    }
    
    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
