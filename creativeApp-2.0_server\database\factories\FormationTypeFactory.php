<?php

namespace Database\Factories;

use App\Models\FormationType;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;
use App\Models\Team;
use App\Models\Department;
use Illuminate\Support\Facades\DB;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FormationType>
 */
class FormationTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FormationType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'title'       => $this->faker->name(),
            // 'slug'        => $this->faker->slug(),
            'short_title' => $this->faker->word(),
            // 'short_title' => $this->faker->randomElement([
            //     "Accuweather",
            //     "Bigtincan",
            //     "Bloomberg",
            //     "Boats",
            //     "CitrusAd",
            //     "Clipcentric",
            //     "Expedia",
            //     "Management",
            //     "Multiview",
            //     "Spiceworks"
            // ]),
            
            'details'     => $this->faker->paragraph(),
            'year' => now()->year,
            'alocated_leave' => rand(1, 10),
            'type'        => $this->faker->randomElement(["attendance", ""]),
            'is_active'   => $this->faker->randomElement(["active", "inactive"]),
            'team_id'  => Team::inRandomOrder()->value('id') ?? "",
            'department_id'  => Department::inRandomOrder()->value('id') ?? "",
            'created_by'  => User::inRandomOrder()->value('id') ?? "",
            'updated_by'  => User::inRandomOrder()->value('id') ?? ""
        ];
    }
}
