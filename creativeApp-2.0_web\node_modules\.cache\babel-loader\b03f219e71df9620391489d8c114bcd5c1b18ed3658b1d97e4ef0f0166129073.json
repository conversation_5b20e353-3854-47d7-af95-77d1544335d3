{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordGenerator.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordGenerator = ({\n  onPasswordGenerated\n}) => {\n  _s();\n  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');\n  const [length, setLength] = useState(64);\n  const [options, setOptions] = useState({\n    uppercase: true,\n    lowercase: true,\n    numbers: true,\n    symbols: true\n  });\n  const [strength, setStrength] = useState('Strong Password');\n  const generatePassword = () => {\n    let charset = '';\n    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';\n    if (options.numbers) charset += '0123456789';\n    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';\n    if (charset === '') {\n      setPassword('');\n      return;\n    }\n    let newPassword = '';\n    for (let i = 0; i < length; i++) {\n      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n    setPassword(newPassword);\n\n    // Calculate strength\n    const newStrength = calculatePasswordStrength(newPassword);\n    setStrength(newStrength);\n\n    // Notify parent component\n    if (onPasswordGenerated) {\n      onPasswordGenerated(newPassword, newStrength);\n    }\n  };\n  const calculatePasswordStrength = pwd => {\n    let score = 0;\n\n    // Length check\n    if (pwd.length >= 12) score += 2;else if (pwd.length >= 8) score += 1;\n\n    // Character variety checks\n    if (/[a-z]/.test(pwd)) score += 1;\n    if (/[A-Z]/.test(pwd)) score += 1;\n    if (/[0-9]/.test(pwd)) score += 1;\n    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;\n\n    // Additional complexity\n    if (pwd.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n  const getStrengthColor = () => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'text-green-600';\n      case 'Moderate Password':\n        return 'text-yellow-600';\n      case 'Weak Password':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(password);\n    // You could add a toast notification here\n  };\n  const handleOptionChange = option => {\n    const newOptions = {\n      ...options,\n      [option]: !options[option]\n    };\n    setOptions(newOptions);\n  };\n  const handleLengthChange = e => {\n    const newLength = parseInt(e.target.value);\n    setLength(newLength);\n  };\n\n  // Auto-generate when options change\n  useEffect(() => {\n    generatePassword();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [length, options]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 p-6 rounded-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold text-gray-900 dark:text-white mb-6\",\n      children: \"Password Manager\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: password,\n          readOnly: true,\n          className: \"flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-900 dark:text-white font-mono\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: copyToClipboard,\n          className: \"px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm font-medium transition-colors\",\n          children: \"Copy Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm font-medium ${getStrengthColor()}`,\n          children: strength\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: generatePassword,\n          className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded text-sm mr-1\",\n            children: \"refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), \"Generate New\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n          children: \"Password Length:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600 dark:text-gray-400\",\n          children: length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"range\",\n        min: \"4\",\n        max: \"128\",\n        value: length,\n        onChange: handleLengthChange,\n        className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"4 characters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"*Between 4 and 128 characters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"uppercase\",\n          type: \"checkbox\",\n          checked: options.uppercase,\n          onChange: () => handleOptionChange('uppercase'),\n          className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"uppercase\",\n          className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n          children: \"Uppercase (A-Z)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"lowercase\",\n          type: \"checkbox\",\n          checked: options.lowercase,\n          onChange: () => handleOptionChange('lowercase'),\n          className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"lowercase\",\n          className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n          children: \"Lowercase (a-z)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"numbers\",\n          type: \"checkbox\",\n          checked: options.numbers,\n          onChange: () => handleOptionChange('numbers'),\n          className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"numbers\",\n          className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n          children: \"Number (0-9)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"symbols\",\n          type: \"checkbox\",\n          checked: options.symbols,\n          onChange: () => handleOptionChange('symbols'),\n          className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"symbols\",\n          className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n          children: \"Symbols (!@#$%^&*)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordGenerator, \"S7FQOxsUEc+C8Pl+C38VgBkDDTk=\");\n_c = PasswordGenerator;\nexport default PasswordGenerator;\nvar _c;\n$RefreshReg$(_c, \"PasswordGenerator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "PasswordGenerator", "onPasswordGenerated", "_s", "password", "setPassword", "length", "<PERSON><PERSON><PERSON><PERSON>", "options", "setOptions", "uppercase", "lowercase", "numbers", "symbols", "strength", "setStrength", "generatePassword", "charset", "newPassword", "i", "char<PERSON>t", "Math", "floor", "random", "newStrength", "calculatePasswordStrength", "pwd", "score", "test", "getStrengthColor", "copyToClipboard", "navigator", "clipboard", "writeText", "handleOptionChange", "option", "newOptions", "handleLengthChange", "e", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "target", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "readOnly", "onClick", "min", "max", "onChange", "id", "checked", "htmlFor", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordGenerator.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst PasswordGenerator = ({ onPasswordGenerated }) => {\n  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');\n  const [length, setLength] = useState(64);\n  const [options, setOptions] = useState({\n    uppercase: true,\n    lowercase: true,\n    numbers: true,\n    symbols: true\n  });\n  const [strength, setStrength] = useState('Strong Password');\n\n  const generatePassword = () => {\n    let charset = '';\n    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';\n    if (options.numbers) charset += '0123456789';\n    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';\n\n    if (charset === '') {\n      setPassword('');\n      return;\n    }\n\n    let newPassword = '';\n    for (let i = 0; i < length; i++) {\n      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));\n    }\n\n    setPassword(newPassword);\n    \n    // Calculate strength\n    const newStrength = calculatePasswordStrength(newPassword);\n    setStrength(newStrength);\n    \n    // Notify parent component\n    if (onPasswordGenerated) {\n      onPasswordGenerated(newPassword, newStrength);\n    }\n  };\n\n  const calculatePasswordStrength = (pwd) => {\n    let score = 0;\n    \n    // Length check\n    if (pwd.length >= 12) score += 2;\n    else if (pwd.length >= 8) score += 1;\n    \n    // Character variety checks\n    if (/[a-z]/.test(pwd)) score += 1;\n    if (/[A-Z]/.test(pwd)) score += 1;\n    if (/[0-9]/.test(pwd)) score += 1;\n    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;\n    \n    // Additional complexity\n    if (pwd.length >= 16) score += 1;\n    \n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  const getStrengthColor = () => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'text-green-600';\n      case 'Moderate Password':\n        return 'text-yellow-600';\n      case 'Weak Password':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(password);\n    // You could add a toast notification here\n  };\n\n  const handleOptionChange = (option) => {\n    const newOptions = { ...options, [option]: !options[option] };\n    setOptions(newOptions);\n  };\n\n  const handleLengthChange = (e) => {\n    const newLength = parseInt(e.target.value);\n    setLength(newLength);\n  };\n\n  // Auto-generate when options change\n  useEffect(() => {\n    generatePassword();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [length, options]);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900 p-6 rounded-lg\">\n      <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">Password Manager</h2>\n      \n      {/* Generated Password Display */}\n      <div className=\"mb-4\">\n        <div className=\"flex items-center space-x-2 mb-2\">\n          <input\n            type=\"text\"\n            value={password}\n            readOnly\n            className=\"flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-900 dark:text-white font-mono\"\n          />\n          <button\n            onClick={copyToClipboard}\n            className=\"px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm font-medium transition-colors\"\n          >\n            Copy Password\n          </button>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className={`text-sm font-medium ${getStrengthColor()}`}>\n            {strength}\n          </span>\n          <button\n            onClick={generatePassword}\n            className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm\"\n          >\n            <span className=\"material-symbols-rounded text-sm mr-1\">refresh</span>\n            Generate New\n          </button>\n        </div>\n      </div>\n\n      {/* Password Length Slider */}\n      <div className=\"mb-4\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Password Length:\n          </label>\n          <span className=\"text-sm text-gray-600 dark:text-gray-400\">{length}</span>\n        </div>\n        <input\n          type=\"range\"\n          min=\"4\"\n          max=\"128\"\n          value={length}\n          onChange={handleLengthChange}\n          className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider\"\n        />\n        <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          <span>4 characters</span>\n          <span>*Between 4 and 128 characters</span>\n        </div>\n      </div>\n\n      {/* Character Options */}\n      <div className=\"space-y-3\">\n        <div className=\"flex items-center\">\n          <input\n            id=\"uppercase\"\n            type=\"checkbox\"\n            checked={options.uppercase}\n            onChange={() => handleOptionChange('uppercase')}\n            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n          />\n          <label htmlFor=\"uppercase\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n            Uppercase (A-Z)\n          </label>\n        </div>\n\n        <div className=\"flex items-center\">\n          <input\n            id=\"lowercase\"\n            type=\"checkbox\"\n            checked={options.lowercase}\n            onChange={() => handleOptionChange('lowercase')}\n            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n          />\n          <label htmlFor=\"lowercase\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n            Lowercase (a-z)\n          </label>\n        </div>\n\n        <div className=\"flex items-center\">\n          <input\n            id=\"numbers\"\n            type=\"checkbox\"\n            checked={options.numbers}\n            onChange={() => handleOptionChange('numbers')}\n            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n          />\n          <label htmlFor=\"numbers\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n            Number (0-9)\n          </label>\n        </div>\n\n        <div className=\"flex items-center\">\n          <input\n            id=\"symbols\"\n            type=\"checkbox\"\n            checked={options.symbols}\n            onChange={() => handleOptionChange('symbols')}\n            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n          />\n          <label htmlFor=\"symbols\" className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n            Symbols (!@#$%^&*)\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordGenerator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,6CAA6C,CAAC;EACvF,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC;IACrCa,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,iBAAiB,CAAC;EAE3D,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIT,OAAO,CAACE,SAAS,EAAEO,OAAO,IAAI,4BAA4B;IAC9D,IAAIT,OAAO,CAACG,SAAS,EAAEM,OAAO,IAAI,4BAA4B;IAC9D,IAAIT,OAAO,CAACI,OAAO,EAAEK,OAAO,IAAI,YAAY;IAC5C,IAAIT,OAAO,CAACK,OAAO,EAAEI,OAAO,IAAI,4BAA4B;IAE5D,IAAIA,OAAO,KAAK,EAAE,EAAE;MAClBZ,WAAW,CAAC,EAAE,CAAC;MACf;IACF;IAEA,IAAIa,WAAW,GAAG,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,EAAEa,CAAC,EAAE,EAAE;MAC/BD,WAAW,IAAID,OAAO,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGN,OAAO,CAACX,MAAM,CAAC,CAAC;IAC3E;IAEAD,WAAW,CAACa,WAAW,CAAC;;IAExB;IACA,MAAMM,WAAW,GAAGC,yBAAyB,CAACP,WAAW,CAAC;IAC1DH,WAAW,CAACS,WAAW,CAAC;;IAExB;IACA,IAAItB,mBAAmB,EAAE;MACvBA,mBAAmB,CAACgB,WAAW,EAAEM,WAAW,CAAC;IAC/C;EACF,CAAC;EAED,MAAMC,yBAAyB,GAAIC,GAAG,IAAK;IACzC,IAAIC,KAAK,GAAG,CAAC;;IAEb;IACA,IAAID,GAAG,CAACpB,MAAM,IAAI,EAAE,EAAEqB,KAAK,IAAI,CAAC,CAAC,KAC5B,IAAID,GAAG,CAACpB,MAAM,IAAI,CAAC,EAAEqB,KAAK,IAAI,CAAC;;IAEpC;IACA,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,OAAO,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;IACjC,IAAI,cAAc,CAACC,IAAI,CAACF,GAAG,CAAC,EAAEC,KAAK,IAAI,CAAC;;IAExC;IACA,IAAID,GAAG,CAACpB,MAAM,IAAI,EAAE,EAAEqB,KAAK,IAAI,CAAC;IAEhC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQf,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,gBAAgB;MACzB,KAAK,mBAAmB;QACtB,OAAO,iBAAiB;MAC1B,KAAK,eAAe;QAClB,OAAO,cAAc;MACvB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC7B,QAAQ,CAAC;IACvC;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG;MAAE,GAAG5B,OAAO;MAAE,CAAC2B,MAAM,GAAG,CAAC3B,OAAO,CAAC2B,MAAM;IAAE,CAAC;IAC7D1B,UAAU,CAAC2B,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMC,SAAS,GAAGC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACC,KAAK,CAAC;IAC1CnC,SAAS,CAACgC,SAAS,CAAC;EACtB,CAAC;;EAED;EACAzC,SAAS,CAAC,MAAM;IACdkB,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAACV,MAAM,EAAEE,OAAO,CAAC,CAAC;EAErB,oBACER,OAAA;IAAK2C,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBACvD5C,OAAA;MAAI2C,SAAS,EAAC,uDAAuD;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAG3FhD,OAAA;MAAK2C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5C,OAAA;QAAK2C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C5C,OAAA;UACEiD,IAAI,EAAC,MAAM;UACXP,KAAK,EAAEtC,QAAS;UAChB8C,QAAQ;UACRP,SAAS,EAAC;QAAmJ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9J,CAAC,eACFhD,OAAA;UACEmD,OAAO,EAAErB,eAAgB;UACzBa,SAAS,EAAC,yGAAyG;UAAAC,QAAA,EACpH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNhD,OAAA;QAAK2C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD5C,OAAA;UAAM2C,SAAS,EAAE,uBAAuBd,gBAAgB,CAAC,CAAC,EAAG;UAAAe,QAAA,EAC1D9B;QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPhD,OAAA;UACEmD,OAAO,EAAEnC,gBAAiB;UAC1B2B,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBAEjG5C,OAAA;YAAM2C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAExE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK2C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5C,OAAA;QAAK2C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5C,OAAA;UAAO2C,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAAC;QAExE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRhD,OAAA;UAAM2C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAEtC;QAAM;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACNhD,OAAA;QACEiD,IAAI,EAAC,OAAO;QACZG,GAAG,EAAC,GAAG;QACPC,GAAG,EAAC,KAAK;QACTX,KAAK,EAAEpC,MAAO;QACdgD,QAAQ,EAAEjB,kBAAmB;QAC7BM,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG,CAAC,eACFhD,OAAA;QAAK2C,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBACjF5C,OAAA;UAAA4C,QAAA,EAAM;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzBhD,OAAA;UAAA4C,QAAA,EAAM;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB5C,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UACEuD,EAAE,EAAC,WAAW;UACdN,IAAI,EAAC,UAAU;UACfO,OAAO,EAAEhD,OAAO,CAACE,SAAU;UAC3B4C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,WAAW,CAAE;UAChDS,SAAS,EAAC;QAAqL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChM,CAAC,eACFhD,OAAA;UAAOyD,OAAO,EAAC,WAAW;UAACd,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAErF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UACEuD,EAAE,EAAC,WAAW;UACdN,IAAI,EAAC,UAAU;UACfO,OAAO,EAAEhD,OAAO,CAACG,SAAU;UAC3B2C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,WAAW,CAAE;UAChDS,SAAS,EAAC;QAAqL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChM,CAAC,eACFhD,OAAA;UAAOyD,OAAO,EAAC,WAAW;UAACd,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAErF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UACEuD,EAAE,EAAC,SAAS;UACZN,IAAI,EAAC,UAAU;UACfO,OAAO,EAAEhD,OAAO,CAACI,OAAQ;UACzB0C,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,SAAS,CAAE;UAC9CS,SAAS,EAAC;QAAqL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChM,CAAC,eACFhD,OAAA;UAAOyD,OAAO,EAAC,SAAS;UAACd,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5C,OAAA;UACEuD,EAAE,EAAC,SAAS;UACZN,IAAI,EAAC,UAAU;UACfO,OAAO,EAAEhD,OAAO,CAACK,OAAQ;UACzByC,QAAQ,EAAEA,CAAA,KAAMpB,kBAAkB,CAAC,SAAS,CAAE;UAC9CS,SAAS,EAAC;QAAqL;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChM,CAAC,eACFhD,OAAA;UAAOyD,OAAO,EAAC,SAAS;UAACd,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CA/MIF,iBAAiB;AAAAyD,EAAA,GAAjBzD,iBAAiB;AAiNvB,eAAeA,iBAAiB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}