import React, { useState, useCallback, useEffect } from "react";

// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";

// Loading spinner component to show while data is loading
import Loading from "./../../common/Loading";

import {confirmation<PERSON><PERSON><PERSON>, ManageColumns, SearchFilter, TableView} from './../../common/coreui';


import { useDispatch } from "react-redux";
import { defaultDateTimeFormat, removeKeys, sortByLabel } from "./../../utils";

// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import { bloodGroupApi, useDeleteBloodGroupMutation, useGetBloodGroupDataQuery, useLazyFetchDataOptionsForBloodGroupQuery } from "./../../features/api";
import { useNavigate } from "react-router-dom";
import EditBlood from "./EditBlood";
import AddBlood from "./AddBlood";
import { useRoleBasedAccess } from "./../../common/useRoleBasedAccess";
import { DateTimeFormatDay, DateTimeFormatHour } from "../../common/DateTimeFormatTable";

// API endpoint and configuration constants
const MODULE_NAME = "Blood Group";

// Main component for listing Product Type List
const BloodGroupDataList = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [dataItemsId, setDataItemsId] = useState(null);
  const [error, setError] = useState(null);
  const [viewData, setViewData] = useState(null);
  const navigate = useNavigate();
  const [addModalVisible, setAddModalVisible] = useState(false);

  
  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);

  
  const { data: dataItems, isFetching, error: fetchError } = useGetBloodGroupDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });

  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForBloodGroupQuery();
       
  const [deleteBloodGroup] = useDeleteBloodGroupMutation();

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "")

    setQueryString(q);
  }

  const handleCopy = (data) => {
    const keysToRemove = ["id", "team", "department", "updated_at", "updated_by", "updater", "created_at", "creator", "created_by", "updated_by"];
    const cleanedData = removeKeys(data, keysToRemove);
    setViewData(null)
    setModalVisible(true);
  }

  const handleEdit = (id) => {
    setViewData(null)
    setDataItemsId(id); 
    setModalVisible(true);
  }

  const handleDelete = (id) => {
    confirmationAlert({onConfirm: () => 
      {        
        deleteBloodGroup(id);
        setViewData(null);
      }});  
  }
 

  let columnSerial = 1;

  const { rolePermissions } = useRoleBasedAccess();

  // Define columns dynamically based on rolePermissions
  const [columns, setColumns] = useState(() => [
    {
        id: columnSerial++,
      name: "Action",
      width: "180px",
      className: "bg-red-300",
      cell: (item) => (
        <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
          {/* View Button */}
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
            onClick={() => setViewData(item)}
          >
            <span className="material-symbols-outlined text-lg">visibility</span>
          </button>
  
          {/* Conditionally render Edit Button based on rolePermissions */}
          {rolePermissions?.hasManagerRole && (
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
              onClick={() => handleEdit(item.id)}
            >
              <span className="material-symbols-outlined text-lg">stylus_note</span>
            </button>
          )}
  
          {/* Copy Button */}
          {rolePermissions?.hasManagerRole && (
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
              onClick={() => handleCopy(item)}
            >
              <span className="material-symbols-outlined text-lg">content_copy</span>
            </button>
          )}
  
          {/* Delete Button */}
          {rolePermissions?.hasManagerRole && (
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
              onClick={() => handleDelete(item.id)}
            >
              <span className="material-symbols-outlined text-sm">delete</span>
            </button>
          )}
        </div>
      ),
    },
    {
        id: columnSerial++,
      name: "S.No",
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },
    {
        id: columnSerial++,
      name: "Resource Status Name",
      db_field: "name",
      selector: (row) => row.name || "",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
        id: columnSerial++,
        name: "Created by",
        selector: (row) => `${row.creator?.fname || ""} ${row.creator?.lname || ""}`,
        db_field: "created_by",
        omit: false,
        sortable: true,
        filterable: true,
    },
    {
      id: columnSerial++,
      name: "Created Date",
      selector: (row) => DateTimeFormatDay(row.created_at),
      db_field: "created_at",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
        id: columnSerial++,
        name: "Created Time",
        selector: (row) => DateTimeFormatHour(row.created_at),
        db_field: "created_at",
        omit: false,
        sortable: true,
        filterable: false,
      },
      {
        id: columnSerial++,
        name: "Updated by",
        selector: (row) => `${row.updater?.fname || ""} ${row.updater?.lname || ""}`,
        db_field: "updated_by",
        omit: false,
        sortable: true,
        filterable: true,
      },
    {
      id: columnSerial++,
      name: "Updated Date",
      selector: (row) => DateTimeFormatDay(row.updated_at),
      db_field: "updated_at",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
        id: columnSerial++,
        name: "Updated Time",
        selector: (row) => DateTimeFormatHour(row.updated_at),
        db_field: "updated_at",
        omit: false,
        sortable: true,
        filterable: false,
    },
  ]);
  
  useEffect(() => {
    // Recalculate or update columns if rolePermissions change
    setColumns((prevColumns) => [
      ...prevColumns.map((col) => {
        if (col.name === "Action") {
          // Update the "Action" column dynamically
          return {
            ...col,
            cell: (item) => (
              <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => setViewData(item)}
                >
                  <span className="material-symbols-outlined text-lg">visibility</span>
                </button>
                {rolePermissions?.hasManagerRole && (
                  <button
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    onClick={() => handleEdit(item.id)}
                  >
                    <span className="material-symbols-outlined text-lg">stylus_note</span>
                  </button>
                )}

                {rolePermissions?.hasManagerRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => handleCopy(item)}
                >
                  <span className="material-symbols-outlined text-lg">content_copy</span>
                </button>
                )}

                {rolePermissions?.hasManagerRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => handleDelete(item.id)}
                >
                  <span className="material-symbols-outlined text-sm">delete</span>
                </button>
                )}
              </div>
            ),
          };
        }
        return col;
      }),
    ]);
  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes
  
  

  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).map((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj })
    }
    setCurrentPage(1);
  };


  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      // Fetch all data items for Excel export
      const result = await dispatch(
        bloodGroupApi.endpoints.getBloodGroupData.initiate({
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues
          query: queryString,
        })
      ).unwrap(); // Wait for the API response
  
      if (!result?.total || result.total < 1) {
        return false;
      }
  
      var sl = 1;
  
      let prepXlsData = result.data.map((item) => {
        if (columns.length) {
          let obj = {};
          columns.forEach((column) => {
            if (!column.omit && column.selector) {
              obj[column.name] = column.name === "S.No" ? sl++ : column.selector(item) || "";
            }
          });
          return obj;
        }
      });
  
      // Create a worksheet from the JSON data and append to a new workbook
      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  
      // Convert workbook to a buffer and create a Blob to trigger a file download
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(blob, `${MODULE_NAME.replace(/ /g,"_")}_${prepXlsData.length}.xlsx`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };
  

  /**
   * Fetch filter options from API for a specific field.
   */
  const fetchDataOptionsForFilterBy = useCallback(
    async (
      itemObject = {},
      type = "group",
      searching = "",
      fieldType = "select"
    ) => {

      let groupByField = itemObject.db_field || "title";

      try {
        setShowFilterOption(groupByField);
        setFilterOptionLoading(true);

        var groupData = [];

        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });
        
        if (response.data) {
          groupData = response.data;
        }

        if (groupData.length) {

          if (fieldType === "searchable") {
            setFilterOptions((prev) => ({
              ...prev,
              [groupByField]: groupData,
            }));

            return groupData;
          }

          const optionsForFilter = groupData
            .map((item) => {
              if(itemObject.selector){
                let label = itemObject.selector(item);

                if(label){
                  if (item.total && item.total > 1) {
                    label += ` (${item.total})`;
                  }

                  return { label, value: item[groupByField] };
                }

              return null;
              }
            }).filter(Boolean);

          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: sortByLabel(optionsForFilter),
          }));

          return optionsForFilter;
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setFilterOptionLoading(false);
      }
    },
    []
  );

  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      <div className="mx-auto pb-6 ">
        {/* Header section with title and action buttons */}
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div className="w-4/12 md:w-10/12 text-start">
            <h2 className="text-2xl font-bold ">{MODULE_NAME}</h2>
          </div>
          <div className="w-8/12 flex items-end justify-end gap-1">
            {/* Manage Columns dropdown */}
            <ManageColumns columns={columns} setColumns={setColumns} />
            
            {/* Export to Excel button, only shown if data exists */}
            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (
              <>
                <button
                  className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={exportToExcel}
                >
                  {isFetching && (
                    <>
                      <span className="material-symbols-outlined animate-spin text-sm me-2">
                        progress_activity
                      </span>
                    </>
                  )}
                  {!isFetching && (
                    <span className="material-symbols-outlined text-sm me-2">
                    file_export
                    </span>
                  )}
                  Export to Excel ({dataItems.total})
                </button>
              </>
            )}
            {/* Button to open modal for adding a new formation */}
            {rolePermissions.hasManagerRole && (
              <button
                className=" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"

                onClick={() => setAddModalVisible(true)}
              >
                Add New
              </button>
            )}
          </div>
        </div>

        {/* Filter fieldset for global search and field-specific filtering */}
        <SearchFilter
            columns={columns}
            selectedFilterOptions={selectedFilterOptions}
            setSelectedFilterOptions={setSelectedFilterOptions}
            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}
            filterOptions={filterOptions}
            filterOptionLoading={filterOptionLoading}
            showFilterOption={showFilterOption}
            resetPage={resetPage}
            setCurrentPage={setCurrentPage}
            buildQueryParams={buildQueryParams}
        />

        {/* Display error message if any error occurs */}
        {fetchError && <div className="text-red-500">{error}</div>}
        {/* Show loading spinner when data is being fetched */}
        {isFetching && <Loading />}

        {/* If no data is available, display an alert message */}
        
        {/* Render the DataTable with the fetched data */}
        <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
          <DataTable
            columns={columns}
            data={dataItems?.data || []}
            className="p-0 scrollbar-horizontal-10"
            fixedHeader
            
            highlightOnHover
            responsive
            pagination
            paginationServer
            paginationPerPage={perPage}
            paginationTotalRows={dataItems?.total || 0}
            onChangePage={(page) => {
              if (page !== currentPage) {
                setCurrentPage(page);
              }
            }}
            onChangeRowsPerPage={(newPerPage) => {
              if(newPerPage !== perPage){
                setPerPage(newPerPage);
                setCurrentPage(1);
              }
            }}
            paginationComponentOptions={{
              selectAllRowsItem: true,
              selectAllRowsItemText: "ALL",
            }}
            sortServer
            onSort={(column, sortDirection="desc") => {
              if(Object.keys(column).length){
                setSortColumn(column.db_field || column.name || "created_at");
                setSortDirection(sortDirection || "desc");
              }
            }}
          />
        </div>

        {/* Add Modal */}
        {addModalVisible && (
            <AddBlood
                isVisible={addModalVisible}
                setVisible={setAddModalVisible}
            />
        )}

        {/* Conditionally render the Edit modal */}
        {modalVisible && (
          <EditBlood
            isVisible={modalVisible}
            setVisible={setModalVisible}
            dataItemsId={dataItemsId}
          />
        )}

        {viewData && (
          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
        )}
       
      </div>
    </section>
  );
};


export default BloodGroupDataList;
