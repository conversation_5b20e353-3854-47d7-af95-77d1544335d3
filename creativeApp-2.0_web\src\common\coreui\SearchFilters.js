import React from "react";
import Select from "react-select";

export const SearchFilter = ({
  columns,
  selectedFilterOptions,
  setSelectedFilterOptions,
  fetchDataOptionsForFilterBy,
  filterOptions,
  filterOptionLoading,
  showFilterOption,
  resetPage,
  setCurrentPage,
  buildQueryParams,
}) => {

    
  return (
    <fieldset className="w-full border border-gray-200 text-start rounded-lg pb-3 relative z-10">
      <legend className="ms-5 font-semibold text-sm py-1 px-2 flex">
        <span className="material-symbols-outlined me-1">manage_search</span>
        Finder
      </legend>
      <div className="flex flex-wrap items-start justify-start gap-1 p-2 max-w-[80vw] pr-64 scrollbar-horizontal">
        {/* Global search input */}
        <div className="w-auto">
        <input
          onChange={(e) => {
            setSelectedFilterOptions({
              ...selectedFilterOptions,
              globalsearch: e.target.value,
            });
          }}
          placeholder="Global Search for all data"
          value={selectedFilterOptions["globalsearch"] || ""}
          className="w-auto h-[40px] py-2 px-4 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
        />
        </div>

        {/* Render field-specific filters for columns */}
        {columns.length > 0 &&
          columns.map(
            (item) =>
              item.filterable && (
                <div key={"column-" + item.id} className="w-auto">
                  {item.filterable === "searchable" ? (
                    <>
                      <input
                        onChange={(e) => {
                          let searchText = e.target.value;
                          setSelectedFilterOptions({
                            ...selectedFilterOptions,
                            [item.id]: searchText,
                          });
                          if (searchText.length > 2) {
                            fetchDataOptionsForFilterBy(item, "field", searchText, item.filterable);
                          }
                        }}
                        list={"datalist-" + item.id}
                        placeholder={item.name}
                        value={selectedFilterOptions[item.id] || ""}
                        className="w-[250px] h-[40px] py-2 px-4 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      />
                      <datalist id={"datalist-" + item.id}>
                        {filterOptions[item.id]?.map((opt) => (
                          <option key={opt.id} value={opt.title}>{opt.title}</option>
                        ))}
                      </datalist>
                    </>
                  ) : !filterOptions[item.id] ? (
                    <button
                      onClick={() => fetchDataOptionsForFilterBy(item)}
                      className="w-auto whitespace-nowrap h-[40px] py-2 px-4 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    >
                      <span dangerouslySetInnerHTML={{__html: item.name}}></span>
                      {filterOptionLoading && showFilterOption === item.id && (
                        <span className="material-symbols-outlined animate-spin text-sm ms-2">progress_activity</span>
                      )}
                    </button>
                  ) : (
                    <Select
                      closeMenuOnSelect={false}
                      isClearable
                      isSearchable
                      isDisabled={filterOptionLoading}
                      isMulti
                      placeholder={item.name}
                      name={item.name}
                      value={selectedFilterOptions[item.db_field]}
                      options={filterOptions[item.id]}
                      onChange={(items) => setSelectedFilterOptions({ ...selectedFilterOptions, [item.db_field]: items })}
                      className="w-auto h-[40px] min-w-24 filter-search capitalize"
                    />
                  )}
                </div>
              )
          )}

          <div className="flex flex-row items-start justify-center absolute top-1 right-4 bg-white p-1 gap-1 rounded-lg">
            {/* Clear All button */}
            <div className="w-auto">
              <button
                onClick={resetPage}
                className="w-auto flex justify-center whitespace-nowrap items-center text-center h-[40px] py-1 px-4 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              >
                <span className="material-symbols-outlined">filter_alt_off</span>
                Clear All
              </button>
            </div>

            {/* Submit button */}
            <div className="w-auto">
              <button
                className="w-[80px] h-[40px] py-2 px-4 text-sm font-medium text-white bg-primary rounded-lg border border-gray-200 hover:bg-dark-100 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-primary"
                onClick={() => {
                  setCurrentPage(1);
                  buildQueryParams(selectedFilterOptions);
                }}
              >
                Submit
              </button>
            </div>
          </div>

      </div>
    </fieldset>
  );
};
