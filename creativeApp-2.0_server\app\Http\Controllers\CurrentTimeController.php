<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;

class CurrentTimeController extends Controller
{
    public function getCurrentTime()
    {
        // Get the current time using Carbon (from the server's time zone)
        $currentTime = Carbon::now();

        // Return the time in a format that React can use
        return response()->json([
            'current_time' => $currentTime->toIso8601String()  // ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ)
        ]);
    }
}
