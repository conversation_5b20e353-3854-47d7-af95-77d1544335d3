import React, { useState } from 'react';
import AddPasswordForm from './AddPasswordForm';

const PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {
  // Sample data - in real app this would come from API/state management
  const [passwordCards, setPasswordCards] = useState([
    {
      id: 1,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Weak Password',
      strengthColor: 'bg-red-100 text-red-600 border-red-300'
    },
    {
      id: 2,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'StrongPass123!@#',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Strong Password',
      strengthColor: 'bg-green-100 text-green-600 border-green-300'
    },
    {
      id: 3,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'ModeratePass456',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Moderate Password',
      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'
    },
    {
      id: 4,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'WeakPass',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Weak Password',
      strengthColor: 'bg-red-100 text-red-600 border-red-300'
    },
    {
      id: 5,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'AnotherStrongPass789!',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Strong Password',
      strengthColor: 'bg-green-100 text-green-600 border-green-300'
    },
    {
      id: 6,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'ModerateSecure123',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Moderate Password',
      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'
    },
    {
      id: 7,
      title: 'Platform Name',
      username: '<EMAIL>',
      password: '••••••••••••',
      actualPassword: 'VeryWeakPass',
      team: 'Team Name',
      department: 'Department name',
      strength: 'Weak Password',
      strengthColor: 'bg-red-100 text-red-600 border-red-300'
    }
  ]);

  const [visiblePasswords, setVisiblePasswords] = useState({});
  const [selectedCards, setSelectedCards] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);

  const togglePasswordVisibility = (id) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const toggleCardSelection = (id) => {
    setSelectedCards(prev => 
      prev.includes(id) 
        ? prev.filter(cardId => cardId !== id)
        : [...prev, id]
    );
  };

  const toggleSelectAll = () => {
    if (selectedCards.length === passwordCards.length) {
      setSelectedCards([]);
    } else {
      setSelectedCards(passwordCards.map(card => card.id));
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleEdit = (id) => {
    console.log('Edit password card:', id);
    // Navigate to edit form or open modal
  };

  const handleDelete = (id) => {
    console.log('Delete password card:', id);
    // Show confirmation dialog and delete
    setPasswordCards(prev => prev.filter(card => card.id !== id));
  };

  const handleDeleteSelected = () => {
    if (selectedCards.length > 0) {
      setPasswordCards(prev => prev.filter(card => !selectedCards.includes(card.id)));
      setSelectedCards([]);
    }
  };

  const handleAddPasswordCard = (newCard) => {
    const cardWithId = {
      ...newCard,
      id: Math.max(...passwordCards.map(c => c.id)) + 1,
      password: '••••••••••••',
      actualPassword: newCard.password
    };
    setPasswordCards(prev => [...prev, cardWithId]);
    setShowAddForm(false);
  };

  // Placeholder avatar images
  const avatarImages = [
    'https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A',
    'https://via.placeholder.com/32x32/10B981/FFFFFF?text=B',
    'https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C',
    'https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D'
  ];

  return (
    <div className="bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6">
        <div className="w-full md:w-1/2">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Teams Password Card</h2>
        </div>
        <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
          {/* Share Icon */}
          <button className="flex items-center justify-center w-10 h-10 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
            <span className="material-symbols-rounded">share</span>
          </button>
          
          {/* Delete Selected Icon */}
          <button 
            onClick={handleDeleteSelected}
            disabled={selectedCards.length === 0}
            className={`flex items-center justify-center w-10 h-10 rounded-lg ${
              selectedCards.length > 0 
                ? 'text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20' 
                : 'text-gray-300 cursor-not-allowed dark:text-gray-600'
            }`}
          >
            <span className="material-symbols-rounded">delete</span>
          </button>
          
          {/* User Avatars */}
          <div className="flex -space-x-2">
            {avatarImages.map((avatar, index) => (
              <img
                key={index}
                src={avatar}
                alt={`User ${index + 1}`}
                className="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800"
              />
            ))}
          </div>
          
          {/* Add Password Card Button */}
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
          >
            <span className="material-symbols-rounded mr-2">add</span>
            Add Password Card
          </button>
        </div>
      </div>

      {/* Add Password Form - Embedded */}
      {showAddForm && (
        <div className="mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Add New Password Card</h3>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <span className="material-symbols-rounded">close</span>
            </button>
          </div>
          <AddPasswordForm 
            onSubmit={handleAddPasswordCard}
            onCancel={() => setShowAddForm(false)}
            generatedPassword={generatedPassword}
            passwordStrength={passwordStrength}
          />
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
          <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
              <th scope="col" className="p-4">
                <div className="flex items-center">
                  <input
                    id="checkbox-all"
                    type="checkbox"
                    checked={selectedCards.length === passwordCards.length}
                    onChange={toggleSelectAll}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label htmlFor="checkbox-all" className="sr-only">checkbox</label>
                </div>
              </th>
              <th scope="col" className="px-6 py-3">Title</th>
              <th scope="col" className="px-6 py-3">User Name</th>
              <th scope="col" className="px-6 py-3">Password</th>
              <th scope="col" className="px-6 py-3">Team</th>
              <th scope="col" className="px-6 py-3">Department</th>
              <th scope="col" className="px-6 py-3">Level</th>
              <th scope="col" className="px-6 py-3">Action</th>
            </tr>
          </thead>
          <tbody>
            {passwordCards.map((card) => (
              <tr key={card.id} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td className="w-4 p-4">
                  <div className="flex items-center">
                    <input
                      id={`checkbox-table-${card.id}`}
                      type="checkbox"
                      checked={selectedCards.includes(card.id)}
                      onChange={() => toggleCardSelection(card.id)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <label htmlFor={`checkbox-table-${card.id}`} className="sr-only">checkbox</label>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3">
                      TG
                    </div>
                    <span className="font-medium text-gray-900 dark:text-white">{card.title}</span>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <span className="text-gray-900 dark:text-white">{card.username}</span>
                    <button
                      onClick={() => copyToClipboard(card.username)}
                      className="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <span className="material-symbols-rounded text-sm">content_copy</span>
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <span className="text-gray-900 dark:text-white mr-2">
                      {visiblePasswords[card.id] ? card.actualPassword : card.password}
                    </span>
                    <button
                      onClick={() => togglePasswordVisibility(card.id)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2"
                    >
                      <span className="material-symbols-rounded text-sm">
                        {visiblePasswords[card.id] ? 'visibility_off' : 'visibility'}
                      </span>
                    </button>
                    <button
                      onClick={() => copyToClipboard(card.actualPassword)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <span className="material-symbols-rounded text-sm">content_copy</span>
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 text-gray-900 dark:text-white">{card.team}</td>
                <td className="px-6 py-4 text-gray-900 dark:text-white">{card.department}</td>
                <td className="px-6 py-4">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`}>
                    {card.strength}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(card.id)}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      <span className="material-symbols-rounded text-sm">edit</span>
                    </button>
                    <button
                      onClick={() => handleDelete(card.id)}
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <span className="material-symbols-rounded text-sm">delete</span>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add New Password Card Button (Bottom) */}
      <div className="flex justify-center mt-6">
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="flex items-center justify-center px-6 py-3 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
        >
          <span className="material-symbols-rounded mr-2">add</span>
          Add New Password Card
        </button>
      </div>
    </div>
  );
};

export default PasswordCardsTable;
