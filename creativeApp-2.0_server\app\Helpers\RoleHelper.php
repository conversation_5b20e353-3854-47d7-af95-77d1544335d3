<?php

namespace App\Helpers;

class RoleHelper
{
    private static array $allowedRoles = [
        'superAdmin' => ['super-admin'],
        'admin' => ['super-admin', 'admin'],
        'hod' => ['super-admin', 'admin', 'hod'],
        'manager' => ['super-admin', 'admin', 'hod', 'manager'],
        'teamLead' => ['super-admin', 'admin', 'hod', 'manager', 'team-lead'],
        'coordinator' => ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator'],
        'shiftLead' => ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],
        'teamMember' => ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],
    ];

    /**
     * Check if the user has at least one allowed role.
     *
     * @param array $userRoles
     * @param string $roleKey
     * @return bool
     */
    public static function hasAccess(array $userRoles, string $roleKey): bool
    {
        if (!isset(self::$allowedRoles[$roleKey])) {
            return false; // Invalid role key
        }

        return !empty(array_intersect($userRoles, self::$allowedRoles[$roleKey]));
    }

    public static function getRoleList(string $roleKey): Array
    {
        if (!isset(self::$allowedRoles[$roleKey])) {
            return false; // Invalid role key
        }

        return self::$allowedRoles[$roleKey];
    }
}
