import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const attendanceFormationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getFormationTypes: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `formation-types?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['FormationTypes'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForFilterBy: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `formation-types-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['FormationTypes'],
    }),

    getFormationTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `formation-types/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'FormationTypes', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createFormationType: builder.mutation({
      query: (newFormationType) => ({
        url: 'formation-types',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['FormationTypes'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateFormationType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `formation-types/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'FormationTypes', id }, 'FormationTypes'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteFormationType: builder.mutation({
      query: (id) => ({
        url: `formation-types/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['FormationTypes'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetFormationTypesQuery, // getFormationTypes
  useLazyFetchDataOptionsForFilterByQuery,
  useGetFormationTypeByIdQuery,
  useLazyGetFormationTypeByIdQuery,
  useCreateFormationTypeMutation,
  useUpdateFormationTypeMutation,
  useDeleteFormationTypeMutation,
} = attendanceFormationApi;
