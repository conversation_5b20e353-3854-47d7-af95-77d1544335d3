<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Database\Seeders\UserSeeder;
use Database\Seeders\AttendanceSeeder;
use Database\Seeders\AttendanceTypeSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            UserSeeder::class,
            AttendanceTypeSeeder::class,
            AttendanceSeeder::class,
            SchedulePlannerSeeder::class,
            NoticeSeeder::class,
            SeatSeeder::class,
        ]);
    }
}
