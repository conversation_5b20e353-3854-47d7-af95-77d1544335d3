
import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import BloodList from '../../pages/blood/BloodList';
import NoticeBoardCategoryList from '../../pages/settings/noticeboardcategory/NoticeBoardCategoryList';

const NoticeBoardCategory = () => {
  return (
    <>
    
   
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-notice-category" buttonName="Add Notice Category" />
        <NoticeBoardCategoryList/>
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
    </>
  );
};

export default NoticeBoardCategory;
