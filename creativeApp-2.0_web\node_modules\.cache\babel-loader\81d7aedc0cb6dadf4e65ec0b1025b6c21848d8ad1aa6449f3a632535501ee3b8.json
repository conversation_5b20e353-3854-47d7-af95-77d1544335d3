{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordManagerDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordManagerDashboard = () => {\n  _s();\n  const navigate = useNavigate();\n\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }, {\n    id: 2,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'StrongPass123!@#',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Strong Password',\n    strengthColor: 'bg-green-100 text-green-600 border-green-300'\n  }, {\n    id: 3,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'ModeratePass456',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Moderate Password',\n    strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n  }, {\n    id: 4,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'WeakPass',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }, {\n    id: 5,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'AnotherStrongPass789!',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Strong Password',\n    strengthColor: 'bg-green-100 text-green-600 border-green-300'\n  }, {\n    id: 6,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'ModerateSecure123',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Moderate Password',\n    strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n  }, {\n    id: 7,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'VeryWeakPass',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [selectedCards, setSelectedCards] = useState([]);\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const toggleCardSelection = id => {\n    setSelectedCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n  const toggleSelectAll = () => {\n    if (selectedCards.length === passwordCards.length) {\n      setSelectedCards([]);\n    } else {\n      setSelectedCards(passwordCards.map(card => card.id));\n    }\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n  const handleEdit = id => {\n    console.log('Edit password card:', id);\n    // Navigate to edit form or open modal\n  };\n  const handleDelete = id => {\n    console.log('Delete password card:', id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 p-6 rounded-xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-1/2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n          children: \"Teams Password Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/add-password-card'),\n          className: \"flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded mr-2\",\n            children: \"add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), \"Add Password Card\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"w-full text-sm text-left text-gray-500 dark:text-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: selectedCards.length === passwordCards.length,\n                  onChange: toggleSelectAll,\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"checkbox-all\",\n                  className: \"sr-only\",\n                  children: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"User Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Action\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: passwordCards.map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"w-4 p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `checkbox-table-${card.id}`,\n                  type: \"checkbox\",\n                  checked: selectedCards.includes(card.id),\n                  onChange: () => toggleCardSelection(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `checkbox-table-${card.id}`,\n                  className: \"sr-only\",\n                  children: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                  children: \"TG\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: card.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900 dark:text-white\",\n                  children: card.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => copyToClipboard(card.username),\n                  className: \"ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"content_copy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900 dark:text-white mr-2\",\n                  children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => togglePasswordVisibility(card.id),\n                  className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: visiblePasswords[card.id] ? 'visibility_off' : 'visibility'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => copyToClipboard(card.actualPassword),\n                  className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"content_copy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 text-gray-900 dark:text-white\",\n              children: card.team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 text-gray-900 dark:text-white\",\n              children: card.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`,\n                children: card.strength\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(card.id),\n                  className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(card.id),\n                  className: \"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, card.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/add-password-card'),\n        className: \"flex items-center justify-center px-6 py-3 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordManagerDashboard, \"6TCq6sMLf0xxZJ1N3QxY97OkncU=\", false, function () {\n  return [useNavigate];\n});\n_c = PasswordManagerDashboard;\nexport default PasswordManagerDashboard;\nvar _c;\n$RefreshReg$(_c, \"PasswordManagerDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "PasswordManagerDashboard", "_s", "navigate", "passwordCards", "setPasswordCards", "id", "title", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "visiblePasswords", "setVisiblePasswords", "selectedCards", "setSelectedCards", "togglePasswordVisibility", "prev", "toggleCardSelection", "includes", "filter", "cardId", "toggleSelectAll", "length", "map", "card", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "console", "log", "handleDelete", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "scope", "type", "checked", "onChange", "htmlFor", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordManagerDashboard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst PasswordManagerDashboard = () => {\n  const navigate = useNavigate();\n  \n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    },\n    {\n      id: 2,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'StrongPass123!@#',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Strong Password',\n      strengthColor: 'bg-green-100 text-green-600 border-green-300'\n    },\n    {\n      id: 3,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'ModeratePass456',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Moderate Password',\n      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n    },\n    {\n      id: 4,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'WeakPass',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    },\n    {\n      id: 5,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'AnotherStrongPass789!',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Strong Password',\n      strengthColor: 'bg-green-100 text-green-600 border-green-300'\n    },\n    {\n      id: 6,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'ModerateSecure123',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Moderate Password',\n      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n    },\n    {\n      id: 7,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'VeryWeakPass',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    }\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [selectedCards, setSelectedCards] = useState([]);\n\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n\n  const toggleCardSelection = (id) => {\n    setSelectedCards(prev => \n      prev.includes(id) \n        ? prev.filter(cardId => cardId !== id)\n        : [...prev, id]\n    );\n  };\n\n  const toggleSelectAll = () => {\n    if (selectedCards.length === passwordCards.length) {\n      setSelectedCards([]);\n    } else {\n      setSelectedCards(passwordCards.map(card => card.id));\n    }\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  const handleEdit = (id) => {\n    console.log('Edit password card:', id);\n    // Navigate to edit form or open modal\n  };\n\n  const handleDelete = (id) => {\n    console.log('Delete password card:', id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900 p-6 rounded-xl\">\n      {/* Header */}\n      <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n        <div className=\"w-full md:w-1/2\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Teams Password Card</h2>\n        </div>\n        <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0\">\n          <button\n            onClick={() => navigate('/add-password-card')}\n            className=\"flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\"\n          >\n            <span className=\"material-symbols-rounded mr-2\">add</span>\n            Add Password Card\n          </button>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full text-sm text-left text-gray-500 dark:text-gray-400\">\n          <thead className=\"text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400\">\n            <tr>\n              <th scope=\"col\" className=\"p-4\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"checkbox-all\"\n                    type=\"checkbox\"\n                    checked={selectedCards.length === passwordCards.length}\n                    onChange={toggleSelectAll}\n                    className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <label htmlFor=\"checkbox-all\" className=\"sr-only\">checkbox</label>\n                </div>\n              </th>\n              <th scope=\"col\" className=\"px-6 py-3\">Title</th>\n              <th scope=\"col\" className=\"px-6 py-3\">User Name</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Password</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Team</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Department</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Level</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Action</th>\n            </tr>\n          </thead>\n          <tbody>\n            {passwordCards.map((card) => (\n              <tr key={card.id} className=\"bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\">\n                <td className=\"w-4 p-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      id={`checkbox-table-${card.id}`}\n                      type=\"checkbox\"\n                      checked={selectedCards.includes(card.id)}\n                      onChange={() => toggleCardSelection(card.id)}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                    />\n                    <label htmlFor={`checkbox-table-${card.id}`} className=\"sr-only\">checkbox</label>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                      TG\n                    </div>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{card.title}</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-gray-900 dark:text-white\">{card.username}</span>\n                    <button\n                      onClick={() => copyToClipboard(card.username)}\n                      className=\"ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">content_copy</span>\n                    </button>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-gray-900 dark:text-white mr-2\">\n                      {visiblePasswords[card.id] ? card.actualPassword : card.password}\n                    </span>\n                    <button\n                      onClick={() => togglePasswordVisibility(card.id)}\n                      className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">\n                        {visiblePasswords[card.id] ? 'visibility_off' : 'visibility'}\n                      </span>\n                    </button>\n                    <button\n                      onClick={() => copyToClipboard(card.actualPassword)}\n                      className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">content_copy</span>\n                    </button>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 text-gray-900 dark:text-white\">{card.team}</td>\n                <td className=\"px-6 py-4 text-gray-900 dark:text-white\">{card.department}</td>\n                <td className=\"px-6 py-4\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`}>\n                    {card.strength}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => handleEdit(card.id)}\n                      className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">edit</span>\n                    </button>\n                    <button\n                      onClick={() => handleDelete(card.id)}\n                      className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">delete</span>\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={() => navigate('/add-password-card')}\n          className=\"flex items-center justify-center px-6 py-3 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordManagerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,CACjD;IACES,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMsB,wBAAwB,GAAIb,EAAE,IAAK;IACvCU,mBAAmB,CAACI,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACd,EAAE,GAAG,CAACc,IAAI,CAACd,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMe,mBAAmB,GAAIf,EAAE,IAAK;IAClCY,gBAAgB,CAACE,IAAI,IACnBA,IAAI,CAACE,QAAQ,CAAChB,EAAE,CAAC,GACbc,IAAI,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,KAAKlB,EAAE,CAAC,GACpC,CAAC,GAAGc,IAAI,EAAEd,EAAE,CAClB,CAAC;EACH,CAAC;EAED,MAAMmB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIR,aAAa,CAACS,MAAM,KAAKtB,aAAa,CAACsB,MAAM,EAAE;MACjDR,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,CAACd,aAAa,CAACuB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACtB,EAAE,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMuB,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;EAED,MAAMI,UAAU,GAAI5B,EAAE,IAAK;IACzB6B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE9B,EAAE,CAAC;IACtC;EACF,CAAC;EAED,MAAM+B,YAAY,GAAI/B,EAAE,IAAK;IAC3B6B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE9B,EAAE,CAAC;IACxC;IACAD,gBAAgB,CAACe,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACtB,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC/D,CAAC;EAED,oBACEN,OAAA;IAAKsC,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAEvDvC,OAAA;MAAKsC,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAC9GvC,OAAA;QAAKsC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BvC,OAAA;UAAIsC,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eACN3C,OAAA;QAAKsC,SAAS,EAAC,wIAAwI;QAAAC,QAAA,eACrJvC,OAAA;UACE4C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,oBAAoB,CAAE;UAC9CmC,SAAS,EAAC,yOAAyO;UAAAC,QAAA,gBAEnPvC,OAAA;YAAMsC,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,qBAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BvC,OAAA;QAAOsC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAC1EvC,OAAA;UAAOsC,SAAS,EAAC,gFAAgF;UAAAC,QAAA,eAC/FvC,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,KAAK;cAAAC,QAAA,eAC7BvC,OAAA;gBAAKsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvC,OAAA;kBACEM,EAAE,EAAC,cAAc;kBACjBwC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE9B,aAAa,CAACS,MAAM,KAAKtB,aAAa,CAACsB,MAAO;kBACvDsB,QAAQ,EAAEvB,eAAgB;kBAC1Ba,SAAS,EAAC;gBAAqN;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChO,CAAC,eACF3C,OAAA;kBAAOiD,OAAO,EAAC,cAAc;kBAACX,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD3C,OAAA;cAAI6C,KAAK,EAAC,KAAK;cAACP,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR3C,OAAA;UAAAuC,QAAA,EACGnC,aAAa,CAACuB,GAAG,CAAEC,IAAI,iBACtB5B,OAAA;YAAkBsC,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC3HvC,OAAA;cAAIsC,SAAS,EAAC,SAAS;cAAAC,QAAA,eACrBvC,OAAA;gBAAKsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvC,OAAA;kBACEM,EAAE,EAAE,kBAAkBsB,IAAI,CAACtB,EAAE,EAAG;kBAChCwC,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE9B,aAAa,CAACK,QAAQ,CAACM,IAAI,CAACtB,EAAE,CAAE;kBACzC0C,QAAQ,EAAEA,CAAA,KAAM3B,mBAAmB,CAACO,IAAI,CAACtB,EAAE,CAAE;kBAC7CgC,SAAS,EAAC;gBAAqN;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChO,CAAC,eACF3C,OAAA;kBAAOiD,OAAO,EAAE,kBAAkBrB,IAAI,CAACtB,EAAE,EAAG;kBAACgC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL3C,OAAA;cAAIsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBvC,OAAA;gBAAKsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvC,OAAA;kBAAKsC,SAAS,EAAC,iGAAiG;kBAAAC,QAAA,EAAC;gBAEjH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN3C,OAAA;kBAAMsC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEX,IAAI,CAACrB;gBAAK;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL3C,OAAA;cAAIsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBvC,OAAA;gBAAKsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvC,OAAA;kBAAMsC,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAEX,IAAI,CAACpB;gBAAQ;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE3C,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACD,IAAI,CAACpB,QAAQ,CAAE;kBAC9C8B,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAE3EvC,OAAA;oBAAMsC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL3C,OAAA;cAAIsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBvC,OAAA;gBAAKsC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCvC,OAAA;kBAAMsC,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EACjDxB,gBAAgB,CAACa,IAAI,CAACtB,EAAE,CAAC,GAAGsB,IAAI,CAAClB,cAAc,GAAGkB,IAAI,CAACnB;gBAAQ;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACP3C,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMzB,wBAAwB,CAACS,IAAI,CAACtB,EAAE,CAAE;kBACjDgC,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAE3EvC,OAAA;oBAAMsC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC/CxB,gBAAgB,CAACa,IAAI,CAACtB,EAAE,CAAC,GAAG,gBAAgB,GAAG;kBAAY;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACT3C,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACD,IAAI,CAAClB,cAAc,CAAE;kBACpD4B,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,eAEtEvC,OAAA;oBAAMsC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL3C,OAAA;cAAIsC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEX,IAAI,CAACjB;YAAI;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxE3C,OAAA;cAAIsC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEX,IAAI,CAAChB;YAAU;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9E3C,OAAA;cAAIsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBvC,OAAA;gBAAMsC,SAAS,EAAE,qDAAqDV,IAAI,CAACd,aAAa,EAAG;gBAAAyB,QAAA,EACxFX,IAAI,CAACf;cAAQ;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL3C,OAAA;cAAIsC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBvC,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CvC,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAACN,IAAI,CAACtB,EAAE,CAAE;kBACnCgC,SAAS,EAAC,+EAA+E;kBAAAC,QAAA,eAEzFvC,OAAA;oBAAMsC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACT3C,OAAA;kBACE4C,OAAO,EAAEA,CAAA,KAAMP,YAAY,CAACT,IAAI,CAACtB,EAAE,CAAE;kBACrCgC,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eAErFvC,OAAA;oBAAMsC,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA3EEf,IAAI,CAACtB,EAAE;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4EZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3C,OAAA;MAAKsC,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCvC,OAAA;QACE4C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,oBAAoB,CAAE;QAC9CmC,SAAS,EAAC,yOAAyO;QAAAC,QAAA,gBAEnPvC,OAAA;UAAMsC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CA1QID,wBAAwB;EAAA,QACXH,WAAW;AAAA;AAAAoD,EAAA,GADxBjD,wBAAwB;AA4Q9B,eAAeA,wBAAwB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}