import React, { useEffect, useState } from 'react';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditReportProblem = ({ isVisible, setVisible, reportProblemId, statusList, updateReportList }) => {
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchProblem = async () => {
            if (!reportProblemId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/report-problem/${reportProblemId}/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch problem: ${response.statusText}`);
                }

                const data = await response.json();
                setSubject(data.report.subject || '');
                setMessage(data.report.message || '');
                setSelectedStatus(data.report.status); // Status should be ID
            } catch (error) {
                setError(error.message);
            }
        };

        fetchProblem();
    }, [reportProblemId]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        setError('');
        setSuccessMessage('');

        const token = localStorage.getItem('token');
        if (!token) {
            setError('Authentication token is missing.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/report-problem/${reportProblemId}/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    subject: subject.trim(),
                    message: message.trim(),
                    status: selectedStatus, // Send status as ID
                }),
            });

            if (!response.ok) {
                throw new Error(`Failed to update problem report: ${response.statusText}`);
            }

            const updatedData = await response.json();

            // Update the parent list with the new data
            if (updateReportList) {
                updateReportList(reportProblemId, {
                    subject: updatedData.subject,
                    message: updatedData.message,
                    status: statusList[updatedData.status], // Convert status ID to name
                });
            }

            setSuccessMessage('Problem report updated successfully!');
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 1000);
        } catch (error) {
            setError(error.message);
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()}
            >
                <h3 className="text-lg font-semibold mb-4">Update Problem Report</h3>
                {error && <div className="text-red-500 mb-2">{error}</div>}
                {successMessage && <div className="text-green-500 mb-2">{successMessage}</div>}

                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="subject" className="block mb-2">Subject</label>
                        <input
                            type="text"
                            id="subject"
                            value={subject}
                            onChange={(e) => setSubject(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="message" className="block mb-2">Message</label>
                        <textarea
                            id="message"
                            value={message}
                            onChange={(e) => setMessage(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label htmlFor="status" className="block mb-2">Status</label>
                        <select
                            id="status"
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="border p-2 w-full rounded"
                        >
                            {Object.entries(statusList).map(([id, name]) => (
                                <option key={id} value={id}>
                                    {name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2 w-full"
                    >
                        Update Problem Report
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditReportProblem;
