{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\holiday-calender\\\\HolidayCalenderGoogleList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Select from \"react-select\";\nimport moment from \"moment\";\nimport { Calendar, dateFnsLocalizer } from \"react-big-calendar\";\nimport \"react-big-calendar/lib/css/react-big-calendar.css\";\nimport { format, parse, startOfWeek, getDay } from \"date-fns\";\nimport \"tailwindcss/tailwind.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst locales = {\n  \"en-US\": require(\"date-fns/locale/en-US\")\n};\nconst localizer = dateFnsLocalizer({\n  format,\n  parse,\n  startOfWeek,\n  getDay,\n  locales\n});\nconst getCurrentYear = () => new Date().getFullYear();\nconst customStyles = {\n  menu: provided => ({\n    ...provided,\n    zIndex: 9999\n  }),\n  menuPortal: base => ({\n    ...base,\n    zIndex: 9999\n  }),\n  control: provided => ({\n    ...provided,\n    width: \"100%\",\n    minWidth: \"100%\"\n  })\n};\nconst HOLIDAY_KEYWORDS = [\"holiday\", \"new year's day\", \"independence day\", \"eid\", \"christmas\", \"puja\", \"shab\", \"martyrs' day\", \"bengali new year\", \"May Day\"];\nconst HolidayCalendarGoogleList = () => {\n  _s();\n  const [regions, setRegions] = useState([]);\n  const [selectedRegion, setSelectedRegion] = useState(null);\n  const [selectedYear, setSelectedYear] = useState({\n    value: getCurrentYear(),\n    label: getCurrentYear().toString()\n  });\n  const [holidays, setHolidays] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState(\"calendar\");\n  const API_KEY = process.env.REACT_APP_GOOGLE_API_KEY || \"AIzaSyBuRzdDIOljoVutin7ygRZdRbcQSBfJlHY\";\n  useEffect(() => {\n    const fetchRegions = async () => {\n      try {\n        const response = await fetch(\"https://restcountries.com/v3.1/all\");\n        const data = await response.json();\n        const countryList = data.map(country => {\n          let code = country.cca2.toLowerCase();\n          if (code === \"us\") code = \"usa\"; // 👈 Fix USA code for Google Calendar\n          if (code === \"gb\") code = \"uk\";\n          if (code === \"au\") code = \"australian\";\n          if (code === \"at\") code = \"austrian\";\n          if (code === \"in\") code = \"indian\";\n          if (code === \"br\") code = \"brazilian\";\n          if (code === \"br\") code = \"brazilian\";\n          if (code === \"cn\") code = \"china\";\n          if (code === \"ru\") code = \"russian\";\n          if (code === \"es\") code = \"spain\";\n          return {\n            value: `en.${code}`,\n            label: country.name.common\n          };\n        }).sort((a, b) => a.label.localeCompare(b.label));\n        setRegions([{\n          value: \"en.bd\",\n          label: \"Bangladesh\"\n        }, ...countryList]);\n        setSelectedRegion({\n          value: \"en.bd\",\n          label: \"Bangladesh\"\n        });\n      } catch (err) {\n        console.error(\"Failed to load countries:\", err);\n      }\n    };\n    fetchRegions();\n  }, []);\n  const yearOptions = Array.from({\n    length: 10\n  }, (_, i) => {\n    const year = getCurrentYear() - i;\n    return {\n      value: year,\n      label: year.toString()\n    };\n  });\n  useEffect(() => {\n    if (!selectedRegion || !selectedYear) return;\n    const fetchHolidays = async () => {\n      setLoading(true);\n      setError(null);\n      setHolidays([]);\n      try {\n        if (!API_KEY) {\n          throw new Error(\"Google API Key is missing! Set it in your .env file.\");\n        }\n\n        // Build calendar ID with proper encoding\n        const baseCalendarId = `${selectedRegion.value}#<EMAIL>`;\n        let calendarId = encodeURIComponent(baseCalendarId);\n        console.log(\"Selected Region:\", selectedRegion);\n        console.log(\"Calendar ID:\", baseCalendarId);\n        const calendarUrl = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events?key=${API_KEY}&timeMin=${selectedYear.value}-01-01T00:00:00Z&timeMax=${selectedYear.value}-12-31T23:59:59Z&orderBy=startTime&singleEvents=true&maxResults=2500`;\n        console.log(\"Fetching holidays from:\", calendarUrl);\n        const response = await fetch(calendarUrl);\n        const data = await response.json();\n        console.log(\"API Response:\", data);\n        if (data.error) {\n          console.error(\"Google Calendar API Error:\", data.error);\n          throw new Error(`Google Calendar API Error: ${data.error.message}`);\n        }\n        if (!data.items || data.items.length === 0) {\n          console.warn(\"No holiday events found for this region/year\");\n          setHolidays([]);\n          return;\n        }\n        const formattedEvents = data.items.filter(holiday => holiday && holiday.summary) // Filter out invalid events\n        .map(holiday => {\n          const isHoliday = HOLIDAY_KEYWORDS.some(keyword => holiday.summary.toLowerCase().includes(keyword.toLowerCase()));\n\n          // Handle different date formats from Google Calendar API\n          let startDate, endDate;\n          if (holiday.start.date) {\n            // All-day event\n            startDate = new Date(holiday.start.date + 'T00:00:00');\n            endDate = holiday.end ? new Date(holiday.end.date + 'T00:00:00') : startDate;\n          } else if (holiday.start.dateTime) {\n            // Timed event\n            startDate = new Date(holiday.start.dateTime);\n            endDate = holiday.end ? new Date(holiday.end.dateTime) : startDate;\n          } else {\n            console.warn(\"Invalid date format for event:\", holiday);\n            return null;\n          }\n          return {\n            id: holiday.id,\n            title: holiday.summary,\n            start: startDate,\n            end: endDate,\n            allDay: !!holiday.start.date,\n            // True if it's a date (not dateTime)\n            isHoliday,\n            color: isHoliday ? \"#ff6347\" : \"#32cd32\",\n            description: holiday.description || \"\"\n          };\n        }).filter(Boolean); // Remove null entries\n\n        console.log(\"Formatted Events:\", formattedEvents);\n        setHolidays(formattedEvents);\n      } catch (err) {\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchHolidays();\n  }, [selectedRegion, selectedYear, API_KEY]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 p-4 rounded-xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"w-4/12 md:w-10/12 text-start\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          class: \"text-2xl font-bold \",\n          children: \"Happenings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-slate-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 transition duration-500 ease-in-out hover:bg-primarySeafoam focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-slate-800 dark:text-slate-400 dark:border-slate-600 dark:hover:text-white dark:hover:bg-slate-700 ${activeTab === \"calendar\" ? \"bg-secondary text-white\" : \"bg-secondary text-slate-800\"}`,\n        onClick: () => setActiveTab(\"calendar\"),\n        children: \"Calendar View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-slate-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-primarySeafoam transition duration-500 ease-in-out focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-slate-800 dark:text-slate-400 dark:border-slate-600 dark:hover:text-white dark:hover:bg-slate-700 ${activeTab === \"list\" ? \"bg-blue-500 text-white\" : \"bg-slate-200 text-slate-800\"}`,\n        onClick: () => setActiveTab(\"list\"),\n        children: \"Full Year Holidays\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex mb-4 w-full\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"w-8/12 flex items-end justify-end gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-1/2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"year\",\n            className: \"font-medium mb-2\",\n            children: \"Select a year:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            options: yearOptions,\n            value: selectedYear,\n            onChange: setSelectedYear,\n            placeholder: \"Select a year\",\n            styles: customStyles,\n            menuPortalTarget: document.body\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full md:w-1/2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"region\",\n            className: \"block font-medium mb-2\",\n            children: \"Select a country:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            options: regions,\n            value: selectedRegion,\n            onChange: setSelectedRegion,\n            placeholder: \"Select a country\",\n            styles: customStyles,\n            menuPortalTarget: document.body\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center text-slate-500\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-red-500\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this) : activeTab === \"calendar\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow\",\n      children: /*#__PURE__*/_jsxDEV(Calendar, {\n        localizer: localizer,\n        events: holidays,\n        startAccessor: \"start\",\n        endAccessor: \"end\",\n        style: {\n          height: 500,\n          backgroundColor: \"white\"\n        },\n        eventPropGetter: event => ({\n          style: {\n            backgroundColor: event.color,\n            color: \"#fff\"\n          }\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n      children: holidays.map(holiday => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 border rounded shadow-md ${holiday.isHoliday ? \"bg-red-300\" : \"bg-green-300\"}`,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold\",\n          children: holiday.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: holiday.start.toDateString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this)]\n      }, holiday.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(HolidayCalendarGoogleList, \"TJvBtsHyj7UCoVOBwhpCusRd/Sc=\");\n_c = HolidayCalendarGoogleList;\nexport default HolidayCalendarGoogleList;\nvar _c;\n$RefreshReg$(_c, \"HolidayCalendarGoogleList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Select", "moment", "Calendar", "dateFnsLocalizer", "format", "parse", "startOfWeek", "getDay", "jsxDEV", "_jsxDEV", "locales", "require", "localizer", "getCurrentYear", "Date", "getFullYear", "customStyles", "menu", "provided", "zIndex", "menuPortal", "base", "control", "width", "min<PERSON><PERSON><PERSON>", "HOLIDAY_KEYWORDS", "HolidayCalendarGoogleList", "_s", "regions", "setRegions", "selectedRegion", "setSelectedRegion", "selected<PERSON>ear", "setSelectedYear", "value", "label", "toString", "holidays", "setHolidays", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "API_KEY", "process", "env", "REACT_APP_GOOGLE_API_KEY", "fetchRegions", "response", "fetch", "data", "json", "countryList", "map", "country", "code", "cca2", "toLowerCase", "name", "common", "sort", "a", "b", "localeCompare", "err", "console", "yearOptions", "Array", "from", "length", "_", "i", "year", "fetchHolidays", "Error", "baseCalendarId", "calendarId", "encodeURIComponent", "log", "calendarUrl", "message", "items", "warn", "formattedEvents", "filter", "holiday", "summary", "isHoliday", "some", "keyword", "includes", "startDate", "endDate", "start", "date", "end", "dateTime", "id", "title", "allDay", "color", "description", "Boolean", "className", "children", "class", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "options", "onChange", "placeholder", "styles", "menuPortalTarget", "document", "body", "events", "startAccessor", "endAccessor", "style", "height", "backgroundColor", "eventPropGetter", "event", "toDateString", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/holiday-calender/HolidayCalenderGoogleList.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport Select from \"react-select\";\r\nimport moment from \"moment\";\r\nimport { Calendar, dateFnsLocalizer } from \"react-big-calendar\";\r\nimport \"react-big-calendar/lib/css/react-big-calendar.css\";\r\nimport { format, parse, startOfWeek, getDay } from \"date-fns\";\r\nimport \"tailwindcss/tailwind.css\";\r\n\r\nconst locales = {\r\n  \"en-US\": require(\"date-fns/locale/en-US\"),\r\n};\r\nconst localizer = dateFnsLocalizer({\r\n  format,\r\n  parse,\r\n  startOfWeek,\r\n  getDay,\r\n  locales,\r\n});\r\n\r\nconst getCurrentYear = () => new Date().getFullYear();\r\n\r\nconst customStyles = {\r\n  menu: (provided) => ({\r\n    ...provided,\r\n    zIndex: 9999,\r\n  }),\r\n  menuPortal: (base) => ({ ...base, zIndex: 9999 }),\r\n  control: (provided) => ({\r\n    ...provided,\r\n    width: \"100%\",\r\n    minWidth: \"100%\",\r\n  }),\r\n};\r\n\r\nconst HOLIDAY_KEYWORDS = [\r\n  \"holiday\",\r\n  \"new year's day\",\r\n  \"independence day\",\r\n  \"eid\",\r\n  \"christmas\",\r\n  \"puja\",\r\n  \"shab\",\r\n  \"martyrs' day\",\r\n  \"bengali new year\",\r\n  \"May Day\",\r\n];\r\n\r\nconst HolidayCalendarGoogleList = () => {\r\n  const [regions, setRegions] = useState([]);\r\n  const [selectedRegion, setSelectedRegion] = useState(null);\r\n  const [selectedYear, setSelectedYear] = useState({\r\n    value: getCurrentYear(),\r\n    label: getCurrentYear().toString(),\r\n  });\r\n  const [holidays, setHolidays] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"calendar\");\r\n\r\n  const API_KEY =\r\n    process.env.REACT_APP_GOOGLE_API_KEY ||\r\n    \"AIzaSyBuRzdDIOljoVutin7ygRZdRbcQSBfJlHY\";\r\n\r\n  useEffect(() => {\r\n    const fetchRegions = async () => {\r\n      try {\r\n        const response = await fetch(\"https://restcountries.com/v3.1/all\");\r\n        const data = await response.json();\r\n\r\n        const countryList = data\r\n          .map((country) => {\r\n            let code = country.cca2.toLowerCase();\r\n            if (code === \"us\") code = \"usa\"; // 👈 Fix USA code for Google Calendar\r\n            if (code === \"gb\") code = \"uk\";\r\n            if (code === \"au\") code = \"australian\";\r\n            if (code === \"at\") code = \"austrian\";\r\n            if (code === \"in\") code = \"indian\";\r\n            if (code === \"br\") code = \"brazilian\";\r\n            if (code === \"br\") code = \"brazilian\";\r\n            if (code === \"cn\") code = \"china\";\r\n            if (code === \"ru\") code = \"russian\";\r\n            if (code === \"es\") code = \"spain\";\r\n\r\n            return {\r\n              value: `en.${code}`,\r\n              label: country.name.common,\r\n            };\r\n          })\r\n          .sort((a, b) => a.label.localeCompare(b.label));\r\n\r\n        setRegions([{ value: \"en.bd\", label: \"Bangladesh\" }, ...countryList]);\r\n        setSelectedRegion({ value: \"en.bd\", label: \"Bangladesh\" });\r\n      } catch (err) {\r\n        console.error(\"Failed to load countries:\", err);\r\n      }\r\n    };\r\n\r\n    fetchRegions();\r\n  }, []);\r\n\r\n  const yearOptions = Array.from({ length: 10 }, (_, i) => {\r\n    const year = getCurrentYear() - i;\r\n    return { value: year, label: year.toString() };\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (!selectedRegion || !selectedYear) return;\r\n\r\n    const fetchHolidays = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      setHolidays([]);\r\n\r\n      try {\r\n        if (!API_KEY) {\r\n          throw new Error(\r\n            \"Google API Key is missing! Set it in your .env file.\"\r\n          );\r\n        }\r\n\r\n        // Build calendar ID with proper encoding\r\n        const baseCalendarId = `${selectedRegion.value}#<EMAIL>`;\r\n        let calendarId = encodeURIComponent(baseCalendarId);\r\n\r\n        console.log(\"Selected Region:\", selectedRegion);\r\n        console.log(\"Calendar ID:\", baseCalendarId);\r\n\r\n        const calendarUrl = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events?key=${API_KEY}&timeMin=${selectedYear.value}-01-01T00:00:00Z&timeMax=${selectedYear.value}-12-31T23:59:59Z&orderBy=startTime&singleEvents=true&maxResults=2500`;\r\n\r\n        console.log(\"Fetching holidays from:\", calendarUrl);\r\n        const response = await fetch(calendarUrl);\r\n        const data = await response.json();\r\n\r\n        console.log(\"API Response:\", data);\r\n\r\n        if (data.error) {\r\n          console.error(\"Google Calendar API Error:\", data.error);\r\n          throw new Error(`Google Calendar API Error: ${data.error.message}`);\r\n        }\r\n\r\n        if (!data.items || data.items.length === 0) {\r\n          console.warn(\"No holiday events found for this region/year\");\r\n          setHolidays([]);\r\n          return;\r\n        }\r\n\r\n        const formattedEvents = data.items\r\n          .filter((holiday) => holiday && holiday.summary) // Filter out invalid events\r\n          .map((holiday) => {\r\n            const isHoliday = HOLIDAY_KEYWORDS.some((keyword) =>\r\n              holiday.summary.toLowerCase().includes(keyword.toLowerCase())\r\n            );\r\n\r\n            // Handle different date formats from Google Calendar API\r\n            let startDate, endDate;\r\n            if (holiday.start.date) {\r\n              // All-day event\r\n              startDate = new Date(holiday.start.date + 'T00:00:00');\r\n              endDate = holiday.end ? new Date(holiday.end.date + 'T00:00:00') : startDate;\r\n            } else if (holiday.start.dateTime) {\r\n              // Timed event\r\n              startDate = new Date(holiday.start.dateTime);\r\n              endDate = holiday.end ? new Date(holiday.end.dateTime) : startDate;\r\n            } else {\r\n              console.warn(\"Invalid date format for event:\", holiday);\r\n              return null;\r\n            }\r\n\r\n            return {\r\n              id: holiday.id,\r\n              title: holiday.summary,\r\n              start: startDate,\r\n              end: endDate,\r\n              allDay: !!holiday.start.date, // True if it's a date (not dateTime)\r\n              isHoliday,\r\n              color: isHoliday ? \"#ff6347\" : \"#32cd32\",\r\n              description: holiday.description || \"\",\r\n            };\r\n          })\r\n          .filter(Boolean); // Remove null entries\r\n\r\n        console.log(\"Formatted Events:\", formattedEvents);\r\n        setHolidays(formattedEvents);\r\n      } catch (err) {\r\n        setError(err.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchHolidays();\r\n  }, [selectedRegion, selectedYear, API_KEY]);\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 p-4 rounded-xl\">\r\n      <div class=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4\">\r\n        <div class=\"w-4/12 md:w-10/12 text-start\">\r\n          <h2 class=\"text-2xl font-bold \">Happenings</h2>\r\n        </div>\r\n\r\n\r\n        <button\r\n          className={`w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-slate-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 transition duration-500 ease-in-out hover:bg-primarySeafoam focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-slate-800 dark:text-slate-400 dark:border-slate-600 dark:hover:text-white dark:hover:bg-slate-700 ${\r\n            activeTab === \"calendar\"\r\n              ? \"bg-secondary text-white\"\r\n              : \"bg-secondary text-slate-800\"\r\n          }`}\r\n          onClick={() => setActiveTab(\"calendar\")}\r\n        >\r\n          Calendar View\r\n        </button>\r\n        <button\r\n          className={`w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-slate-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-primarySeafoam transition duration-500 ease-in-out focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-slate-800 dark:text-slate-400 dark:border-slate-600 dark:hover:text-white dark:hover:bg-slate-700 ${\r\n            activeTab === \"list\"\r\n              ? \"bg-blue-500 text-white\"\r\n              : \"bg-slate-200 text-slate-800\"\r\n          }`}\r\n          onClick={() => setActiveTab(\"list\")}\r\n        >\r\n          Full Year Holidays\r\n        </button>\r\n        \r\n      </div>\r\n\r\n      {/* Tab Navigation */}\r\n      <div className=\"flex mb-4 w-full\">\r\n        <div class=\"w-8/12 flex items-end justify-end gap-1\">\r\n          <div className=\"w-full md:w-1/2\">\r\n            <label htmlFor=\"year\" className=\"font-medium mb-2\">\r\n              Select a year:\r\n            </label>\r\n            <Select\r\n              options={yearOptions}\r\n              value={selectedYear}\r\n              onChange={setSelectedYear}\r\n              placeholder=\"Select a year\"\r\n              styles={customStyles}\r\n              menuPortalTarget={document.body}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"w-full md:w-1/2\">\r\n            <label htmlFor=\"region\" className=\"block font-medium mb-2\">\r\n              Select a country:\r\n            </label>\r\n            <Select\r\n              options={regions}\r\n              value={selectedRegion}\r\n              onChange={setSelectedRegion}\r\n              placeholder=\"Select a country\"\r\n              styles={customStyles}\r\n              menuPortalTarget={document.body}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        \r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"flex justify-center text-slate-500\">Loading...</div>\r\n      ) : error ? (\r\n        <p className=\"text-red-500\">{error}</p>\r\n      ) : activeTab === \"calendar\" ? (\r\n        <div className=\"bg-white p-4 rounded-lg shadow\">\r\n          <Calendar\r\n            localizer={localizer}\r\n            events={holidays}\r\n            startAccessor=\"start\"\r\n            endAccessor=\"end\"\r\n            style={{ height: 500, backgroundColor: \"white\" }}\r\n            eventPropGetter={(event) => ({\r\n              style: { backgroundColor: event.color, color: \"#fff\" },\r\n            })}\r\n          />\r\n        </div>\r\n      ) : (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {holidays.map((holiday) => (\r\n            <div\r\n              key={holiday.id}\r\n              className={`p-4 border rounded shadow-md ${\r\n                holiday.isHoliday ? \"bg-red-300\" : \"bg-green-300\"\r\n              }`}\r\n            >\r\n              <h3 className=\"text-lg font-semibold\">{holiday.title}</h3>\r\n              <p className=\"text-slate-600\">{holiday.start.toDateString()}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HolidayCalendarGoogleList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,oBAAoB;AAC/D,OAAO,mDAAmD;AAC1D,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,QAAQ,UAAU;AAC7D,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,OAAO,GAAG;EACd,OAAO,EAAEC,OAAO,CAAC,uBAAuB;AAC1C,CAAC;AACD,MAAMC,SAAS,GAAGT,gBAAgB,CAAC;EACjCC,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,MAAM;EACNG;AACF,CAAC,CAAC;AAEF,MAAMG,cAAc,GAAGA,CAAA,KAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;AAErD,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAGC,QAAQ,KAAM;IACnB,GAAGA,QAAQ;IACXC,MAAM,EAAE;EACV,CAAC,CAAC;EACFC,UAAU,EAAGC,IAAI,KAAM;IAAE,GAAGA,IAAI;IAAEF,MAAM,EAAE;EAAK,CAAC,CAAC;EACjDG,OAAO,EAAGJ,QAAQ,KAAM;IACtB,GAAGA,QAAQ;IACXK,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC;AAED,MAAMC,gBAAgB,GAAG,CACvB,SAAS,EACT,gBAAgB,EAChB,kBAAkB,EAClB,KAAK,EACL,WAAW,EACX,MAAM,EACN,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,SAAS,CACV;AAED,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC;IAC/CoC,KAAK,EAAErB,cAAc,CAAC,CAAC;IACvBsB,KAAK,EAAEtB,cAAc,CAAC,CAAC,CAACuB,QAAQ,CAAC;EACnC,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAM+C,OAAO,GACXC,OAAO,CAACC,GAAG,CAACC,wBAAwB,IACpC,yCAAyC;EAE3CjD,SAAS,CAAC,MAAM;IACd,MAAMkD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoC,CAAC;QAClE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,MAAMC,WAAW,GAAGF,IAAI,CACrBG,GAAG,CAAEC,OAAO,IAAK;UAChB,IAAIC,IAAI,GAAGD,OAAO,CAACE,IAAI,CAACC,WAAW,CAAC,CAAC;UACrC,IAAIF,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,KAAK,CAAC,CAAC;UACjC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,IAAI;UAC9B,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,YAAY;UACtC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,UAAU;UACpC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,QAAQ;UAClC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,WAAW;UACrC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,WAAW;UACrC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,OAAO;UACjC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,SAAS;UACnC,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,OAAO;UAEjC,OAAO;YACLvB,KAAK,EAAE,MAAMuB,IAAI,EAAE;YACnBtB,KAAK,EAAEqB,OAAO,CAACI,IAAI,CAACC;UACtB,CAAC;QACH,CAAC,CAAC,CACDC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5B,KAAK,CAAC8B,aAAa,CAACD,CAAC,CAAC7B,KAAK,CAAC,CAAC;QAEjDN,UAAU,CAAC,CAAC;UAAEK,KAAK,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAa,CAAC,EAAE,GAAGmB,WAAW,CAAC,CAAC;QACrEvB,iBAAiB,CAAC;UAAEG,KAAK,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAa,CAAC,CAAC;MAC5D,CAAC,CAAC,OAAO+B,GAAG,EAAE;QACZC,OAAO,CAAC1B,KAAK,CAAC,2BAA2B,EAAEyB,GAAG,CAAC;MACjD;IACF,CAAC;IAEDjB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK;IACvD,MAAMC,IAAI,GAAG7D,cAAc,CAAC,CAAC,GAAG4D,CAAC;IACjC,OAAO;MAAEvC,KAAK,EAAEwC,IAAI;MAAEvC,KAAK,EAAEuC,IAAI,CAACtC,QAAQ,CAAC;IAAE,CAAC;EAChD,CAAC,CAAC;EAEFrC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,cAAc,IAAI,CAACE,YAAY,EAAE;IAEtC,MAAM2C,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChCnC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdJ,WAAW,CAAC,EAAE,CAAC;MAEf,IAAI;QACF,IAAI,CAACO,OAAO,EAAE;UACZ,MAAM,IAAI+B,KAAK,CACb,sDACF,CAAC;QACH;;QAEA;QACA,MAAMC,cAAc,GAAG,GAAG/C,cAAc,CAACI,KAAK,sCAAsC;QACpF,IAAI4C,UAAU,GAAGC,kBAAkB,CAACF,cAAc,CAAC;QAEnDV,OAAO,CAACa,GAAG,CAAC,kBAAkB,EAAElD,cAAc,CAAC;QAC/CqC,OAAO,CAACa,GAAG,CAAC,cAAc,EAAEH,cAAc,CAAC;QAE3C,MAAMI,WAAW,GAAG,oDAAoDH,UAAU,eAAejC,OAAO,YAAYb,YAAY,CAACE,KAAK,4BAA4BF,YAAY,CAACE,KAAK,sEAAsE;QAE1PiC,OAAO,CAACa,GAAG,CAAC,yBAAyB,EAAEC,WAAW,CAAC;QACnD,MAAM/B,QAAQ,GAAG,MAAMC,KAAK,CAAC8B,WAAW,CAAC;QACzC,MAAM7B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElCc,OAAO,CAACa,GAAG,CAAC,eAAe,EAAE5B,IAAI,CAAC;QAElC,IAAIA,IAAI,CAACX,KAAK,EAAE;UACd0B,OAAO,CAAC1B,KAAK,CAAC,4BAA4B,EAAEW,IAAI,CAACX,KAAK,CAAC;UACvD,MAAM,IAAImC,KAAK,CAAC,8BAA8BxB,IAAI,CAACX,KAAK,CAACyC,OAAO,EAAE,CAAC;QACrE;QAEA,IAAI,CAAC9B,IAAI,CAAC+B,KAAK,IAAI/B,IAAI,CAAC+B,KAAK,CAACZ,MAAM,KAAK,CAAC,EAAE;UAC1CJ,OAAO,CAACiB,IAAI,CAAC,8CAA8C,CAAC;UAC5D9C,WAAW,CAAC,EAAE,CAAC;UACf;QACF;QAEA,MAAM+C,eAAe,GAAGjC,IAAI,CAAC+B,KAAK,CAC/BG,MAAM,CAAEC,OAAO,IAAKA,OAAO,IAAIA,OAAO,CAACC,OAAO,CAAC,CAAC;QAAA,CAChDjC,GAAG,CAAEgC,OAAO,IAAK;UAChB,MAAME,SAAS,GAAGhE,gBAAgB,CAACiE,IAAI,CAAEC,OAAO,IAC9CJ,OAAO,CAACC,OAAO,CAAC7B,WAAW,CAAC,CAAC,CAACiC,QAAQ,CAACD,OAAO,CAAChC,WAAW,CAAC,CAAC,CAC9D,CAAC;;UAED;UACA,IAAIkC,SAAS,EAAEC,OAAO;UACtB,IAAIP,OAAO,CAACQ,KAAK,CAACC,IAAI,EAAE;YACtB;YACAH,SAAS,GAAG,IAAI/E,IAAI,CAACyE,OAAO,CAACQ,KAAK,CAACC,IAAI,GAAG,WAAW,CAAC;YACtDF,OAAO,GAAGP,OAAO,CAACU,GAAG,GAAG,IAAInF,IAAI,CAACyE,OAAO,CAACU,GAAG,CAACD,IAAI,GAAG,WAAW,CAAC,GAAGH,SAAS;UAC9E,CAAC,MAAM,IAAIN,OAAO,CAACQ,KAAK,CAACG,QAAQ,EAAE;YACjC;YACAL,SAAS,GAAG,IAAI/E,IAAI,CAACyE,OAAO,CAACQ,KAAK,CAACG,QAAQ,CAAC;YAC5CJ,OAAO,GAAGP,OAAO,CAACU,GAAG,GAAG,IAAInF,IAAI,CAACyE,OAAO,CAACU,GAAG,CAACC,QAAQ,CAAC,GAAGL,SAAS;UACpE,CAAC,MAAM;YACL1B,OAAO,CAACiB,IAAI,CAAC,gCAAgC,EAAEG,OAAO,CAAC;YACvD,OAAO,IAAI;UACb;UAEA,OAAO;YACLY,EAAE,EAAEZ,OAAO,CAACY,EAAE;YACdC,KAAK,EAAEb,OAAO,CAACC,OAAO;YACtBO,KAAK,EAAEF,SAAS;YAChBI,GAAG,EAAEH,OAAO;YACZO,MAAM,EAAE,CAAC,CAACd,OAAO,CAACQ,KAAK,CAACC,IAAI;YAAE;YAC9BP,SAAS;YACTa,KAAK,EAAEb,SAAS,GAAG,SAAS,GAAG,SAAS;YACxCc,WAAW,EAAEhB,OAAO,CAACgB,WAAW,IAAI;UACtC,CAAC;QACH,CAAC,CAAC,CACDjB,MAAM,CAACkB,OAAO,CAAC,CAAC,CAAC;;QAEpBrC,OAAO,CAACa,GAAG,CAAC,mBAAmB,EAAEK,eAAe,CAAC;QACjD/C,WAAW,CAAC+C,eAAe,CAAC;MAC9B,CAAC,CAAC,OAAOnB,GAAG,EAAE;QACZxB,QAAQ,CAACwB,GAAG,CAACgB,OAAO,CAAC;MACvB,CAAC,SAAS;QACR1C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC7C,cAAc,EAAEE,YAAY,EAAEa,OAAO,CAAC,CAAC;EAE3C,oBACEpC,OAAA;IAAKgG,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBACvDjG,OAAA;MAAKkG,KAAK,EAAC,4FAA4F;MAAAD,QAAA,gBACrGjG,OAAA;QAAKkG,KAAK,EAAC,8BAA8B;QAAAD,QAAA,eACvCjG,OAAA;UAAIkG,KAAK,EAAC,qBAAqB;UAAAD,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eAGNtG,OAAA;QACEgG,SAAS,EAAE,+aACT9D,SAAS,KAAK,UAAU,GACpB,yBAAyB,GACzB,6BAA6B,EAChC;QACHqE,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,UAAU,CAAE;QAAA8D,QAAA,EACzC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtG,OAAA;QACEgG,SAAS,EAAE,+aACT9D,SAAS,KAAK,MAAM,GAChB,wBAAwB,GACxB,6BAA6B,EAChC;QACHqE,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,MAAM,CAAE;QAAA8D,QAAA,EACrC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEN,CAAC,eAGNtG,OAAA;MAAKgG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BjG,OAAA;QAAKkG,KAAK,EAAC,yCAAyC;QAAAD,QAAA,gBAClDjG,OAAA;UAAKgG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BjG,OAAA;YAAOwG,OAAO,EAAC,MAAM;YAACR,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEnD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtG,OAAA,CAACT,MAAM;YACLkH,OAAO,EAAE9C,WAAY;YACrBlC,KAAK,EAAEF,YAAa;YACpBmF,QAAQ,EAAElF,eAAgB;YAC1BmF,WAAW,EAAC,eAAe;YAC3BC,MAAM,EAAErG,YAAa;YACrBsG,gBAAgB,EAAEC,QAAQ,CAACC;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtG,OAAA;UAAKgG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BjG,OAAA;YAAOwG,OAAO,EAAC,QAAQ;YAACR,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAE3D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtG,OAAA,CAACT,MAAM;YACLkH,OAAO,EAAEtF,OAAQ;YACjBM,KAAK,EAAEJ,cAAe;YACtBqF,QAAQ,EAAEpF,iBAAkB;YAC5BqF,WAAW,EAAC,kBAAkB;YAC9BC,MAAM,EAAErG,YAAa;YACrBsG,gBAAgB,EAAEC,QAAQ,CAACC;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC,EAELxE,OAAO,gBACN9B,OAAA;MAAKgG,SAAS,EAAC,oCAAoC;MAAAC,QAAA,EAAC;IAAU;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,GAClEtE,KAAK,gBACPhC,OAAA;MAAGgG,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAEjE;IAAK;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,GACrCpE,SAAS,KAAK,UAAU,gBAC1BlC,OAAA;MAAKgG,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CjG,OAAA,CAACP,QAAQ;QACPU,SAAS,EAAEA,SAAU;QACrB6G,MAAM,EAAEpF,QAAS;QACjBqF,aAAa,EAAC,OAAO;QACrBC,WAAW,EAAC,KAAK;QACjBC,KAAK,EAAE;UAAEC,MAAM,EAAE,GAAG;UAAEC,eAAe,EAAE;QAAQ,CAAE;QACjDC,eAAe,EAAGC,KAAK,KAAM;UAC3BJ,KAAK,EAAE;YAAEE,eAAe,EAAEE,KAAK,CAAC1B,KAAK;YAAEA,KAAK,EAAE;UAAO;QACvD,CAAC;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENtG,OAAA;MAAKgG,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClErE,QAAQ,CAACkB,GAAG,CAAEgC,OAAO,iBACpB9E,OAAA;QAEEgG,SAAS,EAAE,gCACTlB,OAAO,CAACE,SAAS,GAAG,YAAY,GAAG,cAAc,EAChD;QAAAiB,QAAA,gBAEHjG,OAAA;UAAIgG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEnB,OAAO,CAACa;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1DtG,OAAA;UAAGgG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAEnB,OAAO,CAACQ,KAAK,CAACkC,YAAY,CAAC;QAAC;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GAN3DxB,OAAO,CAACY,EAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpF,EAAA,CAtPID,yBAAyB;AAAAwG,EAAA,GAAzBxG,yBAAyB;AAwP/B,eAAeA,yBAAyB;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}