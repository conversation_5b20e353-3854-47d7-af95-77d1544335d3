import { update } from '@react-spring/web';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const EditTeam = ({ dataItemsId, isVisible, setVisible }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [team, setTeam] = useState(null);
    const [teamName, setTeamName] = useState('');
    const [icon, setIcon] = useState(null);
    const [existingIcon, setExistingIcon] = useState('');
    const [logo, setLogo] = useState(null);
    const [existingLogo, setExistingLogo] = useState('');
    const [poc, setPoc] = useState('');
    const [manager, setManager] = useState('');
    const [teamLead, setTeamLead] = useState('');
    const [workday, setWorkday] = useState('');
    const [launch, setLaunch] = useState('');
    const [departmentId, setDepartmentId] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                // Fetch Users
                const usersResponse = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!usersResponse.ok) {
                    throw new Error('Failed to fetch users');
                }

                const usersData = await usersResponse.json();
                setUsers(usersData.map(user => ({
                    id: user.id,
                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),
                    fname: user.fname,
                    lname: user.lname,
                })));

                // Fetch Departments
                const departmentsResponse = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!departmentsResponse.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const departmentsData = await departmentsResponse.json();
                setDepartments(departmentsData.departments);

                // Fetch Team Details if editing an existing team
                if (dataItemsId) {
                    const teamResponse = await fetch(`${API_URL}/teams/${dataItemsId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (!teamResponse.ok) {
                        throw new Error('Failed to fetch team details');
                    }

                    const teamData = await teamResponse.json();

                    // Set team data to state
                    setTeam(teamData.team); // Set the team object

                    // Set values for form fields based on teamData
                    setTeamName(teamData.team.name || '');  // Set default value for team name
                    setIcon(teamData.team.icon || null);    // Set default value for icon
                    setLogo(teamData.team.logo || null);    // Set default value for logo
                    setPoc(teamData.team.poc || '');        // Set default value for POC
                    setManager(teamData.team.manager || ''); // Set default value for manager
                    setTeamLead(teamData.team.team_lead || ''); // Set default value for team lead
                    setWorkday(teamData.team.workday || '');  // Set default value for workday
                    setLaunch(teamData.team.launch || '');  // Set default value for launch
                    setExistingIcon(teamData.icon || '');
                    setExistingLogo(teamData.logo || '');

                    // Set departmentId by accessing the first department's ID if available
                    const departmentId = teamData.team.departments && teamData.team.departments.length > 0
                        ? teamData.team.departments[0].id
                        : '';
                    setDepartmentId(departmentId); // Set the department ID
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [dataItemsId]);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        setError(''); // Clear any previous error

        // Get user_id from localStorage for 'updated_by'
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Fetch the logged-in user's data for full name
            const loggedUserResponse = await fetch(`${API_URL}/logged-users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!loggedUserResponse.ok) {
                throw new Error('Failed to fetch logged-in user data');
            }
    
            const loggedUserData = await loggedUserResponse.json();
            const fullName = `${loggedUserData.fname} ${loggedUserData.lname}`; // Full name of the logged-in user
            console.log('Logged-in User Full Name:', fullName); // Log fullName for debugging
    
            // Prepare the data object (without file data)
            const requestData = {
                name: teamName.trim(),
                department_id: parseInt(departmentId),
                launch: launch,
                workday: workday,
                poc: poc,
                manager: manager,
                team_lead: teamLead,
                updated_by: updatedBy,
            };
    
            // Convert icon and logo files to Base64 if they are provided
            if (icon && icon instanceof File) { // Check if it's a valid File
                const iconBase64 = await convertToBase64(icon);
                requestData.icon = iconBase64;
            } else {
                console.log("No valid icon file selected.");
            }
    
            if (logo && logo instanceof File) { // Check if it's a valid File
                const logoBase64 = await convertToBase64(logo);
                requestData.logo = logoBase64;
            } else {
                console.log("No valid logo file selected.");
            }
    
            // Log the final request data before submission
            console.log("Request data before submission:", requestData);
    
            // Make the PUT request to update the team with JSON data
            const response = await fetch(`${API_URL}/teams/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData), // Send data as JSON
            });
    
            if (!response.ok) {
                throw new Error('Failed to update team: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Team "${result.name || teamName}" updated successfully!`);
            alertMessage('success');
    
            // Optionally, close the modal after success
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000); 
            
            navigate('/settings');
        } catch (error) {
            alertMessage('error');
        }
    };
    
    // Helper function to convert a file to Base64
    const convertToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            if (file instanceof File) {  // Check if the parameter is a valid File object
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result); // resolve with the Base64 string
                reader.onerror = reject;
                reader.readAsDataURL(file); // Convert file to Base64
            } else {
                reject('The provided object is not a valid File.');
            }
        });
    };
    

    const handleClose = () => {
        setVisible(false);
        navigate('/settings');
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relativ">
                        <div className="flex justify-between items-center mb-4 bg-gray-100">
                            <h4 className="text-xl text-left font-medium text-gray-800 px-6 py-4">Edit Team</h4>
                            <button
                                className="text-3xl text-gray-500 hover:text-gray-800"
                                onClick={handleClose}
                            >
                                &times;
                            </button>
                        </div>
                        <form onSubmit={handleSubmit} className="text-left p-2 overflow-y-auto max-h-[90vh] scrollbar-vertical px-6">
                            <div className='flex flex-wrap items-start justify-between'>    
                                {/* Department */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                        Department
                                    </label>
                                    <select
                                        id="department"
                                        value={departmentId} // Use departmentId as the value
                                        onChange={(e) => setDepartmentId(e.target.value)} // Update departmentId on change
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                        required
                                    >
                                        <option value="">Select Department</option>
                                        {departments.map((department) => (
                                            <option key={department.id} value={department.id}> {/* Use department ID as value */}
                                                {department.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                {/* Team Name */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="teamName" className="block text-sm font-medium text-gray-700 pb-4">Team Name</label>
                                    <input
                                        type="text"
                                        id="teamName"
                                        value={teamName}
                                        onChange={(e) => setTeamName(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                        required
                                    />
                                </div>

                                {/* POC */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="poc" className="block text-sm font-medium text-gray-700 pb-4">POC</label>
                                    <input
                                        type="text"
                                        id="poc"
                                        value={poc}
                                        onChange={(e) => setPoc(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    />
                                </div>

                                {/* Manager */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="manager" className="block text-sm font-medium text-gray-700 pb-4">Manager</label>
                                    <input
                                        type="text"
                                        id="manager"
                                        value={manager}
                                        onChange={(e) => setManager(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    />
                                </div>

                                {/* Team Lead */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="teamLead" className="block text-sm font-medium text-gray-700 pb-4">Team Lead</label>
                                    <input
                                        type="text"
                                        id="teamLead"
                                        value={teamLead}
                                        onChange={(e) => setTeamLead(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    />
                                </div>

                                {/* Workday */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="workday" className="block text-sm font-medium text-gray-700 pb-4">Workday</label>
                                    <input
                                        type="text"
                                        id="workday"
                                        value={workday}
                                        onChange={(e) => setWorkday(e.target.value)}
                                        placeholder='Work day optional!'
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    />
                                </div>


                                {/* Launch */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="launch" className="block text-sm font-medium text-gray-700 pb-4">Launch Date</label>
                                    <input
                                        type="date"
                                        id="launch"
                                        value={launch}
                                        onChange={(e) => setLaunch(e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                    />
                                </div>   
                                <div className="mb-4 w-full sm:w-1/2 px-4 opacity-0"></div> 

                                {/* Icon Preview */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="icon" className="block text-sm font-medium text-gray-700 pb-4">Icon</label>
                                    {icon && icon instanceof File ? (
                                        <>
                                        <img src={URL.createObjectURL(icon)} alt="Icon Preview" className="w-20 h-20 mb-2" />
                                        <input
                                            type="file"
                                            id="icon"
                                            onChange={(e) => setIcon(e.target.files[0])}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                        />
                                        </>
                                    ) : (
                                        <>
                                        {(existingIcon || icon) && (
                                            <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                                            <img
                                                // If 'icon' is a string (existing icon URL), use it directly. Otherwise, fallback to 'existingIcon'
                                                src={icon && typeof icon === 'string' ? icon : existingIcon ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingIcon}` : ''}
                                                alt="Icon Preview"
                                                className="w-auto h-auto object-cover"
                                            />
                                            </div>
                                        )}
                                        <input
                                            type="file"
                                            id="icon"
                                            onChange={(e) => setIcon(e.target.files[0])}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                        />
                                        </>
                                    )}
                                    </div>


                                {/* Logo Preview */}
                                <div className="mb-4 w-full sm:w-1/2 px-4">
                                    <label htmlFor="logo" className="block text-sm font-medium text-gray-700 pb-4">Logo</label>
                                    {logo && logo instanceof File ? (
                                        <>
                                        <img src={URL.createObjectURL(logo)} alt="Logo Preview" className="w-20 h-20 mb-2" />
                                        <input
                                            type="file"
                                            id="logo"
                                            onChange={(e) => setLogo(e.target.files[0])}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                        />
                                        </>
                                    ) : (
                                        <>
                                        {(existingLogo || logo) && (
                                            <div className="w-20 h-20 overflow-hidden bg-gray-200 p-2 rounded-lg">
                                            <img
                                                // If 'logo' is a string (existing logo URL), use it directly. Otherwise, fallback to 'existingLogo'
                                                src={logo && typeof logo === 'string' ? logo : existingLogo ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingLogo}` : ''}
                                                alt="Logo Preview"
                                                className="w-auto h-auto object-cover"
                                            />
                                            </div>
                                        )}
                                        <input
                                            type="file"
                                            id="logo"
                                            onChange={(e) => setLogo(e.target.files[0])}
                                            className="w-full p-2 border border-gray-300 rounded-md"
                                        />
                                        </>
                                    )}
                                    </div>

                            </div>
                            <div className='text-left pt-6'>
                                <button
                                    type="submit"
                                    className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                                >
                                    <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                    {loading ? 'Updating...' : 'Update Team'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditTeam;
