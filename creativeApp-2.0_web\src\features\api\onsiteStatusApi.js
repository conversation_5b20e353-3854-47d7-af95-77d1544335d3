import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const onsiteStatusApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOnsiteStatusData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `onsite-status-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['OnsiteStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForOnsiteStatus: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `onsite-status-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['OnsiteStatusData'],
    }),

    getOnsiteStatusById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `onsite_statuses/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'OnsiteStatusData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createOnsiteStatus: builder.mutation({
      query: (newFormationType) => ({
        url: 'onsite-status-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['OnsiteStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateOnsiteStatus: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `onsite_statuses/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'OnsiteStatusData', id }, 'OnsiteStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteOnsiteStatus: builder.mutation({
      query: (id) => ({
        url: `onsite_statuses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['OnsiteStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetOnsiteStatusDataQuery,
  useLazyFetchDataOptionsForOnsiteStatusQuery,
  useGetOnsiteStatusByIdQuery,
  useLazyGetOnsiteStatusByIdQuery,
  useCreateOnsiteStatusMutation,
  useUpdateOnsiteStatusMutation,
  useDeleteOnsiteStatusMutation,
} = onsiteStatusApi;
