import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';
import { API_URL, ASSET_URL } from './../../common/fetchData/apiConfig'; 

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const EditHub = ({ hubId, isVisible, setVisible }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');

    // Hub details states
    const [hubsTitle, setHubsTitle] = useState('');
    const [hubsDetails, setHubsDetails] = useState('');
    const [hubsUrl, setHubsUrl] = useState('');
    const [hubsCta, setHubsCta] = useState('');
    const [hubsIcon, setHubsIcon] = useState('');
    const [hubsImage, setHubsImage] = useState(null);
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [existingPhoto, setExistingPhoto] = useState('');

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                // Fetch Hub details if editing an existing hub
                if (hubId) {
                    const hubResponse = await fetch(`${API_URL}quick-access-hubs/${hubId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (!hubResponse.ok) {
                        throw new Error('Failed to fetch hub details');
                    }

                    const hubData = await hubResponse.json();
                    setHubsTitle(hubData.hub.hubs_title || '');
                    setHubsDetails(hubData.hub.hubs_details || '');
                    setHubsUrl(hubData.hub.hubs_url || '');
                    setHubsCta(hubData.hub.hubs_cta || '');
                    setHubsIcon(hubData.hub.hubs_icon || '');
                    setExistingPhoto(hubData.hub.hubs_image || ''); // Assuming image URL is part of `hubData.hub`
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [hubId]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        setError(''); // Clear any previous error

        // Get user_id from localStorage for 'created_by'
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Prepare the data object
            const requestData = {
                hubs_title: hubsTitle.trim(),
                hubs_details: hubsDetails,
                hubs_url: hubsUrl,
                hubs_cta: hubsCta,
                hubs_icon: hubsIcon,
                updated_by: updatedBy,
            };

            // Convert icon and image files to Base64 if they are provided
            if (hubsImage && hubsImage instanceof File) { // Check if it's a valid File
                const imageBase64 = await convertToBase64(hubsImage);
                requestData.hubs_image = imageBase64;
            }

            // Log the final request data before submission
            console.log("Request data before submission:", requestData);

            // Make the PUT request to update the hub with JSON data
            const response = await fetch(`${API_URL}quick-access-hubs/${hubId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData), // Send data as JSON
            });

            if (!response.ok) {
                throw new Error('Failed to update hub: ' + response.statusText);
            }

            const result = await response.json();
            alertMessage('success');

            // Optionally, close the modal after success
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 2000);

            navigate('/quickaccesshub');
        } catch (error) {
            alertMessage('error');
        }
    };

    const convertToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            if (file instanceof File) {  // Check if the parameter is a valid File object
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result); // resolve with the Base64 string
                reader.onerror = reject;
                reader.readAsDataURL(file); // Convert file to Base64
            } else {
                reject('The provided object is not a valid File.');
            }
        });
    };

    // Logic for disabling icon and image fields
    const isIconDisabled = hubsImage !== null;  // Only disable if an image is uploaded
    const isImageDisabled = hubsIcon !== ''; // Disable image upload if icon is provided

    // Handle image change (size and dimensions validation)
    const handleImageChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Check file size
            if (file.size > 100 * 1024) { // 100KB limit
                setErrorMessage("File size should be less than 100KB");
                setHubsImage(null); // Clear the image if it exceeds the size
                return;
            }

            // Create a temporary image element to check dimensions
            const img = new Image();
            img.onload = () => {
                if (img.width > 300 || img.height > 300) { // 300x300 dimensions limit
                    setErrorMessage("Image dimensions should be 300x300 pixels or smaller");
                    setHubsImage(null); // Clear the image if it exceeds the dimensions
                } else {
                    setErrorMessage(""); // Clear error message if valid
                    setHubsImage(file); // Set the image if valid
                }
            };
            img.onerror = () => {
                setErrorMessage("Invalid image file");
                setHubsImage(null); // Clear the image if it's not a valid image
            };
            img.src = URL.createObjectURL(file); // Load the image
        }
    };

    // Handle resetting the image
    const handleResetImage = () => {
        setHubsImage(null);  // Reset the image preview
        setExistingPhoto(''); // Clear the existing image URL
        setHubsIcon(''); // Allow user to input icon again after image reset
    };

    // Handle when user starts editing the icon
    const handleIconChange = (e) => {
        setHubsIcon(e.target.value);
    };

    if (!isVisible) return null;

    return (
        <>
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative">
                    
                    <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-gray-800">Edit Quick Access Hub</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="text-left">
                        <div className="flex flex-wrap gap-6 p-6 overflow-y-auto h-auto scrollbar-vertical">
                            {/* Hub Title */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubs_title" className="block text-sm font-medium text-gray-700 pb-4">Hub Title</label>
                                <input
                                    type="text"
                                    id="hubs_title"
                                    name="hubs_title"
                                    value={hubsTitle}
                                    onChange={(e) => setHubsTitle(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    required
                                />
                            </div>

                            {/* Hub Details */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubs_details" className="block text-sm font-medium text-gray-700 pb-4">Hub Details</label>
                                <textarea
                                    id="hubs_details"
                                    name="hubs_details"
                                    value={hubsDetails}
                                    onChange={(e) => setHubsDetails(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />
                            </div>

                            {/* Hub URL */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubs_url" className="block text-sm font-medium text-gray-700 pb-4">Hub URL</label>
                                <input
                                    type="url"
                                    id="hubs_url"
                                    name="hubs_url"
                                    value={hubsUrl}
                                    onChange={(e) => setHubsUrl(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />
                            </div>

                            {/* Hub CTA */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubs_cta" className="block text-sm font-medium text-gray-700 pb-4">Hub CTA</label>
                                <input
                                    type="text"
                                    id="hubs_cta"
                                    name="hubs_cta"
                                    value={hubsCta}
                                    onChange={(e) => setHubsCta(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />
                            </div>

                            {/* Hub Icon */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsIcon" className="block text-sm font-medium text-gray-700 pb-4">Hub Icon (text)</label>
                                <input
                                    type="text"
                                    id="hubsIcon"
                                    name="hubsIcon"
                                    value={hubsIcon}
                                    onChange={handleIconChange}
                                    className={`w-full p-2 border rounded-md ${isIconDisabled ? 'bg-gray-200 opacity-50 cursor-not-allowed' : 'border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50'}`}
                                    disabled={isIconDisabled} // Only disable if an image is uploaded
                                />
                            </div>

                            {/* Image File Upload */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsImage" className="block text-sm font-medium text-gray-700 pb-4">Hub Image</label>
                                <div className='flex flex-row items-start justify-start gap-2 relative'>
                                    
                                    {/* Display existing image preview */}
                                    {(existingPhoto || hubsImage) && (
                                        <div className="w-40 h-40 overflow-hidden bg-gray-200 p-4 rounded-lg">
                                            <img
                                                src={hubsImage ? URL.createObjectURL(hubsImage) : `{ASSET_URL}/${existingPhoto}`}
                                                alt="Hub Image"
                                                className="w-auto h-auto object-cover"
                                            />
                                        </div>
                                    )}

                                    {/* Custom file input */}
                                    <label
                                        htmlFor="hubs_image"
                                        className={`cursor-pointer flex flex-col items-center justify-center gap-2 rounded-lg bg-gray-200 text-gray-800 w-40 h-40 p-4 hover:bg-green-100 ${isImageDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                                    >
                                        <span className="material-symbols-rounded text-6xl text-gray-300">
                                            photo_camera
                                        </span>
                                        <span className="text-sm text-gray-400">Upload new Image</span>
                                    </label>
                                    <input
                                        type="file"
                                        id="hubs_image"
                                        className="hidden"
                                        onChange={handleImageChange}
                                        disabled={isImageDisabled}
                                    />

                                    {/* Error message if file exceeds limits */}
                                    {errorMessage && (
                                        <div className="text-red-600 text-sm mt-2 text-left">{errorMessage}</div>
                                    )}

                                    {/* Reset Image Button */}
                                    {(hubsImage || existingPhoto) && (
                                        <button
                                            type="button"
                                            onClick={handleResetImage}
                                            className="text-red-500 text-sm mt-2 hover:text-red-700 absolute left-[42%] top-[-16px]"
                                        >
                                            <span class="material-symbols-rounded bg-white rounded-full">cancel</span>
                                        </button>
                                    )}
                                </div>
                            </div>


                            <div className='m-auto py-4'>
                                <button
                                    type="submit"
                                    className="min-w-56 bg-primary hover:bg-secondary text-white rounded-full py-3 px-6"
                                >
                                    {loading ? 'Updating...' : 'Update Hub'}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};

export default EditHub;
