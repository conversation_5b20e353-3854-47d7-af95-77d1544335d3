<?php

namespace App\Http\Controllers;

use App\Models\SchedulePlanner;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SchedulePlannerController extends Controller
{
    public function index(Request $request)
    {


        $query = SchedulePlanner::with(['createdBy', 'updatedBy', 'team', 'department', 'schedule']);

        // Filtering
        $filters = [
            'updated_by' => 'updated_by',
            'created_by' => 'created_by',
            'weeknum' => 'weeknum',
            'department_id' => 'department_id',
            'team_id' => 'team_id',
            'user_id' => 'user_id',
            'schedule_id' => 'schedule_id'
        ];

        foreach ($filters as $param => $column) {
            if ($request->filled($param)) {
                $values = explode(',', urldecode($request->input($param)));
                $query->whereIn($column, $values);
            }
        }

        // Global Search
        if ($request->filled('globalsearch')) {
            $search = urldecode($request->input('globalsearch'));
            $query->where(function ($q) use ($search) {
                $q->orWhere('weeknum', 'like', '%' . $search . '%')
                  ->orWhereHas('department', fn ($q) => $q->where('name', 'like', '%' . $search . '%'))
                  ->orWhereHas('team', fn ($q) => $q->where('name', 'like', '%' . $search . '%'))
                  ->orWhereHas('schedule', fn ($q) => $q->where('shift_name', 'like', '%' . $search . '%'))
                  ->orWhereHas('createdBy', fn ($q) => $q->where('fname', 'like', '%' . $search . '%'))
                  ->orWhereHas('updatedBy', fn ($q) => $q->where('fname', 'like', '%' . $search . '%'));
            });
        }

        // Sorting
        $sortBy = $request->query('sort_by', 'weeknum');
        $order = strtolower($request->query('order', 'desc')) === 'asc' ? 'asc' : 'desc';
        $query->orderBy($sortBy, $order)
        ->orderBy('department_id', $order)
        ->orderBy('team_id', $order)
        ->orderBy('schedule_id', $order);

        // Pagination
        $perPage = $request->query('per_page', 15);

        $datas = $query->paginate($perPage);
        
        // ->map(function ($planner) {
        //     $userIds = explode(',', $planner->user_id);
        //     $planner->users = User::with(['resourceTypes:name','designations:name'])->whereIn('id', $userIds)->get();
        //     return $planner;
        // });

        $datas->getCollection()->transform(function ($planner) {
            $userIds = explode(',', $planner->user_id);
            $planner->users = User::with(['resourceTypes:name','designations:name'])->whereIn('id', $userIds)->get();
            return $planner;
        });



        return response()->json( $datas, 200);
    }

    public function group(Request $request)
    {
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }

        $results = SchedulePlanner::with(['createdBy', 'updatedBy','user', 'team', 'department', 'schedule']);

        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    
    }

    public function searchByField(Request $request)
    {
        $column = urldecode($request->query('column'));
        $text = urldecode($request->query('text'));

        if (!$column) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $query = SchedulePlanner::with(['createdBy', 'updatedBy','user', 'team', 'department', 'schedule']);

        if (strpos($column, ".") !== false) {
            [$relation, $field] = explode('.', $column);
            $query->whereHas($relation, fn ($q) => $q->where($field, 'like', '%' . $text . '%'));
        } else {
            $query->where($column, 'like', '%' . $text . '%');
        }

        return response()->json($query->get(), 200);
    }

    public function store(Request $request)
    {
        // Log request files and input fields
        // Log::info('user_id:', ['user_id' => $request->input('user_id')]);


        $validated = $request->validate([
            'department_id' => 'required|exists:departments,id',
            'team_id' => 'required|exists:teams,id',
            'weeknum' => 'required',           
            'user_id' => 'required',
            'schedule_id' => 'required|exists:schedules,id',
            'created_by' => 'required|exists:users,id',
            
        ]);

        try {
            $authUser = $request->user();

            // \Log::info('Authenticated User:', ['user' => $authUser]);
            // \Log::info('Roles:', ['roles' => $authUser->roles]);

            if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {

                $hasData = SchedulePlanner::where(function ($query) use ($request) {
                    return $query
                        ->where('team_id', $request->team_id)
                        ->where('weeknum', $request->weeknum)
                        ->where('schedule_id', $request->schedule_id);
                });

                if ($hasData->exists()) {
                    return response()->json([
                            "status"=> "error",
                            "message"=> "Already Created this sift for this team and this week.",
                            "errors"=> [
                                "schedule_id" => [ "Already Created" ]
                            ]
                    ], 400);
                }

                $insertUsers = explode(",",$request->user_id);
                $hasUserData = SchedulePlanner::where('weeknum', $request->weeknum)->where(function ($query) use ($insertUsers, $request) {                    
                    foreach ($insertUsers as $userId) {
                        $query->orWhereRaw("FIND_IN_SET(?, user_id)", [$userId]);
                    }
                });

                $foundedUserIds = $hasUserData->get()->pluck('user_id')->flatten()->toArray();

                // Step 1: Explode and merge all values into a single array
                $allUserIds = array_merge(...array_map(fn($ids) => explode(',', $ids), $foundedUserIds));

                // Step 2: Remove duplicates and reindex the array
                $uniqueUserIds = array_values(array_unique($allUserIds));

                // Step 1: Extract common user IDs
                $commonUserIds = array_intersect($insertUsers, $uniqueUserIds);

                // Step 2: Create a unique array (no duplicates)
                $newUniqueUserIds = array_values(array_unique($commonUserIds));

                $foundedUsers = User::whereIn('id', $newUniqueUserIds)->get()->toArray();

                $generatedUsersName = array_values(array_filter(array_map(function ($user) use (&$ids) {
                    return $user['fname'] . ' ' . $user['lname'] . ' (' . $user['eid'] . ')';
                }, $foundedUsers)));

                if ($hasUserData->exists()) {
                    return response()->json([
                            "status"=> "error",
                            "message"=> "Already employees assigned.",
                            "errors"=> [
                                "user_id" => $generatedUsersName,
                            ]
                    ], 400);
                }


                $schedulePlanner = SchedulePlanner::create($validated);
                return response()->json([
                    'status' => 'success',
                    'message' => 'Created successfully',
                    'data' => $validated
                ], 201);
            }
        } catch (\Throwable $th) {
            Log::warning('Unauthorized Schedule Planner Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'Unauthorized action.'], 403);
        }
    }

    public function show(SchedulePlanner $schedulePlanner)
    {
        return response()->json($schedulePlanner, 200);
    }

    public function update(Request $request, SchedulePlanner $schedulePlanner)
    {
        $validated = $request->validate([
            'department_id' => 'required|exists:departments,id',
            'team_id' => 'required|exists:teams,id',
            'weeknum' => 'required',
            'user_id' => 'required',
            'schedule_id' => 'required|exists:schedules,id',
            'updated_by' => 'required|exists:users,id',
        ]);

        

        $schedulePlanner->update($validated);

        return response()->json([
            'status' => 'success',
            'message' => 'Updated successfully',
            'data' => $schedulePlanner
        ], 200);
    }

    public function destroy(SchedulePlanner $schedulePlanner)
    {
        $schedulePlanner->delete();
        return response()->json(null, 204);
    }
}
