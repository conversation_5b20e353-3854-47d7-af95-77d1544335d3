import { baseApi } from './baseApi';

export const listApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getLocationsList: builder.query({ query: () => "list/office_location" }),
    getDepartmentsList: builder.query({ query: () => "list/departments" }),
    getTeamsList: builder.query({ query: () => "list/teams" }),
    getShiftsList: builder.query({ query: () => "list/shifts" }),
    getUsersByDefaultTeamList: builder.query({ query: () => "list/users-by-default-team" }),
    getDesignationsList: builder.query({ query: () => "list/designations" }),
  }),
});

export const {

  useGetLocationsListQuery,
  useGetDepartmentsListQuery,
  useGetTeamsListQuery,
  useGetShiftsListQuery,
  useGetUsersByDefaultTeamListQuery,
  useGetDesignationsListQuery,
} = listApi;
