import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditMember from './EditMember';
import TablePagination from '../../common/table/TablePagination';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const MemberOnboardList = ({ searchTerm}) => {
    const [users, setUsers] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "EID", key: "eid" },
        { label: "Email", key: "email" },
        { label: "Team", key: "team" },
        { label: "Department", key: "department" },
        { label: "Billing Status", key: "billing_status" },
        { label: "Resource Status", key: "resource_status" },
        { label: "Responsibility Level", key: "resource_type" },
        { label: "Contact Type", key: "contact_type" },
        { label: "Availability Status", key: "available_status" },
        { label: "Team Member Status", key: "member_status" },
        { label: "Designation", key: "designation" },
    ];

    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();

                const mappedUsers = data.map(user => ({
                    id: user.id,
                    eid: user.eid || '',
                    email: user.email || '',
                    team: user.teams.length > 0 ? user.teams[0].name : '',
                    department: user.departments.length > 0 ? user.departments[0].name : '',
                    billing_status: user.billing_statuses.length > 0 ? user.billing_statuses[0].name : '',
                    resource_status: user.resource_statuses.length > 0 ? user.resource_statuses[0].name : '',
                    resource_type: user.resource_types.length > 0 ? user.resource_types[0].name : '',
                    contact_type: user.contact_types.length > 0 ? user.contact_types[0].name : '',
                    available_status: user.available_statuses.length > 0 ? user.available_statuses[0].name : '',
                    member_status: user.member_statuses.length > 0 ? user.member_statuses[0].name : '',
                    designation: user.designations.length > 0 ? user.designations[0].name : '',
                }));
                
                setUsers(mappedUsers);
                setFilteredUsers(mappedUsers);

            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUsers();
    }, [currentPage, itemsPerPage]);

    // Pagination logic
    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm.toLowerCase().trim();

        const highlightText = (text) => {
            const strText = text ? text.toString() : '';
        
            const regex = new RegExp(`(${normalizedSearchTerm})`, 'gi');
            const parts = strText.split(regex);
        
            return parts.map((part, index) => {
                return regex.test(part) ? (
                    <span key={index} className="bg-yellow-300">{part}</span>
                ) : part;
            });
        };        
        
    
        if (!normalizedSearchTerm) {
            setFilteredUsers(users);
            return;
        }
    
        const filtered = users.filter(user => {
            return Object.values(user).some(value =>
                value && value.toString().toLowerCase().includes(normalizedSearchTerm)
            );
        }).map(user => ({
            id: user.id,
            eid: highlightText(user.eid),
            email: highlightText(user.email || ''),
            team: highlightText(user.teams && user.teams.length > 0 ? user.teams[0].name : ''),
            department: highlightText(user.departments && user.departments.length > 0 ? user.departments[0].name : ''),
            billing_status: highlightText(user.billing_statuses && user.billing_statuses.length > 0 ? user.billing_statuses[0].name : ''),
            resource_status: highlightText(user.resource_statuses && user.resource_statuses.length > 0 ? user.resource_statuses[0].name : ''),
            resource_type: highlightText(user.resource_types && user.resource_types.length > 0 ? user.resource_types[0].name : ''),
            contact_type: highlightText(user.contact_types && user.contact_types.length > 0 ? user.contact_types[0].name : ''),
            available_status: highlightText(user.available_statuses && user.available_statuses.length > 0 ? user.available_statuses[0].name : ''),
            member_status: highlightText(user.member_statuses && user.member_statuses.length > 0 ? user.member_statuses[0].name : ''),
            designation: highlightText(user.designations && user.designations.length > 0 ? user.designations[0].name : ''),
        }));
        
    
        setFilteredUsers(filtered);
    }, [searchTerm, users]);


    const handleEdit = (id) => {
        setSelectedUserId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit} // Pass the edit handler
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedUserId}
                hideDeleteButton={true}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredUsers.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
            {modalVisible && selectedUserId && (
                <EditMember 
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    userId={selectedUserId}
                />
            )}
        </div>
    );
};

export default MemberOnboardList;
