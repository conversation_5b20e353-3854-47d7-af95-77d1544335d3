
let attendanceData = [];
for (let index = 3; index < 29; index++) {

    attendanceData.push({
        colorBg:'#bbf7d0',
        color:'#4d7c0f',
        'title': 'Present',
        'start': new Date(2025, 1, index, 14,0,0),
        'end': new Date(2025, 1, index, 23, 0, 0)
    })
    
    attendanceData.push({
        colorBg:'#ffedd5',
        color:'#e17d00',
        'title': 'Break: 01:00h',
        'start': new Date(2025, 1, index),
        'end': new Date(2025, 1, index)
    })

    attendanceData.push({
        colorBg:'#ffedd5',
        color:'#e17d00',
        'title': 'Break: Prayer',
        'start': new Date(2025, 1, index, 16, 50,0),
        'end': new Date(2025, 1, index, 17, 20,0)
    })

    attendanceData.push({
        colorBg:'#ffedd5',
        color:'#e17d00',
        'title': 'Break: Prayer',
        'start': new Date(2025, 1, index, 18, 0,0),
        'end': new Date(2025, 1, index, 18, 30,0)
    })


    attendanceData.push({
        colorBg:'#cffafe',
        color:'#0e7490',
        'title': 'Duration: 08:00h',
        'start': new Date(2025, 1, index),
        'end': new Date(2025, 1, index)
    })


}

export default [
    ...attendanceData,
  
    {
        colorBg:'pink',
        color:'#fff',
        allDay: true,
        'title': 'Annual Leave',
        'start': new Date(2025, 1, 13, 0, 0, 0),
        'end': new Date(2025, 1, 13, 0, 0, 0)
    },
  
    
  ]
  