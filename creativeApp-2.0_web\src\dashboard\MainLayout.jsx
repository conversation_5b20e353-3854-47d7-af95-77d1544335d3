import React, { useState } from 'react';
import LeftSidebar from './LeftSidebar';
import Header from '../common/Header';
import { Outlet } from 'react-router-dom';


const MainLayout = () => {
    const [isSidebarOpen, setSidebarOpen] = useState(true);

    const toggleSidebar = () => {
        setSidebarOpen(prev => !prev);
    };

  return (
      <div className="flex">
          <Header onToggleSidebar={toggleSidebar} />
          <div className="flex flex-row pt-[55px] w-full bg-neutral-100 dark:bg-gray-700">
              <div className='relative'>
                  <LeftSidebar isOpen={isSidebarOpen} /> {/* No need to pass setSelectedComponent if using routes */}
              </div>
              <div className='w-full m-4 overflow-x-auto rounded-xl'>
                  <Outlet /> {/* Use Outlet to render nested routes */}
              </div>
          </div>
      </div>
  );
};


export default MainLayout;
