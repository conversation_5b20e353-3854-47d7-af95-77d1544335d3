import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditBranch = ({ isVisible, setVisible, dataItemsId }) => {
    const [branchName, setBranchName] = useState('');
    const [selectedLocation, setSelectedLocation] = useState('');
    const [branches, setBranches] = useState([]);
    const [locations, setLocations] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [loading, setLoading] = useState(false);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchBranchName = async () => {
            if (!dataItemsId) return;
    
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }
    
            try {
                const response = await fetch(`${API_URL}/branches`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch branches: ' + response.statusText);
                }
    
                const data = await response.json();
    
                const branchArray = data['branches']; // Adjusted to use 'branches' data
                if (!Array.isArray(branchArray)) {
                    throw new Error('Expected branches to be an array.');
                }
    
                // Find the branch by ID
                const branchData = branchArray.find(branch => branch.id === dataItemsId);
                if (branchData) {
                    setBranchName(branchData.name);
                    // Assuming 'locations' is an array of related location objects
                    const defaultLocationId = branchData.locations?.[0]?.id || '';
                    setSelectedLocation(defaultLocationId);
                }else {
                    throw new Error('Branch not found. Please check the ID.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchLocations = async () => {
            const token = localStorage.getItem('token');
    
            try {
                const response = await fetch(`${API_URL}/locations`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch locations');
                }
    
                const data = await response.json();
                setLocations(data.locations || []); // Adjust based on actual response structure
            } catch (error) {
                setError(error.message);
            }
        };

        fetchBranchName();
        fetchLocations();
    }, [dataItemsId]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedBranchName = branchName.trim();

        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
    
        if (Array.isArray(branches)) {
            const branchExists = branches.some(branch => {
                const branchNameLower = branch.name.toLowerCase().trim();
                return branchNameLower === trimmedBranchName.toLowerCase();
            });
    
            if (branchExists) {
                setError('Branch already exists. Please add a different branch.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
    
            const response = await fetch(`${API_URL}/branches/${dataItemsId}`, { // Update branch endpoint
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedBranchName,
                    location_id: selectedLocation, // Send the selected location ID
                    updated_by: updatedBy,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to update branch: ' + response.statusText);
            }
    
            const result = await response.json();
    
            const updatedBranchName = result.branch.name; // Adjust to use the 'branch' object
    
            //setSuccessMessage(`Branch "${updatedBranchName}" updated successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Office branch updated successfully.',
            });

            setBranchName('');
            setSelectedLocation(''); // Reset selected location
    
            // Optionally, refetch branches
            const newBranchesResponse = await fetch(`${API_URL}/branches`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newBranchesResponse.ok) {
                throw new Error('Failed to fetch branches: ' + newBranchesResponse.statusText);
            }
    
            const newBranchesData = await newBranchesResponse.json();
            setBranches(newBranchesData['branches'] || []);
    
            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);
            
        } catch (error) {
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                    <h3 className="text-base text-left font-medium text-gray-800">Update Branch</h3>
                    <button
                        className="text-2xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit} className='p-6'>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Branch Name</label>
                        <input
                            type="text"
                            id="name"
                            value={branchName}
                            onChange={(e) => setBranchName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>

                    <div className="mb-4">
                        <label htmlFor="location" className="block mb-2">Select Location</label>
                        <select
                            id="location"
                            value={selectedLocation}
                            onChange={(e) => setSelectedLocation(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        >
                            <option value="">Select a Location</option>
                            {locations.length === 0 ? (
                                <option disabled>No locations available</option>
                            ) : (
                                locations.map((location) => (
                                    <option key={location.id} value={location.id}>
                                        {location.locations_name}
                                    </option>
                                ))
                            )}
                        </select>
                    </div>
                    <div className='text-left p-6'>
                            <button
                                type="submit"
                                className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            >
                                <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                {loading ? 'Updating...' : 'Update Branch'}
                            </button>
                        </div>
                </form>
            </div>
        </div>
    );
};

export default EditBranch;
