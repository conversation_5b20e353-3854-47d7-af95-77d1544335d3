import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent'; // Keep the TableContent import
import EditResourceType from './EditResourceType'; // Create and import the EditResourceType component

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ResourceTypeList = () => {
    const [resourceTypes, setResourceTypes] = useState([]); // Store resource types here
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedResourceTypeId, setSelectedResourceTypeId] = useState(null);
    const [error, setError] = useState(null);

    // Define column names for resource types (adjust if necessary)
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Resource Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchResourceTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');

    
            try {
                const response = await fetch(`${API_URL}/resource_types`, { // Adjusted API endpoint for resource types
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
               
    
                // Access resource types with bracket notation (adjusted for resource types)
                if (Array.isArray(data['Resource Types'])) {
                    setResourceTypes(data['Resource Types'].map(type => ({
                        id: type.id,
                        name: type.name,
                        created_by: type.created_by,
                        updated_by: type.updated_by,
                    })));
                } else {
                    setError('Invalid data format: resource types is not an array.');
                }
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchResourceTypes();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/resource_types/${id}`, { // Updated endpoint for resource types
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete resource type: ' + response.statusText);
            }

            // Update the resource types list after deletion
            setResourceTypes(prevTypes => prevTypes.filter(type => type.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedResourceTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={resourceTypes}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedResourceTypeId} // Updated for resource type
            />
            {modalVisible && (
                <EditResourceType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    resourceTypeId={selectedResourceTypeId} // Updated for resource type
                />
            )}
        </div>
    );
};

export default ResourceTypeList;
