import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { alertMessage } from "../../common/coreui";

const getDayOfWeek = (date) => {
  return new Intl.DateTimeFormat("en-US", { weekday: "long" }).format(date);
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const isTokenValid = () => {
  const token = localStorage.getItem("token");
  return token !== null;
};

const AddHolidayCalender = ({ isVisible, setVisible }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [holidays, setHolidays] = useState([]);
  const [holidayName, setHolidayName] = useState("");
  const [locationId, setLocationId] = useState("");
  const [holidayStartDate, setHolidayStartDate] = useState("");
  const [holidayEndDate, setHolidayEndDate] = useState("");
  const [dayOfWeek, setDayOfWeek] = useState("");
  const [days, setDays] = useState(0);
  const [departmentId, setDepartmentId] = useState("");
  const [userDepartments, setUserDepartments] = useState([]);
  const [teamId, setTeamId] = useState("");
  const [error, setError] = useState("");
  const [locations, setLocations] = useState([]);
  const [userTeams, setUserTeams] = useState([]); // State to store logged-in user's teams
  const [loadingLocations, setLoadingLocations] = useState(true);
  const [loadingDepartments, setLoadingDepartments] = useState(true);
  const [loading, setLoading] = useState(false);

const API_URL = process.env.REACT_APP_BASE_API_URL;
  // Fetch logged-in user data to get their teams
  useEffect(() => {
    const fetchUserData = async () => {
      setLoading(true);
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(
            "Failed to fetch logged-in user data: " + response.statusText
          );
        }

        const data = await response.json();
        setUserTeams(data.teams || []); // Save teams data in state
        setUserDepartments(data.departments || []); // Save departments data in state

        // Set the first department as default if available
        if (data.departments && data.departments.length > 0) {
          setDepartmentId(data.departments[0].id);
        }
      } catch (error) {
        alertMessage("error");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  useEffect(() => {
    if (holidayStartDate && holidayEndDate) {
      const start = new Date(holidayStartDate);
      const end = new Date(holidayEndDate);

      if (end >= start) {
        let totalDays = 0;
        const weekdaysList = [];

        let currentDate = new Date(start);

        while (currentDate <= end) {
          const dayName = getDayOfWeek(currentDate);
          if (dayName !== "Saturday" && dayName !== "Sunday") {
            totalDays++;
            weekdaysList.push(dayName);
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }

        setDays(totalDays);
        setDayOfWeek(weekdaysList.join(", "));
      } else {
        setDays(0);
        setDayOfWeek("");
      }
    }
  }, [holidayStartDate, holidayEndDate]);

  useEffect(() => {
    const fetchLocations = async () => {
      setLoadingLocations(true);
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(`${API_URL}/locations`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch locations: " + response.statusText);
        }

        const data = await response.json();
        setLocations(data.locations || []);
      } catch (error) {
        alertMessage("error");
      } finally {
        setLoadingLocations(false);
      }
    };

    fetchLocations();
  }, []);

  useEffect(() => {
    const fetchHolidays = async () => {
      setLoading(true);
      const token = localStorage.getItem("token");
      try {
        const response = await fetch(
          `${API_URL}/holiday-calenders`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch holidays: " + response.statusText);
        }

        const data = await response.json();
        console.log("All Holiday data", data);

        setHolidays(data.holidayCalenders || []);
      } catch (error) {
        alertMessage("error");
      } finally {
        setLoading(false);
      }
    };

    fetchHolidays();
  }, []);

  // Handle form submission
  const createdBy = localStorage.getItem("user_id"); // Get the logged-in user ID from localStorage

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);

    const trimmedHolidayName = holidayName.trim();

    const holidayExists = holidays.some(
      (holiday) =>
        holiday.holiday_name.toLowerCase().trim() ===
        trimmedHolidayName.toLowerCase()
    );

    if (holidayExists) {
      setError(
        "The holiday name has already been taken. Please choose a different name."
      );
      setLoading(false);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${API_URL}/holidaycalenders`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            holiday_name: trimmedHolidayName,
            location_id: locationId,
            holiday_start_date: holidayStartDate,
            holiday_end_date: holidayEndDate,
            day_of_week: dayOfWeek,
            days: days,
            created_by: createdBy,
            department_id: departmentId,
            team_id: teamId,
          }),
        }
      );

      // Safely try to parse JSON
      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        result = null; // in case no JSON returned
      }

      if (!response.ok) {
        if (result?.errors?.holiday_name) {
          setError(result.errors.holiday_name[0]);
        } else {
          setError("Failed to create holiday calendar. Please try again.");
          alertMessage({
            icon: "error",
            title: "Error",
            text: "Failed to create holiday calendar.",
          });
        }
        setLoading(false);
        return;
      }

      // ✅ Success alert
      alertMessage({
        icon: "success",
        title: "Success!",
        text: result?.message || "Holiday added successfully.",
      });

      // Reset form
      setHolidayName("");
      setLocationId("");
      setHolidayStartDate("");
      setHolidayEndDate("");
      setDayOfWeek("");
      setDays("");

      setLoading(false);
    } catch (error) {
      console.error("Unexpected error in handleSubmit:", error);
      alertMessage({
        icon: "error",
        title: "Unexpected Error",
        text: error.message || "Something went wrong.",
      });
      setLoading(false);
    }
  };

  if (!isVisible) return null;

  return (
    <>
      <div className="fixed top-0 left-0 right-0 bottom-0 bg-neutral-950 bg-opacity-80 flex justify-center items-center z-50 overflow-hidden">
        <div className="bg-neutral-50 rounded-xl overflow-hidden shadow-md w-full max-w-4xl relative">
          <div className="flex justify-between items-center border-b border-neutral-200 bg-neutral-100 px-4 py-2">
            <h4 className="text-base text-left font-medium text-slate-800">
              Add New Holiday
            </h4>
            <button
              className="text-3xl text-gray-500 hover:text-red-600"
              onClick={() => setVisible(false)}
            >
              &times;
            </button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="flex flex-wrap gap-[4%] p-6 overflow-y-auto max-h-[90vh] scrollbar-vertical">
              {/* Departments */}
              <div className="mb-8 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="departmentId"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Department <span className="text-red-600">*</span>
                </label>
                <select
                  id="departmentId"
                  value={departmentId}
                  onChange={(e) => setDepartmentId(e.target.value)}
                  className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  disabled={loading || userDepartments.length === 0} // Disable if no departments available
                  required
                >
                  <option value="">Select Department</option>
                  {loading ? (
                    <option disabled>Loading departments...</option>
                  ) : userDepartments.length === 0 ? (
                    <option disabled>No departments available</option>
                  ) : (
                    userDepartments.map((department) => (
                      <option key={department.id} value={department.id}>
                        {department.name} {/* Display department name */}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {/* Selected Teams based on Departments */}
              <div className="mb-8 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="teamId"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Team <span className="text-red-600">*</span>
                </label>
                <select
                  id="teamId"
                  value={teamId}
                  onChange={(e) => setTeamId(e.target.value)}
                  className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  disabled={loading || userTeams.length === 0} // Disable if no teams available
                  required
                >
                  <option value="">Select Team</option>
                  {loading ? (
                    <option disabled>Loading teams...</option>
                  ) : userTeams.length === 0 ? (
                    <option disabled>No teams available</option>
                  ) : (
                    userTeams.map((team) => (
                      <option key={team.id} value={team.id}>
                        {team.name} {/* Display team name */}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {/* Location */}
              <div className="mb-8 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="locationId"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Office Location <span className="text-red-600">*</span>
                </label>
                <select
                  id="locationId"
                  value={locationId}
                  onChange={(e) => setLocationId(e.target.value)}
                  className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  disabled={loadingLocations}
                  required
                >
                  <option value="">Select Office Location</option>
                  {loadingLocations ? (
                    <option disabled>Loading locations...</option>
                  ) : locations.length === 0 ? (
                    <option disabled>No locations available</option>
                  ) : (
                    locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.locations_name}
                      </option>
                    ))
                  )}
                </select>
              </div>
              {/* Holiday Name */}
              <div className="mb-8 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="holidayName"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Holiday Name <span className="text-red-600">*</span>
                </label>
                <input
                  type="text"
                  id="holidayName"
                  value={holidayName}
                  onChange={(e) => setHolidayName(e.target.value)}
                  placeholder="Add a Holiday Name"
                  required
                  className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                />
              </div>
              {/* Start Date */}
              <div className="mb-6 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="holidayStartDate"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Holiday Start Date <span className="text-red-600">*</span>
                </label>
                <input
                  type="date"
                  id="holidayStartDate"
                  value={holidayStartDate}
                  onChange={(e) => setHolidayStartDate(e.target.value)}
                  className="block w-full bg-stone-50 border-stone-200 text-[#1f2937] placeholder-[#9ca3af]rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  required
                />
              </div>
              {/* End Date */}
              <div className="mb-6 w-full md:max-w-[48%] text-left">
                <label
                  htmlFor="holidayEndDate"
                  className="block text-sm text-gray-400 pb-2"
                >
                  Holiday End Date <span className="text-red-600">*</span>
                </label>
                <input
                  type="date"
                  id="holidayEndDate"
                  value={holidayEndDate}
                  onChange={(e) => setHolidayEndDate(e.target.value)}
                  className="block w-full bg-stone-50 border-stone-200 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  required
                />
              </div>
            </div>
            {error && <p className="text-red-500 text-sm">{error}</p>}

            <div className="text-left p-6">
              <button
                type="submit"
                className="w-full bg-primary hover:bg-secondary text-white py-3 rounded-xl flex flex-row gap-4 items-center justify-center m-auto"
              >
                <span class="material-symbols-rounded text-white text-xl font-regular">
                  add_circle
                </span>
                {loading ? "Adding Holiday..." : "Add Holiday"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default AddHolidayCalender;
