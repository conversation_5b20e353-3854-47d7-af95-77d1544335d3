<?php

namespace App\Http\Controllers;

use App\Models\TrainingTopic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TrainingTopicsController extends Controller
{
    /**
     * Display a listing of all training topics.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $trainingTopics = TrainingTopic::all();

        // Log the training topics retrieved
        Log::info('All training topics Retrieved:', ['training_topics_count' => $trainingTopics->count()]);

        return response()->json(['trainingTopics' => $trainingTopics], 200);
    }

    /**
     * Display the specified Training topic.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the Training topic by ID
        $trainingTopic = TrainingTopic::find($id);

        if (!$trainingTopic) {
            return response()->json(['error' => 'Training topic not found.'], 404);
        }

        // Log the Training topic retrieved
        Log::info('Training topic Retrieved:', ['training_topic' => $trainingTopic]);

        return response()->json(['trainingTopic' => $trainingTopic], 200);
    }

    /**
     * Create a new Training topic.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        Log::info('Create Training topic Request:', ['request' => $request->all()]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);

        // Log the request data
        Log::info('Create Training topic Request:', ['request' => $request->all()]);

        // Check if the Training topic name already exists
        if (TrainingTopic::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Training topic already exists.'], 409);
        }

        // Create a new Training topic
        $trainingTopic = TrainingTopic::create([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('Training topic Created:', ['training_topic' => $trainingTopic]);

        return response()->json(['message' => 'Training topic created successfully.', 'training_topic' => $trainingTopic], 201);
    }

    /**
     * Update an existing Training topic.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        Log::info('Update Training topic Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);
    
        // Find the existing Training topic by ID
        $trainingTopic = TrainingTopic::find($id);
    
        // Check if the Training topic exists
        if (!$trainingTopic) {
            return response()->json(['error' => 'Training topic not found.'], 404);
        }
    
        // Check if the Training topic name is being changed and ensure it does not conflict with an existing name
        if ($trainingTopic->name !== $request->name && TrainingTopic::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Training topic with the same name already exists.'], 409);
        }
    
        // Update the Training topic with the new data
        $trainingTopic->update([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,  // Track who updated the Training topic
        ]);
    
        Log::info('Training topic Updated:', ['training_topic' => $trainingTopic]);
    
        return response()->json(['message' => 'Training topic updated successfully.', 'training_topic' => $trainingTopic], 200);
    }
    

    /**
     * Delete a Training topic.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the Training topic
            $trainingTopic = TrainingTopic::findOrFail($id);

            // Delete the Training topic
            $trainingTopic->delete();

            return response()->json(['message' => 'Training topic deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this Training topic.'], 403);
    }
}
