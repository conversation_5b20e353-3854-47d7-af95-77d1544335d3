import React, { useEffect, useState } from 'react';
import Avatar from './../../assets/images/avatar.png';
import { Link } from 'react-router-dom';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

export default function ContactList() {

    const [userData, setUserData] = useState([]); // Store array of users
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();

                setUserData(data);

            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUsers();
    }, []);

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div className="rounded-xl">
            {userData.length > 0 ? (
                <div>
                    {userData.map((user) => (
                        <Link key={user.id} to={`/user/${user.id}`} className="flex flex-row items-center justify-start gap-4 text-left mb-4">
                            <div className="relative">
                                <img src={Avatar} alt="Profile" className="m-auto text-sm w-12" />
                                <span className="flex w-4 h-4 bg-green-500 rounded-full absolute top-0 left-9 border-2 border-white"></span>
                            </div>
                            <div className="block">
                                <h4 className="text-left font-bold text-sm whitespace-nowrap">{user.fname} {user.lname}</h4>
                                <p className="text-left">
                                    {user?.designations?.[0]?.name || "Designation not found"}
                                </p>
                            </div>
                        </Link>
                    ))}
                </div>
            ) : (
                <div>No users found</div>
            )}
        </div>
    );
}
