import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const AddTeamShiftPlan = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [employees, setEmployees] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [selectedEmployee, setSelectedEmployee] = useState('');
    const [selectedSchedule, setSelectedSchedule] = useState('');
    const [selectedDate, setSelectedDate] = useState(null);
    const [weekNumber, setWeekNumber] = useState('');
    const [schedules, setSchedules] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const resetForm = () => {
        setSelectedDepartment('');
        setSelectedTeam('');
        setSelectedEmployee('');
        setSelectedSchedule('');
        setSelectedDate(null);
        setWeekNumber('');
    };

    const calculateWeekNumber = (date) => {
        const inputDate = new Date(date);
        const firstDayOfYear = new Date(inputDate.getFullYear(), 0, 1);
        const pastDaysOfYear = (inputDate - firstDayOfYear + 86400000) / 86400000; // Milliseconds in a day
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    };

    const handleDateChange = (date) => {
        setSelectedDate(date);
        if (date) {
            const week = calculateWeekNumber(date);
            setWeekNumber(`Week ${week}`);
        } else {
            setWeekNumber('');
        }
    };

    useEffect(() => {
        const fetchData = async (endpoint, setState, errorMsg) => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                navigate('/login');
                return;
            }

            try {
                const response = await fetch(`${API_URL}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) throw new Error(errorMsg);

                const data = await response.json();
                if (endpoint === 'users') {
                    setState(data.users || data || []);
                } else {
                    setState(data[endpoint] || []);
                }
            } catch (err) {
                setError(err.message);
            }
        };

        fetchData('departments', setDepartments, 'Failed to fetch departments.');
        fetchData('schedules', setSchedules, 'Failed to fetch schedules.');
        fetchData('users', setEmployees, 'Failed to fetch employees.');
    }, [navigate]);

    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam('');

        const department = departments.find(dep => dep.name === departmentName);
        if (department && Array.isArray(department.teams)) {
            setTeams(department.teams);
        } else {
            setTeams([]);
            setError('No teams found for the selected department.');
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!selectedDepartment || !selectedTeam || !selectedEmployee || !selectedSchedule || !selectedDate) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                navigate('/login');
                return;
            }

            const fullName = `${localStorage.getItem('fname') || ''} ${localStorage.getItem('lname') || ''}`.trim();

            const response = await fetch(`${API_URL}team-shift-plan`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department: selectedDepartment,
                    team: selectedTeam,
                    employee_id: selectedEmployee,
                    schedule: selectedSchedule,
                    date: selectedDate,
                    week_number: weekNumber,
                    created_by: fullName,
                    updated_by: fullName,
                }),
            });

            if (!response.ok) throw new Error('Failed to add shift plan.');

            const result = await response.json();
            setSuccessMessage(`Shift Plan added successfully!`);
            resetForm();
        } catch (err) {
            setError(err.message);
        }
    };

    return (
        <>
            {location.pathname === '/add-team-shift-plan' && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg">
                        <div className="flex justify-between items-center mb-6">
                            <h4 className="text-xl font-semibold">Add Team Shift Plan</h4>
                            <button
                                className="text-gray-600 hover:text-gray-900"
                                onClick={() => navigate(-1)} // Close button action
                            >
                                Close
                            </button>
                        </div>

                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-2">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.map(department => (
                                        <option key={department.id} value={department.name}>
                                            {department.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 mb-2">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.map(team => (
                                        <option key={team.id} value={team.name}>
                                            {team.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="employee" className="block text-sm font-medium text-gray-700 mb-2">
                                    Select Employee
                                </label>
                                <select
                                    id="employee"
                                    value={selectedEmployee}
                                    onChange={(e) => setSelectedEmployee(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select an Employee</option>
                                    {employees.map(employee => (
                                        <option key={employee.id} value={employee.id}>
                                            {employee.eid} - {employee.fname || 'First Name Missing'}{' '}
                                            {employee.lname || 'Last Name Missing'}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="schedule" className="block text-sm font-medium text-gray-700 mb-2">
                                    Select Schedule
                                </label>
                                <select
                                    id="schedule"
                                    value={selectedSchedule}
                                    onChange={(e) => setSelectedSchedule(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    required
                                >
                                    <option value="">Select a Schedule</option>
                                    {schedules.map(schedule => (
                                        <option key={schedule.id} value={schedule.id}>
                                            {schedule.shift_name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="week" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Week
                                </label>
                                <DatePicker
                                    id="week"
                                    selected={selectedDate}
                                    onChange={handleDateChange}
                                    dateFormat="wo 'week of' yyyy"
                                    className="block w-full border-gray-300 rounded-md shadow-sm"
                                    showWeekNumbers
                                    calendarStartDay={1}
                                />
                                {weekNumber && (
                                    <p className="text-gray-700 mt-2">
                                        Selected: <strong>{weekNumber}</strong>
                                    </p>
                                )}
                            </div>

                            <div className="py-4">
                                <button type="submit" className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3">
                                    Add Shift Plan
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm mt-2">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddTeamShiftPlan;
