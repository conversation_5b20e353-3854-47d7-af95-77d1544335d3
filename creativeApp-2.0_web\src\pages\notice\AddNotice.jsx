import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from "./EditorToolbar";
import 'react-quill/dist/quill.snow.css';

const isTokenValid = () => {
    return localStorage.getItem('token') !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';



const AddNotice = () => {
    const location = useLocation();
    const navigate = useNavigate();
    
    const [title, setTitle] = useState('');
    const [content, setContent] = useState('');
    const [priority, setPriority] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [category, setCategory] = useState('');
    const [expiryDate, setExpiryDate] = useState('');
    const [publishedDate, setPublishedDate] = useState('');
    const [status, setStatus] = useState("active"); // Default selected status
    const statuses = ["active", "inactive", "pending", "archived"];
    const [priorities, setPriorities] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [categories, setCategories] = useState([]);
    
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
            try {
                const token = localStorage.getItem('token');
                
                const [departmentsRes, prioritiesRes, categoriesRes] = await Promise.all([
                    fetch(`${API_URL}departments`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    }),
                    fetch(`${API_URL}priorities`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    }),
                    fetch(`${API_URL}notice-board-category`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    })
                ]);
                
                if (!departmentsRes.ok) throw new Error(`Error: ${departmentsRes.statusText}`);
                if (!prioritiesRes.ok) throw new Error(`Error: ${prioritiesRes.statusText}`);
                if (!categoriesRes.ok) throw new Error(`Error: ${categoriesRes.statusText}`);
                
                const departmentsData = await departmentsRes.json();
                const prioritiesData = await prioritiesRes.json();
                const categoriesData = await categoriesRes.json();
                
                setDepartments(departmentsData.departments);
                setPriorities(prioritiesData.priorities);
                setCategories(categoriesData.categories);
            } catch (error) {
                setError(error.message);
            }
        };
        fetchData();
    }, []);

    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam('');

        const department = departments.find(dep => dep.name === departmentName);
        setTeams(department?.teams || []);
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!title || !content || !priority || !selectedTeam || !selectedDepartment || !category) {
            setError('Please fill all required fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            const response = await fetch(`${API_URL}notice`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title,
                    content,
                    priority,
                    team: selectedTeam,
                    department: selectedDepartment,
                    category,
                    expiry_date: expiryDate || null,
                    published_date: publishedDate || null,
                    status: status, // Ensure status is included
                }),
            });

            const responseData = await response.json();
            if (!response.ok) throw new Error(responseData.message || 'Failed to add notice.');

            setSuccessMessage('Notice added successfully!');
            setTitle('');
            setContent('');
            setPriority('');
            setSelectedTeam('');
            setSelectedDepartment('');
            setCategory('');
            setExpiryDate('');
            setPublishedDate('');
            setStatus("active"); // Reset status
        } catch (error) {
            setError(error.message);
        }
    };
    
    const isModalOpen = location.pathname === '/add-notice';
    const handleClose = () => navigate('/noticeboard');

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-2xl relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New Notice</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="title" className="block text-sm font-medium text-gray-700 pb-2">
                                    Title
                                </label>
                                <input
                                    type="text"
                                    id="title"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                                    required
                                />
                            </div>

                            <div className="mb-4">
                                <label htmlFor="content" className="block text-sm font-medium text-gray-700 pb-2">
                                    Content
                                </label>
                                <EditorToolbar />
                                <ReactQuill
                                    id="content"
                                    value={content}
                                    onChange={setContent}
                                    className="bg-white border border-gray-300 rounded-md shadow-sm"
                                    theme="snow"
                                    modules={modules}
                                    formats={formats}
                                />
                            </div>

                            {/* Dropdown Fields */}
                            <div className="grid grid-cols-2 gap-4">
                            <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Priority</label>
                            <select className="w-full border rounded-md p-2" value={priority} onChange={(e) => setPriority(e.target.value)} required>
                                <option value="">Select Priority</option>
                                {priorities.map((p) => <option key={p.id} value={p.name}>{p.name}</option>)}
                            </select>
                        </div>

                                <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Department</label>
                                <select className="w-full border rounded-md p-2" value={selectedDepartment || ''} onChange={handleDepartmentChange} required>
                                    <option value="">Select Department</option>
                                    {departments.map(dep => <option key={dep.id} value={dep.name}>{dep.name}</option>)}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Team</label>
                                <select className="w-full border rounded-md p-2" value={selectedTeam || ''} onChange={(e) => setSelectedTeam(e.target.value)} required>
                                    <option value="">Select Team</option>
                                    {teams.map(team => <option key={team.id} value={team.name}>{team.name}</option>)}
                                </select>
                            </div>
                        </div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Category</label>
                            <select className="w-full border rounded-md p-2" value={category} onChange={(e) => setCategory(e.target.value)} required>
                                <option value="">Select Category</option>
                                {categories.map((c) => <option key={c.id} value={c.name}>{c.name}</option>)}
                            </select>
                        </div>
                            </div>

                            

                            <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Published Date</label>
                            <input type="date" className="w-full border rounded-md p-2" value={publishedDate} onChange={(e) => setPublishedDate(e.target.value)} required />
                            </div>

                            <div className="mt-4">
                                <label className="block text-sm font-medium text-gray-700 pb-2">Expiry Date (Optional)</label>
                                <input type="date" className="w-full border rounded-md p-2" value={expiryDate} onChange={(e) => setExpiryDate(e.target.value)} />
                            </div>

                            <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700">Status</label>
                                <select
                                className="w-full border rounded-md p-2"
                                value={status}
                                onChange={(e) => setStatus(e.target.value)}
                                >
                                {statuses.map((statusOption) => (
                                    <option key={statusOption} value={statusOption}>
                                    {statusOption.charAt(0).toUpperCase() + statusOption.slice(1)}
                                    </option>
                                ))}
                                </select>
                            </div>

                            <div className="py-4">
                                <button type="submit" className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3">
                                    Add Notice
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddNotice;
