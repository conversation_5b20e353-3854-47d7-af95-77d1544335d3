{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\world-time\\\\WorldTime.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport moment from \"moment\";\nimport \"moment-timezone\";\nimport { fetchWeatherApi } from \"openmeteo\";\nimport Select from \"react-select\";\nimport DynamicTimeCard from \"./DynamicTimeCard\";\nimport { useNavigate } from \"react-router-dom\";\nimport Calendar from \"date-bengali-revised\";\nimport uq from '@umalqura/core';\nimport timezonebg from \"../../assets/images/timezonebg.png\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst timeZones = {\n  Dubai: \"Asia/Dubai\",\n  Kabul: \"Asia/Kabul\",\n  Yerevan: \"Asia/Yerevan\",\n  Baku: \"Asia/Baku\",\n  Dhaka: \"Asia/Dhaka\",\n  Brunei: \"Asia/Brunei\",\n  Thimphu: \"Asia/Thimphu\",\n  Shanghai: \"Asia/Shanghai\",\n  Urumqi: \"Asia/Urumqi\",\n  Nicosia: \"Asia/Nicosia\",\n  Famagusta: \"Asia/Famagusta\",\n  Tbilisi: \"Asia/Tbilisi\",\n  Hong_Kong: \"Asia/Hong_Kong\",\n  Jakarta: \"Asia/Jakarta\",\n  Pontianak: \"Asia/Pontianak\",\n  Makassar: \"Asia/Makassar\",\n  Jayapura: \"Asia/Jayapura\",\n  Jerusalem: \"Asia/Jerusalem\",\n  Kolkata: \"Asia/Kolkata\",\n  Baghdad: \"Asia/Baghdad\",\n  Tehran: \"Asia/Tehran\",\n  Amman: \"Asia/Amman\",\n  Tokyo: \"Asia/Tokyo\",\n  Bishkek: \"Asia/Bishkek\",\n  Pyongyang: \"Asia/Pyongyang\",\n  Seoul: \"Asia/Seoul\",\n  Almaty: \"Asia/Almaty\",\n  Qyzylorda: \"Asia/Qyzylorda\",\n  Qostanay: \"Asia/Qostanay\",\n  Aqtobe: \"Asia/Aqtobe\",\n  Aqtau: \"Asia/Aqtau\",\n  Atyrau: \"Asia/Atyrau\",\n  Oral: \"Asia/Oral\",\n  Beirut: \"Asia/Beirut\",\n  Colombo: \"Asia/Colombo\",\n  Yangon: \"Asia/Yangon\",\n  Ulaanbaatar: \"Asia/Ulaanbaatar\",\n  Hovd: \"Asia/Hovd\",\n  Choibalsan: \"Asia/Choibalsan\",\n  Macau: \"Asia/Macau\",\n  Kuala_Lumpur: \"Asia/Kuala_Lumpur\",\n  Kuching: \"Asia/Kuching\",\n  Karachi: \"Asia/Karachi\",\n  Gaza: \"Asia/Gaza\",\n  Hebron: \"Asia/Hebron\",\n  Kathmandu: \"Asia/Kathmandu\",\n  Yekaterinburg: \"Asia/Yekaterinburg\",\n  Qatar: \"Asia/Qatar\",\n  Omsk: \"Asia/Omsk\",\n  Novosibirsk: \"Asia/Novosibirsk\",\n  Barnaul: \"Asia/Barnaul\",\n  Tomsk: \"Asia/Tomsk\",\n  Novokuznetsk: \"Asia/Novokuznetsk\",\n  Krasnoyarsk: \"Asia/Krasnoyarsk\",\n  Irkutsk: \"Asia/Irkutsk\",\n  Chita: \"Asia/Chita\",\n  Yakutsk: \"Asia/Yakutsk\",\n  Khandyga: \"Asia/Khandyga\",\n  Vladivostok: \"Asia/Vladivostok\",\n  Ust_Nera: \"Asia/Ust-Nera\",\n  Singapore: \"Asia/Singapore\",\n  Magadan: \"Asia/Magadan\",\n  Sakhalin: \"Asia/Sakhalin\",\n  Srednekolymsk: \"Asia/Srednekolymsk\",\n  Kamchatka: \"Asia/Kamchatka\",\n  Anadyr: \"Asia/Anadyr\",\n  Bangkok: \"Asia/Bangkok\",\n  Dushanbe: \"Asia/Dushanbe\",\n  Taipei: \"Asia/Taipei\",\n  Dili: \"Asia/Dili\",\n  Ashgabat: \"Asia/Ashgabat\",\n  Damascus: \"Asia/Damascus\",\n  Riyadh: \"Asia/Riyadh\",\n  Samarkand: \"Asia/Samarkand\",\n  Tashkent: \"Asia/Tashkent\",\n  Ho_Chi_Minh: \"Asia/Ho_Chi_Minh\",\n  Andorra: \"Europe/Andorra\",\n  Tirane: \"Europe/Tirane\",\n  Vienna: \"Europe/Vienna\",\n  Brussels: \"Europe/Brussels\",\n  Sofia: \"Europe/Sofia\",\n  Minsk: \"Europe/Minsk\",\n  Zurich: \"Europe/Zurich\",\n  Prague: \"Europe/Prague\",\n  Berlin: \"Europe/Berlin\",\n  Copenhagen: \"Europe/Copenhagen\",\n  Tallinn: \"Europe/Tallinn\",\n  Madrid: \"Europe/Madrid\",\n  Helsinki: \"Europe/Helsinki\",\n  Paris: \"Europe/Paris\",\n  London: \"Europe/London\",\n  Gibraltar: \"Europe/Gibraltar\",\n  Athens: \"Europe/Athens\",\n  Budapest: \"Europe/Budapest\",\n  Dublin: \"Europe/Dublin\",\n  Rome: \"Europe/Rome\",\n  Vilnius: \"Europe/Vilnius\",\n  Luxembourg: \"Europe/Luxembourg\",\n  Riga: \"Europe/Riga\",\n  Monaco: \"Europe/Monaco\",\n  Chisinau: \"Europe/Chisinau\",\n  Malta: \"Europe/Malta\",\n  Amsterdam: \"Europe/Amsterdam\",\n  Oslo: \"Europe/Oslo\",\n  Warsaw: \"Europe/Warsaw\",\n  Lisbon: \"Europe/Lisbon\",\n  Bucharest: \"Europe/Bucharest\",\n  Belgrade: \"Europe/Belgrade\",\n  Kaliningrad: \"Europe/Kaliningrad\",\n  Moscow: \"Europe/Moscow\",\n  Simferopol: \"Europe/Simferopol\",\n  Kirov: \"Europe/Kirov\",\n  Astrakhan: \"Europe/Astrakhan\",\n  Volgograd: \"Europe/Volgograd\",\n  Saratov: \"Europe/Saratov\",\n  Ulyanovsk: \"Europe/Ulyanovsk\",\n  Samara: \"Europe/Samara\",\n  Stockholm: \"Europe/Stockholm\",\n  Istanbul: \"Europe/Istanbul\",\n  Kiev: \"Europe/Kiev\",\n  Uzhgorod: \"Europe/Uzhgorod\",\n  Zaporozhye: \"Europe/Zaporozhye\",\n  Casey: \"Antarctica/Casey\",\n  Davis: \"Antarctica/Davis\",\n  DumontDUrville: \"Antarctica/DumontDUrville\",\n  Mawson: \"Antarctica/Mawson\",\n  Palmer: \"Antarctica/Palmer\",\n  Rothera: \"Antarctica/Rothera\",\n  Syowa: \"Antarctica/Syowa\",\n  Troll: \"Antarctica/Troll\",\n  Vostok: \"Antarctica/Vostok\",\n  Macquarie: \"Antarctica/Macquarie\",\n  Buenos_Aires: \"America/Argentina/Buenos_Aires\",\n  Cordoba: \"America/Argentina/Cordoba\",\n  Salta: \"America/Argentina/Salta\",\n  Jujuy: \"America/Argentina/Jujuy\",\n  Tucuman: \"America/Argentina/Tucuman\",\n  Catamarca: \"America/Argentina/Catamarca\",\n  La_Rioja: \"America/Argentina/La_Rioja\",\n  San_Juan: \"America/Argentina/San_Juan\",\n  Mendoza: \"America/Argentina/Mendoza\",\n  San_Luis: \"America/Argentina/San_Luis\",\n  Rio_Gallegos: \"America/Argentina/Rio_Gallegos\",\n  Ushuaia: \"America/Argentina/Ushuaia\",\n  Barbados: \"America/Barbados\",\n  La_Paz: \"America/La_Paz\",\n  Belem: \"America/Belem\",\n  Fortaleza: \"America/Fortaleza\",\n  Recife: \"America/Recife\",\n  Araguaina: \"America/Araguaina\",\n  Maceio: \"America/Maceio\",\n  Bahia: \"America/Bahia\",\n  Sao_Paulo: \"America/Sao_Paulo\",\n  Campo_Grande: \"America/Campo_Grande\",\n  Cuiaba: \"America/Cuiaba\",\n  Porto_Velho: \"America/Porto_Velho\",\n  Boa_Vista: \"America/Boa_Vista\",\n  Manaus: \"America/Manaus\",\n  Eirunepe: \"America/Eirunepe\",\n  Rio_Branco: \"America/Rio_Branco\",\n  Nassau: \"America/Nassau\",\n  Belize: \"America/Belize\",\n  St_Johns: \"America/St_Johns\",\n  Halifax: \"America/Halifax\",\n  Glace_Bay: \"America/Glace_Bay\",\n  Moncton: \"America/Moncton\",\n  Goose_Bay: \"America/Goose_Bay\",\n  Blanc_Sablon: \"America/Blanc-Sablon\",\n  Toronto: \"America/Toronto\",\n  Nipigon: \"America/Nipigon\",\n  Thunder_Bay: \"America/Thunder_Bay\",\n  Iqaluit: \"America/Iqaluit\",\n  Pangnirtung: \"America/Pangnirtung\",\n  Atikokan: \"America/Atikokan\",\n  Winnipeg: \"America/Winnipeg\",\n  Rainy_River: \"America/Rainy_River\",\n  Resolute: \"America/Resolute\",\n  Rankin_Inlet: \"America/Rankin_Inlet\",\n  Regina: \"America/Regina\",\n  Swift_Current: \"America/Swift_Current\",\n  Edmonton: \"America/Edmonton\",\n  Cambridge_Bay: \"America/Cambridge_Bay\",\n  Yellowknife: \"America/Yellowknife\",\n  Inuvik: \"America/Inuvik\",\n  Creston: \"America/Creston\",\n  Dawson_Creek: \"America/Dawson_Creek\",\n  Fort_Nelson: \"America/Fort_Nelson\",\n  Vancouver: \"America/Vancouver\",\n  Whitehorse: \"America/Whitehorse\",\n  Dawson: \"America/Dawson\",\n  Santiago: \"America/Santiago\",\n  Punta_Arenas: \"America/Punta_Arenas\",\n  Bogota: \"America/Bogota\",\n  Costa_Rica: \"America/Costa_Rica\",\n  Havana: \"America/Havana\",\n  Curacao: \"America/Curacao\",\n  Santo_Domingo: \"America/Santo_Domingo\",\n  Guayaquil: \"America/Guayaquil\",\n  Cayenne: \"America/Cayenne\",\n  Godthab: \"America/Godthab\",\n  Danmarkshavn: \"America/Danmarkshavn\",\n  Scoresbysund: \"America/Scoresbysund\",\n  Thule: \"America/Thule\",\n  Guatemala: \"America/Guatemala\",\n  Guyana: \"America/Guyana\",\n  Tegucigalpa: \"America/Tegucigalpa\",\n  Port_au_Prince: \"America/Port-au-Prince\",\n  Jamaica: \"America/Jamaica\",\n  Martinique: \"America/Martinique\",\n  Mexico_City: \"America/Mexico_City\",\n  Cancun: \"America/Cancun\",\n  Merida: \"America/Merida\",\n  Monterrey: \"America/Monterrey\",\n  Matamoros: \"America/Matamoros\",\n  Caracas: \"America/Caracas\",\n  Mazatlan: \"America/Mazatlan\",\n  Chihuahua: \"America/Chihuahua\",\n  Ojinaga: \"America/Ojinaga\",\n  Hermosillo: \"America/Hermosillo\",\n  Tijuana: \"America/Tijuana\",\n  Bahia_Banderas: \"America/Bahia_Banderas\",\n  Managua: \"America/Managua\",\n  Panama: \"America/Panama\",\n  Lima: \"America/Lima\",\n  Miquelon: \"America/Miquelon\",\n  Puerto_Rico: \"America/Puerto_Rico\",\n  El_Salvador: \"America/El_Salvador\",\n  Grand_Turk: \"America/Grand_Turk\",\n  Paramaribo: \"America/Paramaribo\",\n  Asuncion: \"America/Asuncion\",\n  Port_of_Spain: \"America/Port_of_Spain\",\n  New_York: \"America/New_York\",\n  New_Jersey: \"America/New_York\",\n  Detroit: \"America/Detroit\",\n  Louisville: \"America/Kentucky/Louisville\",\n  Monticello: \"America/Kentucky/Monticello\",\n  Indianapolis: \"America/Indiana/Indianapolis\",\n  Vincennes: \"America/Indiana/Vincennes\",\n  Winamac: \"America/Indiana/Winamac\",\n  Marengo: \"America/Indiana/Marengo\",\n  Petersburg: \"America/Indiana/Petersburg\",\n  Vevay: \"America/Indiana/Vevay\",\n  Tell_City: \"America/Indiana/Tell_City\",\n  Knox: \"America/Indiana/Knox\",\n  Chicago: \"America/Chicago\",\n  Menominee: \"America/Menominee\",\n  Denver: \"America/Denver\",\n  Boise: \"America/Boise\",\n  Phoenix: \"America/Phoenix\",\n  Center: \"America/North_Dakota/Center\",\n  New_Salem: \"America/North_Dakota/New_Salem\",\n  Beulah: \"America/North_Dakota/Beulah\",\n  Los_Angeles: \"America/Los_Angeles\",\n  Anchorage: \"America/Anchorage\",\n  Alaska: \"America/Anchorage\",\n  Juneau: \"America/Juneau\",\n  Sitka: \"America/Sitka\",\n  Metlakatla: \"America/Metlakatla\",\n  Yakutat: \"America/Yakutat\",\n  Nome: \"America/Nome\",\n  Adak: \"America/Adak\",\n  Montevideo: \"America/Montevideo\",\n  Pago_Pago: \"Pacific/Pago_Pago\",\n  Rarotonga: \"Pacific/Rarotonga\",\n  Easter: \"Pacific/Easter\",\n  Galapagos: \"Pacific/Galapagos\",\n  Fiji: \"Pacific/Fiji\",\n  Chuuk: \"Pacific/Chuuk\",\n  Pohnpei: \"Pacific/Pohnpei\",\n  Kosrae: \"Pacific/Kosrae\",\n  Guam: \"Pacific/Guam\",\n  Majuro: \"Pacific/Majuro\",\n  Kwajalein: \"Pacific/Kwajalein\",\n  Tarawa: \"Pacific/Tarawa\",\n  Enderbury: \"Pacific/Enderbury\",\n  Kiritimati: \"Pacific/Kiritimati\",\n  Noumea: \"Pacific/Noumea\",\n  Norfolk: \"Pacific/Norfolk\",\n  Nauru: \"Pacific/Nauru\",\n  Niue: \"Pacific/Niue\",\n  Auckland: \"Pacific/Auckland\",\n  Chatham: \"Pacific/Chatham\",\n  Tahiti: \"Pacific/Tahiti\",\n  Marquesas: \"Pacific/Marquesas\",\n  Gambier: \"Pacific/Gambier\",\n  Port_Moresby: \"Pacific/Port_Moresby\",\n  Bougainville: \"Pacific/Bougainville\",\n  Pitcairn: \"Pacific/Pitcairn\",\n  Palau: \"Pacific/Palau\",\n  Guadalcanal: \"Pacific/Guadalcanal\",\n  Fakaofo: \"Pacific/Fakaofo\",\n  Tongatapu: \"Pacific/Tongatapu\",\n  Funafuti: \"Pacific/Funafuti\",\n  Wake: \"Pacific/Wake\",\n  Honolulu: \"Pacific/Honolulu\",\n  Efate: \"Pacific/Efate\",\n  Wallis: \"Pacific/Wallis\",\n  Apia: \"Pacific/Apia\",\n  Lord_Howe: \"Australia/Lord_Howe\",\n  Hobart: \"Australia/Hobart\",\n  Currie: \"Australia/Currie\",\n  Melbourne: \"Australia/Melbourne\",\n  Sydney: \"Australia/Sydney\",\n  Broken_Hill: \"Australia/Broken_Hill\",\n  Brisbane: \"Australia/Brisbane\",\n  Lindeman: \"Australia/Lindeman\",\n  Adelaide: \"Australia/Adelaide\",\n  Darwin: \"Australia/Darwin\",\n  Perth: \"Australia/Perth\",\n  Eucla: \"Australia/Eucla\",\n  Abidjan: \"Africa/Abidjan\",\n  Algiers: \"Africa/Algiers\",\n  Cairo: \"Africa/Cairo\",\n  El_Aaiun: \"Africa/El_Aaiun\",\n  Ceuta: \"Africa/Ceuta\",\n  Accra: \"Africa/Accra\",\n  Bissau: \"Africa/Bissau\",\n  Nairobi: \"Africa/Nairobi\",\n  Monrovia: \"Africa/Monrovia\",\n  Tripoli: \"Africa/Tripoli\",\n  Casablanca: \"Africa/Casablanca\",\n  Maputo: \"Africa/Maputo\",\n  Windhoek: \"Africa/Windhoek\",\n  Lagos: \"Africa/Lagos\",\n  Khartoum: \"Africa/Khartoum\",\n  Juba: \"Africa/Juba\",\n  Sao_Tome: \"Africa/Sao_Tome\",\n  Ndjamena: \"Africa/Ndjamena\",\n  Tunis: \"Africa/Tunis\",\n  Johannesburg: \"Africa/Johannesburg\",\n  Azores: \"Atlantic/Azores\",\n  Bermuda: \"Atlantic/Bermuda\",\n  Madeira: \"Atlantic/Madeira\",\n  Cape_Verde: \"Atlantic/Cape_Verde\",\n  Canary: \"Atlantic/Canary\",\n  Stanley: \"Atlantic/Stanley\",\n  Faroe: \"Atlantic/Faroe\",\n  South_Georgia: \"Atlantic/South_Georgia\",\n  Reykjavik: \"Atlantic/Reykjavik\",\n  Cocos: \"Indian/Cocos\",\n  Christmas: \"Indian/Christmas\",\n  Chagos: \"Indian/Chagos\",\n  Mauritius: \"Indian/Mauritius\",\n  Maldives: \"Indian/Maldives\",\n  Mahe: \"Indian/Mahe\",\n  Reunion: \"Indian/Reunion\",\n  Kerguelen: \"Indian/Kerguelen\"\n};\nfunction WorldTime() {\n  _s();\n  const defaultDateFormat = \"dddd, LL\";\n  const defaultTimeFormat = \"hh:mm A\";\n  const background = {\n    backgroundImage: `url(${timezonebg})`,\n    backgroundPosition: 'center center',\n    backgroundSize: 'contain',\n    backgroundRepeat: 'no-repeat'\n  };\n  let bnCalendar = new Calendar();\n  const arabicCalendar = uq();\n  const queryParameters = new URLSearchParams(window.location.search);\n  const getDateTime = queryParameters.get(\"datetime\");\n  const getDefaultTimeZones = queryParameters.get(\"defaulttimezone\");\n  const getTimeZones = queryParameters.get(\"timezones\");\n  const [defaultTimeZone, setDefaultTimeZone] = useState({\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\"\n  }); // Store team data\n  const [currentDateTime, setCurrentDateTime] = useState(moment().tz(defaultTimeZone[\"value\"])); // Store team data\n  const [error, setError] = useState(null); // Handle errors\n  const [loading, setLoading] = useState(true); // Loading state\n  const [weatherData, setWeatherData] = useState(null);\n  const [ipData, setIpData] = useState(false);\n  const [fromDateTime, setFromDateTime] = useState(\"\");\n  const [fromtimezone, setFromtimezone] = useState({\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\"\n  });\n  const [totimezone, setTotimezone] = useState({\n    label: \"New York\",\n    value: \"America/New_York\"\n  });\n  const [showCityList, setShowCityList] = useState([{\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\",\n    isFixed: true\n  }, {\n    label: \"Alaska\",\n    value: \"America/Anchorage\"\n  }, {\n    label: \"New York\",\n    value: \"America/New_York\",\n    isFixed: true\n  }, {\n    label: \"New Jersey\",\n    value: \"America/New_York\",\n    isFixed: true\n  }, {\n    label: \"Los Angeles\",\n    value: \"America/Los_Angeles\",\n    isFixed: true\n  }, {\n    label: \"Chicago\",\n    value: \"America/Chicago\"\n  }, {\n    label: \"El Salvador\",\n    value: \"America/El_Salvador\"\n  }, {\n    label: \"London\",\n    value: \"Europe/London\"\n  }, {\n    label: \"Honolulu\",\n    value: \"Pacific/Honolulu\"\n  }, {\n    label: \"Adelaide\",\n    value: \"Australia/Adelaide\"\n  }, {\n    label: \"Sydney\",\n    value: \"Australia/Sydney\"\n  }, {\n    label: \"Perth\",\n    value: \"Australia/Perth\"\n  }, {\n    label: \"Singapore\",\n    value: \"Asia/Singapore\"\n  }, {\n    label: \"Dubai\",\n    value: \"Asia/Dubai\"\n  }, {\n    label: \"Tokyo\",\n    value: \"Asia/Tokyo\"\n  }]);\n  const [fixedCityList, setFixedCityList] = useState([{\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\",\n    isFixed: true\n  }, {\n    label: \"Alaska\",\n    value: \"America/Anchorage\"\n  }, {\n    label: \"New York\",\n    value: \"America/New_York\",\n    isFixed: true\n  }, {\n    label: \"New Jersey\",\n    value: \"America/New_York\",\n    isFixed: true\n  }, {\n    label: \"Los Angeles\",\n    value: \"America/Los_Angeles\",\n    isFixed: true\n  }, {\n    label: \"Chicago\",\n    value: \"America/Chicago\"\n  }, {\n    label: \"El Salvador\",\n    value: \"America/El_Salvador\"\n  }, {\n    label: \"London\",\n    value: \"Europe/London\"\n  }, {\n    label: \"Honolulu\",\n    value: \"Pacific/Honolulu\"\n  }, {\n    label: \"Adelaide\",\n    value: \"Australia/Adelaide\"\n  }, {\n    label: \"Sydney\",\n    value: \"Australia/Sydney\"\n  }, {\n    label: \"Perth\",\n    value: \"Australia/Perth\"\n  }, {\n    label: \"Singapore\",\n    value: \"Asia/Singapore\"\n  }, {\n    label: \"Dubai\",\n    value: \"Asia/Dubai\"\n  }, {\n    label: \"Tokyo\",\n    value: \"Asia/Tokyo\"\n  }]);\n  const [favCityList, setFavCityList] = useState([{\n    label: \"New York\",\n    value: \"America/New_York\"\n  }, {\n    label: \"London\",\n    value: \"Europe/London\"\n  }, {\n    label: \"El Salvador\",\n    value: \"America/El_Salvador\"\n  }]);\n  const params = {\n    latitude: ipData === null || ipData === void 0 ? void 0 : ipData.lat,\n    longitude: ipData === null || ipData === void 0 ? void 0 : ipData.lon,\n    hourly: [\"temperature_2m\", \"relative_humidity_2m\", \"apparent_temperature\", \"precipitation_probability\", \"precipitation\", \"rain\", \"visibility\", \"wind_speed_10m\", \"uv_index\"],\n    daily: [\"weather_code\", \"sunrise\", \"sunset\"],\n    timeformat: \"unixtime\",\n    timezone: \"auto\",\n    past_days: 0,\n    forecast_days: 6\n  };\n  const datetimeToISOString = datetime => {\n    return new Date(datetime).valueOf();\n  };\n  const generateShareUrl = () => {\n    let url = window.location.href.split(\"?\")[0];\n    let params = new URLSearchParams();\n    let isoDateTime = fromDateTime ? new Date(fromDateTime).toISOString() : new Date(currentDateTime).toISOString();\n    let dtmz = fromDateTime ? fromtimezone[\"label\"] : defaultTimeZone[\"label\"];\n    let timezones = showCityList.map(item => item[\"label\"].replaceAll(\" \", \"_\"));\n    params.append(\"datetime\", datetimeToISOString(isoDateTime));\n    params.append(\"defaulttimezone\", dtmz);\n    params.append(\"timezones\", timezones.join(\",\"));\n    url += \"?\" + params.toString(\",\");\n    return url;\n  };\n  const getTimeDifferenceBetweenTimezones = (timezone1, timezone2, time1, time2) => {\n    // time1 and time2 are optional. If not provided, current time in respective timezones is used.\n\n    const moment1 = time1 ? moment.tz(time1, timezone1) : moment.tz(timezone1);\n    const moment2 = time2 ? moment.tz(time2, timezone2) : moment.tz(timezone2);\n    const diffInHours = moment2.diff(moment1, \"hours\");\n    let result = {\n      differenceInHours: diffInHours,\n      timeInTz1: moment1.format(\"HH:mm\"),\n      timeInTz2: moment2.format(\"HH:mm\")\n    };\n\n    // console.log(timezone1, timezone2, time1, time2)\n    // console.log(result)\n\n    return result.differenceInHours;\n  };\n  let singleLayoutClasses = \"\";\n  if (window.location.pathname === \"/world-time-share\") {\n    singleLayoutClasses = \" max-w-[1642px] mx-auto \";\n  }\n  useEffect(() => {\n    if (getDateTime) {\n      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();\n      setFromDateTime(getDateTimeParseData);\n    }\n    if (getDefaultTimeZones) {\n      setDefaultTimeZone({\n        label: getDefaultTimeZones,\n        value: timeZones[getDefaultTimeZones]\n      });\n      setFromtimezone({\n        label: getDefaultTimeZones,\n        value: timeZones[getDefaultTimeZones]\n      });\n    }\n    if (getTimeZones) {\n      let timezones = getTimeZones.split(\",\");\n      let timezonesData = timezones.map(item => {\n        return {\n          label: item,\n          value: timeZones[item]\n        };\n      });\n      setShowCityList(timezonesData);\n    }\n  }, [getDateTime, getDefaultTimeZones, getTimeZones]);\n\n  // Fetch IP data with timeout and retry logic\n  useEffect(() => {\n    const fetchWithTimeout = async (url, options = {}, timeout = 10000) => {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\n      try {\n        const response = await fetch(url, {\n          ...options,\n          signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        return response;\n      } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n      }\n    };\n    const fetchIP = async (retryCount = 0) => {\n      try {\n        const response = await fetchWithTimeout(`//ip-api.com/json/`, {\n          method: \"GET\"\n        }, 8000);\n        if (!response.ok) {\n          throw new Error(\"Failed to fetch IP data: \" + response.statusText);\n        }\n        const data = await response.json();\n        setIpData(data);\n        fetchCurrentDateTimeByIP(data[\"query\"]);\n      } catch (error) {\n        console.warn(\"IP fetch attempt failed:\", error.message);\n\n        // Retry logic - try up to 2 more times\n        if (retryCount < 2) {\n          console.log(`Retrying IP fetch... Attempt ${retryCount + 2}/3`);\n          setTimeout(() => fetchIP(retryCount + 1), 2000 * (retryCount + 1));\n          return;\n        }\n\n        // If all retries fail, use fallback\n        console.log(\"All IP fetch attempts failed, using fallback\");\n        setError(\"Unable to fetch location data, using default timezone\");\n        setIpData({\n          query: \"fallback\",\n          timezone: \"UTC\"\n        });\n        setLoading(false);\n      }\n    };\n    fetchIP();\n  }, []);\n  const fetchCurrentDateTimeByIP = async (ip = \"\", retryCount = 0) => {\n    try {\n      if (ip && ip !== \"fallback\") {\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 8000);\n        try {\n          const response = await fetch(`https://timeapi.io/api/time/current/ip?ipAddress=${ip}`, {\n            method: \"GET\",\n            signal: controller.signal\n          });\n          clearTimeout(timeoutId);\n          if (!response.ok) {\n            throw new Error(\"Failed to fetch time data: \" + response.statusText);\n          }\n          const responseData = await response.json();\n          if (responseData) {\n            setDefaultTimeZone({\n              ...defaultTimeZone,\n              responseData\n            });\n            setCurrentDateTime(moment(responseData[\"dateTime\"]));\n          }\n        } catch (fetchError) {\n          clearTimeout(timeoutId);\n          throw fetchError;\n        }\n      } else {\n        // Fallback to local time\n        console.log(\"Using local time as fallback\");\n        setCurrentDateTime(moment());\n      }\n    } catch (error) {\n      console.warn(\"Time fetch attempt failed:\", error.message);\n\n      // Retry logic - try up to 2 more times\n      if (retryCount < 2 && ip !== \"fallback\") {\n        console.log(`Retrying time fetch... Attempt ${retryCount + 2}/3`);\n        setTimeout(() => fetchCurrentDateTimeByIP(ip, retryCount + 1), 2000 * (retryCount + 1));\n        return;\n      }\n\n      // If all retries fail, use local time\n      console.log(\"Here is the \");\n      console.log(\"All time fetch attempts failed, using local time\");\n      setCurrentDateTime(moment());\n      setError(\"Unable to fetch accurate time, using local time\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (!ipData) return;\n    const fetchWeather = async (retryCount = 0) => {\n      try {\n        const url = \"https://api.open-meteo.com/v1/forecast\";\n\n        // Add timeout to weather API call\n        const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Weather API timeout')), 10000));\n        const weatherPromise = fetchWeatherApi(url, params);\n        const responses = await Promise.race([weatherPromise, timeoutPromise]);\n        const range = (start, stop, step) => Array.from({\n          length: (stop - start) / step\n        }, (_, i) => start + i * step);\n        const response = responses[0];\n        const utcOffsetSeconds = response.utcOffsetSeconds();\n        const hourly = response.hourly();\n        const daily = response.daily();\n        const weather = {\n          hourly: {\n            time: range(Number(hourly.time()), Number(hourly.timeEnd()), hourly.interval()).map(t => new Date((t + utcOffsetSeconds) * 1000)),\n            temperature2m: hourly.variables(0).valuesArray(),\n            relativeHumidity2m: hourly.variables(1).valuesArray(),\n            apparentTemperature: hourly.variables(2).valuesArray(),\n            precipitationProbability: hourly.variables(3).valuesArray(),\n            precipitation: hourly.variables(4).valuesArray(),\n            rain: hourly.variables(5).valuesArray(),\n            visibility: hourly.variables(6).valuesArray(),\n            windSpeed10m: hourly.variables(7).valuesArray(),\n            uvIndex: hourly.variables(8).valuesArray()\n          },\n          daily: {\n            time: range(Number(daily.time()), Number(daily.timeEnd()), daily.interval()).map(t => new Date((t + utcOffsetSeconds) * 1000)),\n            weatherCode: daily.variables(0).valuesArray(),\n            sunrise: daily.variables(1).valuesArray(),\n            sunset: daily.variables(2).valuesArray()\n          }\n        };\n        setWeatherData(weather);\n      } catch (err) {\n        console.warn(\"Weather fetch attempt failed:\", err.message);\n\n        // Retry logic - try up to 2 more times\n        if (retryCount < 2) {\n          console.log(`Retrying weather fetch... Attempt ${retryCount + 2}/3`);\n          setTimeout(() => fetchWeather(retryCount + 1), 3000 * (retryCount + 1));\n          return;\n        }\n\n        // If all retries fail, set empty weather data\n        console.log(\"All weather fetch attempts failed, using fallback\");\n        setWeatherData({\n          hourly: {\n            time: [],\n            temperature2m: [],\n            relativeHumidity2m: []\n          },\n          daily: {\n            time: [],\n            weatherCode: [],\n            sunrise: [],\n            sunset: []\n          }\n        });\n        setError(\"Unable to fetch weather data\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchWeather();\n  }, [ipData]);\n\n  // Periodic refresh to prevent stale connections\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      // Refresh current time every 5 minutes to prevent stale data\n      if (currentDateTime) {\n        setCurrentDateTime(moment());\n      }\n    }, 5 * 60 * 1000); // 5 minutes\n\n    // Connection health check every 10 minutes\n    const healthCheckInterval = setInterval(async () => {\n      try {\n        // Simple ping to check if network is still available\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 3000);\n        await fetch('//ip-api.com/json/', {\n          method: 'HEAD',\n          signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n\n        // If successful, clear any previous errors\n        if (error) {\n          setError(null);\n        }\n      } catch (healthError) {\n        console.warn('Connection health check failed:', healthError.message);\n        // Don't set error for health check failures to avoid UI disruption\n      }\n    }, 10 * 60 * 1000); // 10 minutes\n\n    // Cleanup intervals on component unmount\n    return () => {\n      clearInterval(refreshInterval);\n      clearInterval(healthCheckInterval);\n    };\n  }, [currentDateTime, error]);\n  const convertDateTime = (totimezonevalue = \"\") => {\n    let fromtimezonevalue = fromtimezone.value || defaultTimeZone[\"value\"];\n    var fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\n    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\n    if (moment(retDateTime).isValid()) {\n      return retDateTime;\n    }\n    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\n    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\n    if (moment(retDateTime).isValid()) {\n      return retDateTime;\n    }\n    return false;\n  };\n  const getTimeZoneSelectOptions = () => {\n    let data = [];\n    Object.keys(timeZones).sort().map(label => data.push({\n      label: `${label.replaceAll(\"_\", \" \")}`,\n      value: timeZones[label]\n    }));\n    return data;\n  };\n  const getBnDateTime = dateTime => {\n    let date = new Date(dateTime);\n    let bnDateTime = bnCalendar.fromDate(date).format(\"dddd D MMMM, Y Q\");\n    return bnDateTime;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 rounded-xl p-4 \" + singleLayoutClasses,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-start items-start flex-row mb-[20px]\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-left w-1/2 \",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-xl font-medium \",\n          children: \"World Time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 953,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 951,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-start items-start flex-row mb-[50px] gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 rounded-xl  px-[15px] w-5/12 min-h-[210px] bg-[#0b333f] text-[#fff]\",\n        children: /*#__PURE__*/_jsxDEV(DynamicTimeCard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 959,\n        columnNumber: 9\n      }, this), favCityList.length > 0 && favCityList.map(item => {\n        // let label = key, timezone = timeZoneList[key];\n        let label = item[\"label\"];\n        let timezone = item[\"value\"];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-xl p-[10px] w-3/12 min-h-[210px] bg-[#57B6B2] text-[#fff] \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"justify-center text-2xl inline-block align-text-middle mt-[20px]\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[20px] font-normal\",\n              children: [getTimeDifferenceBetweenTimezones(defaultTimeZone[\"value\"], timezone, currentDateTime.format(\"lll\"), convertDateTime(timezone).format(\"lll\")), \" \", /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 21\n              }, this), \" (\", convertDateTime(timezone).format(\"z\"), \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-bold mb-1 text-[#0B333F]\",\n              children: label.replaceAll(\"_\", \" \") ? label.replaceAll(\"_\", \" \") : label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \" text-[24px] font-bold \",\n              children: convertDateTime(timezone) && convertDateTime(timezone).format(\"LL\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \" text-[16px] font-normal \",\n              children: convertDateTime(timezone) && convertDateTime(timezone).format(\"dddd, hh:mm A\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 17\n          }, this)\n        }, \"timezone-\" + timezone, false, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 15\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200   rounded-xl   p-0 py-3 w-4/12 min-h-[210px]  bg-[#DFECF1] text-[#0B333F]\",\n        children: currentDateTime && moment(currentDateTime).isValid() && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"justify-center justify-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-[24px]  font-semibold\",\n            children: \"Today is\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n            className: \"border border-[#0B333F] my-[10px] h-[1px] w-[100%]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-[20px] font-medium \",\n            children: [moment(fromDateTime).isValid() && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: moment(fromDateTime).format(defaultDateFormat)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: getBnDateTime(currentDateTime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: arabicCalendar.format('fullDate', 'en')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), !moment(fromDateTime).isValid() && moment(currentDateTime).isValid() && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: moment(currentDateTime).format(defaultDateFormat)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: getBnDateTime(currentDateTime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: arabicCalendar.format('fullDate', 'en')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1005,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 957,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full flex justify-start items-start flex-wrap  mb-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-start w-full \",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center rounded-xl gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid shrink-0 grid-cols-1 focus-within:relative w-full \",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              closeMenuOnSelect: false,\n              isClearable: true\n              // isClearable={fixedCityList}\n              ,\n              isSearchable: true,\n              isDisabled: false,\n              isMulti: true,\n              required: false,\n              placeholder: \"Choose your city\",\n              name: \"showCityList\",\n              value: showCityList.length <= 0 ? setShowCityList(fixedCityList) : showCityList,\n              options: getTimeZoneSelectOptions(),\n              onChange: item => {\n                setShowCityList(item);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1046,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full flex justify-start items-start flex-wrap  \",\n      style: background,\n      children: showCityList.length > 0 && showCityList.map(item => {\n        // let label = key, timezone = timeZoneList[key];\n        let label = item[\"label\"];\n        let timezone = item[\"value\"];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6  w-1/3 px-[20px] \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b-2 border-gray-400 pb-4   flex  justify-start items-start text-start \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-1/2 text-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-bold mb-1\",\n                children: label.replaceAll(\"_\", \" \") ? label.replaceAll(\"_\", \" \") : label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-normal\",\n                children: [timezone, \" (\", moment(currentDateTime).tz(timezone).format(\"z\"), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-1/2 text-end leading-tight\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" text-[30px] font-bold \",\n                children: moment(currentDateTime).tz(timezone).format(defaultTimeFormat)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1099,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" text-[14px] font-normal \",\n                children: moment(currentDateTime).tz(timezone).format(defaultDateFormat)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1098,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 17\n          }, this)\n        }, \"timezone-\" + timezone, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1069,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 946,\n    columnNumber: 5\n  }, this);\n}\n_s(WorldTime, \"AMQQhP2ibjO93p0oTf7Kn0uYO6Q=\");\n_c = WorldTime;\nexport default WorldTime;\nvar _c;\n$RefreshReg$(_c, \"WorldTime\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "moment", "fetchWeatherApi", "Select", "DynamicTimeCard", "useNavigate", "Calendar", "uq", "timezonebg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "timeZones", "Dubai", "Kabul", "Yerevan", "Baku", "Dhaka", "Brunei", "<PERSON><PERSON><PERSON><PERSON>", "Shanghai", "Urumqi", "Nicosia", "Famagusta", "Tbilisi", "Hong_Kong", "Jakarta", "Pontianak", "Makassar", "Jayapura", "Jerusalem", "Kolkata", "Baghdad", "Tehran", "Amman", "Tokyo", "Bishkek", "Pyongyang", "Seoul", "Almaty", "Qyzylorda", "Q<PERSON><PERSON><PERSON>", "Aqtobe", "<PERSON><PERSON><PERSON><PERSON>", "Atyrau", "Oral", "Beirut", "Colombo", "Yangon", "Ulaanbaatar", "Hovd", "<PERSON><PERSON><PERSON>", "Macau", "Kuala_Lumpur", "<PERSON><PERSON>", "Karachi", "Gaza", "Hebron", "Kathman<PERSON>", "Yekaterinburg", "Qatar", "Omsk", "Novosibirsk", "Barnaul", "Tomsk", "Novokuznetsk", "Krasnoyarsk", "Irkutsk", "<PERSON><PERSON>", "Yakutsk", "Khandyga", "Vladivostok", "Ust_Nera", "Singapore", "<PERSON><PERSON><PERSON>", "Sakhalin", "Srednekolymsk", "Kamchatka", "<PERSON><PERSON><PERSON>", "Bangkok", "<PERSON><PERSON><PERSON>", "Taipei", "<PERSON><PERSON>", "Ashgabat", "Damascus", "Riyadh", "Samarkand", "Tashkent", "<PERSON><PERSON><PERSON>", "Andorra", "<PERSON><PERSON><PERSON>", "Vienna", "Brussels", "Sofia", "Minsk", "Zurich", "Prague", "Berlin", "Copenhagen", "Tallinn", "Madrid", "Helsinki", "Paris", "London", "Gibraltar", "Athens", "Budapest", "Dublin", "Rome", "Vilnius", "Luxembourg", "Riga", "Monaco", "<PERSON><PERSON><PERSON>", "Malta", "Amsterdam", "Oslo", "Warsaw", "Lisbon", "Bucharest", "Belgrade", "Kaliningrad", "Moscow", "Simferopol", "<PERSON><PERSON>", "Astrakhan", "Volgograd", "<PERSON><PERSON>", "Ulyanovsk", "Samara", "Stockholm", "Istanbul", "Kiev", "Uzhgorod", "Zaporozhye", "<PERSON>", "<PERSON>", "DumontDUrville", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Syowa", "Troll", "Vostok", "Macquarie", "Buenos_Aires", "Cordoba", "Salta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Catamarca", "La_Rioja", "San_Juan", "Mendoza", "<PERSON><PERSON>Luis", "Rio_Gallegos", "Ushuaia", "Barbados", "La_Paz", "Belem", "Fortaleza", "Recife", "Araguaina", "Maceio", "Bahia", "Sao_Paulo", "Campo_Grande", "<PERSON><PERSON><PERSON>", "Porto_Velho", "Boa_Vista", "Manaus", "Eirunepe", "Rio_Branco", "Nassau", "Belize", "St_Johns", "Halifax", "Glace_Bay", "Moncton", "Goose_Bay", "<PERSON><PERSON>", "Toronto", "Nipigon", "Thunder_Bay", "Iqaluit", "Pangnirtung", "Atikokan", "Winnipeg", "Rainy_River", "Resolute", "Rankin_Inlet", "Regina", "Swift_Current", "Edmonton", "Cambridge_Bay", "Yellowknife", "Inuvik", "Creston", "Dawson_Creek", "Fort_Nelson", "Vancouver", "Whitehorse", "<PERSON>", "Santiago", "Punta_Arenas", "Bogota", "Costa_Rica", "Havana", "Curacao", "Santo_Domingo", "Guayaquil", "Cayenne", "<PERSON><PERSON><PERSON>", "Danmarkshavn", "Scoresbysund", "Thule", "Guatemala", "Guyana", "Tegucigalpa", "Port_au_Prince", "Jamaica", "Martinique", "Mexico_City", "Cancun", "<PERSON><PERSON>", "Monterrey", "Matamoros", "Caracas", "Mazatlan", "Chihuahua", "<PERSON><PERSON><PERSON>", "Hermosillo", "Tijuana", "Bahia_Banderas", "Managua", "Panama", "Lima", "Miquelon", "Puerto_Rico", "El_Salvador", "Grand_<PERSON><PERSON>", "Paramaribo", "Asuncion", "Port_of_Spain", "New_York", "New_Jersey", "Detroit", "Louisville", "<PERSON><PERSON><PERSON>", "Indianapolis", "Vincennes", "Winamac", "Marengo", "Petersburg", "<PERSON><PERSON><PERSON>", "Tell_City", "<PERSON>", "Chicago", "Menominee", "Denver", "Boise", "Phoenix", "Center", "New_Salem", "<PERSON><PERSON><PERSON>", "Los_Angeles", "Anchorage", "Alaska", "Juneau", "Sitka", "Metlakatla", "<PERSON><PERSON><PERSON>", "Nome", "Adak", "Montevideo", "Pago_Pago", "Rarotonga", "Easter", "Galapagos", "Fiji", "<PERSON><PERSON>", "Pohn<PERSON>i", "Kosrae", "Guam", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Noumea", "Norfolk", "Nauru", "Niue", "Auckland", "Chatham", "Tahiti", "Marquesas", "Gambier", "Port_Moresby", "Bougainville", "Pitcairn", "<PERSON><PERSON>", "Guadalcanal", "<PERSON><PERSON><PERSON><PERSON>", "Tongatapu", "Funafuti", "Wake", "Honolulu", "Efate", "<PERSON>", "Apia", "<PERSON><PERSON><PERSON>", "Hobart", "<PERSON>", "Melbourne", "Sydney", "Broken_Hill", "Brisbane", "<PERSON><PERSON><PERSON>", "Adelaide", "<PERSON>", "Perth", "<PERSON><PERSON><PERSON>", "Abidjan", "Algiers", "Cairo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Accra", "Bissau", "Nairobi", "Monrovia", "Tripoli", "Casablanca", "Maputo", "Windhoek", "Lagos", "Khartoum", "Juba", "Sao_Tome", "Ndjamena", "<PERSON><PERSON>", "Johannesburg", "Azores", "Bermuda", "Madeira", "Cape_Verde", "Canary", "<PERSON>", "Faroe", "South_Georgia", "Reykjavik", "Cocos", "Christmas", "Chagos", "Mauritius", "Maldives", "<PERSON><PERSON>", "Reunion", "<PERSON><PERSON><PERSON><PERSON>", "WorldTime", "_s", "defaultDateFormat", "defaultTimeFormat", "background", "backgroundImage", "backgroundPosition", "backgroundSize", "backgroundRepeat", "bnCalendar", "arabicCalendar", "queryParameters", "URLSearchParams", "window", "location", "search", "getDateTime", "get", "getDefaultTimeZones", "getTimeZones", "defaultTimeZone", "setDefaultTimeZone", "label", "value", "currentDateTime", "setCurrentDateTime", "tz", "error", "setError", "loading", "setLoading", "weatherData", "setWeatherData", "ipData", "setIpData", "fromDateTime", "setFromDateTime", "fromtimezone", "setFromtimezone", "totimezone", "setTotimezone", "showCityList", "setShowCityList", "isFixed", "fixedCityList", "setFixedCityList", "favCityList", "setFavCityList", "params", "latitude", "lat", "longitude", "lon", "hourly", "daily", "timeformat", "timezone", "past_days", "forecast_days", "datetimeToISOString", "datetime", "Date", "valueOf", "generateShareUrl", "url", "href", "split", "isoDateTime", "toISOString", "dtmz", "timezones", "map", "item", "replaceAll", "append", "join", "toString", "getTimeDifferenceBetweenTimezones", "timezone1", "timezone2", "time1", "time2", "moment1", "moment2", "diffInHours", "diff", "result", "differenceInHours", "timeInTz1", "format", "timeInTz2", "singleLayoutClasses", "pathname", "getDateTimeParseData", "Number", "timezonesData", "fetchWithTimeout", "options", "timeout", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "signal", "clearTimeout", "fetchIP", "retryCount", "method", "ok", "Error", "statusText", "data", "json", "fetchCurrentDateTimeByIP", "console", "warn", "message", "log", "query", "ip", "responseData", "fetchError", "<PERSON><PERSON><PERSON><PERSON>", "timeoutPromise", "Promise", "_", "reject", "weatherPromise", "responses", "race", "range", "start", "stop", "step", "Array", "from", "length", "i", "utcOffsetSeconds", "weather", "time", "timeEnd", "interval", "t", "temperature2m", "variables", "valuesArray", "relativeHumidity2m", "apparentTemperature", "precipitationProbability", "precipitation", "rain", "visibility", "windSpeed10m", "uvIndex", "weatherCode", "sunrise", "sunset", "err", "refreshInterval", "setInterval", "healthCheckInterval", "healthError", "clearInterval", "convertDateTime", "totimezonevalue", "fromtimezonevalue", "fromConvertDateTime", "retDateTime", "clone", "<PERSON><PERSON><PERSON><PERSON>", "getTimeZoneSelectOptions", "Object", "keys", "sort", "push", "getBnDateTime", "dateTime", "date", "bnDateTime", "fromDate", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "closeMenuOnSelect", "isClearable", "isSearchable", "isDisabled", "is<PERSON><PERSON><PERSON>", "required", "placeholder", "name", "onChange", "style", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/world-time/WorldTime.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport moment from \"moment\";\r\nimport \"moment-timezone\";\r\nimport { fetchWeather<PERSON><PERSON> } from \"openmeteo\";\r\nimport Select from \"react-select\";\r\nimport DynamicTimeCard from \"./DynamicTimeCard\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Calendar from \"date-bengali-revised\";\r\nimport uq from '@umalqura/core';\r\nimport timezonebg from \"../../assets/images/timezonebg.png\";\r\n\r\nconst timeZones = {\r\n  Dubai: \"Asia/Dubai\",\r\n  Kabul: \"Asia/Kabul\",\r\n  Yerevan: \"Asia/Yerevan\",\r\n  Baku: \"Asia/Baku\",\r\n  Dhaka: \"Asia/Dhaka\",\r\n  Brunei: \"Asia/Brunei\",\r\n  Thimphu: \"Asia/Thimphu\",\r\n  Shanghai: \"Asia/Shanghai\",\r\n  Urumqi: \"Asia/Urumqi\",\r\n  Nicosia: \"Asia/Nicosia\",\r\n  Famagusta: \"Asia/Famagusta\",\r\n  Tbilisi: \"Asia/Tbilisi\",\r\n  Hong_Kong: \"Asia/Hong_Kong\",\r\n  Jakarta: \"Asia/Jakarta\",\r\n  Pontianak: \"Asia/Pontianak\",\r\n  Makassar: \"Asia/Makassar\",\r\n  Jayapura: \"Asia/Jayapura\",\r\n  Jerusalem: \"Asia/Jerusalem\",\r\n  Kolkata: \"Asia/Kolkata\",\r\n  Baghdad: \"Asia/Baghdad\",\r\n  Tehran: \"Asia/Tehran\",\r\n  Amman: \"Asia/Amman\",\r\n  Tokyo: \"Asia/Tokyo\",\r\n  Bishkek: \"Asia/Bishkek\",\r\n  Pyongyang: \"Asia/Pyongyang\",\r\n  Seoul: \"Asia/Seoul\",\r\n  Almaty: \"Asia/Almaty\",\r\n  Qyzylorda: \"Asia/Qyzylorda\",\r\n  Qostanay: \"Asia/Qostanay\",\r\n  Aqtobe: \"Asia/Aqtobe\",\r\n  Aqtau: \"Asia/Aqtau\",\r\n  Atyrau: \"Asia/Atyrau\",\r\n  Oral: \"Asia/Oral\",\r\n  Beirut: \"Asia/Beirut\",\r\n  Colombo: \"Asia/Colombo\",\r\n  Yangon: \"Asia/Yangon\",\r\n  Ulaanbaatar: \"Asia/Ulaanbaatar\",\r\n  Hovd: \"Asia/Hovd\",\r\n  Choibalsan: \"Asia/Choibalsan\",\r\n  Macau: \"Asia/Macau\",\r\n  Kuala_Lumpur: \"Asia/Kuala_Lumpur\",\r\n  Kuching: \"Asia/Kuching\",\r\n  Karachi: \"Asia/Karachi\",\r\n  Gaza: \"Asia/Gaza\",\r\n  Hebron: \"Asia/Hebron\",\r\n  Kathmandu: \"Asia/Kathmandu\",\r\n  Yekaterinburg: \"Asia/Yekaterinburg\",\r\n  Qatar: \"Asia/Qatar\",\r\n  Omsk: \"Asia/Omsk\",\r\n  Novosibirsk: \"Asia/Novosibirsk\",\r\n  Barnaul: \"Asia/Barnaul\",\r\n  Tomsk: \"Asia/Tomsk\",\r\n  Novokuznetsk: \"Asia/Novokuznetsk\",\r\n  Krasnoyarsk: \"Asia/Krasnoyarsk\",\r\n  Irkutsk: \"Asia/Irkutsk\",\r\n  Chita: \"Asia/Chita\",\r\n  Yakutsk: \"Asia/Yakutsk\",\r\n  Khandyga: \"Asia/Khandyga\",\r\n  Vladivostok: \"Asia/Vladivostok\",\r\n  Ust_Nera: \"Asia/Ust-Nera\",\r\n  Singapore: \"Asia/Singapore\",\r\n  Magadan: \"Asia/Magadan\",\r\n  Sakhalin: \"Asia/Sakhalin\",\r\n  Srednekolymsk: \"Asia/Srednekolymsk\",\r\n  Kamchatka: \"Asia/Kamchatka\",\r\n  Anadyr: \"Asia/Anadyr\",\r\n  Bangkok: \"Asia/Bangkok\",\r\n  Dushanbe: \"Asia/Dushanbe\",\r\n  Taipei: \"Asia/Taipei\",\r\n  Dili: \"Asia/Dili\",\r\n  Ashgabat: \"Asia/Ashgabat\",\r\n  Damascus: \"Asia/Damascus\",\r\n  Riyadh: \"Asia/Riyadh\",\r\n  Samarkand: \"Asia/Samarkand\",\r\n  Tashkent: \"Asia/Tashkent\",\r\n  Ho_Chi_Minh: \"Asia/Ho_Chi_Minh\",\r\n  Andorra: \"Europe/Andorra\",\r\n  Tirane: \"Europe/Tirane\",\r\n  Vienna: \"Europe/Vienna\",\r\n  Brussels: \"Europe/Brussels\",\r\n  Sofia: \"Europe/Sofia\",\r\n  Minsk: \"Europe/Minsk\",\r\n  Zurich: \"Europe/Zurich\",\r\n  Prague: \"Europe/Prague\",\r\n  Berlin: \"Europe/Berlin\",\r\n  Copenhagen: \"Europe/Copenhagen\",\r\n  Tallinn: \"Europe/Tallinn\",\r\n  Madrid: \"Europe/Madrid\",\r\n  Helsinki: \"Europe/Helsinki\",\r\n  Paris: \"Europe/Paris\",\r\n  London: \"Europe/London\",\r\n  Gibraltar: \"Europe/Gibraltar\",\r\n  Athens: \"Europe/Athens\",\r\n  Budapest: \"Europe/Budapest\",\r\n  Dublin: \"Europe/Dublin\",\r\n  Rome: \"Europe/Rome\",\r\n  Vilnius: \"Europe/Vilnius\",\r\n  Luxembourg: \"Europe/Luxembourg\",\r\n  Riga: \"Europe/Riga\",\r\n  Monaco: \"Europe/Monaco\",\r\n  Chisinau: \"Europe/Chisinau\",\r\n  Malta: \"Europe/Malta\",\r\n  Amsterdam: \"Europe/Amsterdam\",\r\n  Oslo: \"Europe/Oslo\",\r\n  Warsaw: \"Europe/Warsaw\",\r\n  Lisbon: \"Europe/Lisbon\",\r\n  Bucharest: \"Europe/Bucharest\",\r\n  Belgrade: \"Europe/Belgrade\",\r\n  Kaliningrad: \"Europe/Kaliningrad\",\r\n  Moscow: \"Europe/Moscow\",\r\n  Simferopol: \"Europe/Simferopol\",\r\n  Kirov: \"Europe/Kirov\",\r\n  Astrakhan: \"Europe/Astrakhan\",\r\n  Volgograd: \"Europe/Volgograd\",\r\n  Saratov: \"Europe/Saratov\",\r\n  Ulyanovsk: \"Europe/Ulyanovsk\",\r\n  Samara: \"Europe/Samara\",\r\n  Stockholm: \"Europe/Stockholm\",\r\n  Istanbul: \"Europe/Istanbul\",\r\n  Kiev: \"Europe/Kiev\",\r\n  Uzhgorod: \"Europe/Uzhgorod\",\r\n  Zaporozhye: \"Europe/Zaporozhye\",\r\n  Casey: \"Antarctica/Casey\",\r\n  Davis: \"Antarctica/Davis\",\r\n  DumontDUrville: \"Antarctica/DumontDUrville\",\r\n  Mawson: \"Antarctica/Mawson\",\r\n  Palmer: \"Antarctica/Palmer\",\r\n  Rothera: \"Antarctica/Rothera\",\r\n  Syowa: \"Antarctica/Syowa\",\r\n  Troll: \"Antarctica/Troll\",\r\n  Vostok: \"Antarctica/Vostok\",\r\n  Macquarie: \"Antarctica/Macquarie\",\r\n  Buenos_Aires: \"America/Argentina/Buenos_Aires\",\r\n  Cordoba: \"America/Argentina/Cordoba\",\r\n  Salta: \"America/Argentina/Salta\",\r\n  Jujuy: \"America/Argentina/Jujuy\",\r\n  Tucuman: \"America/Argentina/Tucuman\",\r\n  Catamarca: \"America/Argentina/Catamarca\",\r\n  La_Rioja: \"America/Argentina/La_Rioja\",\r\n  San_Juan: \"America/Argentina/San_Juan\",\r\n  Mendoza: \"America/Argentina/Mendoza\",\r\n  San_Luis: \"America/Argentina/San_Luis\",\r\n  Rio_Gallegos: \"America/Argentina/Rio_Gallegos\",\r\n  Ushuaia: \"America/Argentina/Ushuaia\",\r\n  Barbados: \"America/Barbados\",\r\n  La_Paz: \"America/La_Paz\",\r\n  Belem: \"America/Belem\",\r\n  Fortaleza: \"America/Fortaleza\",\r\n  Recife: \"America/Recife\",\r\n  Araguaina: \"America/Araguaina\",\r\n  Maceio: \"America/Maceio\",\r\n  Bahia: \"America/Bahia\",\r\n  Sao_Paulo: \"America/Sao_Paulo\",\r\n  Campo_Grande: \"America/Campo_Grande\",\r\n  Cuiaba: \"America/Cuiaba\",\r\n  Porto_Velho: \"America/Porto_Velho\",\r\n  Boa_Vista: \"America/Boa_Vista\",\r\n  Manaus: \"America/Manaus\",\r\n  Eirunepe: \"America/Eirunepe\",\r\n  Rio_Branco: \"America/Rio_Branco\",\r\n  Nassau: \"America/Nassau\",\r\n  Belize: \"America/Belize\",\r\n  St_Johns: \"America/St_Johns\",\r\n  Halifax: \"America/Halifax\",\r\n  Glace_Bay: \"America/Glace_Bay\",\r\n  Moncton: \"America/Moncton\",\r\n  Goose_Bay: \"America/Goose_Bay\",\r\n  Blanc_Sablon: \"America/Blanc-Sablon\",\r\n  Toronto: \"America/Toronto\",\r\n  Nipigon: \"America/Nipigon\",\r\n  Thunder_Bay: \"America/Thunder_Bay\",\r\n  Iqaluit: \"America/Iqaluit\",\r\n  Pangnirtung: \"America/Pangnirtung\",\r\n  Atikokan: \"America/Atikokan\",\r\n  Winnipeg: \"America/Winnipeg\",\r\n  Rainy_River: \"America/Rainy_River\",\r\n  Resolute: \"America/Resolute\",\r\n  Rankin_Inlet: \"America/Rankin_Inlet\",\r\n  Regina: \"America/Regina\",\r\n  Swift_Current: \"America/Swift_Current\",\r\n  Edmonton: \"America/Edmonton\",\r\n  Cambridge_Bay: \"America/Cambridge_Bay\",\r\n  Yellowknife: \"America/Yellowknife\",\r\n  Inuvik: \"America/Inuvik\",\r\n  Creston: \"America/Creston\",\r\n  Dawson_Creek: \"America/Dawson_Creek\",\r\n  Fort_Nelson: \"America/Fort_Nelson\",\r\n  Vancouver: \"America/Vancouver\",\r\n  Whitehorse: \"America/Whitehorse\",\r\n  Dawson: \"America/Dawson\",\r\n  Santiago: \"America/Santiago\",\r\n  Punta_Arenas: \"America/Punta_Arenas\",\r\n  Bogota: \"America/Bogota\",\r\n  Costa_Rica: \"America/Costa_Rica\",\r\n  Havana: \"America/Havana\",\r\n  Curacao: \"America/Curacao\",\r\n  Santo_Domingo: \"America/Santo_Domingo\",\r\n  Guayaquil: \"America/Guayaquil\",\r\n  Cayenne: \"America/Cayenne\",\r\n  Godthab: \"America/Godthab\",\r\n  Danmarkshavn: \"America/Danmarkshavn\",\r\n  Scoresbysund: \"America/Scoresbysund\",\r\n  Thule: \"America/Thule\",\r\n  Guatemala: \"America/Guatemala\",\r\n  Guyana: \"America/Guyana\",\r\n  Tegucigalpa: \"America/Tegucigalpa\",\r\n  Port_au_Prince: \"America/Port-au-Prince\",\r\n  Jamaica: \"America/Jamaica\",\r\n  Martinique: \"America/Martinique\",\r\n  Mexico_City: \"America/Mexico_City\",\r\n  Cancun: \"America/Cancun\",\r\n  Merida: \"America/Merida\",\r\n  Monterrey: \"America/Monterrey\",\r\n  Matamoros: \"America/Matamoros\",\r\n  Caracas: \"America/Caracas\",\r\n  Mazatlan: \"America/Mazatlan\",\r\n  Chihuahua: \"America/Chihuahua\",\r\n  Ojinaga: \"America/Ojinaga\",\r\n  Hermosillo: \"America/Hermosillo\",\r\n  Tijuana: \"America/Tijuana\",\r\n  Bahia_Banderas: \"America/Bahia_Banderas\",\r\n  Managua: \"America/Managua\",\r\n  Panama: \"America/Panama\",\r\n  Lima: \"America/Lima\",\r\n  Miquelon: \"America/Miquelon\",\r\n  Puerto_Rico: \"America/Puerto_Rico\",\r\n  El_Salvador: \"America/El_Salvador\",\r\n  Grand_Turk: \"America/Grand_Turk\",\r\n  Paramaribo: \"America/Paramaribo\",\r\n  Asuncion: \"America/Asuncion\",\r\n  Port_of_Spain: \"America/Port_of_Spain\",\r\n  New_York: \"America/New_York\",\r\n  New_Jersey: \"America/New_York\",\r\n  Detroit: \"America/Detroit\",\r\n  Louisville: \"America/Kentucky/Louisville\",\r\n  Monticello: \"America/Kentucky/Monticello\",\r\n  Indianapolis: \"America/Indiana/Indianapolis\",\r\n  Vincennes: \"America/Indiana/Vincennes\",\r\n  Winamac: \"America/Indiana/Winamac\",\r\n  Marengo: \"America/Indiana/Marengo\",\r\n  Petersburg: \"America/Indiana/Petersburg\",\r\n  Vevay: \"America/Indiana/Vevay\",\r\n  Tell_City: \"America/Indiana/Tell_City\",\r\n  Knox: \"America/Indiana/Knox\",\r\n  Chicago: \"America/Chicago\",\r\n  Menominee: \"America/Menominee\",\r\n  Denver: \"America/Denver\",\r\n  Boise: \"America/Boise\",\r\n  Phoenix: \"America/Phoenix\",\r\n  Center: \"America/North_Dakota/Center\",\r\n  New_Salem: \"America/North_Dakota/New_Salem\",\r\n  Beulah: \"America/North_Dakota/Beulah\",\r\n  Los_Angeles: \"America/Los_Angeles\",\r\n  Anchorage: \"America/Anchorage\",\r\n  Alaska: \"America/Anchorage\",\r\n  Juneau: \"America/Juneau\",\r\n  Sitka: \"America/Sitka\",\r\n  Metlakatla: \"America/Metlakatla\",\r\n  Yakutat: \"America/Yakutat\",\r\n  Nome: \"America/Nome\",\r\n  Adak: \"America/Adak\",\r\n  Montevideo: \"America/Montevideo\",\r\n  Pago_Pago: \"Pacific/Pago_Pago\",\r\n  Rarotonga: \"Pacific/Rarotonga\",\r\n  Easter: \"Pacific/Easter\",\r\n  Galapagos: \"Pacific/Galapagos\",\r\n  Fiji: \"Pacific/Fiji\",\r\n  Chuuk: \"Pacific/Chuuk\",\r\n  Pohnpei: \"Pacific/Pohnpei\",\r\n  Kosrae: \"Pacific/Kosrae\",\r\n  Guam: \"Pacific/Guam\",\r\n  Majuro: \"Pacific/Majuro\",\r\n  Kwajalein: \"Pacific/Kwajalein\",\r\n  Tarawa: \"Pacific/Tarawa\",\r\n  Enderbury: \"Pacific/Enderbury\",\r\n  Kiritimati: \"Pacific/Kiritimati\",\r\n  Noumea: \"Pacific/Noumea\",\r\n  Norfolk: \"Pacific/Norfolk\",\r\n  Nauru: \"Pacific/Nauru\",\r\n  Niue: \"Pacific/Niue\",\r\n  Auckland: \"Pacific/Auckland\",\r\n  Chatham: \"Pacific/Chatham\",\r\n  Tahiti: \"Pacific/Tahiti\",\r\n  Marquesas: \"Pacific/Marquesas\",\r\n  Gambier: \"Pacific/Gambier\",\r\n  Port_Moresby: \"Pacific/Port_Moresby\",\r\n  Bougainville: \"Pacific/Bougainville\",\r\n  Pitcairn: \"Pacific/Pitcairn\",\r\n  Palau: \"Pacific/Palau\",\r\n  Guadalcanal: \"Pacific/Guadalcanal\",\r\n  Fakaofo: \"Pacific/Fakaofo\",\r\n  Tongatapu: \"Pacific/Tongatapu\",\r\n  Funafuti: \"Pacific/Funafuti\",\r\n  Wake: \"Pacific/Wake\",\r\n  Honolulu: \"Pacific/Honolulu\",\r\n  Efate: \"Pacific/Efate\",\r\n  Wallis: \"Pacific/Wallis\",\r\n  Apia: \"Pacific/Apia\",\r\n  Lord_Howe: \"Australia/Lord_Howe\",\r\n  Hobart: \"Australia/Hobart\",\r\n  Currie: \"Australia/Currie\",\r\n  Melbourne: \"Australia/Melbourne\",\r\n  Sydney: \"Australia/Sydney\",\r\n  Broken_Hill: \"Australia/Broken_Hill\",\r\n  Brisbane: \"Australia/Brisbane\",\r\n  Lindeman: \"Australia/Lindeman\",\r\n  Adelaide: \"Australia/Adelaide\",\r\n  Darwin: \"Australia/Darwin\",\r\n  Perth: \"Australia/Perth\",\r\n  Eucla: \"Australia/Eucla\",\r\n  Abidjan: \"Africa/Abidjan\",\r\n  Algiers: \"Africa/Algiers\",\r\n  Cairo: \"Africa/Cairo\",\r\n  El_Aaiun: \"Africa/El_Aaiun\",\r\n  Ceuta: \"Africa/Ceuta\",\r\n  Accra: \"Africa/Accra\",\r\n  Bissau: \"Africa/Bissau\",\r\n  Nairobi: \"Africa/Nairobi\",\r\n  Monrovia: \"Africa/Monrovia\",\r\n  Tripoli: \"Africa/Tripoli\",\r\n  Casablanca: \"Africa/Casablanca\",\r\n  Maputo: \"Africa/Maputo\",\r\n  Windhoek: \"Africa/Windhoek\",\r\n  Lagos: \"Africa/Lagos\",\r\n  Khartoum: \"Africa/Khartoum\",\r\n  Juba: \"Africa/Juba\",\r\n  Sao_Tome: \"Africa/Sao_Tome\",\r\n  Ndjamena: \"Africa/Ndjamena\",\r\n  Tunis: \"Africa/Tunis\",\r\n  Johannesburg: \"Africa/Johannesburg\",\r\n  Azores: \"Atlantic/Azores\",\r\n  Bermuda: \"Atlantic/Bermuda\",\r\n  Madeira: \"Atlantic/Madeira\",\r\n  Cape_Verde: \"Atlantic/Cape_Verde\",\r\n  Canary: \"Atlantic/Canary\",\r\n  Stanley: \"Atlantic/Stanley\",\r\n  Faroe: \"Atlantic/Faroe\",\r\n  South_Georgia: \"Atlantic/South_Georgia\",\r\n  Reykjavik: \"Atlantic/Reykjavik\",\r\n  Cocos: \"Indian/Cocos\",\r\n  Christmas: \"Indian/Christmas\",\r\n  Chagos: \"Indian/Chagos\",\r\n  Mauritius: \"Indian/Mauritius\",\r\n  Maldives: \"Indian/Maldives\",\r\n  Mahe: \"Indian/Mahe\",\r\n  Reunion: \"Indian/Reunion\",\r\n  Kerguelen: \"Indian/Kerguelen\",\r\n};\r\n\r\nfunction WorldTime() {\r\n  const defaultDateFormat = \"dddd, LL\";\r\n  const defaultTimeFormat = \"hh:mm A\";\r\n  const background = {\r\n    backgroundImage: `url(${timezonebg})`,\r\n    backgroundPosition: 'center center',\r\n    backgroundSize: 'contain',\r\n    backgroundRepeat: 'no-repeat'\r\n  }\r\n\r\n  let bnCalendar = new Calendar();\r\n  const arabicCalendar = uq(); \r\n\r\n  \r\n\r\n  const queryParameters = new URLSearchParams(window.location.search);\r\n  const getDateTime = queryParameters.get(\"datetime\");\r\n  const getDefaultTimeZones = queryParameters.get(\"defaulttimezone\");\r\n  const getTimeZones = queryParameters.get(\"timezones\");\r\n\r\n  const [defaultTimeZone, setDefaultTimeZone] = useState({\r\n    label: \"Dhaka\",\r\n    value: \"Asia/Dhaka\",\r\n  }); // Store team data\r\n  const [currentDateTime, setCurrentDateTime] = useState(\r\n    moment().tz(defaultTimeZone[\"value\"])\r\n  ); // Store team data\r\n  const [error, setError] = useState(null); // Handle errors\r\n  const [loading, setLoading] = useState(true); // Loading state\r\n  const [weatherData, setWeatherData] = useState(null);\r\n  const [ipData, setIpData] = useState(false);\r\n  const [fromDateTime, setFromDateTime] = useState(\"\");\r\n  const [fromtimezone, setFromtimezone] = useState({\r\n    label: \"Dhaka\",\r\n    value: \"Asia/Dhaka\",\r\n  });\r\n  const [totimezone, setTotimezone] = useState({\r\n    label: \"New York\",\r\n    value: \"America/New_York\",\r\n  });\r\n  const [showCityList, setShowCityList] = useState([\r\n    {\r\n      label: \"Dhaka\",\r\n      value: \"Asia/Dhaka\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Alaska\",\r\n      value: \"America/Anchorage\",\r\n    },\r\n   \r\n    {\r\n      label: \"New York\",\r\n      value: \"America/New_York\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"New Jersey\",\r\n      value: \"America/New_York\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Los Angeles\",\r\n      value: \"America/Los_Angeles\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Chicago\",\r\n      value: \"America/Chicago\",\r\n    },\r\n    {\r\n      label: \"El Salvador\",\r\n      value: \"America/El_Salvador\",\r\n    },\r\n\r\n    {\r\n      label: \"London\",\r\n      value: \"Europe/London\",\r\n    },\r\n   \r\n    {\r\n      label: \"Honolulu\",\r\n      value: \"Pacific/Honolulu\",\r\n    },\r\n\r\n    {\r\n      label: \"Adelaide\",\r\n      value: \"Australia/Adelaide\",\r\n    },\r\n    {\r\n      label: \"Sydney\",\r\n      value: \"Australia/Sydney\",\r\n    },\r\n    \r\n    {\r\n      label: \"Perth\",\r\n      value: \"Australia/Perth\",\r\n    },\r\n    \r\n    {\r\n      label: \"Singapore\",\r\n      value: \"Asia/Singapore\",\r\n    },\r\n   \r\n    {\r\n      label: \"Dubai\",\r\n      value: \"Asia/Dubai\",\r\n    },\r\n   \r\n    {\r\n      label: \"Tokyo\",\r\n      value: \"Asia/Tokyo\",\r\n    },\r\n   \r\n    \r\n   \r\n    \r\n  ]);\r\n\r\n  \r\n  const [fixedCityList, setFixedCityList] = useState([\r\n    {\r\n      label: \"Dhaka\",\r\n      value: \"Asia/Dhaka\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Alaska\",\r\n      value: \"America/Anchorage\",\r\n    },\r\n   \r\n    {\r\n      label: \"New York\",\r\n      value: \"America/New_York\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"New Jersey\",\r\n      value: \"America/New_York\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Los Angeles\",\r\n      value: \"America/Los_Angeles\",\r\n      isFixed: true,\r\n    },\r\n    {\r\n      label: \"Chicago\",\r\n      value: \"America/Chicago\",\r\n    },\r\n    {\r\n      label: \"El Salvador\",\r\n      value: \"America/El_Salvador\",\r\n    },\r\n\r\n    {\r\n      label: \"London\",\r\n      value: \"Europe/London\",\r\n    },\r\n   \r\n    {\r\n      label: \"Honolulu\",\r\n      value: \"Pacific/Honolulu\",\r\n    },\r\n\r\n    {\r\n      label: \"Adelaide\",\r\n      value: \"Australia/Adelaide\",\r\n    },\r\n    {\r\n      label: \"Sydney\",\r\n      value: \"Australia/Sydney\",\r\n    },\r\n    \r\n    {\r\n      label: \"Perth\",\r\n      value: \"Australia/Perth\",\r\n    },\r\n    \r\n    {\r\n      label: \"Singapore\",\r\n      value: \"Asia/Singapore\",\r\n    },\r\n   \r\n    {\r\n      label: \"Dubai\",\r\n      value: \"Asia/Dubai\",\r\n    },\r\n   \r\n    {\r\n      label: \"Tokyo\",\r\n      value: \"Asia/Tokyo\",\r\n    },\r\n   \r\n    \r\n   \r\n    \r\n  ]);\r\n\r\n\r\n  const [favCityList, setFavCityList] = useState([\r\n    {\r\n      label: \"New York\",\r\n      value: \"America/New_York\",\r\n    },\r\n\r\n    {\r\n      label: \"London\",\r\n      value: \"Europe/London\",\r\n    },\r\n\r\n    {\r\n      label: \"El Salvador\",\r\n      value: \"America/El_Salvador\",\r\n    },\r\n  ]);\r\n\r\n  const params = {\r\n    latitude: ipData?.lat,\r\n    longitude: ipData?.lon,\r\n    hourly: [\r\n      \"temperature_2m\",\r\n      \"relative_humidity_2m\",\r\n      \"apparent_temperature\",\r\n      \"precipitation_probability\",\r\n      \"precipitation\",\r\n      \"rain\",\r\n      \"visibility\",\r\n      \"wind_speed_10m\",\r\n      \"uv_index\",\r\n    ],\r\n    daily: [\"weather_code\", \"sunrise\", \"sunset\"],\r\n    timeformat: \"unixtime\",\r\n    timezone: \"auto\",\r\n    past_days: 0,\r\n    forecast_days: 6,\r\n  };\r\n\r\n  const datetimeToISOString = (datetime) => {\r\n    return new Date(datetime).valueOf();\r\n  };\r\n\r\n  const generateShareUrl = () => {\r\n    let url = window.location.href.split(\"?\")[0];\r\n    let params = new URLSearchParams();\r\n\r\n    let isoDateTime = fromDateTime\r\n      ? new Date(fromDateTime).toISOString()\r\n      : new Date(currentDateTime).toISOString();\r\n    let dtmz = fromDateTime ? fromtimezone[\"label\"] : defaultTimeZone[\"label\"];\r\n    let timezones = showCityList.map((item) =>\r\n      item[\"label\"].replaceAll(\" \", \"_\")\r\n    );\r\n\r\n    params.append(\"datetime\", datetimeToISOString(isoDateTime));\r\n    params.append(\"defaulttimezone\", dtmz);\r\n    params.append(\"timezones\", timezones.join(\",\"));\r\n    url += \"?\" + params.toString(\",\");\r\n    return url;\r\n  };\r\n\r\n  const getTimeDifferenceBetweenTimezones = (\r\n    timezone1,\r\n    timezone2,\r\n    time1,\r\n    time2\r\n  ) => {\r\n    // time1 and time2 are optional. If not provided, current time in respective timezones is used.\r\n\r\n    const moment1 = time1 ? moment.tz(time1, timezone1) : moment.tz(timezone1);\r\n    const moment2 = time2 ? moment.tz(time2, timezone2) : moment.tz(timezone2);\r\n\r\n    const diffInHours = moment2.diff(moment1, \"hours\");\r\n\r\n    let result = {\r\n      differenceInHours: diffInHours,\r\n      timeInTz1: moment1.format(\"HH:mm\"),\r\n      timeInTz2: moment2.format(\"HH:mm\"),\r\n    };\r\n\r\n    // console.log(timezone1, timezone2, time1, time2)\r\n    // console.log(result)\r\n\r\n    return result.differenceInHours;\r\n  };\r\n\r\n  let singleLayoutClasses = \"\";\r\n\r\n  if (window.location.pathname === \"/world-time-share\") {\r\n    singleLayoutClasses = \" max-w-[1642px] mx-auto \";\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (getDateTime) {\r\n      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();\r\n      setFromDateTime(getDateTimeParseData);\r\n    }\r\n\r\n    if (getDefaultTimeZones) {\r\n      setDefaultTimeZone({\r\n        label: getDefaultTimeZones,\r\n        value: timeZones[getDefaultTimeZones],\r\n      });\r\n      setFromtimezone({\r\n        label: getDefaultTimeZones,\r\n        value: timeZones[getDefaultTimeZones],\r\n      });\r\n    }\r\n\r\n    if (getTimeZones) {\r\n      let timezones = getTimeZones.split(\",\");\r\n      let timezonesData = timezones.map((item) => {\r\n        return { label: item, value: timeZones[item] };\r\n      });\r\n      setShowCityList(timezonesData);\r\n    }\r\n  }, [getDateTime, getDefaultTimeZones, getTimeZones]);\r\n\r\n  // Fetch IP data with timeout and retry logic\r\n  useEffect(() => {\r\n    const fetchWithTimeout = async (url, options = {}, timeout = 10000) => {\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\r\n\r\n      try {\r\n        const response = await fetch(url, {\r\n          ...options,\r\n          signal: controller.signal\r\n        });\r\n        clearTimeout(timeoutId);\r\n        return response;\r\n      } catch (error) {\r\n        clearTimeout(timeoutId);\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    const fetchIP = async (retryCount = 0) => {\r\n      try {\r\n        const response = await fetchWithTimeout(`//ip-api.com/json/`, { method: \"GET\" }, 8000);\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Failed to fetch IP data: \" + response.statusText);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setIpData(data);\r\n        fetchCurrentDateTimeByIP(data[\"query\"]);\r\n      } catch (error) {\r\n        console.warn(\"IP fetch attempt failed:\", error.message);\r\n\r\n        // Retry logic - try up to 2 more times\r\n        if (retryCount < 2) {\r\n          console.log(`Retrying IP fetch... Attempt ${retryCount + 2}/3`);\r\n          setTimeout(() => fetchIP(retryCount + 1), 2000 * (retryCount + 1));\r\n          return;\r\n        }\r\n\r\n        // If all retries fail, use fallback\r\n        console.log(\"All IP fetch attempts failed, using fallback\");\r\n        setError(\"Unable to fetch location data, using default timezone\");\r\n        setIpData({ query: \"fallback\", timezone: \"UTC\" });\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchIP();\r\n  }, []);\r\n\r\n  const fetchCurrentDateTimeByIP = async (ip = \"\", retryCount = 0) => {\r\n    try {\r\n      if (ip && ip !== \"fallback\") {\r\n        const controller = new AbortController();\r\n        const timeoutId = setTimeout(() => controller.abort(), 8000);\r\n\r\n        try {\r\n          const response = await fetch(\r\n            `https://timeapi.io/api/time/current/ip?ipAddress=${ip}`,\r\n            {\r\n              method: \"GET\",\r\n              signal: controller.signal\r\n            }\r\n          );\r\n          clearTimeout(timeoutId);\r\n\r\n          if (!response.ok) {\r\n            throw new Error(\"Failed to fetch time data: \" + response.statusText);\r\n          }\r\n\r\n          const responseData = await response.json();\r\n\r\n          if (responseData) {\r\n            setDefaultTimeZone({ ...defaultTimeZone, responseData });\r\n            setCurrentDateTime(moment(responseData[\"dateTime\"]));\r\n          }\r\n        } catch (fetchError) {\r\n          clearTimeout(timeoutId);\r\n          throw fetchError;\r\n        }\r\n      } else {\r\n        // Fallback to local time\r\n        console.log(\"Using local time as fallback\");\r\n        setCurrentDateTime(moment());\r\n      }\r\n    } catch (error) {\r\n      console.warn(\"Time fetch attempt failed:\", error.message);\r\n\r\n      // Retry logic - try up to 2 more times\r\n      if (retryCount < 2 && ip !== \"fallback\") {\r\n        console.log(`Retrying time fetch... Attempt ${retryCount + 2}/3`);\r\n        setTimeout(() => fetchCurrentDateTimeByIP(ip, retryCount + 1), 2000 * (retryCount + 1));\r\n        return;\r\n      }\r\n\r\n      // If all retries fail, use local time\r\n      console.log(\"Here is the \")\r\n      console.log(\"All time fetch attempts failed, using local time\");\r\n      setCurrentDateTime(moment());\r\n      setError(\"Unable to fetch accurate time, using local time\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!ipData) return;\r\n\r\n    const fetchWeather = async (retryCount = 0) => {\r\n      try {\r\n        const url = \"https://api.open-meteo.com/v1/forecast\";\r\n\r\n        // Add timeout to weather API call\r\n        const timeoutPromise = new Promise((_, reject) =>\r\n          setTimeout(() => reject(new Error('Weather API timeout')), 10000)\r\n        );\r\n\r\n        const weatherPromise = fetchWeatherApi(url, params);\r\n        const responses = await Promise.race([weatherPromise, timeoutPromise]);\r\n\r\n        const range = (start, stop, step) =>\r\n          Array.from(\r\n            { length: (stop - start) / step },\r\n            (_, i) => start + i * step\r\n          );\r\n        const response = responses[0];\r\n\r\n        const utcOffsetSeconds = response.utcOffsetSeconds();\r\n        const hourly = response.hourly();\r\n        const daily = response.daily();\r\n\r\n        const weather = {\r\n          hourly: {\r\n            time: range(\r\n              Number(hourly.time()),\r\n              Number(hourly.timeEnd()),\r\n              hourly.interval()\r\n            ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),\r\n            temperature2m: hourly.variables(0).valuesArray(),\r\n            relativeHumidity2m: hourly.variables(1).valuesArray(),\r\n            apparentTemperature: hourly.variables(2).valuesArray(),\r\n            precipitationProbability: hourly.variables(3).valuesArray(),\r\n            precipitation: hourly.variables(4).valuesArray(),\r\n            rain: hourly.variables(5).valuesArray(),\r\n            visibility: hourly.variables(6).valuesArray(),\r\n            windSpeed10m: hourly.variables(7).valuesArray(),\r\n            uvIndex: hourly.variables(8).valuesArray(),\r\n          },\r\n          daily: {\r\n            time: range(\r\n              Number(daily.time()),\r\n              Number(daily.timeEnd()),\r\n              daily.interval()\r\n            ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),\r\n            weatherCode: daily.variables(0).valuesArray(),\r\n            sunrise: daily.variables(1).valuesArray(),\r\n            sunset: daily.variables(2).valuesArray(),\r\n          },\r\n        };\r\n\r\n        setWeatherData(weather);\r\n      } catch (err) {\r\n        console.warn(\"Weather fetch attempt failed:\", err.message);\r\n\r\n        // Retry logic - try up to 2 more times\r\n        if (retryCount < 2) {\r\n          console.log(`Retrying weather fetch... Attempt ${retryCount + 2}/3`);\r\n          setTimeout(() => fetchWeather(retryCount + 1), 3000 * (retryCount + 1));\r\n          return;\r\n        }\r\n\r\n        // If all retries fail, set empty weather data\r\n        console.log(\"All weather fetch attempts failed, using fallback\");\r\n        setWeatherData({\r\n          hourly: { time: [], temperature2m: [], relativeHumidity2m: [] },\r\n          daily: { time: [], weatherCode: [], sunrise: [], sunset: [] }\r\n        });\r\n        setError(\"Unable to fetch weather data\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchWeather();\r\n  }, [ipData]);\r\n\r\n  // Periodic refresh to prevent stale connections\r\n  useEffect(() => {\r\n    const refreshInterval = setInterval(() => {\r\n      // Refresh current time every 5 minutes to prevent stale data\r\n      if (currentDateTime) {\r\n        setCurrentDateTime(moment());\r\n      }\r\n    }, 5 * 60 * 1000); // 5 minutes\r\n\r\n    // Connection health check every 10 minutes\r\n    const healthCheckInterval = setInterval(async () => {\r\n      try {\r\n        // Simple ping to check if network is still available\r\n        const controller = new AbortController();\r\n        const timeoutId = setTimeout(() => controller.abort(), 3000);\r\n\r\n        await fetch('//ip-api.com/json/', {\r\n          method: 'HEAD',\r\n          signal: controller.signal\r\n        });\r\n        clearTimeout(timeoutId);\r\n\r\n        // If successful, clear any previous errors\r\n        if (error) {\r\n          setError(null);\r\n        }\r\n      } catch (healthError) {\r\n        console.warn('Connection health check failed:', healthError.message);\r\n        // Don't set error for health check failures to avoid UI disruption\r\n      }\r\n    }, 10 * 60 * 1000); // 10 minutes\r\n\r\n    // Cleanup intervals on component unmount\r\n    return () => {\r\n      clearInterval(refreshInterval);\r\n      clearInterval(healthCheckInterval);\r\n    };\r\n  }, [currentDateTime, error]);\r\n\r\n\r\n  const convertDateTime = (totimezonevalue = \"\") => {\r\n    let fromtimezonevalue = fromtimezone.value || defaultTimeZone[\"value\"];\r\n    var fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\r\n    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\r\n\r\n    if (moment(retDateTime).isValid()) {\r\n      return retDateTime;\r\n    }\r\n\r\n    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\r\n    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\r\n\r\n    if (moment(retDateTime).isValid()) {\r\n      return retDateTime;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  const getTimeZoneSelectOptions = () => {\r\n    let data = [];\r\n    Object.keys(timeZones)\r\n      .sort()\r\n      .map((label) =>\r\n        data.push({\r\n          label: `${label.replaceAll(\"_\", \" \")}`,\r\n          value: timeZones[label],\r\n        })\r\n      );\r\n\r\n    return data;\r\n  };\r\n\r\n  const getBnDateTime = (dateTime) => {\r\n    let date = new Date(dateTime);\r\n    let bnDateTime = bnCalendar.fromDate(date).format(\"dddd D MMMM, Y Q\");\r\n    return bnDateTime;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={\r\n        \"bg-white dark:bg-gray-900 rounded-xl p-4 \" + singleLayoutClasses\r\n      }\r\n    >\r\n      <div className=\"flex justify-start items-start flex-row mb-[20px]\">\r\n        <div className=\"text-left w-1/2 \">\r\n          <h4 className=\"text-xl font-medium \">World Time</h4>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-start items-start flex-row mb-[50px] gap-3\">\r\n        {/* Current Date Time */}\r\n        <div className=\"border border-gray-200 rounded-xl  px-[15px] w-5/12 min-h-[210px] bg-[#0b333f] text-[#fff]\">\r\n        <DynamicTimeCard />\r\n        </div>\r\n\r\n        {favCityList.length > 0 &&\r\n          favCityList.map((item) => {\r\n            // let label = key, timezone = timeZoneList[key];\r\n            let label = item[\"label\"];\r\n            let timezone = item[\"value\"];\r\n\r\n            return (\r\n              <div\r\n                className=\"border border-gray-200 rounded-xl p-[10px] w-3/12 min-h-[210px] bg-[#57B6B2] text-[#fff] \"\r\n                key={\"timezone-\" + timezone}\r\n              >\r\n                <div className=\"justify-center text-2xl inline-block align-text-middle mt-[20px]\">\r\n                  <p className=\"text-[20px] font-normal\">\r\n                    {getTimeDifferenceBetweenTimezones(\r\n                      defaultTimeZone[\"value\"],\r\n                      timezone,\r\n                      currentDateTime.format(\"lll\"),\r\n                      convertDateTime(timezone).format(\"lll\")\r\n                    )}{\" \"}\r\n                    <small>Hours</small> (\r\n                    {convertDateTime(timezone).format(\"z\")})\r\n                  </p>\r\n                  <h1 className=\"text-4xl font-bold mb-1 text-[#0B333F]\">\r\n                    {label.replaceAll(\"_\", \" \")\r\n                      ? label.replaceAll(\"_\", \" \")\r\n                      : label}\r\n                  </h1>\r\n                  <p className=\" text-[24px] font-bold \">\r\n                    {/* {moment(fromDateTime).tz(timezone).format(defaultDateFormat)} */}\r\n                    {convertDateTime(timezone) &&\r\n                      convertDateTime(timezone).format(\"LL\")}\r\n                  </p>\r\n                  <p className=\" text-[16px] font-normal \">\r\n                    {/* {moment(fromDateTime).tz(timezone).format(defaultTimeFormat)} */}\r\n                    {convertDateTime(timezone) &&\r\n                      convertDateTime(timezone).format(\"dddd, hh:mm A\")}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n\r\n        <div className=\"border border-gray-200   rounded-xl   p-0 py-3 w-4/12 min-h-[210px]  bg-[#DFECF1] text-[#0B333F]\">\r\n          {currentDateTime && moment(currentDateTime).isValid() && (\r\n            <div className=\"justify-center justify-items-center\">\r\n              <h1 className=\"text-[24px]  font-semibold\">Today is</h1>\r\n              <hr className=\"border border-[#0B333F] my-[10px] h-[1px] w-[100%]\" />\r\n\r\n              {/* <h1 className=\"text-[64px] font-bold\">\r\n              {moment(fromDateTime).isValid() && moment(fromDateTime).format(defaultTimeFormat)}\r\n              {!moment(fromDateTime).isValid() && moment(currentDateTime).isValid() && moment(currentDateTime).format(defaultTimeFormat)}\r\n              </h1> */}\r\n              <p className=\"text-[20px] font-medium \">\r\n                {moment(fromDateTime).isValid() && (\r\n                  <>\r\n                    <p>{moment(fromDateTime).format(defaultDateFormat)}</p>\r\n                    <p>{getBnDateTime(currentDateTime)}</p>\r\n                    <p>{arabicCalendar.format('fullDate', 'en')}</p>\r\n                  </>\r\n                )}\r\n                {!moment(fromDateTime).isValid() &&\r\n                  moment(currentDateTime).isValid() && (\r\n                    <>\r\n                      <p>{moment(currentDateTime).format(defaultDateFormat)}</p>\r\n                      <p>{getBnDateTime(currentDateTime)}</p>\r\n                      <p>{arabicCalendar.format('fullDate', 'en')}</p>\r\n                    </>\r\n                  )}\r\n              </p>\r\n\r\n              {/* <h1 className=\"text-[64px] font-bold\">\r\n                {convertDateTime(totimezone.value).format(\"hh:mm A\")}\r\n              </h1>\r\n              <p className=\"text-[20px] font-normal \">\r\n                {convertDateTime(totimezone.value).format(defaultDateFormat)}\r\n              </p> */}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"w-full flex justify-start items-start flex-wrap  mb-10\">\r\n        <div className=\"text-start w-full \">\r\n          <div className=\"flex items-center rounded-xl gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600\">\r\n            <div className=\"grid shrink-0 grid-cols-1 focus-within:relative w-full \">\r\n              <Select\r\n                closeMenuOnSelect={false}\r\n                isClearable={true}\r\n                // isClearable={fixedCityList}\r\n                isSearchable={true}\r\n                isDisabled={false}\r\n                isMulti={true}\r\n                required={false}\r\n                placeholder=\"Choose your city\"\r\n                name=\"showCityList\"\r\n                value={showCityList.length <= 0? setShowCityList(fixedCityList) : showCityList}\r\n                options={getTimeZoneSelectOptions()}\r\n                onChange={(item) => {\r\n                  setShowCityList(item);\r\n                }}\r\n              />\r\n            </div>\r\n            {/* <button onClick={() => navigator.clipboard.writeText(generateShareUrl())} target=\"_blank\" className=\"block min-w-0 rounded-xl text-center grow py-1.5 pr-3 pl-1 border border-gray-200 bg-[#0B333F] hover:bg-[#2d9abb] text-base text-[#fff] placeholder:text-gray-400 focus:outline-none sm:text-sm/6\" >Share the link</button> */}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"w-full flex justify-start items-start flex-wrap  \" style={background}>\r\n        {/* Contacts Navigation */}\r\n\r\n        {/* All Contacts timeZone */}\r\n\r\n        {/* {Object.keys(showCityList).sort().map((label) => { */}\r\n        {showCityList.length > 0 &&\r\n          showCityList.map((item) => {\r\n            // let label = key, timezone = timeZoneList[key];\r\n            let label = item[\"label\"];\r\n            let timezone = item[\"value\"];\r\n\r\n            return (\r\n              <div\r\n                key={\"timezone-\" + timezone}\r\n                className=\"mb-6  w-1/3 px-[20px] \"\r\n              >\r\n                <div className=\"border-b-2 border-gray-400 pb-4   flex  justify-start items-start text-start \">\r\n                  <div className=\"w-1/2 text-start\">\r\n                    <p className=\"font-bold mb-1\">\r\n                      {label.replaceAll(\"_\", \" \")\r\n                        ? label.replaceAll(\"_\", \" \")\r\n                        : label}\r\n                    </p>\r\n                    <p className=\"font-normal\">\r\n                      {timezone} (\r\n                      {moment(currentDateTime).tz(timezone).format(\"z\")})\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"w-1/2 text-end leading-tight\">\r\n                    <div className=\" text-[30px] font-bold \">\r\n                      {moment(currentDateTime)\r\n                        .tz(timezone)\r\n                        .format(defaultTimeFormat)}\r\n                      {/* {convertDateTime(timezone) && convertDateTime(timezone).format(defaultTimeFormat)} */}\r\n                    </div>\r\n\r\n                    <div className=\" text-[14px] font-normal \">\r\n                      {moment(currentDateTime)\r\n                        .tz(timezone)\r\n                        .format(defaultDateFormat)}\r\n                      {/* {convertDateTime(timezone) && convertDateTime(timezone).format(defaultDateFormat)} */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default WorldTime;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,iBAAiB;AACxB,SAASC,eAAe,QAAQ,WAAW;AAC3C,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,EAAE,MAAM,gBAAgB;AAC/B,OAAOC,UAAU,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,kBAAkB;EAC/BC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,iBAAiB;EAC7BC,KAAK,EAAE,YAAY;EACnBC,YAAY,EAAE,mBAAmB;EACjCC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,cAAc;EACvBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,oBAAoB;EACnCC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,YAAY,EAAE,mBAAmB;EACjCC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,eAAe;EACzBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,aAAa,EAAE,oBAAoB;EACnCC,SAAS,EAAE,gBAAgB;EAC3BC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,gBAAgB;EACzBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,UAAU,EAAE,mBAAmB;EAC/BC,OAAO,EAAE,gBAAgB;EACzBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,gBAAgB;EACzBC,UAAU,EAAE,mBAAmB;EAC/BC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,oBAAoB;EACjCC,MAAM,EAAE,eAAe;EACvBC,UAAU,EAAE,mBAAmB;EAC/BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,SAAS,EAAE,kBAAkB;EAC7BC,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,mBAAmB;EAC/BC,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,kBAAkB;EACzBC,cAAc,EAAE,2BAA2B;EAC3CC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE,mBAAmB;EAC3BC,OAAO,EAAE,oBAAoB;EAC7BC,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,kBAAkB;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,SAAS,EAAE,sBAAsB;EACjCC,YAAY,EAAE,gCAAgC;EAC9CC,OAAO,EAAE,2BAA2B;EACpCC,KAAK,EAAE,yBAAyB;EAChCC,KAAK,EAAE,yBAAyB;EAChCC,OAAO,EAAE,2BAA2B;EACpCC,SAAS,EAAE,6BAA6B;EACxCC,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAE,4BAA4B;EACtCC,OAAO,EAAE,2BAA2B;EACpCC,QAAQ,EAAE,4BAA4B;EACtCC,YAAY,EAAE,gCAAgC;EAC9CC,OAAO,EAAE,2BAA2B;EACpCC,QAAQ,EAAE,kBAAkB;EAC5BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,YAAY,EAAE,sBAAsB;EACpCC,OAAO,EAAE,iBAAiB;EAC1BC,OAAO,EAAE,iBAAiB;EAC1BC,WAAW,EAAE,qBAAqB;EAClCC,OAAO,EAAE,iBAAiB;EAC1BC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,kBAAkB;EAC5BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,aAAa,EAAE,uBAAuB;EACtCC,QAAQ,EAAE,kBAAkB;EAC5BC,aAAa,EAAE,uBAAuB;EACtCC,WAAW,EAAE,qBAAqB;EAClCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,aAAa,EAAE,uBAAuB;EACtCC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,YAAY,EAAE,sBAAsB;EACpCC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,cAAc,EAAE,wBAAwB;EACxCC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,oBAAoB;EAChCC,WAAW,EAAE,qBAAqB;EAClCC,MAAM,EAAE,gBAAgB;EACxBC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,QAAQ,EAAE,kBAAkB;EAC5BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,oBAAoB;EAChCC,OAAO,EAAE,iBAAiB;EAC1BC,cAAc,EAAE,wBAAwB;EACxCC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,WAAW,EAAE,qBAAqB;EAClCC,UAAU,EAAE,oBAAoB;EAChCC,UAAU,EAAE,oBAAoB;EAChCC,QAAQ,EAAE,kBAAkB;EAC5BC,aAAa,EAAE,uBAAuB;EACtCC,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,kBAAkB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,6BAA6B;EACzCC,UAAU,EAAE,6BAA6B;EACzCC,YAAY,EAAE,8BAA8B;EAC5CC,SAAS,EAAE,2BAA2B;EACtCC,OAAO,EAAE,yBAAyB;EAClCC,OAAO,EAAE,yBAAyB;EAClCC,UAAU,EAAE,4BAA4B;EACxCC,KAAK,EAAE,uBAAuB;EAC9BC,SAAS,EAAE,2BAA2B;EACtCC,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,6BAA6B;EACrCC,SAAS,EAAE,gCAAgC;EAC3CC,MAAM,EAAE,6BAA6B;EACrCC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,UAAU,EAAE,oBAAoB;EAChCC,OAAO,EAAE,iBAAiB;EAC1BC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,oBAAoB;EAChCC,SAAS,EAAE,mBAAmB;EAC9BC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,eAAe;EACtBC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,KAAK,EAAE,eAAe;EACtBC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,YAAY,EAAE,sBAAsB;EACpCC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,eAAe;EACtBC,WAAW,EAAE,qBAAqB;EAClCC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,QAAQ,EAAE,kBAAkB;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,eAAe;EACtBC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAE,qBAAqB;EAChCC,MAAM,EAAE,kBAAkB;EAC1BC,MAAM,EAAE,kBAAkB;EAC1BC,SAAS,EAAE,qBAAqB;EAChCC,MAAM,EAAE,kBAAkB;EAC1BC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,oBAAoB;EAC9BC,MAAM,EAAE,kBAAkB;EAC1BC,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,iBAAiB;EACxBC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE,gBAAgB;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,OAAO,EAAE,gBAAgB;EACzBC,QAAQ,EAAE,iBAAiB;EAC3BC,OAAO,EAAE,gBAAgB;EACzBC,UAAU,EAAE,mBAAmB;EAC/BC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,YAAY,EAAE,qBAAqB;EACnCC,MAAM,EAAE,iBAAiB;EACzBC,OAAO,EAAE,kBAAkB;EAC3BC,OAAO,EAAE,kBAAkB;EAC3BC,UAAU,EAAE,qBAAqB;EACjCC,MAAM,EAAE,iBAAiB;EACzBC,OAAO,EAAE,kBAAkB;EAC3BC,KAAK,EAAE,gBAAgB;EACvBC,aAAa,EAAE,wBAAwB;EACvCC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,iBAAiB,GAAG,UAAU;EACpC,MAAMC,iBAAiB,GAAG,SAAS;EACnC,MAAMC,UAAU,GAAG;IACjBC,eAAe,EAAE,OAAOtW,UAAU,GAAG;IACrCuW,kBAAkB,EAAE,eAAe;IACnCC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAE;EACpB,CAAC;EAED,IAAIC,UAAU,GAAG,IAAI5W,QAAQ,CAAC,CAAC;EAC/B,MAAM6W,cAAc,GAAG5W,EAAE,CAAC,CAAC;EAI3B,MAAM6W,eAAe,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EACnE,MAAMC,WAAW,GAAGL,eAAe,CAACM,GAAG,CAAC,UAAU,CAAC;EACnD,MAAMC,mBAAmB,GAAGP,eAAe,CAACM,GAAG,CAAC,iBAAiB,CAAC;EAClE,MAAME,YAAY,GAAGR,eAAe,CAACM,GAAG,CAAC,WAAW,CAAC;EAErD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAG9X,QAAQ,CAAC;IACrD+X,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,CAAC;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlY,QAAQ,CACpDC,MAAM,CAAC,CAAC,CAACkY,EAAE,CAACN,eAAe,CAAC,OAAO,CAAC,CACtC,CAAC,CAAC,CAAC;EACH,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGrY,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACsY,OAAO,EAAEC,UAAU,CAAC,GAAGvY,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACwY,WAAW,EAAEC,cAAc,CAAC,GAAGzY,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0Y,MAAM,EAAEC,SAAS,CAAC,GAAG3Y,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4Y,YAAY,EAAEC,eAAe,CAAC,GAAG7Y,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8Y,YAAY,EAAEC,eAAe,CAAC,GAAG/Y,QAAQ,CAAC;IAC/C+X,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjZ,QAAQ,CAAC;IAC3C+X,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnZ,QAAQ,CAAC,CAC/C;IACE+X,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,YAAY;IACnBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,kBAAkB;IACzBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,kBAAkB;IACzBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,qBAAqB;IAC5BoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAKF,CAAC;EAGF,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtZ,QAAQ,CAAC,CACjD;IACE+X,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,YAAY;IACnBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,kBAAkB;IACzBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,kBAAkB;IACzBoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,qBAAqB;IAC5BoB,OAAO,EAAE;EACX,CAAC,EACD;IACErB,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAKF,CAAC;EAGF,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxZ,QAAQ,CAAC,CAC7C;IACE+X,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,CACF,CAAC;EAEF,MAAMyB,MAAM,GAAG;IACbC,QAAQ,EAAEhB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,GAAG;IACrBC,SAAS,EAAElB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEmB,GAAG;IACtBC,MAAM,EAAE,CACN,gBAAgB,EAChB,sBAAsB,EACtB,sBAAsB,EACtB,2BAA2B,EAC3B,eAAe,EACf,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,UAAU,CACX;IACDC,KAAK,EAAE,CAAC,cAAc,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC5CC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,CAACE,OAAO,CAAC,CAAC;EACrC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,GAAG,GAAGnD,MAAM,CAACC,QAAQ,CAACmD,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAIlB,MAAM,GAAG,IAAIpC,eAAe,CAAC,CAAC;IAElC,IAAIuD,WAAW,GAAGhC,YAAY,GAC1B,IAAI0B,IAAI,CAAC1B,YAAY,CAAC,CAACiC,WAAW,CAAC,CAAC,GACpC,IAAIP,IAAI,CAACrC,eAAe,CAAC,CAAC4C,WAAW,CAAC,CAAC;IAC3C,IAAIC,IAAI,GAAGlC,YAAY,GAAGE,YAAY,CAAC,OAAO,CAAC,GAAGjB,eAAe,CAAC,OAAO,CAAC;IAC1E,IAAIkD,SAAS,GAAG7B,YAAY,CAAC8B,GAAG,CAAEC,IAAI,IACpCA,IAAI,CAAC,OAAO,CAAC,CAACC,UAAU,CAAC,GAAG,EAAE,GAAG,CACnC,CAAC;IAEDzB,MAAM,CAAC0B,MAAM,CAAC,UAAU,EAAEf,mBAAmB,CAACQ,WAAW,CAAC,CAAC;IAC3DnB,MAAM,CAAC0B,MAAM,CAAC,iBAAiB,EAAEL,IAAI,CAAC;IACtCrB,MAAM,CAAC0B,MAAM,CAAC,WAAW,EAAEJ,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/CX,GAAG,IAAI,GAAG,GAAGhB,MAAM,CAAC4B,QAAQ,CAAC,GAAG,CAAC;IACjC,OAAOZ,GAAG;EACZ,CAAC;EAED,MAAMa,iCAAiC,GAAGA,CACxCC,SAAS,EACTC,SAAS,EACTC,KAAK,EACLC,KAAK,KACF;IACH;;IAEA,MAAMC,OAAO,GAAGF,KAAK,GAAGxb,MAAM,CAACkY,EAAE,CAACsD,KAAK,EAAEF,SAAS,CAAC,GAAGtb,MAAM,CAACkY,EAAE,CAACoD,SAAS,CAAC;IAC1E,MAAMK,OAAO,GAAGF,KAAK,GAAGzb,MAAM,CAACkY,EAAE,CAACuD,KAAK,EAAEF,SAAS,CAAC,GAAGvb,MAAM,CAACkY,EAAE,CAACqD,SAAS,CAAC;IAE1E,MAAMK,WAAW,GAAGD,OAAO,CAACE,IAAI,CAACH,OAAO,EAAE,OAAO,CAAC;IAElD,IAAII,MAAM,GAAG;MACXC,iBAAiB,EAAEH,WAAW;MAC9BI,SAAS,EAAEN,OAAO,CAACO,MAAM,CAAC,OAAO,CAAC;MAClCC,SAAS,EAAEP,OAAO,CAACM,MAAM,CAAC,OAAO;IACnC,CAAC;;IAED;IACA;;IAEA,OAAOH,MAAM,CAACC,iBAAiB;EACjC,CAAC;EAED,IAAII,mBAAmB,GAAG,EAAE;EAE5B,IAAI9E,MAAM,CAACC,QAAQ,CAAC8E,QAAQ,KAAK,mBAAmB,EAAE;IACpDD,mBAAmB,GAAG,0BAA0B;EAClD;EAEArc,SAAS,CAAC,MAAM;IACd,IAAI0X,WAAW,EAAE;MACf,IAAI6E,oBAAoB,GAAG,IAAIhC,IAAI,CAACiC,MAAM,CAAC9E,WAAW,CAAC,CAAC,CAACoD,WAAW,CAAC,CAAC;MACtEhC,eAAe,CAACyD,oBAAoB,CAAC;IACvC;IAEA,IAAI3E,mBAAmB,EAAE;MACvBG,kBAAkB,CAAC;QACjBC,KAAK,EAAEJ,mBAAmB;QAC1BK,KAAK,EAAEnX,SAAS,CAAC8W,mBAAmB;MACtC,CAAC,CAAC;MACFoB,eAAe,CAAC;QACdhB,KAAK,EAAEJ,mBAAmB;QAC1BK,KAAK,EAAEnX,SAAS,CAAC8W,mBAAmB;MACtC,CAAC,CAAC;IACJ;IAEA,IAAIC,YAAY,EAAE;MAChB,IAAImD,SAAS,GAAGnD,YAAY,CAAC+C,KAAK,CAAC,GAAG,CAAC;MACvC,IAAI6B,aAAa,GAAGzB,SAAS,CAACC,GAAG,CAAEC,IAAI,IAAK;QAC1C,OAAO;UAAElD,KAAK,EAAEkD,IAAI;UAAEjD,KAAK,EAAEnX,SAAS,CAACoa,IAAI;QAAE,CAAC;MAChD,CAAC,CAAC;MACF9B,eAAe,CAACqD,aAAa,CAAC;IAChC;EACF,CAAC,EAAE,CAAC/E,WAAW,EAAEE,mBAAmB,EAAEC,YAAY,CAAC,CAAC;;EAEpD;EACA7X,SAAS,CAAC,MAAM;IACd,MAAM0c,gBAAgB,GAAG,MAAAA,CAAOhC,GAAG,EAAEiC,OAAO,GAAG,CAAC,CAAC,EAAEC,OAAO,GAAG,KAAK,KAAK;MACrE,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAEL,OAAO,CAAC;MAE/D,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAACzC,GAAG,EAAE;UAChC,GAAGiC,OAAO;UACVS,MAAM,EAAEP,UAAU,CAACO;QACrB,CAAC,CAAC;QACFC,YAAY,CAACN,SAAS,CAAC;QACvB,OAAOG,QAAQ;MACjB,CAAC,CAAC,OAAO7E,KAAK,EAAE;QACdgF,YAAY,CAACN,SAAS,CAAC;QACvB,MAAM1E,KAAK;MACb;IACF,CAAC;IAED,MAAMiF,OAAO,GAAG,MAAAA,CAAOC,UAAU,GAAG,CAAC,KAAK;MACxC,IAAI;QACF,MAAML,QAAQ,GAAG,MAAMR,gBAAgB,CAAC,oBAAoB,EAAE;UAAEc,MAAM,EAAE;QAAM,CAAC,EAAE,IAAI,CAAC;QAEtF,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,GAAGR,QAAQ,CAACS,UAAU,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClCjF,SAAS,CAACgF,IAAI,CAAC;QACfE,wBAAwB,CAACF,IAAI,CAAC,OAAO,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACd0F,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAE3F,KAAK,CAAC4F,OAAO,CAAC;;QAEvD;QACA,IAAIV,UAAU,GAAG,CAAC,EAAE;UAClBQ,OAAO,CAACG,GAAG,CAAC,gCAAgCX,UAAU,GAAG,CAAC,IAAI,CAAC;UAC/DP,UAAU,CAAC,MAAMM,OAAO,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,IAAIA,UAAU,GAAG,CAAC,CAAC,CAAC;UAClE;QACF;;QAEA;QACAQ,OAAO,CAACG,GAAG,CAAC,8CAA8C,CAAC;QAC3D5F,QAAQ,CAAC,uDAAuD,CAAC;QACjEM,SAAS,CAAC;UAAEuF,KAAK,EAAE,UAAU;UAAEjE,QAAQ,EAAE;QAAM,CAAC,CAAC;QACjD1B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8E,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,wBAAwB,GAAG,MAAAA,CAAOM,EAAE,GAAG,EAAE,EAAEb,UAAU,GAAG,CAAC,KAAK;IAClE,IAAI;MACF,IAAIa,EAAE,IAAIA,EAAE,KAAK,UAAU,EAAE;QAC3B,MAAMvB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;QAE5D,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,oDAAoDiB,EAAE,EAAE,EACxD;YACEZ,MAAM,EAAE,KAAK;YACbJ,MAAM,EAAEP,UAAU,CAACO;UACrB,CACF,CAAC;UACDC,YAAY,CAACN,SAAS,CAAC;UAEvB,IAAI,CAACG,QAAQ,CAACO,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,GAAGR,QAAQ,CAACS,UAAU,CAAC;UACtE;UAEA,MAAMU,YAAY,GAAG,MAAMnB,QAAQ,CAACW,IAAI,CAAC,CAAC;UAE1C,IAAIQ,YAAY,EAAE;YAChBtG,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEuG;YAAa,CAAC,CAAC;YACxDlG,kBAAkB,CAACjY,MAAM,CAACme,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;UACtD;QACF,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBjB,YAAY,CAACN,SAAS,CAAC;UACvB,MAAMuB,UAAU;QAClB;MACF,CAAC,MAAM;QACL;QACAP,OAAO,CAACG,GAAG,CAAC,8BAA8B,CAAC;QAC3C/F,kBAAkB,CAACjY,MAAM,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOmY,KAAK,EAAE;MACd0F,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAE3F,KAAK,CAAC4F,OAAO,CAAC;;MAEzD;MACA,IAAIV,UAAU,GAAG,CAAC,IAAIa,EAAE,KAAK,UAAU,EAAE;QACvCL,OAAO,CAACG,GAAG,CAAC,kCAAkCX,UAAU,GAAG,CAAC,IAAI,CAAC;QACjEP,UAAU,CAAC,MAAMc,wBAAwB,CAACM,EAAE,EAAEb,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,IAAIA,UAAU,GAAG,CAAC,CAAC,CAAC;QACvF;MACF;;MAEA;MACAQ,OAAO,CAACG,GAAG,CAAC,cAAc,CAAC;MAC3BH,OAAO,CAACG,GAAG,CAAC,kDAAkD,CAAC;MAC/D/F,kBAAkB,CAACjY,MAAM,CAAC,CAAC,CAAC;MAC5BoY,QAAQ,CAAC,iDAAiD,CAAC;IAC7D,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDxY,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2Y,MAAM,EAAE;IAEb,MAAM4F,YAAY,GAAG,MAAAA,CAAOhB,UAAU,GAAG,CAAC,KAAK;MAC7C,IAAI;QACF,MAAM7C,GAAG,GAAG,wCAAwC;;QAEpD;QACA,MAAM8D,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAC3C3B,UAAU,CAAC,MAAM2B,MAAM,CAAC,IAAIjB,KAAK,CAAC,qBAAqB,CAAC,CAAC,EAAE,KAAK,CAClE,CAAC;QAED,MAAMkB,cAAc,GAAGze,eAAe,CAACua,GAAG,EAAEhB,MAAM,CAAC;QACnD,MAAMmF,SAAS,GAAG,MAAMJ,OAAO,CAACK,IAAI,CAAC,CAACF,cAAc,EAAEJ,cAAc,CAAC,CAAC;QAEtE,MAAMO,KAAK,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,IAAI,KAC9BC,KAAK,CAACC,IAAI,CACR;UAAEC,MAAM,EAAE,CAACJ,IAAI,GAAGD,KAAK,IAAIE;QAAK,CAAC,EACjC,CAACR,CAAC,EAAEY,CAAC,KAAKN,KAAK,GAAGM,CAAC,GAAGJ,IACxB,CAAC;QACH,MAAMhC,QAAQ,GAAG2B,SAAS,CAAC,CAAC,CAAC;QAE7B,MAAMU,gBAAgB,GAAGrC,QAAQ,CAACqC,gBAAgB,CAAC,CAAC;QACpD,MAAMxF,MAAM,GAAGmD,QAAQ,CAACnD,MAAM,CAAC,CAAC;QAChC,MAAMC,KAAK,GAAGkD,QAAQ,CAAClD,KAAK,CAAC,CAAC;QAE9B,MAAMwF,OAAO,GAAG;UACdzF,MAAM,EAAE;YACN0F,IAAI,EAAEV,KAAK,CACTvC,MAAM,CAACzC,MAAM,CAAC0F,IAAI,CAAC,CAAC,CAAC,EACrBjD,MAAM,CAACzC,MAAM,CAAC2F,OAAO,CAAC,CAAC,CAAC,EACxB3F,MAAM,CAAC4F,QAAQ,CAAC,CAClB,CAAC,CAAC1E,GAAG,CAAE2E,CAAC,IAAK,IAAIrF,IAAI,CAAC,CAACqF,CAAC,GAAGL,gBAAgB,IAAI,IAAI,CAAC,CAAC;YACrDM,aAAa,EAAE9F,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAChDC,kBAAkB,EAAEjG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACrDE,mBAAmB,EAAElG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtDG,wBAAwB,EAAEnG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC3DI,aAAa,EAAEpG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAChDK,IAAI,EAAErG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACvCM,UAAU,EAAEtG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC7CO,YAAY,EAAEvG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC/CQ,OAAO,EAAExG,MAAM,CAAC+F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAC3C,CAAC;UACD/F,KAAK,EAAE;YACLyF,IAAI,EAAEV,KAAK,CACTvC,MAAM,CAACxC,KAAK,CAACyF,IAAI,CAAC,CAAC,CAAC,EACpBjD,MAAM,CAACxC,KAAK,CAAC0F,OAAO,CAAC,CAAC,CAAC,EACvB1F,KAAK,CAAC2F,QAAQ,CAAC,CACjB,CAAC,CAAC1E,GAAG,CAAE2E,CAAC,IAAK,IAAIrF,IAAI,CAAC,CAACqF,CAAC,GAAGL,gBAAgB,IAAI,IAAI,CAAC,CAAC;YACrDiB,WAAW,EAAExG,KAAK,CAAC8F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAC7CU,OAAO,EAAEzG,KAAK,CAAC8F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACzCW,MAAM,EAAE1G,KAAK,CAAC8F,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UACzC;QACF,CAAC;QAEDrH,cAAc,CAAC8G,OAAO,CAAC;MACzB,CAAC,CAAC,OAAOmB,GAAG,EAAE;QACZ5C,OAAO,CAACC,IAAI,CAAC,+BAA+B,EAAE2C,GAAG,CAAC1C,OAAO,CAAC;;QAE1D;QACA,IAAIV,UAAU,GAAG,CAAC,EAAE;UAClBQ,OAAO,CAACG,GAAG,CAAC,qCAAqCX,UAAU,GAAG,CAAC,IAAI,CAAC;UACpEP,UAAU,CAAC,MAAMuB,YAAY,CAAChB,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,IAAIA,UAAU,GAAG,CAAC,CAAC,CAAC;UACvE;QACF;;QAEA;QACAQ,OAAO,CAACG,GAAG,CAAC,mDAAmD,CAAC;QAChExF,cAAc,CAAC;UACbqB,MAAM,EAAE;YAAE0F,IAAI,EAAE,EAAE;YAAEI,aAAa,EAAE,EAAE;YAAEG,kBAAkB,EAAE;UAAG,CAAC;UAC/DhG,KAAK,EAAE;YAAEyF,IAAI,EAAE,EAAE;YAAEe,WAAW,EAAE,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG;QAC9D,CAAC,CAAC;QACFpI,QAAQ,CAAC,8BAA8B,CAAC;MAC1C,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED+F,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC5F,MAAM,CAAC,CAAC;;EAEZ;EACA3Y,SAAS,CAAC,MAAM;IACd,MAAM4gB,eAAe,GAAGC,WAAW,CAAC,MAAM;MACxC;MACA,IAAI3I,eAAe,EAAE;QACnBC,kBAAkB,CAACjY,MAAM,CAAC,CAAC,CAAC;MAC9B;IACF,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEnB;IACA,MAAM4gB,mBAAmB,GAAGD,WAAW,CAAC,YAAY;MAClD,IAAI;QACF;QACA,MAAMhE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;QAE5D,MAAME,KAAK,CAAC,oBAAoB,EAAE;UAChCK,MAAM,EAAE,MAAM;UACdJ,MAAM,EAAEP,UAAU,CAACO;QACrB,CAAC,CAAC;QACFC,YAAY,CAACN,SAAS,CAAC;;QAEvB;QACA,IAAI1E,KAAK,EAAE;UACTC,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF,CAAC,CAAC,OAAOyI,WAAW,EAAE;QACpBhD,OAAO,CAACC,IAAI,CAAC,iCAAiC,EAAE+C,WAAW,CAAC9C,OAAO,CAAC;QACpE;MACF;IACF,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEpB;IACA,OAAO,MAAM;MACX+C,aAAa,CAACJ,eAAe,CAAC;MAC9BI,aAAa,CAACF,mBAAmB,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,CAAC5I,eAAe,EAAEG,KAAK,CAAC,CAAC;EAG5B,MAAM4I,eAAe,GAAGA,CAACC,eAAe,GAAG,EAAE,KAAK;IAChD,IAAIC,iBAAiB,GAAGpI,YAAY,CAACd,KAAK,IAAIH,eAAe,CAAC,OAAO,CAAC;IACtE,IAAIsJ,mBAAmB,GAAGlhB,MAAM,CAACkY,EAAE,CAACF,eAAe,EAAEiJ,iBAAiB,CAAC;IACvE,IAAIE,WAAW,GAAGD,mBAAmB,CAACE,KAAK,CAAC,CAAC,CAAClJ,EAAE,CAAC8I,eAAe,CAAC;IAEjE,IAAIhhB,MAAM,CAACmhB,WAAW,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;MACjC,OAAOF,WAAW;IACpB;IAEAD,mBAAmB,GAAGlhB,MAAM,CAACkY,EAAE,CAACF,eAAe,EAAEiJ,iBAAiB,CAAC;IACnEE,WAAW,GAAGD,mBAAmB,CAACE,KAAK,CAAC,CAAC,CAAClJ,EAAE,CAAC8I,eAAe,CAAC;IAE7D,IAAIhhB,MAAM,CAACmhB,WAAW,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;MACjC,OAAOF,WAAW;IACpB;IACA,OAAO,KAAK;EACd,CAAC;EAED,MAAMG,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI5D,IAAI,GAAG,EAAE;IACb6D,MAAM,CAACC,IAAI,CAAC5gB,SAAS,CAAC,CACnB6gB,IAAI,CAAC,CAAC,CACN1G,GAAG,CAAEjD,KAAK,IACT4F,IAAI,CAACgE,IAAI,CAAC;MACR5J,KAAK,EAAE,GAAGA,KAAK,CAACmD,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;MACtClD,KAAK,EAAEnX,SAAS,CAACkX,KAAK;IACxB,CAAC,CACH,CAAC;IAEH,OAAO4F,IAAI;EACb,CAAC;EAED,MAAMiE,aAAa,GAAIC,QAAQ,IAAK;IAClC,IAAIC,IAAI,GAAG,IAAIxH,IAAI,CAACuH,QAAQ,CAAC;IAC7B,IAAIE,UAAU,GAAG7K,UAAU,CAAC8K,QAAQ,CAACF,IAAI,CAAC,CAAC5F,MAAM,CAAC,kBAAkB,CAAC;IACrE,OAAO6F,UAAU;EACnB,CAAC;EAED,oBACErhB,OAAA;IACEuhB,SAAS,EACP,2CAA2C,GAAG7F,mBAC/C;IAAA8F,QAAA,gBAEDxhB,OAAA;MAAKuhB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAChExhB,OAAA;QAAKuhB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BxhB,OAAA;UAAIuhB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5hB,OAAA;MAAKuhB,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBAEtExhB,OAAA;QAAKuhB,SAAS,EAAC,4FAA4F;QAAAC,QAAA,eAC3GxhB,OAAA,CAACN,eAAe;UAAA+hB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,EAEL/I,WAAW,CAAC6F,MAAM,GAAG,CAAC,IACrB7F,WAAW,CAACyB,GAAG,CAAEC,IAAI,IAAK;QACxB;QACA,IAAIlD,KAAK,GAAGkD,IAAI,CAAC,OAAO,CAAC;QACzB,IAAIhB,QAAQ,GAAGgB,IAAI,CAAC,OAAO,CAAC;QAE5B,oBACEva,OAAA;UACEuhB,SAAS,EAAC,2FAA2F;UAAAC,QAAA,eAGrGxhB,OAAA;YAAKuhB,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/ExhB,OAAA;cAAGuhB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GACnC5G,iCAAiC,CAChCzD,eAAe,CAAC,OAAO,CAAC,EACxBoC,QAAQ,EACRhC,eAAe,CAACiE,MAAM,CAAC,KAAK,CAAC,EAC7B8E,eAAe,CAAC/G,QAAQ,CAAC,CAACiC,MAAM,CAAC,KAAK,CACxC,CAAC,EAAE,GAAG,eACNxb,OAAA;gBAAAwhB,QAAA,EAAO;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,MACpB,EAACtB,eAAe,CAAC/G,QAAQ,CAAC,CAACiC,MAAM,CAAC,GAAG,CAAC,EAAC,GACzC;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ5hB,OAAA;cAAIuhB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACnDnK,KAAK,CAACmD,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GACvBnD,KAAK,CAACmD,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAC1BnD;YAAK;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACL5hB,OAAA;cAAGuhB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAEnClB,eAAe,CAAC/G,QAAQ,CAAC,IACxB+G,eAAe,CAAC/G,QAAQ,CAAC,CAACiC,MAAM,CAAC,IAAI;YAAC;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACJ5hB,OAAA;cAAGuhB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAErClB,eAAe,CAAC/G,QAAQ,CAAC,IACxB+G,eAAe,CAAC/G,QAAQ,CAAC,CAACiC,MAAM,CAAC,eAAe;YAAC;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC,GA5BD,WAAW,GAAGrI,QAAQ;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BxB,CAAC;MAEV,CAAC,CAAC,eAEJ5hB,OAAA;QAAKuhB,SAAS,EAAC,kGAAkG;QAAAC,QAAA,EAC9GjK,eAAe,IAAIhY,MAAM,CAACgY,eAAe,CAAC,CAACqJ,OAAO,CAAC,CAAC,iBACnD5gB,OAAA;UAAKuhB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDxhB,OAAA;YAAIuhB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD5hB,OAAA;YAAIuhB,SAAS,EAAC;UAAoD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAMrE5hB,OAAA;YAAGuhB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,GACpCjiB,MAAM,CAAC2Y,YAAY,CAAC,CAAC0I,OAAO,CAAC,CAAC,iBAC7B5gB,OAAA,CAAAE,SAAA;cAAAshB,QAAA,gBACExhB,OAAA;gBAAAwhB,QAAA,EAAIjiB,MAAM,CAAC2Y,YAAY,CAAC,CAACsD,MAAM,CAACvF,iBAAiB;cAAC;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvD5hB,OAAA;gBAAAwhB,QAAA,EAAIN,aAAa,CAAC3J,eAAe;cAAC;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC5hB,OAAA;gBAAAwhB,QAAA,EAAI/K,cAAc,CAAC+E,MAAM,CAAC,UAAU,EAAE,IAAI;cAAC;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,eAChD,CACH,EACA,CAACriB,MAAM,CAAC2Y,YAAY,CAAC,CAAC0I,OAAO,CAAC,CAAC,IAC9BrhB,MAAM,CAACgY,eAAe,CAAC,CAACqJ,OAAO,CAAC,CAAC,iBAC/B5gB,OAAA,CAAAE,SAAA;cAAAshB,QAAA,gBACExhB,OAAA;gBAAAwhB,QAAA,EAAIjiB,MAAM,CAACgY,eAAe,CAAC,CAACiE,MAAM,CAACvF,iBAAiB;cAAC;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D5hB,OAAA;gBAAAwhB,QAAA,EAAIN,aAAa,CAAC3J,eAAe;cAAC;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC5hB,OAAA;gBAAAwhB,QAAA,EAAI/K,cAAc,CAAC+E,MAAM,CAAC,UAAU,EAAE,IAAI;cAAC;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,eAChD,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5hB,OAAA;MAAKuhB,SAAS,EAAC,wDAAwD;MAAAC,QAAA,eACrExhB,OAAA;QAAKuhB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCxhB,OAAA;UAAKuhB,SAAS,EAAC,yNAAyN;UAAAC,QAAA,eACtOxhB,OAAA;YAAKuhB,SAAS,EAAC,yDAAyD;YAAAC,QAAA,eACtExhB,OAAA,CAACP,MAAM;cACLoiB,iBAAiB,EAAE,KAAM;cACzBC,WAAW,EAAE;cACb;cAAA;cACAC,YAAY,EAAE,IAAK;cACnBC,UAAU,EAAE,KAAM;cAClBC,OAAO,EAAE,IAAK;cACdC,QAAQ,EAAE,KAAM;cAChBC,WAAW,EAAC,kBAAkB;cAC9BC,IAAI,EAAC,cAAc;cACnB9K,KAAK,EAAEkB,YAAY,CAACkG,MAAM,IAAI,CAAC,GAAEjG,eAAe,CAACE,aAAa,CAAC,GAAGH,YAAa;cAC/EwD,OAAO,EAAE6E,wBAAwB,CAAC,CAAE;cACpCwB,QAAQ,EAAG9H,IAAI,IAAK;gBAClB9B,eAAe,CAAC8B,IAAI,CAAC;cACvB;YAAE;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5hB,OAAA;MAAKuhB,SAAS,EAAC,mDAAmD;MAACe,KAAK,EAAEnM,UAAW;MAAAqL,QAAA,EAMlFhJ,YAAY,CAACkG,MAAM,GAAG,CAAC,IACtBlG,YAAY,CAAC8B,GAAG,CAAEC,IAAI,IAAK;QACzB;QACA,IAAIlD,KAAK,GAAGkD,IAAI,CAAC,OAAO,CAAC;QACzB,IAAIhB,QAAQ,GAAGgB,IAAI,CAAC,OAAO,CAAC;QAE5B,oBACEva,OAAA;UAEEuhB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eAElCxhB,OAAA;YAAKuhB,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FxhB,OAAA;cAAKuhB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BxhB,OAAA;gBAAGuhB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC1BnK,KAAK,CAACmD,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GACvBnD,KAAK,CAACmD,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAC1BnD;cAAK;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACJ5hB,OAAA;gBAAGuhB,SAAS,EAAC,aAAa;gBAAAC,QAAA,GACvBjI,QAAQ,EAAC,IACV,EAACha,MAAM,CAACgY,eAAe,CAAC,CAACE,EAAE,CAAC8B,QAAQ,CAAC,CAACiC,MAAM,CAAC,GAAG,CAAC,EAAC,GACpD;cAAA;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN5hB,OAAA;cAAKuhB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CxhB,OAAA;gBAAKuhB,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACrCjiB,MAAM,CAACgY,eAAe,CAAC,CACrBE,EAAE,CAAC8B,QAAQ,CAAC,CACZiC,MAAM,CAACtF,iBAAiB;cAAC;gBAAAuL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CAAC,eAEN5hB,OAAA;gBAAKuhB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCjiB,MAAM,CAACgY,eAAe,CAAC,CACrBE,EAAE,CAAC8B,QAAQ,CAAC,CACZiC,MAAM,CAACvF,iBAAiB;cAAC;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA9BD,WAAW,GAAGrI,QAAQ;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+BxB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5L,EAAA,CAtvBQD,SAAS;AAAAwM,EAAA,GAATxM,SAAS;AAwvBlB,eAAeA,SAAS;AAAC,IAAAwM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}