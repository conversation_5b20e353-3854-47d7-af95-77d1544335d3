<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;

class MemberOnbaordNotification extends Notification
{
    use Queueable;

    private $user;
    private $password;
    private $verificationUrl;

    public function __construct($user, $password)
    {
        $this->user = $user;
        $this->password = $password;

        // Generate the verification URL with the user ID and SHA1 hash of the user's email
        $this->verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(2400),
            ['id' => $this->user->id, 'hash' => sha1($this->user->email)]
        );
    }

    /**
     * Determine the channels the notification will be sent on.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Build the email message with the verification link
        return (new MailMessage)
            ->subject('Welcome to the SEBPO Creative Team – Let’s Get Started!')
            ->greeting('Hello! 👋')
            ->line('Welcome to the SEBPO Creative Team – we’re excited to have you on board!')
            ->line('Your account has been successfully created, and you’re all set to access the Creative App. Please find your login details below:')
            ->line('🔗 Login URL: https://creativeapp.sebpo.net/banner/test/frontend')
            ->line('🆔 Your EID: ' . $this->user->eid)
            ->line('🔑 Temporary Password: ' . $this->password)
            ->line('⚠️ Important: Please change your password after your first login by visiting your profile settings.')
            // ->line('To activate your account and verify your email address, please click the button below:')
            // ->action('Verify Your Email', $this->verificationUrl)
            ->line('Thank you for joining the SEBPO Creative Team. We look forward to collaborating with you and creating amazing work together! 🤝')
            ->line('If you\'re experiencing any issues with your account or have questions, please feel free to reach out to your team lead or buddy.')
            ->line('Warm regards,')
            ->line('The SEBPO Creative Team')
            ->salutation('This email and any accompanying attachments are intended only to be read or used by the named addressee(s). It is confidential and contains legally privileged information. If you have received this message in error, please notify the sender immediately and delete the message.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'eid' => $this->user->eid,
            'email' => $this->user->email,
        ];
    }
}
