import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditQuickAccessHub from './EditQuickAccessHub';
import TablePagination from '../../common/table/TablePagination'; // Optional if you want pagination

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const QuickAccessHubList = ({ searchTerm }) => {
    const [hubs, setHubs] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedHubId, setSelectedHubId] = useState(null);
    const [filteredHubs, setFilteredHubs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "Hub Icon", key: "hubs_icon" },
        { label: "Hub Image", key: "hubs_image" },
        { label: "Hub Title", key: "hubs_title" },
        { label: "Hub Details", key: "hubs_details" },
        { label: "Hub URL", key: "hubs_url" },
        { label: "Call to Action", key: "hubs_cta" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchHubs = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/quick-access-hubs`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                const mappedHubs = data.hubs.map(hub => ({
                    id: hub.id,
                    hubs_icon: hub.hubs_icon,
                    hubs_image: hub.hubs_image,
                    hubs_title: hub.hubs_title,
                    hubs_details: hub.hubs_details,
                    hubs_url: hub.hubs_url,
                    hubs_cta: hub.hubs_cta,
                    created_by: hub.created_by,
                    updated_by: hub.updated_by,
                }));

                setHubs(mappedHubs);
                setFilteredHubs(mappedHubs);
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchHubs();
    }, []);

    useEffect(() => {
        const normalizedSearchTerm = (searchTerm || '').toLowerCase().trim();

        if (!normalizedSearchTerm) {
            setFilteredHubs(hubs);
            return;
        }

        const filtered = hubs.filter(hub => {
            return Object.values(hub).some(value =>
                value && value.toString().toLowerCase().includes(normalizedSearchTerm)
            );
        });

        setFilteredHubs(filtered);
    }, [searchTerm, hubs]);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/quick-access-hubs/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete hub: ' + response.statusText);
            }

            setHubs(prevHubs => prevHubs.filter(hub => hub.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedHubId(id);
        setModalVisible(true); // Set modal visibility
    };

    const renderCustomCell = (column, hub) => {
        if (column.key === 'hubs_icon' && hub.hubs_icon) {
            const iconSrc = hub.hubs_icon.startsWith('images/')
                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${hub.hubs_icon}`
                : hub.hubs_icon;
            return <img src={iconSrc} alt="Hub Icon" className="w-6 object-cover" />;
        }
        if (column.key === 'hubs_image' && hub.hubs_image) {
            const imageSrc = hub.hubs_image.startsWith('images/')
                ? `${process.env.REACT_APP_BASE_STORAGE_URL}/${hub.hubs_image}`
                : hub.hubs_image;
            return <img src={imageSrc} alt="Hub Image" className="w-32 object-cover" />;
        }
        if (column.key === 'hubs_url' && hub.hubs_url) {
            try {
                const url = new URL(hub.hubs_url);
                return (
                    <a href={hub.hubs_url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                        {hub.hubs_title + ' link'}
                    </a>
                );
            } catch (e) {
                return <span className="text-red-500">Invalid URL</span>;
            }
        }

        return hub[column.key];
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={filteredHubs}
                columnNames={columnNames}
                onEdit={handleEdit} // Ensure the handleEdit function is passed down
                onDelete={handleDelete}
                renderCustomCell={renderCustomCell}
                setSelectedServiceId={setSelectedHubId}
                setModalVisible={setModalVisible} // Pass the setModalVisible function down
            />
            {modalVisible && (
                <EditQuickAccessHub
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    hubId={selectedHubId}
                />
            )}
        </div>
    );
};

export default QuickAccessHubList;
