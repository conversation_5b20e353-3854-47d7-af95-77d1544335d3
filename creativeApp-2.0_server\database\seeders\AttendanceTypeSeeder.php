<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\FormationType;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttendanceTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // FormationType::factory(100)->create();

        // {
        //     P: 'Present',
        //     NSP: 'Night Shift Present',
        //     SL: 'Sick Leave',
        //     W: 'Weekend',
        //     CL: 'Casual Leave',
        //     A: 'Absent',
        //     AL: 'Anual Leave',
        //     H: 'Holiday',
        //     LWP: 'Leave Without Pay',
        //     OT: '#9FC5E8',
        //     ADL: '#EA9999',
        //     OTN: '#7373FF',
        //   }

       
        // $attendanceTypes = [
        //     ['title' => 'Present', 'slug' => 'present', 'short_title' => 'P', 'details' => 'Employee is present at work.', 'is_active' => active, 'type' => 'attendance'],
        //     ['title' => 'Absent', 'slug' => 'absent', 'short_title' => 'A', 'details' => 'Employee is absent from work.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Late', 'slug' => 'late', 'short_title' => 'L', 'details' => 'Employee arrived late to work.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Early Leave', 'slug' => 'early-leave', 'short_title' => 'EL', 'details' => 'Employee left work early.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Sick Leave', 'slug' => 'sick-leave', 'short_title' => 'SL', 'details' => 'Employee is absent due to illness.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Vacation', 'slug' => 'vacation', 'short_title' => 'V', 'details' => 'Employee is on vacation.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Business Trip', 'slug' => 'business-trip', 'short_title' => 'BT', 'details' => 'Employee is on a business trip.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Holiday', 'slug' => 'holiday', 'short_title' => 'H', 'details' => 'Employee is absent due to a holiday.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Training', 'slug' => 'training', 'short_title' => 'T', 'details' => 'Employee is attending training.', 'is_active' => active , 'type' => 'attendance'],
        //     ['title' => 'Leave Without Pay', 'slug' => 'leave-without-pay', 'short_title' => 'LWOP', 'details' => 'Employee is on leave without pay.', 'is_active' => active , 'type' => 'attendance'],
        // ];

        foreach ($attendanceTypes as $attendanceType) {
            FormationType::create($attendanceType);
        }
           
    }
}