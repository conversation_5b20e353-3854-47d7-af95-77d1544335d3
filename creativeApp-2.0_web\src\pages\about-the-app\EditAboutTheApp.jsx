import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from "./EditorToolbar";
import 'react-quill/dist/quill.snow.css';

import {API_URL} from './../../common/fetchData/apiConfig.js';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};


const EditAboutTheApp = ({ isVisible, setVisible, aboutTheAppId }) => {
    const [definition, setDefinition] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const fetchAboutTheApp = async () => {
            if (!isTokenValid()) {
                setError('Authentication token is missing.');
                return;
            }

            if (!aboutTheAppId) {
                setError('Invalid About The App ID.');
                return;
            }

            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`${API_URL}about-the-app/${aboutTheAppId}`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch About The App data.');
                }

                const data = await response.json();
                const aboutTheApp = data.aboutTheApp || {};
                setDefinition(aboutTheApp.definition || '');
            } catch (error) {
                setError(error.message);
            }
        };

        if (isVisible) {
            fetchAboutTheApp();
        }
    }, [aboutTheAppId, isVisible]);

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!definition.trim()) {
            setError('Please fill all fields.');
            return;
        }

        setIsLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}about-the-app/${aboutTheAppId}`, {
                method: 'PUT',
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ definition }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to update About The App.');
            }

            setSuccessMessage('About The App updated successfully!');
        } catch (error) {
            setError(error.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        setDefinition('');
        setError('');
        setSuccessMessage('');
        setVisible(false);
    };

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg h-[80vh] overflow-y-auto relative">
        <h4 className="text-xl font-semibold mb-4 py-4">Edit About The App</h4>
        
        <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-900 text-2xl font-bold"
            aria-label="Close"
        >
            &times;
        </button>
        
        <form onSubmit={handleSubmit}>
            <div className="my-4">
                <EditorToolbar />
                <ReactQuill
                    id="definition"
                    value={definition}
                    onChange={setDefinition}
                    className="bg-white border border-gray-300 rounded-md shadow-sm"
                    theme="snow"
                    modules={modules}
                    formats={formats}
                />
            </div>

            <div className="py-4">
                <button
                    type="submit"
                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                    disabled={isLoading}
                >
                    {isLoading ? 'Updating...' : 'Update About The App'}
                </button>
            </div>

            {error && <p className="text-red-500 text-sm">{error}</p>}
            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
        </form>
    </div>
</div>

    );
};

export default EditAboutTheApp;
