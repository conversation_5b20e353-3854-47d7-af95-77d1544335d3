import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditRevisionType from './EditRevisionType';

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const RevisionTypeList = () => {
    const [revisionTypes, setRevisionTypes] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRevisionTypeId, setSelectedRevisionTypeId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Product Type Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchRevisionTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/revision-types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setRevisionTypes(data.revisionTypes.map(revisionType => ({
                    id: revisionType.id,
                    department: revisionType.department,
                    team: revisionType.team,
                    name: revisionType.name,
                    created_by: revisionType.created_by,
                    updated_by: revisionType.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchRevisionTypes();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/revision-type/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete revision type: ' + response.statusText);
            }

            // Update the revision types list after deletion
            setRevisionTypes(prevRevisionTypes => prevRevisionTypes.filter(revisionType => revisionType.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedRevisionTypeId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={revisionTypes}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedRevisionTypeId}
            />
            {modalVisible && (
                <EditRevisionType
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    revisionTypeId={selectedRevisionTypeId}
                />
            )}
        </div>
    );
};

export default RevisionTypeList;
