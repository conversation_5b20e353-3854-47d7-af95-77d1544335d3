<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Report_problem;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ReportProblemController extends Controller
{
    /**
     * Display a listing of all report problem records.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $reports = Report_problem::all();

        Log::info('All report problem entries retrieved', ['entries_count' => $reports->count()]);

        return response()->json(['reports' => $reports], 200);
    }

    /**
     * Display the specified report problem entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $report = Report_problem::find($id);

        if (!$report) {
            return response()->json(['error' => 'Report problem entry not found.'], 404);
        }

        Log::info('Report problem entry retrieved', ['entry' => $report]);

        return response()->json(['report' => $report], 200);
    }

    /**
     * Create a new report problem entry.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $authUser = $request->user();

        Log::info('Create report problem request received', ['request' => $request->all(), 'user' => $authUser->id]);

        $request->validate([
            'subject' => 'required|string',
            'message' => 'required|string',
            'status' => 'required|string',
        ]);

        $report = Report_problem::create([
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => $request->status,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('Report problem entry created', ['entry' => $report]);

        return response()->json([
            'message' => 'Report problem submitted successfully.',
            'report' => $report
        ], 201);
    }

    /**
     * Update an existing report problem entry.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        Log::info('Update report problem entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        $request->validate([
            'subject' => 'required|string',
            'message' => 'required|string',
            'status' => 'required|string',
        ]);

        $report = Report_problem::find($id);

        if (!$report) {
            return response()->json(['error' => 'Report problem entry not found.'], 404);
        }

        $report->update([
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => $request->status,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,
        ]);

        Log::info('Report problem entry updated', ['entry' => $report]);

        return response()->json([
            'message' => 'Report problem updated successfully.',
            'report' => $report
        ], 200);
    }

    /**
     * Delete a report problem entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $report = Report_problem::findOrFail($id);
            $report->delete();

            Log::info('Report problem entry deleted', ['entry_id' => $id]);

            return response()->json(['message' => 'Report problem entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this report problem entry.'], 403);
    }
}
