<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class CustomResetPasswordNotification extends Notification
{
    use Queueable;

    protected $resetUrl;

    public function __construct($resetUrl)
    {
        $this->resetUrl = $resetUrl;
    }

    public function via($notifiable)
    {
        return ['mail'];  // Send via email
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->subject('Password Reset Request')
                    ->line('We received a request to reset your password.')
                    ->action('Reset Password', $this->resetUrl)  // Make sure this points to the React route
                    ->line('If you did not request a password reset, no further action is required.');
    }

    public function toArray($notifiable)
    {
        return [
            'reset_url' => $this->resetUrl,
        ];
    }
}
