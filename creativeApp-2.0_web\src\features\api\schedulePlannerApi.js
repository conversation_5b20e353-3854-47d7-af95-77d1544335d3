import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';

export const schedulePlannerApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSchedulePlanners: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `schedule-planners?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        return queryString;
      },
      providesTags: ['SchedulePlanners'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForFilterBySchedulePlanners: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `schedule-planners-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['SchedulePlanners'],
    }),

    getSchedulePlannerById: builder.query({
      query: (id) => {
        if (id == null || id == undefined) {
          id = "";
        }
        return `schedule-planners/${id}`;
      },
      providesTags: (result, error, id) => [{ type: 'SchedulePlanners', id }],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createSchedulePlanner: builder.mutation({
      query: (newSchedulePlanner) => ({
        url: 'schedule-planners',
        method: 'POST',
        body: newSchedulePlanner,
      }),
      invalidatesTags: ['SchedulePlanners'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateSchedulePlanner: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `schedule-planners/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'SchedulePlanners', id }, 'SchedulePlanners'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteSchedulePlanner: builder.mutation({
      query: (id) => ({
        url: `schedule-planners/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['SchedulePlanners'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetSchedulePlannersQuery,
  useLazyFetchDataOptionsForFilterBySchedulePlannersQuery,
  useGetSchedulePlannerByIdQuery,
  useLazyGetSchedulePlannerByIdQuery,
  useCreateSchedulePlannerMutation,
  useUpdateSchedulePlannerMutation,
  useDeleteSchedulePlannerMutation,
} = schedulePlannerApi;
