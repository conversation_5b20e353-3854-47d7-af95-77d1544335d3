import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditBlood from './EditBlood';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const BloodList = () => {
    const [bloods, setBloods] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true); // Initially loading is true
    const [selectedBloodId, setSelectedBloodId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for Blood group
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Blood Group", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchBloods = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/bloods`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setBloods(data.bloods.map(blood => ({
                    id: blood.id,
                    name: blood.name,
                    created_by: blood.created_by,
                    updated_by: blood.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchBloods();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/bloods/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete blood group: ' + response.statusText);
            }

            // Update the blood group list after deletion
            setBloods(prevBloods => prevBloods.filter(blood => blood.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedBloodId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Show message when no bloods are available
    if (bloods.length === 0) {
        return <div className="text-gray-500">No data available</div>; // Show "No data available" if bloods array is empty
    }

    return (
        <div>
            <TableContent
                tableContent={bloods}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedBloodId}
            />
            {modalVisible && (
                <EditBlood
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    bloodId={selectedBloodId}
                />
            )}
        </div>
    );
};

export default BloodList;
