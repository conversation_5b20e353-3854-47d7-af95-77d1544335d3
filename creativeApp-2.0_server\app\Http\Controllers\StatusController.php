<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Status;
use Illuminate\Support\Facades\Log;

class StatusController extends Controller
{

    /**
     * Display a listing of all status.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $statuses = Status::all();

        // Log the status retrieved
        Log::info('All status Retrieved:', ['status_count' => $statuses->count()]);

        return response()->json(['status' => $statuses], 200);
    }

    /**
     * Display the specified status.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the status by ID
        $status = Status::find($id);

        if (!$status) {
            return response()->json(['error' => 'status not found.'], 404);
        }

        // Log the status retrieved
        Log::info('status Retrieved:', ['status' => $status]);

        return response()->json(['status' => $status], 200);
    }

    /**
     * Create a new status.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
        ]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'bg_cls' => 'required|string|max:255',
            'text_cls' => 'required|string|max:255',
            
        ]);

        // Log the request data
        Log::info('Create status Request:', ['request' => $request->all()]);

        // Check if the status name already exists
        if (Status::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'status already exists.'], 409);
        }

        // Create a new status
        $status = Status::create([
            'name' => $request->name,
            'bg_cls' => $request->bg_cls,
            'text_cls' => $request->text_cls,
            'creator_id' => $authUser->id,
        ]);

        Log::info('status Created:', ['status' => $status]);

        return response()->json(['message' => 'status created successfully.', 'status' => $status], 201);
    }

    /**
     * Update an existing status.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
        ]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Log the request data
        Log::info('Update status Request:', ['request' => $request->all()]);

        // Find the status by ID
        $status = Status::find($id);

        if (!$status) {
            return response()->json(['error' => 'status not found.'], 404);
        }

        // Check if the status name is being updated and does not already exist
        if ($status->name !== $request->name && Status::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'status name already exists.'], 409);
        }

        // Update the status
        $status->update([
            'name' => $request->name,            
            'bg_cls' => $request->bg_cls,
            'text_cls' => $request->text_cls,
            'updater_id' => $authUser->id
        ]);

        // Log the updated status
        Log::info('status Updated:', ['status' => $status]);

        return response()->json(['message' => 'status updated successfully.', 'status' => $status], 200);
    }

    /**
     * Delete a status.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the status
            $status = Status::findOrFail($id);

            // Delete the status
            $status->delete();

            Log::info('Status Deleted:', ['status_id' => $id]);

            return response()->json(['message' => 'Status deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete the Status.'], 403);
    }
}
