import React from 'react'
import { flattenObject } from "../../utils";

export const FormError = ({error=null, field=null}) => {
    let errorList = null;

    if(field && error && error.errors && error.errors[field]){
      errorList = flattenObject(error.errors[field]);
      
    }
    
    // else if(error.errors) {
    //   errorList = flattenObject(error.errors);
    // }

  return (error && error.status && (<>
    {field === null && error.message && (<div className="border p-2 text-center border-red-600 text-red-600 m-5 rounded-xl " >{error.message}</div>)}

      <ul className='ms-1 my-2 text-left text-red-600 '>
        {errorList && Object.values(errorList).map( (item, key) => {
          return <li key={"error-"+key}>{item}</li>
        }) }
      </ul>
    </>))
}