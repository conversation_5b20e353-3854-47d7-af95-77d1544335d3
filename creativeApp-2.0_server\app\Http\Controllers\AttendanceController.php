<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\User;
use App\Models\SchedulePlanner;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Carbon\Carbon;
use App\Helpers\DateTimeHelper;
use App\Helpers\RoleHelper;

class AttendanceController extends Controller
{
    /**
     * Get all attendance entries with filtering, searching, sorting, and pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Attendance::with(['creator','updater', 'user', 'team', 'department', 'schedule_planner','schedule_planner.schedule', 'user.designations']);

        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedSchedulePlannerIds = $request->filled('schedule_planner_id') ? urldecode($request->input('schedule_planner_id')) : null;
        $decodedUserId = $request->filled('user_id') ? urldecode($request->input('user_id')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedType = $request->filled('entry_type') ? urldecode($request->input('entry_type')) : null;
        
       

        // Filtering
        if ($decodedSchedulePlannerIds) {
            $decodedSchedulePlannerIds = explode(',', $decodedSchedulePlannerIds);
            $query->where(function ($q) use ($decodedSchedulePlannerIds) {
                foreach ($decodedSchedulePlannerIds as $decodedSchedulePlannerId) {
                    $q->orWhere('schedule_planner_id', '=', trim($decodedSchedulePlannerId));
                }
            });
        }
        
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }

        
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }

        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        if ($decodedType) {
            $types = explode(',', $decodedType);
            $query->where(function ($q) use ($types) {
                foreach ($types as $type) {
                    $q->orWhere('entry_type', '=', trim($type));
                }
            });
        }

        if ($decodedUserId) {
            $decodedUserIds = explode(',', $decodedUserId);
            $query->where(function ($q) use ($decodedUserIds) {
                foreach ($decodedUserIds as $decodedUserId) {
                    $q->orWhere('user_id', '=', trim($decodedUserId));
                }
            });
        }

        $authUser = $request->user();

        // \Log::info('Authenticated User:', ['user' => $authUser]);
        // \Log::info('Roles:', ['roles' => $authUser->roles]);

        if (!($authUser->roles()->whereIn('name', RoleHelper::getRoleList('hod'))->exists())) {
            $query->where('user_id', $authUser->id);
        } 

        // return response()->json($authUser, 200);
       
        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->whereBetween('date', [$request->date_from, $request->date_to]);
        }

        // Full-text search
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('title', 'like', '%' . $globalSearch . '%')
                  ->orWhere('date', 'like', '%' . $globalSearch . '%')
                  ->orWhere('start', 'like', '%' . $globalSearch . '%')
                  ->orWhere('end', 'like', '%' . $globalSearch . '%')
                  ->orWhere('duration', 'like', '%' . $globalSearch . '%')
                  ->orWhere('details', 'like', '%' . $globalSearch . '%')
                  ->orWhere('approval_status', 'like', '%' . $globalSearch . '%')
                  ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('schedule_planner', function ($query) use ($globalSearch) {
                        $query->where('weeknum', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

                //   ->orWhere('updated_by', 'like', '%' . $globalSearch . '%')
                //   ->orWhere('created_by', 'like', '%' . $globalSearch . '%');
            });
        }

        // Sorting & Ordering
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);

        // Pagination
        $perPage = $request->input('per_page', 10);
        $entries = $query->paginate($perPage);

        return response()->json($entries);
    }

    /**
     * Get a single attendance entry by ID.
     */
    public function show($id): JsonResponse
    {
        $entry = Attendance::find($id);

        if (!$entry) {
            return response()->json(['message' => 'Entry not found'], 404);
        }

        return response()->json($entry);
    }

    /**
     * Get a single attendance entry by ID.
     */
    public function attendanceByUserId(Request $request): JsonResponse
    {

        $currentYear = now()->year;
        $currentWeek = now()->weekOfYear;


        $user_id = $request->filled('user_id') ? $request->query('user_id') : Auth::id(); // Get authenticated user ID if not provided
        $date = $request->filled('date') ? $request->query('date') : now()->toDateString(); // Set date to today if not provided
        $entry_type = $request->filled('entry_type') ? $request->query('entry_type') : 'attendance'; // Set date to today if not provided
        $weeknum = $request->filled('weeknum') ? $request->query('weeknum') : $currentWeek."/".$currentYear; // Set date to today if not provided

        // $userDetails = User::find($user_id);

        // return response()->json($date);

        $attendance = Attendance::where('user_id', $user_id)
                    ->where('entry_type', $entry_type)
                    ->where('end', null)
                    // ->whereDate('date', $date)
                    ->first();
                    

        $schedulePlanner = SchedulePlanner::with(['schedule'])->whereRaw("FIND_IN_SET(?, user_id)", [$user_id])
                ->where('weeknum', $weeknum)
                ->first();

        $responseData = [ 
            'attendance'=> $attendance, 
            'schedulePlanner' => $schedulePlanner, 
            // 'userDetails' => $userDetails 
        ];

        return response()->json($responseData);
    }

    /**
     * Store a new attendance entry.
     */
    public function store(Request $request): JsonResponse
    {
        $user_id = $request->filled('user_id') ? $request->input('user_id') : Auth::id(); // Get authenticated user ID if not provided
        $start = $request->filled('start') ? $request->input('start') : Carbon::now()->format('Y-m-d H:i:s'); // Get authenticated user ID if not provided
        $end = $request->filled('end') ? $request->input('end') : null; // Get authenticated user ID if not provided


        $validated = $request->validate([
            // 'department_id' => 'required|integer|exists:departments,id',
            // 'team_id' => 'required|integer|exists:teams,id',
            // 'schedule_planner_id' => 'required|integer|exists:schedule_planner,id',
            // 'user_id' => 'required|integer|exists:users,id',
            // 'date' => 'required|date',
            'start' => Rule::requiredIf(in_array($request->entry_type, ['break', 'late_entry'])).'|nullable|date_format:Y-m-d H:i:s',
            'end' => Rule::requiredIf($request->entry_type == 'early_leave').'nullable|date_format:Y-m-d H:i:s|after_or_equal:start',
            'duration' => 'nullable|date_format:Y-m-d H:i:s',
            'entry_type' => 'required|in:late_entry,early_leave,break,attendance',
            'details' => 'nullable|string',
            'approval_status' => 'nullable|in:pending,approved,rejected',
            'approval_notes' => 'nullable|string',
        ]);

        $now = Carbon::now()->format('Y-m-d H:i:s');
        $date = Carbon::parse($start);
        $weekNumber = $date->weekOfYear; // Get week number
        $year = $date->year; // Get year
        $weeknum = $weekNumber . '/' . $year; // Combine week number and year

        $user_id = $request->filled('user_id') ? $request->input('user_id') : Auth::id(); // Get authenticated user ID if not provided
        
        $insertUsers = explode(",",$user_id);
        $scheduleData = SchedulePlanner::where('weeknum', $weeknum)->where(function ($query) use ($insertUsers, $request) {                    
            foreach ($insertUsers as $userId) {
                $query->orWhereRaw("FIND_IN_SET(?, user_id)", [$userId]);
            }
        })->first();

        if($scheduleData == null){
            return response()->json([
                 "status"=> "error",
                 'message' => 'We do not found any schedule for selected users in this week.',
                ], 404);
        }

        if($validated['entry_type'] == 'attendance'){
            $duration = $date->diff($now)->format('%H:%I:%S');
        }else{
            $endDateTime = Carbon::parse($end)?? $now;
            $duration = $date->diff($endDateTime)->format('%H:%I:%S');
        }

        if($end){
            $validated['end'] = $end;
        }

        if($duration != null){
            $validated['duration'] = $duration;
        }

         // Merge schedule planner details into validated data
        $validated['date'] = $date->format('Y-m-d');
        $validated['start'] = $date->format('Y-m-d H:i:s');
        $validated['schedule_planner_id'] = $scheduleData->id;
        $validated['department_id'] = $scheduleData->department_id;
        $validated['team_id'] = $scheduleData->team_id;
        $validated['user_id'] = $user_id;
        $validated['created_by'] = Auth::id();
        

        $entry = Attendance::create($validated);

        return response()->json(['message' => 'Entry created successfully', 'entry' => $entry], 201);
    }

    /**,     * Update an existing attendance entry.
     */
    public function update(Request $request, $id): JsonResponse
    {

        $entry = Attendance::find($id);

        if (!$entry) {
            return response()->json(['message' => 'Entry not found'], 404);
        }

        $user_id = $request->filled('user_id') ? $request->input('user_id') : Auth::id(); // Get authenticated user ID if not provided
        $start = $request->filled('start') ? $request->input('start') : $entry['start']; // Get authenticated user ID if not provided
        $end = $request->filled('end') ? $request->input('end') : Carbon::parse($entry['end']);; // Get authenticated user ID if not provided

        $validated = $request->validate([
            // 'department_id' => 'sometimes|integer|exists:departments,id',
            // 'team_id' => 'sometimes|integer|exists:teams,id',
            // 'user_id' => 'sometimes|integer|exists:users,id',
            // 'schedule_planner_id' => 'sometimes|integer|exists:schedule_planner,id',
            // 'date' => 'sometimes|date',
            // 'start' => 'nullable|date_format:Y-m-d H:i:s',
            // 'end' => 'nullable|date_format:Y-m-d H:i:s|after_or_equal:start',
            'start' => Rule::requiredIf(in_array($request->entry_type, ['break', 'late_entry'])).'|nullable|date_format:Y-m-d H:i:s',
            'end' => Rule::requiredIf($request->entry_type == 'early_leave').'nullable|date_format:Y-m-d H:i:s|after_or_equal:start',
            'duration' => 'nullable|date_format:H:i:s',
            'entry_type' => 'sometimes|in:late_entry,early_leave,break,attendance',
            'details' => 'nullable|string',
            'approval_status' => 'nullable|in:pending,approved,rejected',
            'approval_notes' => 'nullable|string',
        ]);

        $now = Carbon::now()->format('Y-m-d H:i:s');
        $date = Carbon::parse($start);
        $weekNumber = $date->weekOfYear; // Get week number
        $year = $date->year; // Get year
        $weeknum = $weekNumber . '/' . $year; // Combine week number and year

        $insertUsers = explode(",",$user_id);
        $scheduleData = SchedulePlanner::where('weeknum', $weeknum)->where(function ($query) use ($insertUsers, $request) {                    
            foreach ($insertUsers as $userId) {
                $query->orWhereRaw("FIND_IN_SET(?, user_id)", [$userId]);
            }
        })->first();

        if($scheduleData == null){
            return response()->json([
                 "status"=> "error",
                 'message' => 'We do not found any schedule for selected users in this week.',
                ], 404);
        }

        if($entry['entry_type'] == 'attendance'){
            $endDateTime = Carbon::parse($end)?? $now;
            $duration = $date->diffInSeconds($endDateTime);
          
        }else{
            $endDateTime = Carbon::parse($end)?? $now;
            $duration = $date->diffInSeconds($endDateTime);
        }

        if($end){
            $validated['end'] = $end;
        }

        if($duration != null){
            $diffInSeconds = $duration;
            $hours = floor($diffInSeconds / 3600);
            $minutes = floor(($diffInSeconds % 3600) / 60);
            $seconds = $diffInSeconds % 60;

            $duration = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);

            $validated['duration'] = $duration;
        }

        $validated['date'] = $date->format('Y-m-d');
        $validated['schedule_planner_id'] = $scheduleData->id;
        $validated['department_id'] = $scheduleData->department_id;
        $validated['team_id'] = $scheduleData->team_id;
        $validated['user_id'] = $user_id;
        $validated['updated_by'] = Auth::id();
        

        // return response()->json([$validated, $entry], 400);

        $entry->update($validated);

        return response()->json(['message' => 'Entry updated successfully', 'entry' => $entry]);
    }

    /**
     * Delete an attendance entry.
     */
    public function destroy($id): JsonResponse
    {
        $entry = Attendance::find($id);

        if (!$entry) {
            return response()->json(['message' => 'Entry not found'], 404);
        }

        $entry->delete();

        return response()->json(['message' => 'Entry deleted successfully']);
    }

    public function group2(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Attendance::with(['creator','updater', 'user', 'team', 'department','schedule_planner','schedule_planner.schedule', 'user.designations']);

        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);


            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];
            

            // $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
            //     $query->where($fieldName, 'like', '%' . $text . '%');
            // });
        }
        
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));

        
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Attendance::with(['creator','updater', 'team', 'department', 'user', 'schedule_planner','schedule_planner.schedule', 'user.designations']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }
        
        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = Attendance::with(['creator','updater', 'team', 'department', 'user', 'schedule_planner','schedule_planner.schedule', 'user.designations']);

        

        
        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        }else{
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    public function getUserAttendanceByDate(Request $request)
    {
        $user_id = $request->filled('user_id') ? $request->query('user_id') : Auth::id(); // Get authenticated user ID if not provided
        $date = $request->filled('date') ? $request->query('date') : now()->toDateString(); // Set date to today if not provided
        // $weeknum = $request->filled('weeknum') ? $request->query('weeknum') : now()->weekOfYear."/".now()->year; // Set date to today if not provided

        $attendance = Attendance::where('user_id', $user_id)
                        ->where('date', $date)  
                        ->get()
                        ->groupBy(['entry_type']);

        // $schedulePlanner = SchedulePlanner::with(['schedule'])->whereRaw("FIND_IN_SET(?, user_id)", [$user_id])
        //         ->where('weeknum', $weeknum)
        //         ->first();

        return response()->json($attendance, 200);
    }

    public function getUserAttendanceByID(Request $request)
    {
        $user_id = $request->filled('user_id') ? $request->query('user_id') : Auth::id(); // Get authenticated user ID if not provided
        $date = $request->filled('date') ? $request->query('date') : now()->toDateString(); // Set date to today if not provided
        // $weeknum = $request->filled('weeknum') ? $request->query('weeknum') : now()->weekOfYear."/".now()->year; // Set date to today if not provided

        $attendance = Attendance::where('user_id', $user_id);
                        // ->where('duration', '!=', null)  
                        // ->where('duration', '!=', "");  
                        // ->where('date', $date)  
                        // ->get();
                        // ->groupBy(['date']);
                        // ->groupBy(['entry_type']);

        $attendance->orderBy("start", "desc");

        // $schedulePlanner = SchedulePlanner::with(['schedule'])->whereRaw("FIND_IN_SET(?, user_id)", [$user_id])
        //         ->where('weeknum', $weeknum)
        //         ->first();

        return response()->json($attendance->get(), 200);
    }

}
