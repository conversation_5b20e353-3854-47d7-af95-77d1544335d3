import React from 'react';
import HolidayCalender from '../pages/holiday-calender/HolidayCalender';
import AddHolidayCalender from '../pages/holiday-calender/AddHolidayCalender';
import HolidayCalenderList from '../pages/holiday-calender/HolidayCalenderList';
import HolidayTableHeader from '../common/table/HolidayTableHeader';
import TableLayoutWrapper2 from '../common/table/TableLayoutWrapper2';
import TableHeader from '../common/table/TableHeader';
import TeamShiftPlanList from '../pages/team-shift-plan/TeamShiftPlanList';


const TeamShiftPlan = () => {
  return (
    <div>
      <h2 className="text-2xl font-bold">Shift Planner</h2>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-team-shift-plan" buttonName="Add New Team Shift Plan" />
      <TeamShiftPlanList/>
      </TableLayoutWrapper2>
     
    </div>
  );
};

export default TeamShiftPlan;
