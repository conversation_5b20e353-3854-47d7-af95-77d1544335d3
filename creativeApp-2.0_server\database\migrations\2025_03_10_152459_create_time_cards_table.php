<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('time_cards', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->string('ticket');
            $table->integer('unit');
            $table->time('hour');
            $table->string('account');
            $table->string('campaign');
            $table->string('sla')->nullable();
            $table->string('high_priority')->nullable();
            $table->integer('client_error');
            $table->integer('internal_error');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();

            $table->unsignedBigInteger('department_id')->nullable();
            $table->foreign('department_id')->references('id')->on('departments')->onDelete('cascade');

            $table->unsignedBigInteger('team_id')->nullable();
            $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');                  
                   
            $table->unsignedBigInteger('shift_id')->nullable();
            $table->foreign('shift_id')->references('id')->on('schedules')->onDelete('cascade');

            // Foreign key for Product Type table
            $table->unsignedBigInteger('product_type_id')->nullable();
            $table->foreign('product_type_id')->references('id')->on('product_types')->onDelete('cascade');

            // Foreign key for Task Type table
            $table->unsignedBigInteger('task_type_id')->nullable();
            $table->foreign('task_type_id')->references('id')->on('task_types')->onDelete('cascade');

            // Foreign key for Task Type table
            $table->unsignedBigInteger('record_type_id')->nullable();
            $table->foreign('record_type_id')->references('id')->on('record_types')->onDelete('cascade');

            // Foreign key for Revision Type table
            $table->unsignedBigInteger('revision_type_id')->nullable();
            $table->foreign('revision_type_id')->references('id')->on('revision_types')->onDelete('cascade');

            // Foreign key for priorities table
            $table->unsignedBigInteger('priority_id')->nullable();
            $table->foreign('priority_id')->references('id')->on('priorities')->onDelete('cascade');
        
            // Foreign key for Team reporter table
            $table->unsignedBigInteger('reporter_id')->nullable();
            $table->foreign('reporter_id')->references('id')->on('reporters')->onDelete('cascade');
       
            // Foreign key for Region table
            $table->unsignedBigInteger('region_id')->nullable();
            $table->foreign('region_id')->references('id')->on('regions')->onDelete('cascade');

            // Foreign key for review and release table
            $table->unsignedBigInteger('review_id')->nullable();
            $table->foreign('review_id')->references('id')->on('reviews')->onDelete('cascade');

            // Foreign key for workflow table
            $table->unsignedBigInteger('workflow_id')->nullable();
            $table->foreign('workflow_id')->references('id')->on('workflows')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_cards');
    }
};
