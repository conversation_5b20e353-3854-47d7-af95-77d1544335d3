<?php

namespace App\Http\Controllers;

use App\Models\NoticeBoardCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NoticeBoardCategoryController extends Controller
{
    /**
     * Display a listing of all categories.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $categories = NoticeBoardCategory::all();

        // Log the categories retrieved
        Log::info('All categories Retrieved', ['count' => $categories->count()]);

        return response()->json(['categories' => $categories], 200);
    }

    /**
     * Display the specified category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the category by ID
        $category = NoticeBoardCategory::find($id);

        if (!$category) {
            return response()->json(['error' => 'Category not found.'], 404);
        }

        // Log the category retrieved
        Log::info('Category Retrieved', ['category' => $category]);

        return response()->json(['category' => $category], 200);
    }

    /**
     * Create a new category.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user details
        Log::info('Authenticated User', ['user_id' => $authUser->id]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255|unique:notice_board_categories,name',
        ]);

        // Log the request data
        Log::info('Create Category Request', ['request' => $request->all()]);

        // Create a new category
        $category = NoticeBoardCategory::create([
            'name' => $request->name,
            'creator_id' => $authUser->id,
        ]);

        Log::info('Category Created', ['category' => $category]);

        return response()->json(['message' => 'Category created successfully.', 'category' => $category], 201);
    }

    /**
     * Update an existing category.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user details
        Log::info('Authenticated User', ['user_id' => $authUser->id]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255|unique:notice_board_categories,name,' . $id,
        ]);

        // Log the request data
        Log::info('Update Category Request', ['request' => $request->all()]);

        // Find the category by ID
        $category = NoticeBoardCategory::findOrFail($id);

        // Update the category
        $category->update([
            'name' => $request->name,
            'updater_id' => $authUser->id,
        ]);

        // Log the updated category
        Log::info('Category Updated', ['category' => $category]);

        return response()->json(['message' => 'Category updated successfully.', 'category' => $category], 200);
    }

    /**
     * Delete a category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the category
            $category = NoticeBoardCategory::findOrFail($id);

            // Delete the category
            $category->delete();

            Log::info('Category Deleted', ['category_id' => $id]);

            return response()->json(['message' => 'Category deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this category.'], 403);
    }
}
