
import './App.css';
import { RouterProvider } from 'react-router-dom';
import creativeroutes from './routes';
import MainLayout from './dashboard/MainLayout';
import { ThemeProvider } from './common/utility/ThemeContext';

function App() {
  console.log(process.env.REACT_APP_BASE_API_URL)
  return (
    <div className="App">
      <ThemeProvider>
        <RouterProvider router={creativeroutes}>
          <MainLayout />
        </RouterProvider>
      </ThemeProvider>
    </div>
  );
}

export default App;
