import React, { useState, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON> } from "react-icons/fa";
import { Button } from "@headlessui/react";

const OfficeSeatPlan = () => {
  const [users, setUsers] = useState([]);

  useEffect(() => {
    fetch("seatPlan.json")
      .then((response) => response.json())
      .then((data) => setUsers(data.users || []))
      .catch((error) => console.error("Error loading seat plan:", error));
  }, []);

  const tables = [
    { id: 1, name: "Table 1" },
    { id: 2, name: "Table 2" },
    { id: 3, name: "Table 3" },
  ];

  return (
    <div className="p-6 bg-gray-100 min-h-screen flex flex-col items-center">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Office Seat Plan</h1>
      <div className="flex flex-wrap gap-8 justify-center w-full overflow-auto p-4">
        {tables.map((table, tableIndex) => (
          <div key={table.id} className="flex flex-col items-center bg-white p-6 rounded-xl shadow-lg w-80 text-center border border-gray-300">
            <h2 className="text-lg font-semibold mb-3 text-gray-700 uppercase">{table.name}</h2>
            <div className="flex flex-col items-center gap-2">
              {/* Top Row of Chairs */}
              <div className="flex justify-evenly w-full">
                {users.slice(tableIndex * 3, tableIndex * 3 + 3).map((user, index) => (
                  <div key={user.id} className="flex flex-col items-center">
                    <span className="text-xs font-bold text-gray-600">Row {tableIndex + 1}, Seat {index + 1}</span>
                    <FaChair className="text-gray-500 text-4xl" />
                    <span className="text-sm text-gray-800 mt-1 font-medium">{user.name}</span>
                  </div>
                ))}
              </div>
              {/* Table */}
              <div className="w-64 h-24 border-4 border-gray-800 bg-gray-300 rounded-lg flex items-center justify-center shadow-md">
                <span className="text-gray-800 font-bold text-lg">Table</span>
              </div>
              {/* Bottom Row of Chairs */}
              <div className="flex justify-evenly w-full">
                {users.slice(tableIndex * 3 + 3, tableIndex * 3 + 6).map((user, index) => (
                  <div key={user.id} className="flex flex-col items-center">
                    <span className="text-xs font-bold text-gray-600">Row {tableIndex + 1}, Seat {index + 4}</span>
                    <FaChair className="text-gray-500 text-4xl" />
                    <span className="text-sm text-gray-800 mt-1 font-medium">{user.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
      
    </div>
  );
};

export default OfficeSeatPlan;
