<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Location;  // Changed to Location
use Illuminate\Support\Facades\Log;

class LocationController extends Controller
{
    /**
     * Show all locations with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $locations = Location::all();  // Changed to Location
    
        // Log the locations retrieved
        Log::info('All Locations Retrieved:', ['locations_count' => $locations->count()]);
    
        return response()->json(['locations' => $locations], 200);
    }

    // Filter logic for data table
    public function locationData(Request $request)
    {
        $query = Location::with(['creator', 'updater']);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedLocationName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedUpdatedAt = $request->filled('updated_at') ? urldecode($request->input('updated_at')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering by name
        if ($decodedLocationName) {
            $names = explode(',', $decodedLocationName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by created_at
        if ($decodedCreatedAt) {
            $decodedCreatedAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($decodedCreatedAts) {
                foreach ($decodedCreatedAts as $decodedCreatedAt) {
                    $q->orWhere('created_at', '=', trim($decodedCreatedAt));
                }
            });
        }
    
        // Filtering by updated_at
        if ($decodedUpdatedAt) {
            $decodedUpdatedAts = explode(',', $decodedUpdatedAt);
            $query->where(function ($q) use ($decodedUpdatedAts) {
                foreach ($decodedUpdatedAts as $decodedUpdated) {
                    $q->orWhere('updated_at', '=', trim($decodedUpdatedAt));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $productTypes = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($productTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Location::with(['creator', 'updater']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Location::with(['creator','updater']);
        $results->select($column, $column. ' as location', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }
    

    /**
     * Display the specified location.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the location by ID
        $location = Location::find($id);  // Changed to Location

        if (!$location) {
            return response()->json(['error' => 'Location not found.'], 404);
        }

        // Log the location retrieved
        Log::info('Location Retrieved:', ['location' => $location]);

        return response()->json(['location' => $location], 200);
    }

    /**
     * Create a new location by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createLocation(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        $request->validate([
            'locations_name' => 'required|string|max:255'  // Changed from 'locations_name' to 'location name'
        ]);

        // Log the request data
        Log::info('Create Location Request:', ['request' => $request->all()]);

        // Check if the location name already exists
        if (Location::where('locations_name', $request->locations_name)->exists()) {  // Changed to Location and field name
            return response()->json(['error' => 'Location already exists.'], 409);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Create a new location with the full name
            $location = Location::create([
                'locations_name' => $request->locations_name,  // Changed to 'location name'
                'created_by' => $authUser->id
            ]);

            Log::info('Location Created:', ['location' => $location]);

            return response()->json(['message' => 'Location created successfully.', 'location' => $location], 201);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Location Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create a location.'], 403);
    }

    /**
     * Update an existing location by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLocation(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate the request data
        $request->validate([
            'locations_name' => 'required|string|max:255'  // Changed from 'locations_name' to 'location name'
        ]);

        // Log the request data
        Log::info('Update Location Request:', ['request' => $request->all()]);

        // Find the location by ID
        $location = Location::find($id);  // Changed to Location
        
        if (!$location) {
            return response()->json(['error' => 'Location not found.'], 404);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Check if the location name is being updated and does not already exist
            if ($location->locations_name !== $request->locations_name && Location::where('locations_name', $request->locations_name)->exists()) {  // Changed to Location and field name
                return response()->json(['error' => 'Location name already exists.'], 409);
            }

            // Update the location
            $location->locations_name = $request->locations_name;  // Changed to 'location name'
            $location->updated_by = $authUser->id;
            $location->save();

            // Log the updated location
            Log::info('Location Updated:', ['location' => $location]);

            return response()->json(['message' => 'Location updated successfully.', 'location' => $location], 200);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Location Update Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to update this location.'], 403);
    }

    /**
     * Delete a location by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteLocation($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the location
            $location = Location::findOrFail($id);  // Changed to Location

            // Delete the location
            $location->delete();

            return response()->json(['message' => 'Location deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this location.'], 403);
    }
}
