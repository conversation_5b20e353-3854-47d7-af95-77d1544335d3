import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { API_URL } from './../../common/fetchData/apiConfig'; 
import useFetchApiData from './../../common/fetchData/useFetchApiData';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddQuickAccessHub = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [hubs, setHubs] = useState([]);
    const [hubsIcon, setHubsIcon] = useState('');
    const [hubsImage, setHubsImage] = useState(null); // File field for hub image
    const [hubsTitle, setHubsTitle] = useState('');
    const [hubsDetails, setHubsDetails] = useState('');
    const [hubsUrl, setHubsUrl] = useState('');
    const [hubsCta, setHubsCta] = useState('');
    const [loggedInUser, setLoggedInUser] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [errorMessage, setErrorMessage] = useState(''); // For error message when both fields are empty

    const token = localStorage.getItem('token');

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchHubs = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}quick-access-hubs`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setHubs(data.quickaccesshub); // Update to use quickaccesshub array
            } catch (error) {
                setError(error.message);
            }
        };

        fetchHubs();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
    
        const trimmedHubTitle = hubsTitle.trim();
    
        const hubExists = hubs?.some(hub => hub.hubs_title.toLowerCase().trim() === trimmedHubTitle.toLowerCase());
    
        if (hubExists) {
            setError('Hub title already exists. Please add a different hub.');
            setTimeout(() => setError(''), 3000);
            return;
        }
    
        setError('');
    
        // Validate that at least one of the fields (Icon or Image) is filled
        if (!hubsIcon && !hubsImage) {
            setErrorMessage('Please add either an icon or an image.');
            return;
        }
    
        setErrorMessage(''); // Clear any previous error messages
    
        // Get user_id from localStorage for 'created_by'
        const createdBy = loggedInUser;
    
        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        // Create FormData object to handle file uploads
        const formData = new FormData();
        formData.append('hubs_icon', hubsIcon); // Append icon
        if (hubsImage) {
            formData.append('hubs_image', hubsImage); // Append image file (if exists)
        }
        formData.append('hubs_title', trimmedHubTitle); // Append hub title
        formData.append('hubs_details', hubsDetails); // Append hub details
        formData.append('hubs_url', hubsUrl); // Append hub URL
        formData.append('hubs_cta', hubsCta); // Append hub CTA
        formData.append('created_by', createdBy); // Append createdBy (full name of logged-in user)
    
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}quick-access-hubs`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    // Do not set 'Content-Type' header when using FormData, browser will set it correctly
                },
                body: formData,
            });
    
            if (!response.ok) {
                throw new Error('Failed to save hub: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Quick Access Hub "${result.hubs_title || trimmedHubTitle}" created successfully!`);
            alertMessage('success');
    
            // Reset form fields
            setHubsImage(null);
            setHubsIcon('');
            setHubsTitle('');
            setHubsDetails('');
            setHubsUrl('');
            setHubsCta('');
    
            // Refetch hubs
            const newHubsResponse = await fetch(`${API_URL}quick-access-hubs`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
            const newHubsData = await newHubsResponse.json();
            setHubs(newHubsData.quickaccesshub); // Update to use quickaccesshub array
    
        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };

    // Handle image change (size and dimensions validation)
    const handleImageChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Check file size
            if (file.size > 100 * 1024) { // 100KB limit
                setErrorMessage("File size should be less than 100KB");
                setHubsImage(null); // Clear the image if it exceeds the size
                return;
            }

            // Create a temporary image element to check dimensions
            const img = new Image();
            img.onload = () => {
                if (img.width > 300 || img.height > 300) { // 300x300 dimensions limit
                    setErrorMessage("Image dimensions should be 300x300 pixels or smaller");
                    setHubsImage(null); // Clear the image if it exceeds the dimensions
                } else {
                    setErrorMessage(""); // Clear error message if valid
                    setHubsImage(file); // Set the image if valid
                }
            };
            img.onerror = () => {
                setErrorMessage("Invalid image file");
                setHubsImage(null); // Clear the image if it's not a valid image
            };
            img.src = URL.createObjectURL(file); // Load the image
        }
    };
    
    

    if (!isVisible) return null;

    return (
        <>
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-3xl relative">

                    <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                        <h4 className="text-base text-left font-medium text-gray-800">Add New Quick Access</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className=' text-left'>
                        <div className="flex flex-wrap gap-6 p-6 overflow-y-auto max-h-[60vh] scrollbar-vertical">

                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsTitle" className="block text-sm font-medium text-gray-700 pb-4">Title</label>
                                <input
                                    type="text"
                                    id="hubsTitle"
                                    value={hubsTitle}
                                    onChange={(e) => setHubsTitle(e.target.value)}
                                    required
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsDetails" className="block text-sm font-medium text-gray-700 pb-4">Details</label>
                                <textarea
                                    id="hubsDetails"
                                    value={hubsDetails}
                                    onChange={(e) => setHubsDetails(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsUrl" className="block text-sm font-medium text-gray-700 pb-4">URL</label>
                                <input
                                    type="url"
                                    id="hubsUrl"
                                    value={hubsUrl}
                                    onChange={(e) => setHubsUrl(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsCta" className="block text-sm font-medium text-gray-700 pb-4">CTA</label>
                                <input
                                    type="text"
                                    id="hubsCta"
                                    value={hubsCta}
                                    onChange={(e) => setHubsCta(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsIcon" className="block text-sm font-medium text-gray-700 pb-4">Icon</label>
                                <input
                                    type="text"
                                    id="hubsIcon"
                                    value={hubsIcon}
                                    onChange={(e) => setHubsIcon(e.target.value)}
                                    disabled={!!hubsImage} // Disable if image is uploaded
                                    className={`block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 ${!!hubsImage ? 'bg-gray-200 opacity-50 cursor-not-allowed' : ''}`}
                                />
                            </div>

                            {/* Image Upload */}
                            <div className="mb-4 w-full sm:w-[47%]">
                                <label htmlFor="hubsImage" className="block text-sm font-medium text-gray-700 pb-4">
                                    Hub Image
                                </label>
                                <div className='flex flex-row items-start justify-start gap-2'>

                                    {/* Display existing image preview */}
                                    {hubsImage && (
                                        <div className="w-40 h-40 overflow-hidden bg-gray-200 p-4 rounded-lg">
                                            <img
                                                src={URL.createObjectURL(hubsImage)} // Show image preview
                                                alt="Image Preview"
                                                className="w-auto h-auto object-cover"
                                            />
                                        </div>
                                    )}

                                    {/* Custom file input */}
                                    <label htmlFor="hubs_image" className={`cursor-pointer flex flex-col items-center justify-center gap-2 rounded-lg bg-gray-200 text-gray-800 w-40 h-40 p-4 hover:bg-green-100 ${!!hubsIcon ? 'opacity-50 cursor-not-allowed' : ''}`}>
                                        <span className="material-symbols-rounded text-6xl text-gray-300">photo_camera</span>
                                        <span className="text-sm text-gray-400 text-regular">Upload new Image</span>
                                    </label>
                                    <input
                                        type="file"
                                        id="hubs_image"
                                        className="hidden" // Hide the default file input button
                                        onChange={handleImageChange}  // Handle the file input change with validation
                                        disabled={!!hubsIcon} // Disable if the icon is provided
                                    />

                                    {/* Display error message if file exceeds limits */}
                                    {errorMessage && (
                                        <div className="text-red-600 text-sm mt-2 text-left">{errorMessage}</div>
                                    )}

                                </div>
                            </div>

                        </div>

                        {/* Error message display */}
                        {errorMessage && <p className="text-red-500 text-sm mt-4">{errorMessage}</p>}

                        {/* Submit Button */}
                        <div className="pt-8 pb-6 m-auto text-center">
                            <button
                                type="submit"
                                className="min-w-56 bg-primary hover:bg-secondary text-white rounded-full py-3 px-6"
                            >
                                Add Quick Access
                            </button>
                        </div>
                        <div>
                            {/* Error message display */}
                            {error && <p className="text-red-500 text-sm mt-4">{error}</p>}
                        </div>
                        {successMessage && 
                        <div className='bg-[#DAF8E6] p-6 rounded-lg border-l-4 border-green-500 flex flex-row items-center'>
                            <span className="material-symbols-rounded bg-green-500 text-gray-700 p-1 rounded-md">check_circle</span>
                            {/* Success message display */}
                            <p className="text-green-500 text-xl font-medium pl-6">{successMessage}</p>
                        </div>}
                    </form>
                </div>
            </div>
        </>
    );
};

export default AddQuickAccessHub;
