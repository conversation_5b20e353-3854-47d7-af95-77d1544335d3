<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\ReviewRelease;
use Illuminate\Support\Facades\Log;


class ReviewRleaseController extends Controller
{
    /**
     * Display a listing of all Review & Release.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $reviews = ReviewRelease::all();

        // Log the Review & Release retrieved
        Log::info('All Review & Release Retrieved:', ['review_count' => $reviews->count()]);

        return response()->json(['reviews' => $reviews], 200);
    }

    /**
     * Display the specified Review & Release.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the Review & Release by ID
        $reviewRelease = ReviewRelease::find($id);

        if (!$reviewRelease) {
            return response()->json(['error' => 'Review & Release not found.'], 404);
        }

        // Log the Review & Release retrieved
        Log::info('Review & Release Retrieved:', ['review' => $reviewRelease]);

        return response()->json(['review' => $reviewRelease], 200);
    }

    // Filter logic for data table
    public function reviewsData(Request $request)
    {
        $query = ReviewRelease::with(['creator', 'updater', 'team', 'department']);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedName = $request->filled('name') ? urldecode($request->input('name')) : null;
    
        // Filtering by department_id
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }
    
        // Filtering by team_id
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }
    
        // Filtering by name
        if ($decodedName) {
            $names = explode(',', $decodedName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                  ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $reviews = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($reviews, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = ReviewRelease::with(['creator', 'updater', 'team', 'department']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = ReviewRelease::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }


    /**
     * Create a new Review & Release.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        Log::info('Create Review & Release Request:', ['request' => $request->all()]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Log the request data
        Log::info('Create Review & Release Request:', ['request' => $request->all()]);

        // Create a new Review & Release
        $reviewRelease = ReviewRelease::create([
            'name' => $request->name,
            'department_id' => $request->department_id,
            'team_id' => $request->team_id,
            'created_by' => $authUser->id,
        ]);

        $reviewRelease->updated_at = null;
        $reviewRelease->saveQuietly(); 

        Log::info('Review & Release Created:', ['review' => $reviewRelease]);

        return response()->json(['message' => 'Review & Release created successfully.', 'review' => $reviewRelease], 201);
    }

    /**
     * Update an existing Review & Release.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        Log::info('Update Review & Release Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);
    
        // Find the existing Review & Release by ID
        $reviewRelease = ReviewRelease::find($id);
    
        // Check if the Review & Release exists
        if (!$reviewRelease) {
            return response()->json(['error' => 'Review & Release not found.'], 404);
        }
    
        // Update the Review & Release with the new data
        $reviewRelease->update([
            'name' => $request->name,
            'department_id' => $request->department_id,
            'team_id' => $request->team_id,
            'updated_by' => $authUser->id, // Track who updated the Review & Release
        ]);
    
        Log::info('Review & Release Updated:', ['review' => $reviewRelease]);
    
        return response()->json(['message' => 'Review & Release updated successfully.', 'review' => $reviewRelease], 200);
    }
    

    /**
     * Delete a Review & Release.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the Review & Release
            $reviewRelease = ReviewRelease::findOrFail($id);

            // Delete the Review & Release
            $reviewRelease->delete();

            return response()->json(['message' => 'Review & Release deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this Review & Release.'], 403);
    }
}
