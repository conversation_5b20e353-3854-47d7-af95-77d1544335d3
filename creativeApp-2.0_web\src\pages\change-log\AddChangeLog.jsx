import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from './EditorToolbar';
import 'react-quill/dist/quill.snow.css';

const API_URL = `${process.env.REACT_APP_BASE_API_URL}/`;

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddChangeLog = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const [version, setVersion] = useState('');
    const [date, setDate] = useState('');
    const [area, setArea] = useState('');
    const [type, setType] = useState('');
    const [description, setDescription] = useState('');
    const [author, setAuthor] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!isTokenValid()) {
            navigate('/login');
        }
    }, [navigate]);

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!version || !date || !area || !type || !description) {
            setError('Please fill in all required fields.');
            return;
        }

        setError('');
        setSuccessMessage('');
        setLoading(true);

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                setLoading(false);
                return;
            }

            const response = await fetch(`${API_URL}change-log`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ version, date, area, type, description, author }),
            });

            if (!response.ok) {
                throw new Error('Failed to add change log.');
            }

            setSuccessMessage('Change Log added successfully!');
            setVersion('');
            setDate('');
            setArea('');
            setType('');
            setDescription('');
            setAuthor('');
        } catch (error) {
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    const isModalOpen = location.pathname === '/add-change-log';

    const handleClose = () => {
        navigate('/change-log');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-2xl relative">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New Change Log</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-3">
                                <label className="block text-sm font-medium text-gray-700">Version *</label>
                                <input type="text" value={version} onChange={e => setVersion(e.target.value)} className="w-full border border-gray-300 p-2 rounded" />
                            </div>

                            <div className="mb-3">
                                <label className="block text-sm font-medium text-gray-700">Date *</label>
                                <input type="date" value={date} onChange={e => setDate(e.target.value)} className="w-full border border-gray-300 p-2 rounded" />
                            </div>

                            <div className="mb-3">
                                <label className="block text-sm font-medium text-gray-700">Area *</label>
                                <input type="text" value={area} onChange={e => setArea(e.target.value)} className="w-full border border-gray-300 p-2 rounded" />
                            </div>

                            <div className="mb-3">
                                <label className="block text-sm font-medium text-gray-700">Type *</label>
                                <input type="text" value={type} onChange={e => setType(e.target.value)} className="w-full border border-gray-300 p-2 rounded" />
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700">Description *</label>
                                <EditorToolbar />
                                <ReactQuill
                                    value={description}
                                    onChange={setDescription}
                                    className="bg-white border border-gray-300 rounded-md shadow-sm"
                                    theme="snow"
                                    modules={modules}
                                    formats={formats}
                                />
                            </div>

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-gray-700">Author</label>
                                <input type="text" value={author} onChange={e => setAuthor(e.target.value)} className="w-full border border-gray-300 p-2 rounded" />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className={`w-full text-white rounded-md py-3 ${loading ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
                                    disabled={loading}
                                >
                                    {loading ? 'Adding...' : 'Add Change Log'}
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddChangeLog;
