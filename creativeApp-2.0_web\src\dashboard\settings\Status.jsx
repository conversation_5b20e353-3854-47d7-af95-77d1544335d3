import React, { useState } from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import StatusList from '../../pages/settings/status/StatusList';
import AddStatus from '../../pages/settings/status/AddStatus';

const Status = () => {
    // use this for re render table after form submit
    const [fetchData, setFetchData] = useState(false);
    // button component for header, this is for add new status
    const handleAdd = <AddStatus setFetchData={setFetchData} />
    return (
        <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
            <TableLayoutWrapper2>
                <TableHeader handleModal={handleAdd} />
                {/* <StatusList fetchData={fetchData} setFetchData={setFetchData} /> */}
            </TableLayoutWrapper2>
        </div>
    );
};

export default Status;