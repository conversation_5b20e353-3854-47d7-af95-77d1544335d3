import React from "react";
import {
  useGetAttendanceByUserIdQuery,
  useCreateAttendanceRecordMutation,
  useUpdateAttendanceRecordMutation,
} from "../../features/api";
import { alertMessage } from "../coreui";
import { calculateShiftEndTime, calculateDuration } from "../../utils";
import moment from "moment";

const shiftCanEndTimeInMinutes = 30;

export const AttendanceBtn = ({
  userId = null,
  entry_type = "attendance",
  className = "",
}) => {
  // If no userId is provided, return nothing or an alternative UI

  // Fetch attendance data for the user
  const { data, error, isLoading } = useGetAttendanceByUserIdQuery({
    user_id: userId,
    date: moment().format("YYYY-MM-DD"),
    entry_type: entry_type,
  });



  const [updateAttendanceRecord] = useUpdateAttendanceRecordMutation();
  const [createAttendanceRecord] = useCreateAttendanceRecordMutation();

  const user_id = localStorage.getItem("user_id");

  if (!isLoading && data && data?.schedulePlanner === null ) {
    alertMessage({
      title: `You don't have any assigned&nbsp;shift.`,
      html: `<div>Please reach out to your Team Lead or Manager.</div>`,
      icon: "error",
      timer: null,
      toast: false,
      position: "center",
      confirmButtonColor: "#d95e5e",
      showConfirmButton: true,
    });
    return (<div className="rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800">Shift not assigned</div>);
  }


  // Determine button text based on attendance status
  const isShiftActive = data?.attendance; // Adjust this based on API response structure
  let startEndText = isShiftActive ? "End" : "Start";
  let shiftName = data?.schedulePlanner?.schedule?.shift_name || "Shift";
  // let timeData = isShiftActive ? defaultTimeFormat(data?.schedulePlanner?.schedule?.shift_end) : defaultTimeFormat(data?.schedulePlanner?.schedule?.shift_start) || "";
  // let shiftTime =
  //   defaultTimeFormat(data?.schedulePlanner?.schedule?.shift_end) +
  //     "-" +
  //     defaultTimeFormat(data?.schedulePlanner?.schedule?.shift_start) || "";

  let isDisable = (data?.attendance?.start && data?.attendance?.end) ?? false;

  let btnBgColor = isDisable ? "bg-[#ccc]" : "bg-[#fff]";
  startEndText = isDisable ? "Close" : startEndText;

  const AttendanceType = {
    attendance: `<span class="material-symbols-outlined me-2">co_present</span> <span>${startEndText} ${shiftName}</span>`,
    break: `<span class="material-symbols-outlined  me-2">local_cafe</span> <span>${startEndText} Break</span> `,
    early_leave: `<span class="material-symbols-outlined  me-2">exit_to_app</span> <span>Early Leave</span> `,
    late_entry: `<span class="material-symbols-outlined  me-2">assignment_late</span> <span>Late Entry</span> `,
  };

  let buttonText = AttendanceType[entry_type];

  // Handle error state
  if (error) {
    buttonText = `<span class="text-red-500">Error fetching attendance</span>`;
  }

  const handleSubmit = async () => {
    let formData = data?.attendance || {};
    let userData = data?.schedulePlanner || {};
    let currentDateTime = moment().format("YYYY-MM-DD HH:mm:ss");

    let response = null;
    try {
      if (formData && formData.id) {

        let startTime =  data?.attendance?.start || "";
        let attendanceDate =  moment(data?.attendance?.date || "").format("YYYY-MM-DD");
        let scheduleStartTime =userData?.schedule?.shift_start || "";
        let scheduleEndTime = userData?.schedule?.shift_end || "";
        let shiftEndTime = calculateShiftEndTime(attendanceDate, scheduleStartTime, scheduleEndTime);

        let officeEndTime = calculateDuration(
          currentDateTime,
          shiftEndTime,
          "YYYY-MM-DD HH:mm:ss",
          "humanize"
        );
        let officeEndDuration = calculateDuration(
          currentDateTime,
          shiftEndTime,
          "YYYY-MM-DD HH:mm:ss",
          "minutes"
        );

        if (officeEndDuration > shiftCanEndTimeInMinutes) {
          alertMessage({
            title: `Your shift remain ${officeEndTime}`,
            // html: `We respectfully request that you remain available for the next ${officeEndTime} to ensure a smooth transition and continuity of service.<br/>We appreciate your cooperation in this matter.<br/>If you want to early leave, please apply for early leave.`,
            // html: `<div>You can not end your shift before ${shiftCanEndTimeInMinutes}min.</div><div>If you want to early leave, please apply for early leave.</div>`,
            html: `<div>You can not end your shift before ${shiftCanEndTimeInMinutes}min.</div><small class="text-red-600">If you have any emergency, please apply for an early leave.</small>`,
            icon: "error",
            timer: null,
            toast: false,
            position: "center",
            confirmButtonColor: "#d95e5e",
            showConfirmButton: true,
          });
          return false;
        }

        let duration = calculateDuration(
          startTime,
          currentDateTime,
          "YYYY-MM-DD HH:mm:ss"
        );

        formData = {
          id: formData.id,
          end: isShiftActive ? currentDateTime : "",
          duration: duration,
          updated_by: user_id,
        };

        response = await updateAttendanceRecord({
          id: formData.id,
          ...formData,
        });
      } else {
        formData = {
          ...data?.attendance,
          department_id: userData?.department_id || "",
          team_id: userData?.team_id || "",
          user_id: userId || user_id,
          schedule_planner_id: userData?.id || "",
          date: moment().format("YYYY-MM-DD"),
          start: isShiftActive ? "" : currentDateTime,
          end: isShiftActive ? currentDateTime : "",
          duration: "",
          entry_type: entry_type,
          created_by: user_id,
        };
        response = await createAttendanceRecord({
          ...formData,
        });
      }

      if (response && response?.error) {
        alertMessage("error");
        // setError(response.error.data);
      } else {
        alertMessage("success");
      }
    } catch (error) {}
  };

  return (
    <div className="flex flex-col w-full">
      <button
        disabled={isDisable}
        onClick={handleSubmit}
        className={`w-full font-semibold justify-center py-2 rounded-xl  text-[#076d92] flex items-center align-middle ${btnBgColor} ${className}`}
      >
        {isLoading && (
          <span className="material-symbols-outlined animate-spin text-sm me-2">
            progress_activity
          </span>
        )}
        <div className="flex flex-col">
          <span
            className="flex items-center align-middle"
            dangerouslySetInnerHTML={{ __html: buttonText }}
          ></span>
          {/* {shiftTime && entry_type==='attendance' && <div className='' >{shiftTime}</div>} */}
        </div>
      </button>
    </div>
  );
};

export const BreakBtn = ({
  userId = null,
  entry_type = "break",
  className = "",
}) => {
  // If no userId is provided, return nothing or an alternative UI

  // Fetch attendance data for the user
  const { data, error, isLoading } = useGetAttendanceByUserIdQuery({
    user_id: userId,
    date: moment().format("YYYY-MM-DD"),
    entry_type: entry_type,
  });



  const [updateAttendanceRecord] = useUpdateAttendanceRecordMutation();
  const [createAttendanceRecord] = useCreateAttendanceRecordMutation();

  const user_id = localStorage.getItem("user_id");

  if (!isLoading && data && data?.schedulePlanner === null ) {
    alertMessage({
      title: `You don't have any assigned&nbsp;shift.`,
      html: `<div>Please reach out to your Team Lead or Manager.</div>`,
      icon: "error",
      timer: null,
      toast: false,
      position: "center",
      confirmButtonColor: "#d95e5e",
      showConfirmButton: true,
    });
    // return (<div className="rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800">Shift not assigned</div>);
    return <></>;
  }


  // Determine button text based on attendance status
  const isShiftActive = data?.attendance; // Adjust this based on API response structure
  let startEndText = isShiftActive ? "End" : "Start";
  let shiftName = data?.schedulePlanner?.schedule?.shift_name || "Shift";

  let isDisable = (data?.attendance?.start && data?.attendance?.end) ?? false;

  let btnBgColor = isDisable ? "bg-[#ccc]" : "bg-[#fff]";
  startEndText = isDisable ? "Close" : startEndText;

  const AttendanceType = {
    attendance: `<span class="material-symbols-outlined me-2">co_present</span> <span>${startEndText} ${shiftName}</span>`,
    break: `<span class="material-symbols-outlined  me-2">local_cafe</span> <span>${startEndText} Break</span> `,
    early_leave: `<span class="material-symbols-outlined  me-2">exit_to_app</span> <span>Early Leave</span> `,
    late_entry: `<span class="material-symbols-outlined  me-2">assignment_late</span> <span>Late Entry</span> `,
  };

  let buttonText = AttendanceType[entry_type];

  // Handle error state
  if (error) {
    buttonText = `<span class="text-red-500">Error fetching attendance</span>`;
  }

  const handleSubmit = async () => {
    let formData = data?.attendance || {};
    let userData = data?.schedulePlanner || {};
    let currentDateTime = moment().format("YYYY-MM-DD HH:mm:ss");

    let response = null;
    try {
      if (formData && formData.id) {

        let startTime =  data?.attendance?.start || "";
        let attendanceDate =  moment(data?.attendance?.date || "").format("YYYY-MM-DD");
        let scheduleStartTime =userData?.schedule?.shift_start || "";
        let scheduleEndTime = userData?.schedule?.shift_end || "";
        let shiftEndTime = calculateShiftEndTime(attendanceDate, scheduleStartTime, scheduleEndTime);

        let duration = calculateDuration(
          startTime,
          currentDateTime,
          "YYYY-MM-DD HH:mm:ss"
        );

        formData = {
          id: formData.id,
          end: isShiftActive ? currentDateTime : "",
          duration: duration,
          updated_by: user_id,
        };

        response = await updateAttendanceRecord({
          id: formData.id,
          ...formData,
        });
      } else {
        formData = {
          ...data?.attendance,
          department_id: userData?.department_id || "",
          team_id: userData?.team_id || "",
          user_id: userId || user_id,
          schedule_planner_id: userData?.id || "",
          date: moment().format("YYYY-MM-DD"),
          start: isShiftActive ? "" : currentDateTime,
          end: isShiftActive ? currentDateTime : "",
          duration: "",
          entry_type: entry_type,
          approval_status: "pending",
          created_by: user_id,
        };
        response = await createAttendanceRecord({
          ...formData,
        });
      }

      if (response && response?.error) {
        alertMessage("error");
        // setError(response.error.data);
      } else {
        alertMessage("success");
      }
    } catch (error) {}
  };

  return (
    <div className="flex flex-col w-full">
      <button
        disabled={isDisable}
        onClick={handleSubmit}
        className={`w-full font-semibold justify-center py-2 rounded-xl  text-[#076d92] flex items-center align-middle ${btnBgColor} ${className}`}
      >
        {isLoading && (
          <span className="material-symbols-outlined animate-spin text-sm me-2">
            progress_activity
          </span>
        )}
        <div className="flex flex-col">
          <span
            className="flex items-center align-middle"
            dangerouslySetInnerHTML={{ __html: buttonText }}
          ></span>
          {/* {shiftTime && entry_type==='attendance' && <div className='' >{shiftTime}</div>} */}
        </div>
      </button>
    </div>
  );
};
