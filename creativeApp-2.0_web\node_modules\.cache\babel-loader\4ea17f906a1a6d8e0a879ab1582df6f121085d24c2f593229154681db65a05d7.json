{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\Welcome.jsx\";\nimport React from 'react';\nimport PasswordManagerDashboard from '../components/password-manager/PasswordManagerDashboard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Welcome = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: /*#__PURE__*/_jsxDEV(PasswordManagerDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Welcome;\nexport default Welcome;\nvar _c;\n$RefreshReg$(_c, \"Welcome\");", "map": {"version": 3, "names": ["React", "PasswordManagerDashboard", "jsxDEV", "_jsxDEV", "Welcome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/Welcome.jsx"], "sourcesContent": ["import React from 'react';\r\nimport PasswordManagerDashboard from '../components/password-manager/PasswordManagerDashboard';\r\n\r\nconst Welcome = () => {\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <PasswordManagerDashboard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,wBAAwB,MAAM,yDAAyD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAC5EH,OAAA,CAACF,wBAAwB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC;AAEV,CAAC;AAACC,EAAA,GANIP,OAAO;AAQb,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}