<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Email Verification</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 h-screen flex justify-center items-center p-4">

    <div class="bg-white p-8 rounded-lg shadow-lg max-w-2xl w-full">
        @if(isset($verificationUrl))
            <p class="text-gray-800 text-lg mb-4">Hello! 👋</p>

            <p class="text-gray-700 mb-4">
                Please click the button below to verify your email address.
            </p>

            <div class="text-center my-6">
                <a href="{{ $verificationUrl }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded inline-block transition">
                    Verify Email Address
                </a>
            </div>

            <p class="text-gray-600 text-sm mb-4">
                If you're having trouble clicking the button, copy and paste the URL below into your browser:
            </p>

            <p class="text-blue-600 break-words mb-6">
                <a href="{{ $verificationUrl }}">{{ $verificationUrl }}</a>
            </p>

            <p class="text-gray-700 text-sm mb-6">
                If your account is not working after verification, please contact us at
                <a href="mailto:<EMAIL>" class="text-blue-600"><EMAIL></a>.
            </p>

            <p class="text-gray-800 mb-2">Regards,</p>
            <p class="text-gray-800 font-semibold mb-6">The SEBPO Creative Team</p>

            <hr class="my-4">

            <p class="text-xs text-gray-500">
                This email and any accompanying attachments are intended only to be read or used by the named addressee(s). It is confidential and contains legally privileged information. If you have received this message in error, please notify the sender immediately and delete the message.
            </p>

        @elseif(isset($message))
            <!-- After-verification message with redirect -->
            <h2 class="text-center text-2xl font-semibold mb-4">{{ $message }}</h2>

            @isset($redirect_url)
                <p class="text-center text-gray-600">Redirecting you to the login page...</p>
                <script>
                    setTimeout(function () {
                        window.location.href = '{{ $redirect_url }}';
                    }, 2000); // Redirect after 2 seconds
                </script>
            @endisset
        @else
            <p class="text-center text-red-500">Invalid email verification content.</p>
        @endif
    </div>

</body>
</html>
