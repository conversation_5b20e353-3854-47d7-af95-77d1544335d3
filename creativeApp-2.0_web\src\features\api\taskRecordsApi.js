import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const taskRecordsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTaskRecordsData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `task-details-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['TaskRecordsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForTaskRecords: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `task-details-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['TaskRecordsData'],
    }),

    getTaskRecordById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `task-details-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'TaskRecordsData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createTaskRecord: builder.mutation({
      query: (newFormationType) => ({
        url: 'task-details-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['TaskRecordsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateTaskRecord: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `task-details-data/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'TaskRecordsData', id }, 'TaskRecordsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteTaskRecord: builder.mutation({
      query: (id) => ({
        url: `task-detail/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TaskRecordsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetTaskRecordsDataQuery, // getTaskRecordsData
  useLazyFetchDataOptionsForTaskRecordsQuery,
  useGetTaskRecordByIdQuery,
  useLazyGetTaskRecordByIdQuery,
  useCreateTaskRecordMutation,
  useUpdateTaskRecordMutation,
  useDeleteTaskRecordMutation,
} = taskRecordsApi;
