import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const taskTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTaskTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `task-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['TaskTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForTaskType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `task-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['TaskTypeData'],
    }),

    getTaskTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `task-type-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'TaskTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createTaskType: builder.mutation({
      query: (newFormationType) => ({
        url: 'task-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['TaskTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateTaskType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `task-type/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'TaskTypeData', id }, 'TaskTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteTaskType: builder.mutation({
      query: (id) => ({
        url: `task-type/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TaskTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetTaskTypeDataQuery,
  useLazyFetchDataOptionsForTaskTypeQuery,
  useGetTaskTypeByIdQuery,
  useLazyGetTaskTypeByIdQuery,
  useCreateTaskTypeMutation,
  useUpdateTaskTypeMutation,
  useDeleteTaskTypeMutation,
} = taskTypeApi;
