import { useState, useEffect } from 'react';
import FetchLoggedInRole from './fetchData/FetchLoggedInRole';


const allowedRoles = {
  superAdmin: ['super-admin'],
  admin: ['super-admin', 'admin'],
  hod: ['super-admin', 'admin', 'hod'],
  manager: ['super-admin', 'admin', 'hod', 'manager'],
  teamLead: ['super-admin', 'admin', 'hod', 'manager', 'team-lead'],
  coordinator: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator'],
  shiftLead: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],
  teamMember: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']
};

// Custom hook to check user roles
export const useRoleBasedAccess = () => {
  const { userData, loading, error } = FetchLoggedInRole();
  const [rolePermissions, setRolePermissions] = useState({
    hasSuperAdminRole: false,
    hasAdminRole: false,
    hasHodRole: false,
    hasManagerRole: false,
    hasTeamLeadRole: false,
    hasCoordinatorRole: false,
    hasShiftLeadRole: false,
    hasTeamMemberRole: false,
  });

  useEffect(() => {
    if (userData?.roles) {
      const userRoles = userData.roles;

      setRolePermissions({
        hasSuperAdminRole: allowedRoles.superAdmin.some(role => userRoles.includes(role)),
        hasAdminRole: allowedRoles.admin.some(role => userRoles.includes(role)),
        hasHodRole: allowedRoles.hod.some(role => userRoles.includes(role)),
        hasManagerRole: allowedRoles.manager.some(role => userRoles.includes(role)),
        hasTeamLeadRole: allowedRoles.teamLead.some(role => userRoles.includes(role)),
        hasCoordinatorRole: allowedRoles.coordinator.some(role => userRoles.includes(role)),
        hasShiftLeadRole: allowedRoles.shiftLead.some(role => userRoles.includes(role)),
        hasTeamMemberRole: allowedRoles.teamMember.some(role => userRoles.includes(role)),
      });
    }
  }, [userData]);

  return { loading, error, rolePermissions };
};
