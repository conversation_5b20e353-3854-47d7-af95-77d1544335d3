import React, { useEffect, useState } from 'react';
import <PERSON><PERSON>hang<PERSON><PERSON>og from './EditChangeLog';
import AddChange<PERSON>og from './AddChangeLog';

const API_URL = process.env.REACT_APP_BASE_API_URL;
const TOKEN_KEY = 'token';

const getToken = () => localStorage.getItem(TOKEN_KEY);
const isTokenValid = () => !!getToken();

const getAuthHeaders = () => ({
    Authorization: `Bearer ${getToken()}`,
    'Content-Type': 'application/json',
});

const ChangeLogList = () => {
    const [changeLogs, setChangeLogs] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedChangeLogId, setSelectedChangeLogId] = useState(null);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchChangeLogs = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/change-logs`, {
                    method: 'GET',
                    headers: getAuthHeaders(),
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch data: ${response.statusText}`);
                }

                const data = await response.json();

                const logs = Array.isArray(data?.changeLogs || data) ? (data.changeLogs || data) : [];

                setChangeLogs(logs);
            } catch (error) {
                console.error('Error fetching data:', error);
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchChangeLogs();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/change-log/${id}`, {
                method: 'DELETE',
                headers: getAuthHeaders(),
            });

            if (!response.ok) {
                throw new Error(`Failed to delete entry: ${response.statusText}`);
            }

            setChangeLogs((prev) => prev.filter((log) => log.id !== id));
        } catch (error) {
            console.error('Error deleting data:', error);
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedChangeLogId(id);
        setModalVisible(true);
    };

    if (loading) return <div className="flex items-center justify-center py-10">Loading...</div>;
    if (error) return <div className="text-red-500 text-center py-6">{error}</div>;

    return (
        <div className="py-6">
            <div className="flex flex-wrap -mx-2">
                {changeLogs.length > 0 ? (
                    changeLogs.map((log) => (
                        <div key={log.id} className="w-full sm:w-1/1 md:w-1/1 p-4">
                            <div className="bg-white shadow-md rounded-xl border border-gray-200 p-4">
                                <div className="mb-2">
                                    <p><strong>Version:</strong> {log.version}</p>
                                    <p><strong>Date:</strong> {log.date}</p>
                                    <p><strong>Area:</strong> {log.area}</p>
                                    <p><strong>Type:</strong> {log.type}</p>
                                    <p><strong>Author:</strong> {log.author || 'N/A'}</p>
                                </div>
                                <div className="mb-4 ql-editor ql-snow text-gray-700" dangerouslySetInnerHTML={{ __html: log.description }} />
                                <div className="flex justify-end gap-4">
                                    <button
                                        onClick={() => handleEdit(log.id)}
                                        className="btn btn-sm bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-xl"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => handleDelete(log.id)}
                                        className="btn btn-sm bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-xl"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="text-gray-500 text-center w-full">No change logs found.</div>
                )}
            </div>

            {modalVisible && (
                <EditChangeLog
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    changeLogId={selectedChangeLogId}
                />
            )}
        </div>
    );
};

export default ChangeLogList;
