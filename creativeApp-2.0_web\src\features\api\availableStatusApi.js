import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const availableStatusApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAvailableStatusData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `available-status-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['AvailableStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForAvailableStatus: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `available-status-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['AvailableStatusData'],
    }),

    getAvailableStatusById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `available_statuses/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'AvailableStatusData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createAvailableStatus: builder.mutation({
      query: (newFormationType) => ({
        url: 'available-status-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['AvailableStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateAvailableStatus: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `available_statuses/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'AvailableStatusData', id }, 'AvailableStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteAvailableStatus: builder.mutation({
      query: (id) => ({
        url: `available_statuses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['AvailableStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetAvailableStatusDataQuery,
  useLazyFetchDataOptionsForAvailableStatusQuery,
  useGetAvailableStatusByIdQuery,
  useLazyGetAvailableStatusByIdQuery,
  useCreateAvailableStatusMutation,
  useUpdateAvailableStatusMutation,
  useDeleteAvailableStatusMutation,
} = availableStatusApi;
