<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Give_feedback;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class GiveFeedbackController extends Controller
{
    /**
     * Display a listing of all feedback records.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $feedbacks = Give_feedback::all();

        Log::info('All feedback entries retrieved', ['entries_count' => $feedbacks->count()]);

        return response()->json(['feedbacks' => $feedbacks], 200);
    }

    /**
     * Display the specified feedback entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $feedback = Give_feedback::find($id);

        if (!$feedback) {
            return response()->json(['error' => 'Feedback entry not found.'], 404);
        }

        Log::info('Feedback entry retrieved', ['entry' => $feedback]);

        return response()->json(['feedback' => $feedback], 200);
    }

    /**
     * Create a new feedback entry.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $authUser = $request->user();

        Log::info('Create feedback request received', ['request' => $request->all(), 'user' => $authUser->id]);

        $request->validate([
            'subject' => 'required|string',
            'message' => 'required|string',
            'status' => 'required|string',
        ]);

        $feedback = Give_feedback::create([
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => $request->status,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('Feedback entry created', ['entry' => $feedback]);

        return response()->json([
            'message' => 'Feedback submitted successfully.',
            'feedback' => $feedback
        ], 201);
    }

    /**
     * Update an existing feedback entry.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        // Log the request data and authenticated user details
        Log::info('Update App Support entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        $request->validate([
            'subject' => 'required|string',
            'message' => 'required|string',
            'status' => 'required|string',
        ]);

        $feedback = Give_feedback::find($id);

        if (!$feedback) {
            return response()->json(['error' => 'Feedback entry not found.'], 404);
        }

        $feedback->update([
            'subject' => $request->subject,
            'message' => $request->message,
            'status' => $request->status,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,
        ]);

        Log::info('Feedback entry updated', ['entry' => $feedback]);

        return response()->json([
            'message' => 'Feedback updated successfully.',
            'feedback' => $feedback
        ], 200);
    }

    /**
     * Delete a feedback entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $feedback = Give_feedback::findOrFail($id);
            $feedback->delete();

            Log::info('Feedback entry deleted', ['entry_id' => $id]);

            return response()->json(['message' => 'Feedback entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this feedback.'], 403);
    }
}