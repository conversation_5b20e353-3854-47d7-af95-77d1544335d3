<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('eid')->nullable()->unique();
            $table->string('photo')->nullable();
            $table->string('fname')->nullable();
            $table->string('lname')->nullable();
            $table->string('email')->unique();
            $table->text('about')->nullable();
            $table->date('birthday')->nullable();
            $table->string('birthday_celebration')->nullable();
            $table->date('birthday_celebration_date')->nullable();
            $table->string('gender')->nullable();
            $table->string('marital_status')->nullable();
            $table->string('nick_name')->nullable();
            $table->string('primary_contact')->nullable();
            $table->string('secondary_contact')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->string('relation_contact')->nullable();
            $table->string('present_address')->nullable();
            $table->string('permanent_address')->nullable();
            $table->string('blood_donate')->nullable();
            $table->string('prev_designation')->nullable();
            $table->string('report_to')->nullable();
            $table->string('desk_id')->nullable();
            $table->date('joining_date')->nullable(); 
            $table->date('termination_date')->nullable();
            $table->date('employment_end')->nullable();
            $table->date('work_anniversary')->nullable();
            $table->timestamp('email_verified_at')->nullable(); // Corrected type
            $table->string('password')->nullable(); // Consider making this not nullable for security
            $table->rememberToken();
            $table->timestamps();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
        });
    }
    
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
};
