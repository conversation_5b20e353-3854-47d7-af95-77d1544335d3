import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
const API_URL = process.env.REACT_APP_BASE_API_URL;
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddMember = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [resourceStatuses, setResourceStatuses] = useState([]);
    const [resourceTypes, setResourceTypes] = useState([]);
    const [billingStatuses, setBillingStatuses] = useState([]);
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [selectedTeam, setSelectedTeam] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedResourceStatus, setSelectedResourceStatus] = useState('');
    const [selectedResourceType, setSelectedResourceType] = useState('');
    const [selectedBillingStatus, setSelectedBillingStatus] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const fetchUsers = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }

            const data = await response.json();
            setUsers(data);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the roles to select for users
    const fetchRoles = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/roles`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user!');
            }
    
            const data = await response.json();
            const rolesData = data.roles;
    
            const rolesMap = rolesData.reduce((acc, role) => {
                acc[role.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setRoles(rolesData);
            setSelectedRoles(rolesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetch teams
    const fetchTeams = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/teams`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch teams: ' + response.statusText);
            }

            const data = await response.json();

            setTeams(data.teams);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetch departments
    const fetchDepartments = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/departments`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch departments: ' + response.statusText);
            }

            const data = await response.json();

            setDepartments(data.departments);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetch resource statuses
    const fetchResourceStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/resource_statuses`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch resource statuses: ' + response.statusText);
            }

            const data = await response.json();

            setResourceStatuses(data.resource_status);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetch resource types
    const fetchResourceTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/resource_types`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch resource types: ' + response.statusText);
            }

            const data = await response.json();

            setResourceTypes(data['Resource Types']);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetch billing statuses
    const fetchBillingStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/billing_statuses`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch billing statuses: ' + response.statusText);
            }

            const data = await response.json();

            setBillingStatuses(data['billing statuses']);
        } catch (error) {
            setError(error.message);
        }
    };

    useEffect(() => {
        fetchUsers();
        fetchRoles();
        fetchTeams();
        fetchDepartments();
        fetchResourceStatuses();
        fetchResourceTypes();
        fetchBillingStatuses();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedEid = eid.trim();
        const trimmedEmail = email.trim();
    
        if (!trimmedEid || !trimmedEmail) {
            setError('EID and Email are required.');
            return;
        }
    
        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());
        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());
    
        if (eidExists || emailExists) {
            let message = 'The ';
            if (eidExists) message += 'EID ';
            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';
            message += 'already exists. Please add a new EID and/or Email.';
            setError(message);
            setTimeout(() => setError(''), 3000);
            return;
        }
    
        setError('');

        // Prepare roles array based on selected roles
        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/users`, {
                method: 'POST',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },

                body: JSON.stringify({
                    eid: trimmedEid,
                    email: trimmedEmail,
                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers
                    team: selectedTeam,
                    department: selectedDepartment,
                    resource_status: selectedResourceStatus,
                    resource_type: selectedResourceType,
                    billing_status: selectedBillingStatus
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save user: ' + response.statusText);
            }
    
            const result = await response.json();
            setSuccessMessage(`User with EID "${trimmedEid}" added successfully!`);
            setEid('');
            setEmail('');
            setSelectedRoles({}); // Reset roles
            setSelectedTeam('');
            setSelectedDepartment('');
            setSelectedResourceStatus('');
            setSelectedResourceType('');
            setSelectedBillingStatus('');
    
            fetchUsers();
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-member';

    const handleClose = () => {
        navigate('/settings');
    };


    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-start z-50">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full md:max-w-[70%] relative mt-12 max-h-[90vh] overflow-hidden overflow-y-auto">
                        <button onClick={handleClose} className="absolute top-0 right-2 text-gray-400 hover:text-gray-900 text-4xl">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Onboard new team member</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="flex flex-wrap justify-between gap-4">
                                <div className="mb-4 w-full sm:max-w-[48%] text-left">
                                    <label htmlFor="eid" className="block text-sm font-medium text-gray-700 pb-4">
                                        EID
                                    </label>
                                    <input
                                        type="text"
                                        id="eid"
                                        value={eid}
                                        onChange={(e) => setEid(e.target.value)}
                                        required
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                <div className="mb-4 w-full sm:max-w-[48%] text-left">
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    />
                                </div>
                                <div className='flex justify-between gap-4 flex-wrap'>
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Roles
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {roles.map(role => (
                                                <label className="inline-flex items-center" key={role.id}>
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedRoles[role.id] || false}
                                                        onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}
                                                        className="form-checkbox"
                                                    />
                                                    <span className="ml-2">{role.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                    
                                    {/* New Fields for Teams, Departments, Resource Statuses, Resource Types, and Billing Statuses */}
                                    {/* Team Checkbox */}
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Teams
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {teams.map(team => (
                                                <label className="inline-flex items-center" key={team.id}>
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedTeam[team.id] || false}
                                                        onChange={() => setSelectedTeam(prev => ({ ...prev, [team.id]: !prev[team.id] }))}
                                                        className="form-checkbox"
                                                    />
                                                    <span className="ml-2">{team.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
        
                                    {/* Department Checkbox */}
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Departments
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {departments.map(department => (
                                                <label className="inline-flex items-center" key={department.id}>
                                                    <input
                                                        type="radio"
                                                        name="department" // All radios in the group should have the same name
                                                        checked={selectedDepartment === department.id} // Only one department can be selected at a time
                                                        onChange={() => setSelectedDepartment(department.id)} // Set the selected department's id
                                                        className="form-radio"
                                                    />
                                                    <span className="ml-2">{department.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
        
                                    {/* Resource Status Checkbox */}
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Resource Statuses
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {resourceStatuses.map(status => (
                                                <label className="inline-flex items-center" key={status.id}>
                                                    <input
                                                        type="radio"
                                                        name="resourceStatus" // All radios in the group should have the same name
                                                        checked={selectedResourceStatus === status.id} // Only one status can be selected at a time
                                                        onChange={() => setSelectedResourceStatus(status.id)} // Set the selected resource status id
                                                        className="form-radio"
                                                    />
                                                    <span className="ml-2">{status.name}</span>
                                                </label>
                                            ))}
                                        </div>

                                    </div>
        
                                    {/* Resource Type Checkbox */}
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Resource Types
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {resourceTypes.map(type => (
                                                <label className="inline-flex items-center" key={type.id}>
                                                    <input
                                                        type="radio"
                                                        name="resourceType" // Group all radio buttons with the same name
                                                        checked={selectedResourceType === type.id} // Only one type can be selected at a time
                                                        onChange={() => setSelectedResourceType(type.id)} // Set the selected resource type id
                                                        className="form-radio"
                                                    />
                                                    <span className="ml-2">{type.name}</span>
                                                </label>
                                            ))}
                                        </div>

                                    </div>
        
                                    {/* Billing Status Checkbox */}
                                    <div className="mb-4 w-full lg:w-[32%] md:w-[48%] border border-gray-400 rounded-md p-4">
                                        <label className="block text-sm font-medium text-gray-700 pb-4">
                                            Billing Statuses
                                        </label>
                                        <div className="flex flex-col w-48 m-auto">
                                            {billingStatuses.map(status => (
                                                <label className="inline-flex items-center" key={status.id}>
                                                    <input
                                                        type="radio"
                                                        name="billingStatus" // Group all radio buttons with the same name
                                                        checked={selectedBillingStatus === status.id} // Only one billing status can be selected at a time
                                                        onChange={() => setSelectedBillingStatus(status.id)} // Set the selected billing status id
                                                        className="form-radio"
                                                    />
                                                    <span className="ml-2">{status.name}</span>
                                                </label>
                                            ))}
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <p>{error && <span className="text-red-500 text-sm pt-4">{error}</span>}</p>
                            <div className='py-4 w-56 m-auto'>
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add User
                                </button>
                            </div>
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
    
    
};

export default AddMember;
