<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class UniqueTeamWeekSchedule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return DB::table('schedule_planner')
        ->where('team_id', request('team_id'))
        ->where('weeknum', request('weeknum'))
        ->where('schedule_id', request('schedule_id'))
        ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Already Created this sift for this team and this week.';
    }
}
