<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Seat;

class SeatController extends Controller
{
    // Fetch all seats
    public function index()
    {
        return response()->json(['users' => Seat::all()]);
    }

    // Create a new seat
    public function store(Request $request)
    {
        $seat = Seat::create([
            'name' => $request->name,
            'row' => $request->row,
            'seat' => $request->seat,
        ]);

        return response()->json(['message' => 'Seat added successfully', 'seat' => $seat], 201);
    }

    // Update a seat
    public function update(Request $request, $id)
    {
        $seat = Seat::find($id);
        if (!$seat) {
            return response()->json(['message' => 'Seat not found'], 404);
        }

        $seat->update([
            'name' => $request->name ?? $seat->name,
            'row' => $request->row ?? $seat->row,
            'seat' => $request->seat ?? $seat->seat,
        ]);

        return response()->json(['message' => 'Seat updated successfully', 'seat' => $seat]);
    }

    // Delete a seat
    public function destroy($id)
    {
        $seat = Seat::find($id);
        if (!$seat) {
            return response()->json(['message' => 'Seat not found'], 404);
        }

        $seat->delete();
        return response()->json(['message' => 'Seat deleted successfully']);
    }
}
