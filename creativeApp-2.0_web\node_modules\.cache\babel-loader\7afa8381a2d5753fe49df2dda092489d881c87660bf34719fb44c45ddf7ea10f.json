{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddPasswordForm = ({\n  onSubmit,\n  onCancel,\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Update form when generated password changes\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n  const calculatePasswordStrength = password => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n\n    // Length check\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n\n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n\n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      // Add strength color for display\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = {\n        ...formData,\n        strengthColor\n      };\n      onSubmit(cardData);\n\n      // Reset form\n      setFormData({\n        title: '',\n        username: '',\n        password: '',\n        team: '',\n        department: '',\n        strength: 'Weak Password'\n      });\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"title\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"Title *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"title\",\n        name: \"title\",\n        value: formData.title,\n        onChange: handleInputChange,\n        className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n        placeholder: \"Enter platform or service name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 26\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"username\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"User Name *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"username\",\n        name: \"username\",\n        value: formData.username,\n        onChange: handleInputChange,\n        className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n        placeholder: \"Enter username or email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.username\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"password\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"Password *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: showPassword ? \"text\" : \"password\",\n          id: \"password\",\n          name: \"password\",\n          value: formData.password,\n          onChange: handleInputChange,\n          className: `w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n          placeholder: \"Enter password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-y-0 right-0 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: useGeneratedPassword,\n            className: \"px-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\",\n            title: \"Use generated password\",\n            children: \"Use Gen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: togglePasswordVisibility,\n            className: \"pr-3 pl-1 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"material-symbols-rounded text-sm\",\n              children: showPassword ? 'visibility_off' : 'visibility'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.password\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 29\n      }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `inline-block px-2 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`,\n          children: formData.strength\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"team\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"Team *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"team\",\n        name: \"team\",\n        value: formData.team,\n        onChange: handleInputChange,\n        className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.team ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n        placeholder: \"Enter team name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), errors.team && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.team\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"department\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"Department *\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"department\",\n        name: \"department\",\n        value: formData.department,\n        onChange: handleInputChange,\n        className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n        placeholder: \"Enter department name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), errors.department && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-red-600\",\n        children: errors.department\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 31\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"strength\",\n        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n        children: \"Strength Level\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-full px-3 py-2 border rounded-md ${getStrengthColor(formData.strength)} cursor-not-allowed`,\n        children: formData.strength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n        children: \"Strength is automatically calculated based on your password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:col-span-2 flex space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n        children: \"Save Password Card\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: onCancel,\n        className: \"flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordForm, \"F1Olx8D+5A6wCuBFI1UfhgP0Hgw=\");\n_c = AddPasswordForm;\nexport default AddPasswordForm;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AddPasswordForm", "onSubmit", "onCancel", "generatedPassword", "passwordStrength", "_s", "formData", "setFormData", "title", "username", "password", "team", "department", "strength", "showPassword", "setShowPassword", "errors", "setErrors", "prev", "handleInputChange", "e", "name", "value", "target", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "strengthColor", "cardData", "togglePasswordVisibility", "useGeneratedPassword", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "onChange", "placeholder", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst AddPasswordForm = ({ onSubmit, onCancel, generatedPassword, passwordStrength }) => {\n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Update form when generated password changes\n  useEffect(() => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  }, [generatedPassword, passwordStrength]);\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n\n  const calculatePasswordStrength = (password) => {\n    if (!password) return 'Weak Password';\n    \n    let score = 0;\n    \n    // Length check\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    \n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    \n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    \n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    \n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    \n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    \n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    \n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      // Add strength color for display\n      const strengthColor = getStrengthColor(formData.strength);\n      const cardData = {\n        ...formData,\n        strengthColor\n      };\n      \n      onSubmit(cardData);\n      \n      // Reset form\n      setFormData({\n        title: '',\n        username: '',\n        password: '',\n        team: '',\n        department: '',\n        strength: 'Weak Password'\n      });\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const useGeneratedPassword = () => {\n    if (generatedPassword) {\n      setFormData(prev => ({\n        ...prev,\n        password: generatedPassword,\n        strength: passwordStrength\n      }));\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {/* Title */}\n      <div>\n        <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Title *\n        </label>\n        <input\n          type=\"text\"\n          id=\"title\"\n          name=\"title\"\n          value={formData.title}\n          onChange={handleInputChange}\n          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n            errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n          }`}\n          placeholder=\"Enter platform or service name\"\n        />\n        {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n      </div>\n\n      {/* Username */}\n      <div>\n        <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          User Name *\n        </label>\n        <input\n          type=\"text\"\n          id=\"username\"\n          name=\"username\"\n          value={formData.username}\n          onChange={handleInputChange}\n          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n            errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n          }`}\n          placeholder=\"Enter username or email\"\n        />\n        {errors.username && <p className=\"mt-1 text-sm text-red-600\">{errors.username}</p>}\n      </div>\n\n      {/* Password */}\n      <div>\n        <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Password *\n        </label>\n        <div className=\"relative\">\n          <input\n            type={showPassword ? \"text\" : \"password\"}\n            id=\"password\"\n            name=\"password\"\n            value={formData.password}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 pr-20 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n            }`}\n            placeholder=\"Enter password\"\n          />\n          <div className=\"absolute inset-y-0 right-0 flex items-center\">\n            <button\n              type=\"button\"\n              onClick={useGeneratedPassword}\n              className=\"px-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n              title=\"Use generated password\"\n            >\n              Use Gen\n            </button>\n            <button\n              type=\"button\"\n              onClick={togglePasswordVisibility}\n              className=\"pr-3 pl-1 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <span className=\"material-symbols-rounded text-sm\">\n                {showPassword ? 'visibility_off' : 'visibility'}\n              </span>\n            </button>\n          </div>\n        </div>\n        {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n        \n        {/* Password Strength Indicator */}\n        {formData.password && (\n          <div className=\"mt-2\">\n            <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>\n              {formData.strength}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Team */}\n      <div>\n        <label htmlFor=\"team\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Team *\n        </label>\n        <input\n          type=\"text\"\n          id=\"team\"\n          name=\"team\"\n          value={formData.team}\n          onChange={handleInputChange}\n          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n            errors.team ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n          }`}\n          placeholder=\"Enter team name\"\n        />\n        {errors.team && <p className=\"mt-1 text-sm text-red-600\">{errors.team}</p>}\n      </div>\n\n      {/* Department */}\n      <div>\n        <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Department *\n        </label>\n        <input\n          type=\"text\"\n          id=\"department\"\n          name=\"department\"\n          value={formData.department}\n          onChange={handleInputChange}\n          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n            errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n          }`}\n          placeholder=\"Enter department name\"\n        />\n        {errors.department && <p className=\"mt-1 text-sm text-red-600\">{errors.department}</p>}\n      </div>\n\n      {/* Strength Level (Read-only, auto-calculated) */}\n      <div>\n        <label htmlFor=\"strength\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Strength Level\n        </label>\n        <div className={`w-full px-3 py-2 border rounded-md ${getStrengthColor(formData.strength)} cursor-not-allowed`}>\n          {formData.strength}\n        </div>\n        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n          Strength is automatically calculated based on your password\n        </p>\n      </div>\n\n      {/* Submit Buttons */}\n      <div className=\"md:col-span-2 flex space-x-4\">\n        <button\n          type=\"submit\"\n          className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          Save Password Card\n        </button>\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          Cancel\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default AddPasswordForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC;IACvCY,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIM,iBAAiB,EAAE;MACrBI,WAAW,CAACW,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPR,QAAQ,EAAEP,iBAAiB;QAC3BU,QAAQ,EAAET;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACD,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;EAEzC,MAAMe,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChB,WAAW,CAACW,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACG,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIN,MAAM,CAACK,IAAI,CAAC,EAAE;MAChBJ,SAAS,CAACC,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACG,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvB,MAAMR,QAAQ,GAAGW,yBAAyB,CAACF,KAAK,CAAC;MACjDf,WAAW,CAACW,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPL,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMW,yBAAyB,GAAId,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IAErC,IAAIe,KAAK,GAAG,CAAC;;IAEb;IACA,IAAIf,QAAQ,CAACgB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAIf,QAAQ,CAACgB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;;IAEzC;IACA,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;;IAE7C;IACA,IAAIf,QAAQ,CAACgB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IAErC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;EAED,MAAMG,gBAAgB,GAAIf,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,8CAA8C;MACvD,KAAK,mBAAmB;QACtB,OAAO,iDAAiD;MAC1D,KAAK,eAAe;QAClB,OAAO,wCAAwC;MACjD;QACE,OAAO,2CAA2C;IACtD;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACxB,QAAQ,CAACE,KAAK,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACtB,KAAK,GAAG,mBAAmB;IACvC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACrB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACpB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACoB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACnB,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACL,QAAQ,CAACM,UAAU,CAACmB,IAAI,CAAC,CAAC,EAAE;MAC/BD,SAAS,CAAClB,UAAU,GAAG,wBAAwB;IACjD;IAEAK,SAAS,CAACa,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMQ,YAAY,GAAId,CAAC,IAAK;IAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAElB,IAAIN,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,MAAMO,aAAa,GAAGR,gBAAgB,CAACtB,QAAQ,CAACO,QAAQ,CAAC;MACzD,MAAMwB,QAAQ,GAAG;QACf,GAAG/B,QAAQ;QACX8B;MACF,CAAC;MAEDnC,QAAQ,CAACoC,QAAQ,CAAC;;MAElB;MACA9B,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,EAAE;QACRC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMyB,wBAAwB,GAAGA,CAAA,KAAM;IACrCvB,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIpC,iBAAiB,EAAE;MACrBI,WAAW,CAACW,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPR,QAAQ,EAAEP,iBAAiB;QAC3BU,QAAQ,EAAET;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,oBACEL,OAAA;IAAME,QAAQ,EAAEiC,YAAa;IAACM,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAE7E1C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,OAAO;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAEnG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,OAAO;QACV3B,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;QACtByC,QAAQ,EAAE9B,iBAAkB;QAC5BqB,SAAS,EAAE,gLACTxB,MAAM,CAACR,KAAK,GAAG,gBAAgB,GAAG,sCAAsC,EACvE;QACH0C,WAAW,EAAC;MAAgC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EACD9B,MAAM,CAACR,KAAK,iBAAIT,OAAA;QAAGyC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEzB,MAAM,CAACR;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAGN/C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,UAAU;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAEtG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,UAAU;QACb3B,IAAI,EAAC,UAAU;QACfC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;QACzBwC,QAAQ,EAAE9B,iBAAkB;QAC5BqB,SAAS,EAAE,gLACTxB,MAAM,CAACP,QAAQ,GAAG,gBAAgB,GAAG,sCAAsC,EAC1E;QACHyC,WAAW,EAAC;MAAyB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,EACD9B,MAAM,CAACP,QAAQ,iBAAIV,OAAA;QAAGyC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEzB,MAAM,CAACP;MAAQ;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,eAGN/C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,UAAU;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAEtG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QAAKyC,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvB1C,OAAA;UACEgD,IAAI,EAAEjC,YAAY,GAAG,MAAM,GAAG,UAAW;UACzCkC,EAAE,EAAC,UAAU;UACb3B,IAAI,EAAC,UAAU;UACfC,KAAK,EAAEhB,QAAQ,CAACI,QAAS;UACzBuC,QAAQ,EAAE9B,iBAAkB;UAC5BqB,SAAS,EAAE,sLACTxB,MAAM,CAACN,QAAQ,GAAG,gBAAgB,GAAG,sCAAsC,EAC1E;UACHwC,WAAW,EAAC;QAAgB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/C,OAAA;UAAKyC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC3D1C,OAAA;YACEgD,IAAI,EAAC,QAAQ;YACbI,OAAO,EAAEZ,oBAAqB;YAC9BC,SAAS,EAAC,4FAA4F;YACtGhC,KAAK,EAAC,wBAAwB;YAAAiC,QAAA,EAC/B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/C,OAAA;YACEgD,IAAI,EAAC,QAAQ;YACbI,OAAO,EAAEb,wBAAyB;YAClCE,SAAS,EAAC,wFAAwF;YAAAC,QAAA,eAElG1C,OAAA;cAAMyC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC/C3B,YAAY,GAAG,gBAAgB,GAAG;YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL9B,MAAM,CAACN,QAAQ,iBAAIX,OAAA;QAAGyC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEzB,MAAM,CAACN;MAAQ;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAGjFxC,QAAQ,CAACI,QAAQ,iBAChBX,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB1C,OAAA;UAAMyC,SAAS,EAAE,kEAAkEZ,gBAAgB,CAACtB,QAAQ,CAACO,QAAQ,CAAC,EAAG;UAAA4B,QAAA,EACtHnC,QAAQ,CAACO;QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN/C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,MAAM;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAElG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,MAAM;QACT3B,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEhB,QAAQ,CAACK,IAAK;QACrBsC,QAAQ,EAAE9B,iBAAkB;QAC5BqB,SAAS,EAAE,gLACTxB,MAAM,CAACL,IAAI,GAAG,gBAAgB,GAAG,sCAAsC,EACtE;QACHuC,WAAW,EAAC;MAAiB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EACD9B,MAAM,CAACL,IAAI,iBAAIZ,OAAA;QAAGyC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEzB,MAAM,CAACL;MAAI;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC,eAGN/C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,YAAY;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAExG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,YAAY;QACf3B,IAAI,EAAC,YAAY;QACjBC,KAAK,EAAEhB,QAAQ,CAACM,UAAW;QAC3BqC,QAAQ,EAAE9B,iBAAkB;QAC5BqB,SAAS,EAAE,gLACTxB,MAAM,CAACJ,UAAU,GAAG,gBAAgB,GAAG,sCAAsC,EAC5E;QACHsC,WAAW,EAAC;MAAuB;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,EACD9B,MAAM,CAACJ,UAAU,iBAAIb,OAAA;QAAGyC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAEzB,MAAM,CAACJ;MAAU;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eAGN/C,OAAA;MAAA0C,QAAA,gBACE1C,OAAA;QAAO2C,OAAO,EAAC,UAAU;QAACF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAAC;MAEtG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR/C,OAAA;QAAKyC,SAAS,EAAE,sCAAsCZ,gBAAgB,CAACtB,QAAQ,CAACO,QAAQ,CAAC,qBAAsB;QAAA4B,QAAA,EAC5GnC,QAAQ,CAACO;MAAQ;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACN/C,OAAA;QAAGyC,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAAC;MAE7D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN/C,OAAA;MAAKyC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3C1C,OAAA;QACEgD,IAAI,EAAC,QAAQ;QACbP,SAAS,EAAC,4KAA4K;QAAAC,QAAA,EACvL;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/C,OAAA;QACEgD,IAAI,EAAC,QAAQ;QACbI,OAAO,EAAEjD,QAAS;QAClBsC,SAAS,EAAC,+KAA+K;QAAAC,QAAA,EAC1L;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACzC,EAAA,CArTIL,eAAe;AAAAoD,EAAA,GAAfpD,eAAe;AAuTrB,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}