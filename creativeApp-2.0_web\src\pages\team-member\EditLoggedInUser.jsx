import React, { useState, useEffect } from 'react';
import useFetchApiData from '../../common/fetchData/useFetchApiData';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditLoggedInUser = ({ visible, setVisible }) => {
    const [user, setUser] = useState(null);
    const [error, setError] = useState(null);
    const [errorMessage, setErrorMessage] = useState("");

    // State for each user field
    const [fname, setFname] = useState('');
    const [lname, setLname] = useState('');
    const [about, setAbout] = useState('');
    const [birthday, setBirthday] = useState('');
    const [birthdayCelebration, setBirthdayCelebration] = useState('');
    const [birthdayCelebrationDate, setBirthdayCelebrationDate] = useState('');
    const [gender, setGender] = useState('');
    const [maritalStatus, setMaritalStatus] = useState('');
    const [nickName, setNickName] = useState('');
    const [primaryContact, setPrimaryContact] = useState('');
    const [secondaryContact, setSecondaryContact] = useState('');
    const [emergencyContact, setEmergencyContact] = useState('');
    const [relationContact, setRelationContact] = useState('');
    const [presentAddress, setPresentAddress] = useState('');
    const [permanentAddress, setPermanentAddress] = useState('');
    const [bloodDonate, setBloodDonate] = useState('');
    const [prevDesignation, setPrevDesignation] = useState('');
    const [deskId, setDeskId] = useState('');
    const [joiningDate, setJoiningDate] = useState('');
    const [bloodsGroup, setBloodsGroup] = useState([]); // Blood groups list
    const [selectedBloods, setSelectedBloods] = useState([]); // Selected blood groups
    const [workAnniversary, setWorkAnniversary] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(false);

    const [photo, setPhoto] = useState(null); // State for photo file
    const [existingPhoto, setExistingPhoto] = useState(''); // Store existing photo URL
    const [newPhoto, setNewPhoto] = useState(null);

    const token = localStorage.getItem('token');

    // Fetching data using the custom hook
    const { data: bloodsGroupData } = useFetchApiData(`${API_URL}/bloods`, token);

    // useEffect(() => {
    //     if (bloodsGroupData && bloodsGroupData.bloods) {
    //         console.log('Blood Group List from Edit', bloodsGroupData.bloods);
    //         setBloodsGroup(bloodsGroupData.bloods || []);
    //     }
    // }, [bloodsGroupData]);

    // Fetch user data from the API
    const fetchUserData = async () => {
        const token = localStorage.getItem('token');
        const userId = localStorage.getItem('user_id');
    
        if (!userId) {
            setError('User ID not found in local storage');
            return;
        }
    
        try {
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Failed to fetch user data');
            }
    
            const data = await response.json();
            console.log("Edited Data", data);
    
            setUser(data.user);
    
            // Utility function to format date as yyyy-MM-dd
            const formatDate = (date) => {
                if (!date) return '';
                const formattedDate = new Date(date);
                return formattedDate.toISOString().split('T')[0]; // Extracts yyyy-MM-dd
            };
    
            // Set the fields to the fetched user data with fallback for null or undefined
            setFname(data.fname || '');
            setLname(data.lname || '');
            setAbout(data.about || '');
            setBirthday(formatDate(data.birthday) || ''); // Format the birthday
            setBirthdayCelebration(data.birthday_celebration || '');
            setBirthdayCelebrationDate(formatDate(data.birthday_celebration_date) || ''); // Format the celebration date
            setGender(data.gender || '');
            setMaritalStatus(data.marital_status || '');
            setNickName(data.nick_name || '');
            setPrimaryContact(data.primary_contact || '');
            setSecondaryContact(data.secondary_contact || '');
            setEmergencyContact(data.emergency_contact || '');
            setRelationContact(data.relation_contact || '');
            setPresentAddress(data.present_address || '');
            setPermanentAddress(data.permanent_address || '');
            setBloodDonate(data.blood_donate || '');
            setPrevDesignation(data.prev_designation || '');
            setDeskId(data.desk_id || '');
            setJoiningDate(formatDate(data.joining_date) || ''); // Format the joining date
            setWorkAnniversary(formatDate(data.work_anniversary) || ''); // Format the work anniversary date
    
            // Set the selected bloods (only IDs) from the fetched data
            setSelectedBloods(data.bloods ? data.bloods.map(blood => blood.id) : []);

            // Set the existing photo URL if available
            setExistingPhoto(data.photo || '');

        } catch (error) {
            setError(error.message);
        }
    };

    const fetchBloodData = async () => {
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/bloods`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Failed to fetch user data');
            }
    
            const data = await response.json();

            setBloodsGroup(data.bloods || []);


        } catch (error) {
            setError(error.message);
        }
    };

    useEffect(() => {
        fetchUserData();
        fetchBloodData();
    }, []);

    // Handle blood group selection change
    const handleBloodGroupChange = (event) => {
        const value = parseInt(event.target.value, 10);
        setSelectedBloods((prevSelected) => {
            if (prevSelected.includes(value)) {
                return prevSelected.filter((id) => id !== value); // Remove if already selected
            }
            return [...prevSelected, value]; // Add new selection
        });
    };

    // Handle photo upload change
    // const handlePhotoChange = (event) => {
    //     const file = event.target.files[0];
    //     if (file) {
    //         setPhoto(file); // Update the photo state
    //     }
    // };

    const handlePhotoChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Check file size
            if (file.size > 100 * 1024) { // 100KB limit
                setErrorMessage("File size should be less than 100KB");
                setPhoto(null); // Clear the photo if it exceeds the size
                return;
            }

            // Create a temporary image element to check dimensions
            const img = new Image();
            img.onload = () => {
                if (img.width > 300 || img.height > 300) { // 300x300 dimensions limit
                    setErrorMessage("Image dimensions should be 300x300 pixels or smaller");
                    setPhoto(null); // Clear the photo if it exceeds the dimensions
                } else {
                    setErrorMessage(""); // Clear error message if valid
                    setPhoto(file); // Set the photo if valid
                }
            };
            img.onerror = () => {
                setErrorMessage("Invalid image file");
                setPhoto(null); // Clear the photo if it's not a valid image
            };
            img.src = URL.createObjectURL(file); // Load the image
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);

        setError(''); // Clear any previous error
    
        try {
            const token = localStorage.getItem('token');
            const userId = localStorage.getItem('user_id');
    
            if (!userId) {
                setError('User ID not found in local storage');
                return;
            }
    
            // Prepare form data (excluding files)
            const requestData = {
                fname,
                lname,
                about,
                birthday,
                birthday_celebration: birthdayCelebration,
                birthday_celebration_date: birthdayCelebrationDate,
                gender,
                marital_status: maritalStatus,
                nick_name: nickName,
                primary_contact: primaryContact,
                secondary_contact: secondaryContact,
                emergency_contact: emergencyContact,
                relation_contact: relationContact,
                present_address: presentAddress,
                permanent_address: permanentAddress,
                blood_donate: bloodDonate,
                prev_designation: prevDesignation,
                desk_id: deskId,
                joining_date: joiningDate,
                work_anniversary: workAnniversary,
                bloods: selectedBloods,
                updated_by: userId,
            };
    
            // Convert the photo to Base64 if provided
            if (photo && photo instanceof File) {
                const photoBase64 = await convertToBase64(photo);
                requestData.photo = photoBase64;
            } else {
                console.log("No valid photo file selected.");
            }
    
            // Log the final request data before submission
            console.log("Request data before submission:", requestData);
    
            // Make the PUT request to update the user data
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            });
    
            if (!response.ok) {
                throw new Error('Failed to update user');
            }
    
            const updatedUser = await response.json();
            //setSuccessMessage(`User "${updatedUser.fname}" updated successfully!`);
            alertMessage('success');

            setLoading(false);
    
            setTimeout(() => {
                setVisible(false);
                //setSuccessMessage('');
            }, 2000);
    
        } catch (error) {
            //setError(error.message);
            alertMessage('error');
            setLoading(false);
        }
    };
    
    // Helper function to convert a file to Base64
    const convertToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            if (file instanceof File) {  // Check if the parameter is a valid File object
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result); // resolve with the Base64 string
                reader.onerror = reject;
                reader.readAsDataURL(file); // Convert file to Base64
            } else {
                reject('The provided object is not a valid File.');
            }
        });
    };
    

    if (!visible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50 overflow-hidden"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg w-full max-w-6xl"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                    <h4 className="text-base text-left font-medium text-gray-800">Edit Profile</h4>
                    <button
                        className="text-3xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='flex flex-wrap gap-6 px-6 pb-6 text-left overflow-y-auto max-h-[80vh] scrollbar-vertical'>
                        

                        {/* First Name */}
                        <div className='w-full md:max-w-[23%] py-2 text-left'>
                            <label htmlFor="fname" className="block mb-2">First Name <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="fname"
                                value={fname || ""}
                                onChange={(e) => setFname(e.target.value)}
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder={fname === '' ? 'Enter first name' : ''}
                                required
                            />
                        </div>

                        {/* Last Name */}
                        <div className='w-full md:max-w-[23%] py-2 text-left'>
                            <label htmlFor="lname" className="block mb-2">Last Name <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="lname"
                                value={lname}
                                onChange={(e) => setLname(e.target.value)}
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder={lname === '' ? 'Enter last name' : ''}
                                required
                            />
                        </div>

                        {/* Birthday */}
                        <div className='w-full md:max-w-[23%] py-2 text-left'>
                            <label htmlFor="birthday" className="block mb-2">Birthday <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="date"
                                id="birthday"
                                value={birthday}
                                onChange={(e) => setBirthday(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder={birthday === '' ? 'Select your birthday' : ''}
                            />
                        </div>

                        {/* Celebrate Your Birthday? */}
                        <div className="w-full md:max-w-[23%] py-2 text-left">
                            <label htmlFor="birthday_celebration" className="block mb-2">
                                Would you like to celebrate? <span className='text-red-600 text-base'>*</span>
                            </label>
                            <select
                                id="birthday_celebration"
                                value={birthdayCelebration}
                                onChange={(e) => setBirthdayCelebration(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select celebrate birthday?</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>

                        {/* Birthday Celebration Date */}
                        <div className='w-full md:max-w-[23%] py-2 text-left'>
                            <label htmlFor="birthday_celebration_date" className="block mb-2">Birthday Celebration Date</label>
                            <input
                                type="date"
                                id="birthday_celebration_date"
                                value={birthdayCelebrationDate}
                                onChange={(e) => setBirthdayCelebrationDate(e.target.value)}
                                disabled={birthdayCelebration === "No"}
                                className={`py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 ${
                                    birthdayCelebration === "No" ? "bg-gray-100 cursor-not-allowed" : ""
                                }`}
                            />
                        </div>

                        {/* Gender */}
                        <div className="w-full md:max-w-[23%] py-2 text-left">
                            <label htmlFor="gender" className="block mb-2">Gender <span className='text-red-600 text-base'>*</span></label>
                            <select
                                id="gender"
                                value={gender}
                                onChange={(e) => setGender(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                            </select>
                        </div>

                        {/* Marital Status */}
                        <div className="w-full md:max-w-[23%] py-2 text-left">
                            <label htmlFor="marital_status" className="block mb-2">Marital Status <span className='text-red-600 text-base'>*</span></label>
                            <select
                                id="marital_status"
                                value={maritalStatus}
                                onChange={(e) => setMaritalStatus(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select Marital Status</option>
                                <option value="single">Single</option>
                                <option value="married">Married</option>
                                <option value="divorced">Divorced</option>
                            </select>
                        </div>

                        {/* Nick Name */}
                        <div className='w-full md:max-w-[23%] py-2 text-left'>
                            <label htmlFor="nick_name" className="block mb-2">Preferred Nickname <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="nick_name"
                                value={nickName}
                                onChange={(e) => setNickName(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Contacts */}
                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="primary_contact" className="block mb-2">Primary Phone Number <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="primary_contact"
                                value={primaryContact}
                                onChange={(e) => setPrimaryContact(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Primary Contact"
                            />
                        </div>

                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="secondary_contact" className="block mb-2">Secondary Phone Number <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="secondary_contact"
                                value={secondaryContact}
                                onChange={(e) => setSecondaryContact(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Secondary Contact"
                            />
                        </div>

                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="emergency_contact" className="block mb-2">Emergency Contact Number <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="emergency_contact"
                                value={emergencyContact}
                                required
                                onChange={(e) => setEmergencyContact(e.target.value)}
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Emergency Contact"
                            />
                        </div>

                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="relation_contact" className="block mb-2">Emergency Contact Relationship <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="relation_contact"
                                value={relationContact}
                                onChange={(e) => setRelationContact(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Relation Contact"
                            />
                        </div>

                        {/* Blood Group */}
                        <div className="w-full md:max-w-[23%] py-2 text-left">
                            <label htmlFor="bloods" className="block mb-2">Blood Group <span className='text-red-600 text-base'>*</span></label>
                            <select
                                id="bloods"
                                
                                value={selectedBloods}
                                onChange={(e) => setSelectedBloods(Array.from(e.target.selectedOptions, option => option.value))}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select Blood Group(s)</option>
                                {bloodsGroup.map((blood) => (
                                    <option key={blood.id} value={blood.id}>
                                        {blood.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Blood Donation */}
                        <div className="w-full md:max-w-[23%] py-2 text-left">
                            <label htmlFor="blood_donate" className="block mb-2">Would you like to permit blood donation? <span className='text-red-600 text-base'>*</span></label>
                            <select
                                id="blood_donate"
                                value={bloodDonate}
                                onChange={(e) => setBloodDonate(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="" disabled>Select Blood Donation</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>

                        {/* Previous Designation */}
                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="prev_designation" className="block mb-2">Previous Designation in the Company <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="prev_designation"
                                value={prevDesignation}
                                onChange={(e) => setPrevDesignation(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Desk ID */}
                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="desk_id" className="block mb-2">Desk ID <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="text"
                                id="desk_id"
                                value={deskId}
                                onChange={(e) => setDeskId(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Joining Date */}
                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="joining_date" className="block mb-2">Joining Date <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="date"
                                id="joining_date"
                                value={joiningDate}
                                onChange={(e) => setJoiningDate(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>

                        {/* Work Anniversary */}
                        <div className="w-full md:max-w-[23%] py-2">
                            <label htmlFor="work_anniversary" className="block mb-2">Work Anniversary <span className='text-red-600 text-base'>*</span></label>
                            <input
                                type="date"
                                id="work_anniversary"
                                value={workAnniversary}
                                onChange={(e) => setWorkAnniversary(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                        </div>
                        
                        {/* About */}
                        <div className='w-full md:max-w-[48%] py-2 text-left'>
                            <label htmlFor="about" className="block mb-2">About <span className='text-red-600 text-base'>*</span></label>
                            <textarea
                                id="about"
                                value={about}
                                onChange={(e) => setAbout(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder={about === '' ? 'Tell us something about yourself' : ''}
                            />
                        </div>

                        {/* Address */}
                        <div className="w-full md:max-w-[48%] py-2">
                            <label htmlFor="present_address" className="block mb-2">Present Address <span className='text-red-600 text-base'>*</span></label>
                            <textarea
                                id="present_address"
                                value={presentAddress}
                                onChange={(e) => setPresentAddress(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Present Address"
                            />
                        </div>

                        <div className="w-full md:max-w-[48%] py-2">
                            <label htmlFor="permanent_address" className="block mb-2">Permanent Address <span className='text-red-600 text-base'>*</span></label>
                            <textarea
                                id="permanent_address"
                                value={permanentAddress}
                                onChange={(e) => setPermanentAddress(e.target.value)}
                                required
                                className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                placeholder="Permanent Address"
                            />
                        </div>

                        {/* Handle Photo Upload */}
                        <div className="w-full md:max-w-[48%] py-2">
                            <label htmlFor="photo" className="block mb-2 text-left">Profile Photo <span className='text-red-600 text-base'>*</span></label>
                            <div className="flex flex-row justify-start items-center gap-4">
                                {/* Display existing photo if available */}
                                {(existingPhoto || photo) && (
                                <div className="w-40 h-40 overflow-hidden bg-gray-200 p-4 rounded-lg">
                                    <img
                                    // Show the new photo preview if available, else fallback to the existing photo
                                    src={photo ? URL.createObjectURL(photo) : `${process.env.REACT_APP_BASE_STORAGE_URL}/${existingPhoto}`}
                                    alt="Profile"
                                    className="w-auto h-auto object-cover"
                                    />
                                </div>
                                )}

                                {/* Custom file input */}
                                <label htmlFor="photo" className="cursor-pointer flex flex-col items-center justify-center gap-2 rounded-lg bg-gray-200 text-gray-800 w-40 h-40 p-4 hover:bg-green-100">
                                <span className="material-symbols-rounded text-6xl text-gray-300">photo_camera</span>
                                <span className="text-sm text-gray-400 text-regular">Upload new Photo</span> {/* Custom text */}
                                </label>
                                <input
                                type="file"
                                name="photo"
                                id="photo"
                                className="hidden" // Hide the default file input button
                                onChange={handlePhotoChange} // Handle the file input change
                                />
                            </div>

                            {/* Display error message if file exceeds limits */}
                            {errorMessage && (
                                <div className="text-red-600 text-sm mt-2 text-left">{errorMessage}</div>
                            )}
                        </div>
                    </div>
                    
                    {successMessage && <div className="text-green-500 py-4 text-center">{successMessage}</div>}

                    {/* Submit Button */}
                    <div className="flex justify-center mb-8">
                        <button type="submit" className="w-auto min-w-48 btn bg-primary text-white hover:bg-secondary px-6 py-4 rounded-full">
                            {loading ? 'Updating...' : 'Update Profile'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditLoggedInUser;
