{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\routes.js\";\nimport React from 'react';\nimport { createBrowserRouter, Navigate } from 'react-router-dom';\nimport MainLayout from './dashboard/MainLayout';\nimport Login from './common/Login';\nimport Dashboard from './dashboard/Dashboard';\nimport UserList from './pages/team-member/TeamMemberList';\nimport Teams from './dashboard/settings/Teams';\nimport AddLocation from './pages/location/AddLocation';\nimport AddBranch from './pages/branch/AddBranch';\nimport Department from './dashboard/settings/Department';\nimport AddTeam from './pages/team/AddTeam';\nimport AddMember from './pages/team-member/AddMember';\nimport Settings from './dashboard/Settings';\nimport AddRole from './pages/role/AddRole';\nimport AddDepartment from './pages/department/AddDepartment';\nimport AddBillingStatus from './pages/billing-status/AddBillingStatus';\nimport AddResourceStatus from './pages/resource-status/AddResourceStatus';\nimport AddResourceType from './pages/resource-type/AddResourceType';\nimport AddDesignation from './pages/designation/AddDesignation';\nimport MemberIndex from './dashboard/MemberIndex';\nimport AddBlood from './pages/blood/AddBlood';\nimport AddAvailableStatus from './pages/available-status/AddAvailableStatus';\nimport AddContactType from './pages/contact-type/AddContactType';\nimport AddMemberStatus from './pages/member-status/AddMemberStatus';\nimport AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';\nimport MemberOnboard from './dashboard/MemberOnboard';\nimport AddSchedule from './pages/schedule/AddSchedule';\nimport ProtectedRoute from './route/ProtectedRoute';\nimport TeamContacts from './dashboard/TeamContacts';\n\n// Abdur Rahman\nimport Holiday from './dashboard/Holiday';\nimport QuickAccess from './dashboard/QuickAccessHubs';\nimport Training from './dashboard/Training';\nimport AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';\nimport AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';\nimport AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';\nimport AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';\nimport AddTraining from './pages/training/AddTraining';\nimport TaskDetails from './dashboard/task-details/TaskDetails';\nimport Formation from './dashboard/task-details/Formation';\nimport AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';\nimport AddTimeCard from './pages/time-card/AddTimeCard';\nimport TimeCard from './dashboard/time-card/TimeCard';\nimport AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';\nimport TeamShiftPlan from './dashboard/TeamShiftPlan';\nimport Profile from './dashboard/Profile';\nimport WorldTime from './pages/world-time/WorldTime';\nimport TimeZoneConvert from './pages/world-time/TimeZoneConvert';\nimport AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';\nimport Attendance from './pages/attendance/Attendance/Attendance';\nimport SchedulePlaners from './pages/schedule-planers/SchedulePlaners';\n\n// Imran Ahmed\nimport Todo from './dashboard/Todo';\nimport AddTodo from './pages/todo/commonTodo/AddTodo';\nimport Abouttheapp from './dashboard/Abouttheapp';\nimport AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';\nimport AddChangeLog from './pages/change-log/AddChangeLog';\nimport Changelog from './dashboard/Changelog';\nimport AddReporter from './pages/time-card/reporter/AddReporter';\nimport Reporter from './dashboard/time-card/Reporter';\nimport Appsupport from './dashboard/Appsupport';\nimport AddAppsupport from './pages/app-support/AddAppsupport';\nimport Givefeedback from './dashboard/Givefeedback';\nimport Reportproblem from './dashboard/Reportproblem';\nimport AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';\nimport AddReportProblem from './pages/report-problem/AddReportProblem';\nimport Teamsnapshot from './dashboard/Teamsnapshot';\nimport Notice from './dashboard/NoticeBoard';\nimport NoticeBoard from './dashboard/NoticeBoard';\nimport AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';\nimport AddNotice from './pages/notice/AddNotice';\nimport HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';\n//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\nimport OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';\nimport NotFound from './common/utility/NotFound';\nimport Unauthorized from './common/utility/UnAuthorized';\nimport ResetPassword from './common/login/ResetPassword';\nimport UpdatePassword from './common/login/UpdatePassword';\nimport Welcome from './dashboard/Welcome';\nimport Creativetools from './dashboard/Creativetools';\nimport AddPasswordCard from './components/password-manager/AddPasswordCard';\n\n// Define your routes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreativeRoutes = [{\n  path: '/login',\n  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 14\n  }, this)\n},\n// {\n//   path: '*',\n//   element: <NotFound />,\n// },\n{\n  path: 'reset-password',\n  element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '/password/reset/:token',\n  element: /*#__PURE__*/_jsxDEV(UpdatePassword, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 14\n  }, this)\n}, {\n  path: 'world-time-share',\n  element: /*#__PURE__*/_jsxDEV(WorldTime, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 14\n  }, this)\n}, {\n  path: 'time-zone-convert-share',\n  element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '/',\n  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n    children: /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 146\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 14\n  }, this),\n  children: [{\n    path: '/',\n    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 18\n    }, this) // Default to Dashboard\n  }, {\n    path: 'unauthorized',\n    element: /*#__PURE__*/_jsxDEV(Unauthorized, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'member-onboard',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(MemberOnboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'world-time',\n    element: /*#__PURE__*/_jsxDEV(WorldTime, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-zone-convert',\n    element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-zone-convert',\n    element: /*#__PURE__*/_jsxDEV(TimeZoneConvert, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'attendance',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Attendance, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'attendance-formation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AttendanceFormation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'team-members',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(TeamContacts, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'member-index',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(MemberIndex, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'profile',\n    element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-member',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(AddMember, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'teams',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Teams, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-team',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTeam, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-role',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(AddRole, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'departments',\n    element: /*#__PURE__*/_jsxDEV(Department, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 18\n    }, this),\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Department, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-department',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddDepartment, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-billing-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBillingStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-resource-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddResourceStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-resource-type',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddResourceType, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-designation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddDesignation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-location',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddLocation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-branch',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBranch, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-schedule',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddSchedule, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-available-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddAvailableStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-contact-type',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddContactType, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-blood',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddBlood, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-member-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddMemberStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-onsite-status',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddOnsiteStatus, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'settings',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin'],\n      children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 18\n    }, this)\n  },\n  // Task Details\n  {\n    path: 'add-task',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTaskRecord, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'task-records',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(TaskDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'formation',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(Formation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-reporter',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddReporter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'reporters',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Reporter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 18\n    }, this)\n  },\n  // Routes for All Users\n  {\n    path: 'add-time',\n    element: /*#__PURE__*/_jsxDEV(AddTimeCard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'time-cards',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(TimeCard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 18\n    }, this)\n  },\n  // Abdur Rahman\n  {\n    path: 'holidaycalenders',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(Holiday, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-holiday',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddHolidayCalender, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'quickaccesshub',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member'],\n      children: /*#__PURE__*/_jsxDEV(QuickAccess, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 150\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-hub',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddQuickAccessHub, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'training',\n    element: /*#__PURE__*/_jsxDEV(Training, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-training',\n    element: /*#__PURE__*/_jsxDEV(AddTraining, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-training-category',\n    element: /*#__PURE__*/_jsxDEV(AddTrainingCategory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-trainingtopic',\n    element: /*#__PURE__*/_jsxDEV(AddTrainingTopic, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-team-shift-plan',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead'],\n      children: /*#__PURE__*/_jsxDEV(AddTeamShiftPlan, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'shift-plan',\n    element: /*#__PURE__*/_jsxDEV(TeamShiftPlan, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'schedule-planners',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(SchedulePlaners, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 120\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'achieve',\n    element: /*#__PURE__*/_jsxDEV(Todo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'achieve/add-todo',\n    element: /*#__PURE__*/_jsxDEV(AddTodo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'about-the-app',\n    element: /*#__PURE__*/_jsxDEV(Abouttheapp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-about-the-app',\n    element: /*#__PURE__*/_jsxDEV(AddAboutTheApp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'change-log',\n    element: /*#__PURE__*/_jsxDEV(Changelog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-change-log',\n    element: /*#__PURE__*/_jsxDEV(AddChangeLog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'app-support',\n    element: /*#__PURE__*/_jsxDEV(Appsupport, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-app-support',\n    element: /*#__PURE__*/_jsxDEV(AddAppsupport, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'give-feedback',\n    element: /*#__PURE__*/_jsxDEV(Givefeedback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-feedback',\n    element: /*#__PURE__*/_jsxDEV(AddGiveFeedback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'report-problem',\n    element: /*#__PURE__*/_jsxDEV(Reportproblem, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-report-problem',\n    element: /*#__PURE__*/_jsxDEV(AddReportProblem, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'team-snapshot',\n    element: /*#__PURE__*/_jsxDEV(Teamsnapshot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'noticeboard',\n    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n      requiredRoles: ['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],\n      children: /*#__PURE__*/_jsxDEV(NoticeBoard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 135\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-notice-category',\n    element: /*#__PURE__*/_jsxDEV(AddNoticeBoardCategory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'add-notice',\n    element: /*#__PURE__*/_jsxDEV(AddNotice, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'events',\n    element: /*#__PURE__*/_jsxDEV(HolidayCalendarGoogleList, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'seat-plan',\n    element: /*#__PURE__*/_jsxDEV(OfficeSeatPlan, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'welcome',\n    element: /*#__PURE__*/_jsxDEV(Welcome, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: 'creative-tools',\n    element: /*#__PURE__*/_jsxDEV(Creativetools, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 18\n    }, this)\n  }]\n}, {\n  path: 'users',\n  element: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 418,\n    columnNumber: 14\n  }, this)\n}, {\n  path: '*',\n  element: /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 422,\n    columnNumber: 14\n  }, this)\n}];\n\n// Create the router\nconst router = createBrowserRouter(CreativeRoutes);\nexport default router;", "map": {"version": 3, "names": ["React", "createBrowserRouter", "Navigate", "MainLayout", "<PERSON><PERSON>", "Dashboard", "UserList", "Teams", "AddLocation", "AddBranch", "Department", "AddTeam", "AddMember", "Settings", "AddRole", "AddDepartment", "AddBillingStatus", "AddResourceStatus", "AddResourceType", "AddDesignation", "MemberIndex", "AddBlood", "AddAvailableStatus", "AddContactType", "AddMemberStatus", "AddOnsiteStatus", "MemberOnboard", "AddSchedule", "ProtectedRoute", "TeamContacts", "Holiday", "QuickAccess", "Training", "AddHolidayCalender", "AddTrainingCategory", "AddTrainingTopic", "AddQuickAccessHub", "AddTraining", "TaskDetails", "Formation", "AddTaskRecord", "AddTimeCard", "TimeCard", "AddTeamShiftPlan", "TeamShiftPlan", "Profile", "WorldTime", "TimeZoneConvert", "AttendanceFormation", "Attendance", "SchedulePlaners", "Todo", "AddTodo", "Abouttheapp", "AddAboutTheApp", "AddChangeLog", "Changelog", "AddReport<PERSON>", "Reporter", "Appsupport", "AddAppsupport", "Givefeedback", "Reportproblem", "AddGiveFeedback", "AddReportProblem", "Teamsnapshot", "Notice", "NoticeBoard", "AddNoticeBoardCategory", "AddNotice", "HolidayCalendarGoogleList", "OfficeSeatPlan", "NotFound", "Unauthorized", "ResetPassword", "UpdatePassword", "Welcome", "Creativetools", "AddPasswordCard", "jsxDEV", "_jsxDEV", "CreativeRoutes", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "requiredRoles", "children", "to", "router"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/routes.js"], "sourcesContent": ["import React from 'react';\r\nimport { createBrowserRouter, Navigate } from 'react-router-dom';\r\nimport MainLayout from './dashboard/MainLayout';\r\n\r\nimport Login from './common/Login';\r\nimport Dashboard from './dashboard/Dashboard';\r\nimport UserList from './pages/team-member/TeamMemberList';\r\nimport Teams from './dashboard/settings/Teams';\r\nimport AddLocation from './pages/location/AddLocation';\r\nimport AddBranch from './pages/branch/AddBranch';\r\nimport Department from './dashboard/settings/Department';\r\nimport AddTeam from './pages/team/AddTeam';\r\nimport AddMember from './pages/team-member/AddMember';\r\nimport Settings from './dashboard/Settings';\r\nimport AddRole from './pages/role/AddRole';\r\nimport AddDepartment from './pages/department/AddDepartment';\r\nimport AddBillingStatus from './pages/billing-status/AddBillingStatus';\r\nimport AddResourceStatus from './pages/resource-status/AddResourceStatus';\r\nimport AddResourceType from './pages/resource-type/AddResourceType';\r\nimport AddDesignation from './pages/designation/AddDesignation';\r\nimport MemberIndex from './dashboard/MemberIndex';\r\nimport AddBlood from './pages/blood/AddBlood';\r\nimport AddAvailableStatus from './pages/available-status/AddAvailableStatus';\r\nimport AddContactType from './pages/contact-type/AddContactType';\r\nimport AddMemberStatus from './pages/member-status/AddMemberStatus';\r\nimport AddOnsiteStatus from './pages/onsite-status/AddOnsiteStatus';\r\nimport MemberOnboard from './dashboard/MemberOnboard';\r\nimport AddSchedule from './pages/schedule/AddSchedule';\r\nimport ProtectedRoute from './route/ProtectedRoute';\r\nimport TeamContacts from './dashboard/TeamContacts';\r\n\r\n// Abdur Rahman\r\nimport Holiday from './dashboard/Holiday';\r\nimport QuickAccess from './dashboard/QuickAccessHubs';\r\nimport Training from './dashboard/Training'\r\nimport AddHolidayCalender from './pages/holiday-calender/AddHolidayCalender';\r\nimport AddTrainingCategory from './pages/training/training-category/AddTrainingCategory';\r\nimport AddTrainingTopic from './pages/training/training-topic/AddTrainingTopic';\r\nimport AddQuickAccessHub from './pages/quickaccesshub/AddQuickAccessHub';\r\nimport AddTraining from './pages/training/AddTraining';\r\nimport TaskDetails from './dashboard/task-details/TaskDetails';\r\nimport Formation from './dashboard/task-details/Formation';\r\nimport AddTaskRecord from './pages/task-details/task-record/AddTaskRecord';\r\nimport AddTimeCard from './pages/time-card/AddTimeCard';\r\nimport TimeCard from './dashboard/time-card/TimeCard';\r\nimport AddTeamShiftPlan from './pages/team-shift-plan/AddTeamShiftPlan';\r\nimport TeamShiftPlan from './dashboard/TeamShiftPlan';\r\nimport Profile from './dashboard/Profile';\r\nimport WorldTime from './pages/world-time/WorldTime';\r\nimport TimeZoneConvert from './pages/world-time/TimeZoneConvert';\r\nimport AttendanceFormation from './pages/attendance/AttendanceFormation/AttendanceFormationList';\r\nimport Attendance from './pages/attendance/Attendance/Attendance';\r\nimport SchedulePlaners from './pages/schedule-planers/SchedulePlaners';\r\n\r\n// Imran Ahmed\r\nimport Todo from './dashboard/Todo';\r\nimport AddTodo from './pages/todo/commonTodo/AddTodo';\r\nimport Abouttheapp from './dashboard/Abouttheapp';\r\nimport AddAboutTheApp from './pages/about-the-app/AddAboutTheApp';\r\nimport AddChangeLog from './pages/change-log/AddChangeLog';\r\nimport Changelog from './dashboard/Changelog';\r\nimport AddReporter from './pages/time-card/reporter/AddReporter';\r\nimport Reporter from './dashboard/time-card/Reporter';\r\nimport Appsupport from './dashboard/Appsupport';\r\nimport AddAppsupport from './pages/app-support/AddAppsupport';\r\nimport Givefeedback from './dashboard/Givefeedback';\r\nimport Reportproblem from './dashboard/Reportproblem';\r\nimport AddGiveFeedback from './pages/give-feedback/AddGiveFeedback';\r\nimport AddReportProblem from './pages/report-problem/AddReportProblem';\r\nimport Teamsnapshot from './dashboard/Teamsnapshot';\r\nimport Notice from './dashboard/NoticeBoard';\r\nimport NoticeBoard from './dashboard/NoticeBoard';\r\nimport AddNoticeBoardCategory from './pages/settings/noticeboardcategory/AddNoticeBoardCategory';\r\nimport AddNotice from './pages/notice/AddNotice';\r\nimport HolidayCalendarGoogleList from './pages/holiday-calender/HolidayCalenderGoogleList';\r\n//import OfficeSeatPlan from './pages/holiday-calender/OfficeSeatPlan';\r\nimport OfficeSeatPlan from './pages/seat-plan/OfficeSeatPlan';\r\nimport NotFound from './common/utility/NotFound';\r\nimport Unauthorized from './common/utility/UnAuthorized';\r\nimport ResetPassword from './common/login/ResetPassword';\r\nimport UpdatePassword from './common/login/UpdatePassword';\r\nimport Welcome from './dashboard/Welcome';\r\nimport Creativetools from './dashboard/Creativetools';\r\nimport AddPasswordCard from './components/password-manager/AddPasswordCard';\r\n\r\n\r\n// Define your routes\r\nconst CreativeRoutes = [\r\n  {\r\n    path: '/login',\r\n    element: <Login />,\r\n  },\r\n  // {\r\n  //   path: '*',\r\n  //   element: <NotFound />,\r\n  // },\r\n  {\r\n    path: 'reset-password',\r\n    element: <ResetPassword />,\r\n  },\r\n  {\r\n    path: '/password/reset/:token',\r\n    element: <UpdatePassword />,\r\n  },\r\n  {\r\n    path: 'world-time-share',\r\n    element: <WorldTime />,\r\n  },\r\n  {\r\n    path: 'time-zone-convert-share',\r\n    element: <TimeZoneConvert />,\r\n  },\r\n  {\r\n    path: '/',\r\n    element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><MainLayout /></ProtectedRoute>,\r\n    children: [\r\n      {\r\n        path: '/',\r\n        element: <Dashboard />, // Default to Dashboard\r\n      },\r\n      {\r\n        path: 'unauthorized',\r\n        element: <Unauthorized />,\r\n      },\r\n      {\r\n        path: 'member-onboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberOnboard /></ProtectedRoute>,\r\n        \r\n      },\r\n      \r\n      {\r\n        path: 'world-time',\r\n        element: <WorldTime />,\r\n      },\r\n\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'time-zone-convert',\r\n        element: <TimeZoneConvert />,\r\n      },\r\n      {\r\n        path: 'attendance',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Attendance/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'attendance-formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AttendanceFormation/></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'team-members',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TeamContacts/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'member-index',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><MemberIndex /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'profile',\r\n        element: <Profile />,\r\n      },\r\n      {\r\n        path: 'add-member',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><AddMember/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'teams',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Teams/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-team',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeam/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-role',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><AddRole/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'departments',\r\n        element: <Department />,\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Department/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-department',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDepartment/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-billing-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBillingStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-resource-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddResourceType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-designation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddDesignation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-location',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddLocation/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-branch',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBranch/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-schedule',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddSchedule/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-available-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddAvailableStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-contact-type',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddContactType/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-blood',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddBlood/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-member-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddMemberStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-onsite-status',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddOnsiteStatus/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'settings',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin']}><Settings /></ProtectedRoute>,\r\n      },\r\n\r\n      // Task Details\r\n      {\r\n        path: 'add-task',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddTaskRecord /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'task-records',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><TaskDetails /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'formation',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><Formation /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'add-reporter',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><AddReporter /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'reporters',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Reporter /></ProtectedRoute>,\r\n      },\r\n\r\n      // Routes for All Users\r\n      {\r\n        path: 'add-time',\r\n        element: <AddTimeCard />,\r\n      },\r\n\r\n      {\r\n        path: 'time-cards',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><TimeCard /></ProtectedRoute>,\r\n      },\r\n\r\n      // Abdur Rahman\r\n      {\r\n        path: 'holidaycalenders',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><Holiday /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-holiday',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddHolidayCalender /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'quickaccesshub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']}><QuickAccess/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-hub',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddQuickAccessHub /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'training',\r\n        element: <Training />,\r\n      },\r\n      {\r\n        path: 'add-training',\r\n        element: <AddTraining/>,\r\n      },\r\n      {\r\n        path: 'add-training-category',\r\n        element: <AddTrainingCategory />,\r\n      },\r\n\r\n      {\r\n        path: 'add-trainingtopic',\r\n        element: <AddTrainingTopic />,\r\n      },\r\n\r\n      {\r\n        path: 'add-team-shift-plan',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead']}><AddTeamShiftPlan /></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'shift-plan',\r\n        element: <TeamShiftPlan/>,\r\n      },\r\n      \r\n      {\r\n        path: 'schedule-planners',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'shift-lead']}><SchedulePlaners /></ProtectedRoute>,\r\n      },\r\n\r\n      {\r\n        path: 'achieve',\r\n        element: <Todo />\r\n      },\r\n      {\r\n        path: 'achieve/add-todo',\r\n        element: <AddTodo />\r\n      },\r\n      {\r\n        path: 'about-the-app',\r\n        element: <Abouttheapp/>\r\n      },\r\n      {\r\n        path: 'add-about-the-app',\r\n        element: <AddAboutTheApp/>\r\n      },\r\n      {\r\n        path: 'change-log',\r\n        element: <Changelog/>\r\n      },\r\n      {\r\n        path: 'add-change-log',\r\n        element: <AddChangeLog/>\r\n      },\r\n      {\r\n        path: 'app-support',\r\n        element: <Appsupport/>\r\n      },\r\n      {\r\n        path: 'add-app-support',\r\n        element: <AddAppsupport/>\r\n      },\r\n      {\r\n        path: 'give-feedback',\r\n        element: <Givefeedback/>\r\n      },\r\n      {\r\n        path: 'add-feedback',\r\n        element: <AddGiveFeedback/>\r\n      },\r\n      \r\n      {\r\n        path: 'report-problem',\r\n        element: <Reportproblem/>\r\n      },\r\n      {\r\n        path: 'add-report-problem',\r\n        element: <AddReportProblem/>\r\n      },\r\n      {\r\n        path: 'team-snapshot',\r\n        element: <Teamsnapshot/>\r\n      },\r\n      {\r\n        path: 'noticeboard',\r\n        element: <ProtectedRoute requiredRoles={['admin', 'super-admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead']}><NoticeBoard/></ProtectedRoute>,\r\n      },\r\n      {\r\n        path: 'add-notice-category',\r\n        element: <AddNoticeBoardCategory/>\r\n      },\r\n      {\r\n        path: 'add-notice',\r\n        element: <AddNotice/>\r\n      },\r\n     \r\n      {\r\n        path: 'events',\r\n        element: <HolidayCalendarGoogleList/>\r\n      },\r\n      {\r\n        path: 'seat-plan',\r\n        element: <OfficeSeatPlan/>\r\n      },\r\n\r\n      {\r\n        path: 'welcome',\r\n        element: <Welcome/>\r\n      },\r\n      {\r\n        path: 'creative-tools',\r\n        element: <Creativetools/>\r\n      }\r\n     \r\n      \r\n\r\n      \r\n    ],\r\n  },\r\n  {\r\n    path: 'users',\r\n    element: <UserList />,\r\n  },\r\n  {\r\n    path: '*',\r\n    element: <Navigate to=\"/login\" />,\r\n  }, \r\n];\r\n\r\n// Create the router\r\nconst router = createBrowserRouter(CreativeRoutes);\r\n\r\nexport default router;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,EAAEC,QAAQ,QAAQ,kBAAkB;AAChE,OAAOC,UAAU,MAAM,wBAAwB;AAE/C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,YAAY,MAAM,0BAA0B;;AAEnD;AACA,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,mBAAmB,MAAM,wDAAwD;AACxF,OAAOC,gBAAgB,MAAM,kDAAkD;AAC/E,OAAOC,iBAAiB,MAAM,0CAA0C;AACxE,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,aAAa,MAAM,gDAAgD;AAC1E,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,mBAAmB,MAAM,gEAAgE;AAChG,OAAOC,UAAU,MAAM,0CAA0C;AACjE,OAAOC,eAAe,MAAM,0CAA0C;;AAEtE;AACA,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,OAAO,MAAM,iCAAiC;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,6DAA6D;AAChG,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,yBAAyB,MAAM,oDAAoD;AAC1F;AACA,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,eAAe,MAAM,+CAA+C;;AAG3E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG,CACrB;EACEC,IAAI,EAAE,QAAQ;EACdC,OAAO,eAAEH,OAAA,CAAC5E,KAAK;IAAAgF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;EACEL,IAAI,EAAE,gBAAgB;EACtBC,OAAO,eAAEH,OAAA,CAACN,aAAa;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC3B,CAAC,EACD;EACEL,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,eAAEH,OAAA,CAACL,cAAc;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC5B,CAAC,EACD;EACEL,IAAI,EAAE,kBAAkB;EACxBC,OAAO,eAAEH,OAAA,CAAClC,SAAS;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACvB,CAAC,EACD;EACEL,IAAI,EAAE,yBAAyB;EAC/BC,OAAO,eAAEH,OAAA,CAACjC,eAAe;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACpD,cAAc;IAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;IAAAC,QAAA,eAACT,OAAA,CAAC7E,UAAU;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAgB,CAAC;EAC5KE,QAAQ,EAAE,CACR;IACEP,IAAI,EAAE,GAAG;IACTC,OAAO,eAAEH,OAAA,CAAC3E,SAAS;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,CAAE;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACP,YAAY;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtD,aAAa;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAErG,CAAC,EAED;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAAClC,SAAS;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACvB,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACjC,eAAe;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACjC,eAAe;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC/B,UAAU;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC5K,CAAC,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAChC,mBAAmB;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzI,CAAC,EAED;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACnD,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9K,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC5D,WAAW;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACnG,CAAC,EACD;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAACnC,OAAO;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrB,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACpE,SAAS;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChG,CAAC,EACD;IACEL,IAAI,EAAE,OAAO;IACbC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACzE,KAAK;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3H,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrE,OAAO;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC7H,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAClE,OAAO;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzK,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACtE,UAAU;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBJ,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtE,UAAU;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChI,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACjE,aAAa;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACnI,CAAC,EACD;IACEL,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAChE,gBAAgB;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACtI,CAAC,EACD;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC/D,iBAAiB;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACvI,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC9D,eAAe;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC7D,cAAc;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpI,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxE,WAAW;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjI,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvE,SAAS;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/H,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrD,WAAW;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjI,CAAC,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC1D,kBAAkB;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACxI,CAAC,EACD;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACzD,cAAc;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpI,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC3D,QAAQ;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9H,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxD,eAAe;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvD,eAAe;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACrI,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACnE,QAAQ;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChG,CAAC;EAED;EACA;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACxC,aAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACjK,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC1C,WAAW;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/J,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACzC,SAAS;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAChI,CAAC,EAED;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACvB,WAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC/J,CAAC,EAED;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtB,QAAQ;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3K,CAAC;EAED;EACA;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACvC,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EAED;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACtC,QAAQ;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC3K,CAAC;EAED;EACA;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAClD,OAAO;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC1K,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC/C,kBAAkB;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACzI,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACjD,WAAW;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC7K,CAAC,EACD;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC5C,iBAAiB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACxI,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAAChD,QAAQ;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAAC3C,WAAW;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACxB,CAAC,EACD;IACEL,IAAI,EAAE,uBAAuB;IAC7BC,OAAO,eAAEH,OAAA,CAAC9C,mBAAmB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACjC,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAAC7C,gBAAgB;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EAED;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACrC,gBAAgB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACvI,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACpC,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EAED;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAAC9B,eAAe;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EACpJ,CAAC,EAED;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAAC7B,IAAI;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClB,CAAC,EACD;IACEL,IAAI,EAAE,kBAAkB;IACxBC,OAAO,eAAEH,OAAA,CAAC5B,OAAO;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACrB,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAAC3B,WAAW;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACxB,CAAC,EACD;IACEL,IAAI,EAAE,mBAAmB;IACzBC,OAAO,eAAEH,OAAA,CAAC1B,cAAc;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC3B,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACxB,SAAS;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtB,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACzB,YAAY;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACrB,UAAU;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACvB,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACpB,aAAa;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAACnB,YAAY;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACjB,eAAe;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC5B,CAAC,EAED;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAAClB,aAAa;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC,EACD;IACEL,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,eAAEH,OAAA,CAAChB,gBAAgB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,OAAO,eAAEH,OAAA,CAACf,YAAY;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACpD,cAAc;MAAC4D,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAE;MAAAC,QAAA,eAACT,OAAA,CAACb,WAAW;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB;EAC9J,CAAC,EACD;IACEL,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,eAAEH,OAAA,CAACZ,sBAAsB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACnC,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACX,SAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtB,CAAC,EAED;IACEL,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEH,OAAA,CAACV,yBAAyB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACtC,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACT,cAAc;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC3B,CAAC,EAED;IACEL,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEH,OAAA,CAACJ,OAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EACpB,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACH,aAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC;EAC1B,CAAC;AAML,CAAC,EACD;EACEL,IAAI,EAAE,OAAO;EACbC,OAAO,eAAEH,OAAA,CAAC1E,QAAQ;IAAA8E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AACtB,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAAC9E,QAAQ;IAACwF,EAAE,EAAC;EAAQ;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAClC,CAAC,CACF;;AAED;AACA,MAAMI,MAAM,GAAG1F,mBAAmB,CAACgF,cAAc,CAAC;AAElD,eAAeU,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}