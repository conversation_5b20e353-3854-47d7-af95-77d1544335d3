import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const reviewReleaseApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getReviewReleaseData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `review-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ReviewReleaseData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForReviewRelease: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `review-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ReviewReleaseData'],
    }),

    getReviewReleaseById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `review/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ReviewReleaseData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createReviewRelease: builder.mutation({
      query: (newFormationType) => ({
        url: 'review-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ReviewReleaseData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateReviewRelease: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `review/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ReviewReleaseData', id }, 'ReviewReleaseData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteReviewRelease: builder.mutation({
      query: (id) => ({
        url: `review/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ReviewReleaseData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetReviewReleaseDataQuery,
  useLazyFetchDataOptionsForReviewReleaseQuery,
  useGetReviewReleaseByIdQuery,
  useLazyGetReviewReleaseByIdQuery,
  useCreateReviewReleaseMutation,
  useUpdateReviewReleaseMutation,
  useDeleteReviewReleaseMutation,
} = reviewReleaseApi;
