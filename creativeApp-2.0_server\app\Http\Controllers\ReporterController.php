<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Reporter;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Validator;
use Carbon\Carbon;

class ReporterController extends Controller
{
    /**
     * Display a listing of all reporters.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $reporters = Reporter::all();

        // Log the reporters retrieved
        Log::info('All Reporters Retrieved:', ['reporters_count' => $reporters->count()]);

        return response()->json(['reporters' => $reporters], 200);
    }


    //Query and Filtered data for Data table
    public function reportersData(Request $request)
    {
        // Define the query with all necessary relationships
        $query = Reporter::with(['creator','updater', 'teams', 'departments']);
    
        // Decode input parameters for each field
        $decodedTeam = $request->filled('team') ? urldecode($request->input('team')) : null;
        $decodedDepartment = $request->filled('department') ? urldecode($request->input('department')) : null;
        $decodedReporterName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedLocation = $request->filled('location') ? urldecode($request->input('location')) : null;
        $decodedTimezone = $request->filled('timezone') ? urldecode($request->input('timezone')) : null;
        $decodedEmail = $request->filled('email') ? urldecode($request->input('email')) : null;
        $decodedStartTime = $request->filled('start_time') ? urldecode($request->input('start_time')) : null;
        $decodedEndTime = $request->filled('end_time') ? urldecode($request->input('end_time')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering: Add the logic for each new field
    
        // Filter by team
        if ($decodedTeam) {
            $teams = explode(',', $decodedTeam);
            $query->where(function ($q) use ($teams) {
                foreach ($teams as $team) {
                    $q->orWhere('team', '=', trim($team));
                }
            });
        }

        if ($decodedDepartment) {
            $departments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($departments) {
                foreach ($departments as $department) {
                    $q->orWhere('department', '=', trim($department));
                }
            });
        }
    
        // Filter by Reporter name
        if ($decodedReporterName) {
            $reporterNames = explode(',', $decodedReporterName);
            $query->where(function ($q) use ($reporterNames) {
                foreach ($reporterNames as $reporterName) {
                    $q->orWhere('name', 'like', '%' . trim($reporterName) . '%');
                }
            });
        }
    
        // Filter by location name
        if ($decodedLocation) {
            $locations = explode(',', $decodedLocation);
            $query->where(function ($q) use ($locations) {
                foreach ($locations as $location) {
                    $q->orWhere('name', '=', trim($location));
                }
            });
        }
    
        // Filter by timezone
        if ($decodedTimezone) {
            $timezones = explode(',', $decodedTimezone);
            $query->where(function ($q) use ($timezones) {
                foreach ($timezones as $timezone) {
                    $q->orWhere('timezone', '=', trim($timezone));
                }
            });
        }
    
        // Filter by email
        if ($decodedEmail) {
            $emails = explode(',', $decodedEmail);
            $query->where(function ($q) use ($emails) {
                foreach ($emails as $email) {
                    $q->orWhere('email', 'like', '%' . trim($email) . '%');
                }
            });
        }
    
        // Filter by start time
        if ($decodedStartTime) {
            $startTimes = explode(',', $decodedStartTime);
            $query->where(function ($q) use ($startTimes) {
                foreach ($startTimes as $productType) {
                    $q->orWhere('start_time', '=', trim($startTime));
                }
            });
        }
    
        // Filter by end time
        if ($decodedEndTime) {
            $endTimes = explode(',', $decodedEndTime);
            $query->where(function ($q) use ($endTimes) {
                foreach ($endTimes as $productType) {
                    $q->orWhere('end_time', '=', trim($endTime));
                }
            });
        }
    
        // Filter by created_by
        if ($decodedCreatedBy) {
            $query->where('created_by', '=', trim($decodedCreatedBy));
        }
    
        // Filter by updated_by
        if ($decodedUpdatedBy) {
            $query->where('updated_by', '=', trim($decodedUpdatedBy));
        }

        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;

        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhere('location', 'like', '%' . $globalSearch . '%')
                    ->orWhere('timezone', 'like', '%' . $globalSearch . '%')
                    ->orWhere('email', 'like', '%' . $globalSearch . '%')

                    ->orWhereHas('departments', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('teams', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhere('start_time', 'like', '%' . $globalSearch . '%')
                    ->orWhere('end_time', 'like', '%' . $globalSearch . '%')
                    // Corrected 'creator' and 'updater' relationships
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }
    
        // Sorting logic
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
        $query->orderBy($sortBy, $order);
    
        // Pagination
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $taskrecords = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($taskrecords, 200);
    }
    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Reporter::with([
            'creator','updater', 'teams', 'departments'
        ]);              

        
        $results->select($column, $column. ' as reporter', \DB::raw("COUNT(*) as total"));

        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }
        
        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = Reporter::with([
            'creator','updater', 'teams', 'departments',
        ]); 

        
        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        }else{
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }
    


    /**
     * Display the specified reporter.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the reporter by ID
        $reporter = Reporter::find($id);

        if (!$reporter) {
            return response()->json(['error' => 'Reporter not found.'], 404);
        }

        // Log the reporter retrieved
        Log::info('Reporter Retrieved:', ['reporter' => $reporter]);

        return response()->json(['reporter' => $reporter], 200);
    }

    /**
     * Create a new reporter.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Check if the user has the required roles
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'])->exists()) {
            return response()->json(['error' => 'Only super-admin, admin, and hod can create reporter data.'], 403);
        }
    
        // Log the authenticated user's details
        \Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        \Log::info('Create Reporter Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'department' => 'required|integer',
            'team' => 'required|integer',
            'name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'timezone' => 'nullable|string|max:255',
            'email' => 'nullable|string|max:255|unique:reporters,email',
            'start_time' => [
                'nullable',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'end_time' => [
                'nullable',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
        ], [
            'start_time.regex' => 'The start time must be in the format h:i A (e.g., 7:00 PM).',
            'end_time.regex' => 'The end time must be in the format h:i A (e.g., 4:00 AM).',
        ]);
    
        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid input data',
                'errors' => $validator->errors()
            ], 400);
        }
    
        // Log the validated request data
        \Log::info('Create Reporter Request After Validation:', ['validated_request' => $validator->validated()]);
    
        // Convert start_time and end_time to 24-hour format using Carbon
        try {
            $startTime = Carbon::createFromFormat('h:i A', $request->start_time)->format('H:i:s');
            $endTime = Carbon::createFromFormat('h:i A', $request->end_time)->format('H:i:s');
    
            // Check if end_time is earlier than start_time (overnight shift scenario)
            if ($endTime <= $startTime) {
                $endTime = Carbon::createFromFormat('H:i:s', $endTime)->addDay()->format('H:i:s');
            }
    
            \Log::info('Start Time in 24-hour format:', ['start_time' => $startTime]);
            \Log::info('End Time in 24-hour format:', ['end_time' => $endTime]);
    
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error processing time format.',
                'error' => $e->getMessage(),
            ], 400);
        }
    
        // Create the reporter's data
        $reporter = Reporter::create([
            'department' => $request->department,
            'team' => $request->team,
            'name' => $request->name,
            'location' => $request->location,
            'timezone' => $request->timezone,
            'email' => $request->email,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'created_by' => $authUser->id,
        ]);

        $reporter->updated_at = null;
        $reporter->saveQuietly(); 
    
        \Log::info('Reporter Created:', ['reporter' => $reporter]);
    
        return response()->json(['message' => 'Reporter created successfully.', 'reporter' => $reporter], 201);
    }
    

    /**
     * Update an existing Reporter.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Check if the user has the required roles
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'])->exists()) {
            return response()->json(['error' => 'Only super-admin, admin, and hod can update reporter data.'], 403);
        }
    
        // Log the authenticated user's details
        \Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        // Find the existing reporter by ID
        $reporter = Reporter::find($id);
    
        // Check if reporter exists
        if (!$reporter) {
            return response()->json(['error' => 'Reporter not found.'], 404);
        }
    
        \Log::info('Update Reporter Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'department' => 'required|integer',
            'team' => 'required|integer',
            'name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'timezone' => 'nullable|string|max:255',
            'email' => 'nullable|string|max:255|unique:reporters,email,' . $reporter->id, // Allow updating the email for the same reporter
            'start_time' => [
                'nullable',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'end_time' => [
                'nullable',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
        ], [
            'start_time.regex' => 'The start time must be in the format h:i A (e.g., 7:00 PM).',
            'end_time.regex' => 'The end time must be in the format h:i A (e.g., 4:00 AM).',
        ]);
    
        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid input data',
                'errors' => $validator->errors()
            ], 400);
        }
    
        // Log the validated request data
        \Log::info('Update Reporter Request After Validation:', ['validated_request' => $validator->validated()]);
    
        // Convert start_time and end_time to 24-hour format using Carbon
        try {
            $startTime = Carbon::createFromFormat('h:i A', $request->start_time)->format('H:i:s');
            $endTime = Carbon::createFromFormat('h:i A', $request->end_time)->format('H:i:s');
    
            // Check if end_time is earlier than start_time (overnight shift scenario)
            if ($endTime <= $startTime) {
                $endTime = Carbon::createFromFormat('H:i:s', $endTime)->addDay()->format('H:i:s');
            }
    
            \Log::info('Start Time in 24-hour format:', ['start_time' => $startTime]);
            \Log::info('End Time in 24-hour format:', ['end_time' => $endTime]);
    
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error processing time format.',
                'error' => $e->getMessage(),
            ], 400);
        }
    
        // Update the reporter's data
        $reporter->update([
            'department' => $request->department,
            'team' => $request->team,
            'name' => $request->name,
            'location' => $request->location,
            'timezone' => $request->timezone,
            'email' => $request->email,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'updated_by' => $authUser->id
        ]);
    
        \Log::info('Reporter Updated:', ['reporter' => $reporter]);
    
        return response()->json(['message' => 'Reporter updated successfully.', 'reporter' => $reporter], 200);
    }
    

    /**
     * Delete a reporter.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id, Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Check if the user has the required roles
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod'])->exists()) {
            return response()->json(['error' => 'Only super-admin, admin, and hod can delete reporter data.'], 403);
        }

        // Find the reporter by ID using route model binding (recommended)
        $reporter = Reporter::find($id);

        // Check if the reporter exists
        if (!$reporter) {
            return response()->json(['error' => 'Reporter not found.'], 404);
        }

        // Log the deletion request
        \Log::info('Delete Reporter Request:', [
            'user_id' => $authUser->id,
            'reporter_id' => $reporter->id,
            'reporter_name' => $reporter->name
        ]);

        // Delete the reporter
        try {
            $reporter->delete();

            // Log the successful deletion
            \Log::info('Reporter Deleted:', ['reporter_id' => $reporter->id]);

            return response()->json(['message' => 'Reporter deleted successfully.'], 200);

        } catch (\Exception $e) {
            // Log error if deletion fails
            \Log::error('Error deleting reporter:', [
                'error' => $e->getMessage(),
                'reporter_id' => $reporter->id,
                'user_id' => $authUser->id
            ]);

            return response()->json(['error' => 'Error deleting reporter.', 'details' => $e->getMessage()], 500);
        }
    }

    
    
}
