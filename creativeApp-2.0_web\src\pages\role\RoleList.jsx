import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import { useNavigate } from 'react-router-dom';
import EditRole from './EditRole';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const RoleList = () => {
    const [roles, setRoles] = useState([]);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedRoleId, setSelectedRoleId] = useState(null);
    const navigate = useNavigate();

    // Update column names for roles
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Role Name", key: "name" }, // Updated to include fullName
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchroles = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/roles`, { 
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();

                setRoles(data.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    created_by: role.created_by,
                    updated_by: role.updated_by,
                }))); 
            } catch (error) {
                setError(error.message);
            }
        };

        fetchroles();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/roles/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete role: ' + response.statusText);
            }

            // Update the roles list after deletion
            setRoles(prevRoles => prevRoles.filter(role => role.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedRoleId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={roles}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedRoleId}
            />
            {modalVisible && (
                <EditRole
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    roleId={selectedRoleId}
                />
            )}
        </div>
    );
};

export default RoleList;
