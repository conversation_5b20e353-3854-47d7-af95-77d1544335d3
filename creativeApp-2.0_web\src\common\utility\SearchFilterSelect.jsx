import React, { useState, useEffect, useRef } from 'react';

const SearchFilterSelect = ({ options, value, onChange, placeholder = "Search and select...", id }) => {
    const [inputValue, setInputValue] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const containerRef = useRef(null);

    useEffect(() => {
        setInputValue(value || '');
    }, [value]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const filteredOptions = options.filter(option =>
        option.toLowerCase().includes(inputValue.toLowerCase())
    );

    const handleSelect = (selectedValue) => {
        setInputValue(selectedValue);
        onChange({ target: { value: selectedValue } }); // mimic event for compatibility
        setIsOpen(false);
    };

    return (
        <div className="relative" ref={containerRef}>
            <input
                id={id}
                type="text"
                value={inputValue}
                onChange={(e) => {
                    setInputValue(e.target.value);
                    setIsOpen(true);
                }}
                onFocus={() => setIsOpen(true)}
                placeholder={placeholder}
                className="block w-full border border-stone-200 rounded-md shadow-sm px-3 bg-stone-50 focus:outline-none focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
            />
            {isOpen && filteredOptions.length > 0 && (
                <ul className="absolute z-10 mt-1 max-h-60 min-w-[100%] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                    {filteredOptions.map((option, index) => (
                        <li
                            key={index}
                            onClick={() => handleSelect(option)}
                            className="cursor-pointer select-none px-4 py-2 hover:bg-blue-100"
                        >
                            {option}
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default SearchFilterSelect;
