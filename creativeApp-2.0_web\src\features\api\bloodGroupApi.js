import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const bloodGroupApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getBloodGroupData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `blood-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['BloodGroupData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForBloodGroup: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `blood-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['BloodGroupData'],
    }),

    getBloodGroupById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `bloods/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'BloodGroupData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createBloodGroup: builder.mutation({
      query: (newFormationType) => ({
        url: 'blood-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['BloodGroupData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateBloodGroup: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `bloods/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'BloodGroupData', id }, 'BloodGroupData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteBloodGroup: builder.mutation({
      query: (id) => ({
        url: `bloods/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BloodGroupData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetBloodGroupDataQuery,
  useLazyFetchDataOptionsForBloodGroupQuery,
  useGetBloodGroupByIdQuery,
  useLazyGetBloodGroupByIdQuery,
  useCreateBloodGroupMutation,
  useUpdateBloodGroupMutation,
  useDeleteBloodGroupMutation,
} = bloodGroupApi;
