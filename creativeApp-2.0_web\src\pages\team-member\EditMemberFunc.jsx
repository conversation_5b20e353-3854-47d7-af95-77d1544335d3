import React, { useEffect, useState } from 'react';

import useFetchData from './../../common/fetchData/useFetchData';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditMember = ({ isVisible, setVisible, userId }) => {
    const token = localStorage.getItem('token');

    // States for form fields
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [selectedTeams, setSelectedTeams] = useState({});
    const [selectedDepartments, setSelectedDepartments] = useState([]);
    const [selectedSchedules, setSelectedSchedules] = useState([]);
    const [selectedDesignations, setSelectedDesignations] = useState('');
    const [selectedResourceTypes, setSelectedResourceTypes] = useState('');
    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);
    const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);
    const [selectedContactTypes, setSelectedContactTypes] = useState([]);
    const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);
    const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);
    const [selectedBranches, setSelectedBranches] = useState([]);
    const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);

    const [successMessage, setSuccessMessage] = useState('');
    const [error, setError] = useState('');

    // Assuming data fetching hooks are correctly implemented
    const { data: roles, loading: rolesLoading } = useFetchData(`${API_URL}/roles`, token);
    const { data: teams, loading: teamsLoading } = useFetchData(`${API_URL}/teams`, token);
    const { data: departments, loading: departmentsLoading } = useFetchData(`${API_URL}/departments`, token);
    const { data: designations, loading: designationsLoading } = useFetchData(`${API_URL}/designations`, token);
    const { data: resourceTypes, loading: resourceTypesLoading } = useFetchData(`${API_URL}/resource_types`, token);
    const { data: resourceStatuses, loading: resourceStatusesLoading } = useFetchData(`${API_URL}/resource_statuses`, token);
    const { data: billingStatuses, loading: billingStatusesLoading } = useFetchData(`${API_URL}/billing_statuses`, token);
    const { data: contactTypes, loading: contactTypesLoading } = useFetchData(`${API_URL}/contact_types`, token);
    const { data: availableStatuses, loading: availableStatusesLoading } = useFetchData(`${API_URL}/available_statuses`, token);
    const { data: memberStatuses, loading: memberStatusesLoading } = useFetchData(`${API_URL}/member_statuses`, token);
    const { data: branches, loading: branchesLoading } = useFetchData(`${API_URL}/branches`, token);
    const { data: onsiteStatuses, loading: onsiteStatusesLoading } = useFetchData(`${API_URL}/onsite_statuses`, token);
    const { data: schedules, loading: schedulesLoading } = useFetchData(`${API_URL}/schedules`, token);
    const { data: memberData, loading: memberDataLoading } = useFetchData(`${API_URL}/users/${userId}`, token);

    // Update form with member data
    useEffect(() => {
        if (memberData) {
            setEid(memberData.eid || '');
            setEmail(memberData.email || '');
            setSelectedRoles(mapToSelectedState(memberData.roles || []));
            setSelectedTeams(mapToSelectedState(memberData.teams || []));
            setSelectedDepartments(memberData.departments ? memberData.departments.map(department => department.id) : []);
            setSelectedSchedules(memberData.schedules ? memberData.schedules.map(schedule => schedule.id) : []);
            setSelectedDesignations(memberData.designation || ''); // assuming designation is a string
            setSelectedResourceTypes(memberData.resourceType || ''); // assuming resourceType is a string
            setSelectedAvailableStatuses(memberData.availableStatuses || []);
            setSelectedBillingStatuses(memberData.billingStatuses || []);
            setSelectedBranches(memberData.branches || []);
            setSelectedContactTypes(memberData.contactTypes || []);
            setSelectedMemberStatuses(memberData.memberStatuses || []);
            setSelectedOnsiteStatuses(memberData.onsiteStatuses || []);
        }
    }, [memberData]);

    // Utility function to map selected data
    const mapToSelectedState = (data = []) => {
        const map = {};
        data.forEach(item => map[item.id] = true);
        return map;
    };

    // Handlers for selection changes (Checkboxes and Radio buttons)
    const handleRoleChange = (roleId) => {
        setSelectedRoles(prev => ({ ...prev, [roleId]: !prev[roleId] }));
    };

    const handleTeamChange = (teamId) => {
        setSelectedTeams(prev => ({ ...prev, [teamId]: !prev[teamId] }));
    };

    const handleDepartmentChange = (departmentId) => {
        setSelectedDepartments(prev => prev.includes(departmentId)
            ? prev.filter(id => id !== departmentId)
            : [...prev, departmentId]);
    };

    const handleScheduleChange = (scheduleId) => {
        setSelectedSchedules(prev => prev.includes(scheduleId)
            ? prev.filter(id => id !== scheduleId)
            : [...prev, scheduleId]);
    };

    const handleDesignationChange = (designationId) => {
        setSelectedDesignations(designationId);
    };

    const handleResourceTypeChange = (resourceTypeId) => {
        setSelectedResourceTypes(resourceTypeId);
    };

    const handleResourceStatusChange = (statusId) => {
        setSelectedResourceStatuses(prev => prev.includes(statusId)
            ? prev.filter(id => id !== statusId)
            : [...prev, statusId]);
    };

    const handleBillingStatusChange = (statusId) => {
        setSelectedBillingStatuses(prev => prev.includes(statusId)
            ? prev.filter(id => id !== statusId)
            : [...prev, statusId]);
    };

    const handleContactTypeChange = (contactTypeId) => {
        setSelectedContactTypes(prev => prev.includes(contactTypeId)
            ? prev.filter(id => id !== contactTypeId)
            : [...prev, contactTypeId]);
    };

    const handleAvailableStatusChange = (statusId) => {
        setSelectedAvailableStatuses(prev => prev.includes(statusId)
            ? prev.filter(id => id !== statusId)
            : [...prev, statusId]);
    };

    const handleMemberStatusChange = (statusId) => {
        setSelectedMemberStatuses(prev => prev.includes(statusId)
            ? prev.filter(id => id !== statusId)
            : [...prev, statusId]);
    };

    const handleBranchChange = (branchId) => {
        setSelectedBranches(prev => prev.includes(branchId)
            ? prev.filter(id => id !== branchId)
            : [...prev, branchId]);
    };

    const handleOnsiteStatusChange = (statusId) => {
        setSelectedOnsiteStatuses(prev => prev.includes(statusId)
            ? prev.filter(id => id !== statusId)
            : [...prev, statusId]);
    };

    // Submit handler
    const handleSubmit = async (event) => {
        event.preventDefault();
        const payload = {
            eid,
            email,
            availableStatuses: selectedAvailableStatuses,
            branches: selectedBranches,
            billingStatuses: selectedBillingStatuses,
            contactTypes: selectedContactTypes,
            departments: selectedDepartments,
            designations: selectedDesignations,
            memberStatuses: selectedMemberStatuses,
            onsiteStatuses: selectedOnsiteStatuses,
            roles: Object.keys(selectedRoles).filter(id => selectedRoles[id]),
            resourceTypes: selectedResourceTypes,
            resourceStatuses: selectedResourceStatuses,
            schedules: selectedSchedules,
            teams: Object.keys(selectedTeams).filter(id => selectedTeams[id]),
        };

        try {
            const response = await fetch(`${API_URL}/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                throw new Error('Failed to update member: ' + response.statusText);
            }

            const result = await response.json();
            setSuccessMessage(`Member "${result.eid}" updated successfully!`);
            setTimeout(() => {
                setSuccessMessage('');
            }, 2000);
        } catch (error) {
            setError(error.message);
        }
    };

    // Display loading state if data is still being fetched 
    if (!isVisible || rolesLoading || teamsLoading || departmentsLoading || designationsLoading || resourceTypesLoading || resourceStatusesLoading || billingStatusesLoading || contactTypesLoading || availableStatusesLoading || memberStatusesLoading || branchesLoading || onsiteStatusesLoading || schedulesLoading || memberDataLoading) { 
        return <div>Loading...</div>; 
    }

    // Render Options (Checkboxes and Radio Buttons)
    const renderOptions = (options, selectedValues, handleChange, type = 'checkbox') => {
        if (!Array.isArray(options)) return null;

        return options.map(option => (
            <div key={option.id} className="mb-2">
                {type === 'checkbox' ? (
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            value={option.id}
                            checked={selectedValues.includes(option.id)}
                            onChange={() => handleChange(option.id)}
                            className="mr-2"
                        />
                        {option.name}
                    </label>
                ) : type === 'radio' ? (
                    <label className="flex items-center">
                        <input
                            type="radio"
                            value={option.id}
                            checked={selectedValues === option.id}
                            onChange={() => handleChange(option.id)}
                            className="mr-2"
                        />
                        {option.name}
                    </label>
                ) : (
                    <option value={option.id}>{option.name}</option>
                )}
            </div>
        ));
    };

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50 overflow-hidden"
            onClick={() => setVisible(false)}
        >
            <div className="relative bg-white rounded-lg shadow-lg w-full max-w-6xl p-5 overflow-y-auto h-[80vh] mt-10"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                    <h4 className="text-2xl text-left font-semibold mb-6">Edit Onboard Team Member</h4>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className='flex flex-wrap gap-4'>
                        {/* EID */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label htmlFor="eid" className="block text-sm font-medium text-gray-700 pb-4">EID</label>
                            <input
                                type="text"
                                id="eid"
                                value={eid}
                                onChange={(e) => setEid(e.target.value)}
                                className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>
    
                        {/* Email */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 pb-4">Email</label>
                            <input
                                type="email"
                                id="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>
    
                        {/* Roles (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Roles</label>
                            {renderOptions(roles, selectedRoles, handleRoleChange)}
                        </div>
    
                        {/* Teams (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Teams</label>
                            {renderOptions(teams, selectedTeams, handleTeamChange)}
                        </div>
    
                        {/* Departments (Radio Buttons) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Departments</label>
                            {renderOptions(departments, selectedDepartments, handleDepartmentChange, 'radio')}
                        </div>
    
                        {/* Schedules (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Schedules</label>
                            {renderOptions(schedules, selectedSchedules, handleScheduleChange)}
                        </div>
    
                        {/* Designations (Select Dropdown) */}
                        <div className="mb-4 w-full md:max-w-1/4 text-left">
                            <label htmlFor="designations" className="block text-sm font-medium text-gray-700 pb-4">Designation</label>
                            <select
                                id="designations"
                                value={selectedDesignations}
                                onChange={(e) => handleDesignationChange(e.target.value)}
                                className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select Designation</option>
                                {renderOptions(designations, selectedDesignations, handleDesignationChange)}
                            </select>
                        </div>
    
                        {/* Resource Types (Select Dropdown) */}
                        <div className="mb-4 w-full md:max-w-1/4 text-left">
                            <label htmlFor="resourceTypes" className="block text-sm font-medium text-gray-700 pb-4">Resource Type</label>
                            <select
                                id="resourceTypes"
                                value={selectedResourceTypes}
                                onChange={(e) => handleResourceTypeChange(e.target.value)}
                                className="py-3 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            >
                                <option value="">Select Resource Type</option>
                                {renderOptions(resourceTypes, selectedResourceTypes, handleResourceTypeChange)}
                            </select>
                        </div>
    
                        {/* Resource Statuses (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Resource Statuses</label>
                            {renderOptions(resourceStatuses, selectedResourceStatuses, handleResourceStatusChange)}
                        </div>
    
                        {/* Billing Statuses (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Billing Statuses</label>
                            {renderOptions(billingStatuses, selectedBillingStatuses, handleBillingStatusChange)}
                        </div>
    
                        {/* Contact Types (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Contact Types</label>
                            {renderOptions(contactTypes, selectedContactTypes, handleContactTypeChange)}
                        </div>
    
                        {/* Available Statuses (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Available Statuses</label>
                            {renderOptions(availableStatuses, selectedAvailableStatuses, handleAvailableStatusChange)}
                        </div>
    
                        {/* Member Statuses (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Member Statuses</label>
                            {renderOptions(memberStatuses, selectedMemberStatuses, handleMemberStatusChange)}
                        </div>
    
                        {/* Branches (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Branches</label>
                            {renderOptions(branches, selectedBranches, handleBranchChange)}
                        </div>
    
                        {/* Onsite Statuses (Checkboxes) */}
                        <div className='mb-4 w-full md:max-w-1/4 text-left'>
                            <label className="block mb-2">Onsite Statuses</label>
                            {renderOptions(onsiteStatuses, selectedOnsiteStatuses, handleOnsiteStatusChange)}
                        </div>
                    </div>
    
                    {/* Success and Error Messages */}
                    {successMessage && <div style={{ color: 'green' }}>{successMessage}</div>}
                    {error && <div style={{ color: 'red' }}>{error}</div>}
    
                    <button
                        type="submit"
                        className="bg-primary text-white hover:bg-secondary rounded-md px-4 py-3 w-full"
                    >
                        Update Member
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditMember;
