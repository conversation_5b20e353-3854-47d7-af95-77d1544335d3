import React, { useEffect, useState, useRef } from "react";

export const ManageColumns = ({ columns, setColumns }) => {
  // <PERSON>le click outside to close the dropdown Manage Columns
  const [isOpen, setIsOpen] = useState(false);
  const manageColumnsRef = useRef(null);
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        manageColumnsRef.current &&
        !manageColumnsRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleSelection = (id) => {
    setColumns((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, omit: !item.omit } : item
      )
    );
  };

  return (
    <>
      <div className="w-auto " ref={manageColumnsRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm text-primary transition duration-500 ease-in-out font-medium text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary hover:bg-primarySeafoam hover:shadow-md focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <span className="mx-2 text-sm ">Manage Columns</span>
          <span className="material-symbols-outlined ">
            {isOpen ? "keyboard_arrow_up" : "keyboard_arrow_down"}
          </span>
        </button>
        {isOpen && (
          <div className="absolute w-[420px]  z-[100] mt-2 p-0 border rounded-lg shadow-2xl bg-neutral-100 overflow-hidden">
            <div className="grid grid-cols-2 gap-2 p-4 h-auto overflow-y-auto bg-neutral-50">
              {columns.map((item) => {
                return (
                  item.omit !== undefined && (
                    <SelectItem
                      key={item.id}
                      item={item}
                      onToggle={toggleSelection}
                    />
                  )
                );
              })}
            </div>
            <div className="flex justify-start border-t border-gray-200 px-4">
              <button
                className="flex w-auto h-[30px] text-center my-3 justify-center items-center py-1 px-4 text-sm transition duration-500 ease-in-out bg-primaryGold font-medium text-yellow-800 focus:outline-none rounded-full hover:bg-secondaryOrange hover:text-yellow-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                onClick={() => {
                  setColumns((prev) =>
                    prev.map((item) =>
                      item.omit ? { ...item, omit: false } : item
                    )
                  );
                }}
              >
                <span className="material-symbols-outlined me-1  text-sm">
                  rule
                </span>
                Select All
              </button>
              <button
                className="flex w-auto h-[30px] ms-2 text-center my-3 justify-center items-center py-1 px-4 text-sm font-medium transition duration-500 ease-in-out focus:outline-none bg-red-600 rounded-full border border-red-700 text-white hover:bg-red-700 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                onClick={() => setIsOpen(!isOpen)}
              >
                <span className="material-symbols-outlined  me-1 text-sm">
                  close
                </span>
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

function SelectItem({ item, onToggle }) {
  return (
    <label className="flex items-center cursor-pointer manage-column">
      <input
        type="checkbox"
        className={`w-4 h-4 flex items-center justify-center border rounded ${
          !item.omit ? "bg-primary border-primary" : "border-gray-400"
        }`}
        checked={!item.omit}
        onChange={() => onToggle(item.id)}
      />
      <div>{item.omit}</div>

      <span className="text-slate-800 pl-2 text-left text-sm">{item.name}</span>
    </label>
  );
}
