import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FetchResetPassword } from './../../common/fetchData/FetchLogin'; // Assuming you have this fetch function
import BgImg from './../../assets/images/login-bg.png';

export default function UpdatePassword() {
    const location = useLocation();
    const navigate = useNavigate();

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [passwordConfirmation, setPasswordConfirmation] = useState('');
    const [error, setError] = useState('');
    const [isPasswordReset, setIsPasswordReset] = useState(false); // For reset password state
    const [showSuccessMessage, setShowSuccessMessage] = useState(false); // Show success message after password reset
    const [isPasswordVisible, setIsPasswordVisible] = useState(false);
    const [isConfirmationVisible, setIsConfirmationVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    // Extract email from the query string when the component mounts
    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const emailFromUrl = queryParams.get('email');
        
        if (emailFromUrl) {
            setEmail(emailFromUrl);
        } else {
            setError('Invalid email.');
        }
    }, [location]);

    const handleResetPassword = async (e) => {
        e.preventDefault();

        setLoading(true);

        setError(''); // Clear any previous error
    
        // Validate password confirmation
        if (password !== passwordConfirmation) {
            setError('Passwords do not match!');
            return;
        }
    
        // Extract token from URL path
        const pathParts = location.pathname.split('/'); // Split the path by '/'
        const tokenFromUrl = pathParts[pathParts.length - 1]; // Token is the last part of the path
        
        // Email from query params
        const queryParams = new URLSearchParams(location.search);
        const emailFromUrl = queryParams.get('email');
    
        console.log('Token from URL:', tokenFromUrl); // Log the token from URL
        console.log('Email from URL:', emailFromUrl); // Log the email from URL
    
        // Ensure that token and email are available
        if (!tokenFromUrl || !emailFromUrl) {
            setError('Invalid token or email.');
            return;
        }
    
        try {
            // Call the FetchResetPassword function with the token and email
            const data = await FetchResetPassword(emailFromUrl, password, passwordConfirmation, tokenFromUrl);
            
            // Show success message after successful password reset
            setShowSuccessMessage(true);

            setLoading(false);
            
            // Redirect to login page after a brief delay
            setTimeout(() => {
                navigate('/login'); // Redirect to login page
            }, 2000); // Wait for 2 seconds before redirecting
            
        } catch (err) {
            console.error("Error during password reset:", err);

            // Check if the error indicates an expired or invalid token
            if (err.message && err.message.includes('expired')) {
                setError(
                    <div>
                        <p>The password reset link has expired.</p>
                        <p>Please <a href="/reset-password" className="text-blue-500">request a new reset link</a>.</p>
                    </div>
                );
            } else {
                setError('Failed to reset password. Please try again.');
            }
        }
    };

    return (
        <section className="bg-gray-50 dark:bg-gray-900">
            <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
                <div className="w-full bg-white rounded-3xl shadow dark:border md:mt-0 sm:max-w-6xl xl:p-0 dark:bg-gray-800 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-center gap-12">
                    <div className="p-6 space-y-4 md:space-y-6 sm:p-8 w-full sm:w-1/2 text-left">
                        <h6 className='text-base text-gray-600 pb-1'>{isPasswordReset ? 'Your password has been reset!' : 'Reset Your Password'}</h6>
                        <h1 className="text-2xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white mt-0">
                            {isPasswordReset ? 'Your password has been reset!' : 'Update Your Password'}
                        </h1>

                        {error && <p className="text-red-500">{error}</p>}

                        {showSuccessMessage && (
                            <p className="text-green-500 font-medium">
                                Your password has been successfully updated! Redirecting to login...
                            </p>
                        )}

                        <form className="space-y-4 md:space-y-6" onSubmit={handleResetPassword}>
                            <div className='relative'>
                                <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">New Password</label>
                                <input
                                    type={isPasswordVisible ? "text" : "password"} // Toggle password visibility
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    placeholder="Enter your new password"
                                    required
                                />
                                {/* Visibility icon for new password */}
                                <span
                                    onClick={() => setIsPasswordVisible(!isPasswordVisible)} // Toggle the password visibility
                                    className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400 cursor-pointer"
                                >
                                    {isPasswordVisible ? 'visibility_off' : 'visibility'} {/* Toggle icon based on visibility state */}
                                </span>
                            </div>

                            <div className='relative'>
                                <label htmlFor="password_confirmation" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirm Password</label>
                                <input
                                    type={isConfirmationVisible ? "text" : "password"} // Toggle password visibility for confirmation
                                    id="password_confirmation"
                                    value={passwordConfirmation}
                                    onChange={(e) => setPasswordConfirmation(e.target.value)}
                                    className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    placeholder="Confirm your new password"
                                    required
                                />
                                {/* Visibility icon for confirmation password */}
                                <span
                                    onClick={() => setIsConfirmationVisible(!isConfirmationVisible)} // Toggle the confirmation password visibility
                                    className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400 cursor-pointer"
                                >
                                    {isConfirmationVisible ? 'visibility_off' : 'visibility'} {/* Toggle icon based on visibility state */}
                                </span>
                            </div>

                            <button
                                type="submit"
                                className="w-full text-white bg-secondaryOrange hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-xl text-sm px-5 py-3 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            >
                                {loading ? 'Updating...' : 'Update Password'}
                            </button>
                        </form>
                    </div>
                    <div className='bg-primary rounded-3xl w-full sm:w-1/2 my-6 mr-6'>
                        <h2 className='text-white text-4xl font-bold pt-16 px-12 pb-8 leading-normal text-left'>Get back to work with a fresh start!</h2>
                        <img src={BgImg} alt="Background" />
                    </div>
                </div>
            </div>
        </section>
    );
}
