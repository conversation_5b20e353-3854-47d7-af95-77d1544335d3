import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddBranch = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [branches, setBranches] = useState([]);
    const [branchName, setBranchName] = useState('');
    const [selectedLocation, setSelectedLocation] = useState('');
    const [locations, setLocations] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [loading, setLoading] = useState(false);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    const fetchUsers = async () => {
        const token = localStorage.getItem('token');
        if (!token) {
            return;
        }
    
        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Failed to fetch user details');
            }
    
            const userData = await response.json();

    
            // Store user details in localStorage
            localStorage.setItem('fname', userData.first_name);
            localStorage.setItem('lname', userData.last_name);
    
        } catch (error) {
            console.error('Error fetching user details:', error);
        }
    };
    

    // Call fetchUsers after login or page load
    useEffect(() => {
        fetchUsers();
    }, []);

    // Fetch branches and locations on load
    useEffect(() => {
        const fetchBranches = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/branches`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setBranches(data['branches'] || []);
            } catch (error) {
                setError(error.message);
            }
        };

        const fetchLocations = async () => {
            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/locations`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch locations');
                }

                const data = await response.json();
                console.log('Locations:', data);

                // Ensure that the data returned has locations as expected
                setLocations(data.locations || []); // Adjust if needed based on the actual response
            } catch (error) {
                setError(error.message);
            }
        };

        fetchBranches();
        fetchLocations();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedBranchName = branchName.trim();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        // Check if the branch already exists
        if (Array.isArray(branches)) {
            const branchExists = branches.some(branch => {
                const branchNameLower = branch.name.toLowerCase().trim();
                return branchNameLower === trimmedBranchName.toLowerCase();
            });
    
            if (branchExists) {
                setError('Branch already exists. Please add a different branch.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
    
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            // Create the branch
            const response = await fetch(`${API_URL}/branches`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedBranchName,
                    location_id: selectedLocation,  // Send the selected location ID here
                    created_by: createdBy,
                }),
            });
    
            if (!response.ok) {
                console.error('Failed to save branch:', response.statusText);
                throw new Error('Failed to save branch: ' + response.statusText);
            }
    
            const result = await response.json();
    
            //setSuccessMessage(`Branch "${result.name || trimmedBranchName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Office branch added successfully.',
            });

            setBranchName('');
            setSelectedLocation(''); // Reset selected location
    
            // Refetch branches
            const newBranchesResponse = await fetch(`${API_URL}/branches`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newBranchesResponse.ok) {
                throw new Error('Failed to fetch branches: ' + newBranchesResponse.statusText);
            }
    
            const newBranchesData = await newBranchesResponse.json();
            setBranches(newBranchesData['branches'] || []);
    
        } catch (error) {
            alertMessage('error');
            console.error('Error during submission:', error); // Debugging line
        }
    };
    

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
           
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Branch</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        
                        <div className="mb-4">
                            <label htmlFor="location" className="block text-sm font-medium text-gray-700 pb-4">
                                Select Location
                            </label>
                            <select
                                id="location"
                                value={selectedLocation}
                                onChange={(e) => setSelectedLocation(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select a Location</option>
                                {locations.length === 0 ? (
                                    <option disabled>No locations available</option>
                                ) : (
                                    locations.map((location) => (
                                        <option key={location.id} value={location.id}>
                                            {location.locations_name} {/* Corrected field name */}
                                        </option>
                                    ))
                                )}
                            </select>
                        </div>

                        <div className="mb-4">
                            <label htmlFor="branchName" className="block text-sm font-medium text-gray-700 pb-4">
                                Branch Name
                            </label>
                            <input
                                type="text"
                                id="branchName"
                                value={branchName}
                                onChange={(e) => setBranchName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>

                        <div className='text-left p-6'>
                            <button
                                type="submit"
                                className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            >
                                <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                {loading ? 'Adding branch...' : 'Add Branch'}
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddBranch;
