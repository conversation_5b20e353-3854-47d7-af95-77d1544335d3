import React, { useState, useEffect } from 'react';

const PasswordGenerator = ({ onPasswordGenerated }) => {
  const [password, setPassword] = useState('xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn');
  const [length, setLength] = useState(64);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true
  });
  const [strength, setStrength] = useState('Strong Password');

  const generatePassword = () => {
    let charset = '';
    if (options.uppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (options.lowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (options.numbers) charset += '0123456789';
    if (options.symbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (charset === '') {
      setPassword('');
      return;
    }

    let newPassword = '';
    for (let i = 0; i < length; i++) {
      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    setPassword(newPassword);
    
    // Calculate strength
    const newStrength = calculatePasswordStrength(newPassword);
    setStrength(newStrength);
    
    // Notify parent component
    if (onPasswordGenerated) {
      onPasswordGenerated(newPassword, newStrength);
    }
  };

  const calculatePasswordStrength = (pwd) => {
    let score = 0;
    
    // Length check
    if (pwd.length >= 12) score += 2;
    else if (pwd.length >= 8) score += 1;
    
    // Character variety checks
    if (/[a-z]/.test(pwd)) score += 1;
    if (/[A-Z]/.test(pwd)) score += 1;
    if (/[0-9]/.test(pwd)) score += 1;
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;
    
    // Additional complexity
    if (pwd.length >= 16) score += 1;
    
    if (score >= 6) return 'Strong Password';
    if (score >= 4) return 'Moderate Password';
    return 'Weak Password';
  };

  const getStrengthColor = () => {
    switch (strength) {
      case 'Strong Password':
        return 'text-green-600';
      case 'Moderate Password':
        return 'text-yellow-600';
      case 'Weak Password':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(password);
    // You could add a toast notification here
  };

  const handleOptionChange = (option) => {
    const newOptions = { ...options, [option]: !options[option] };
    setOptions(newOptions);
  };

  const handleLengthChange = (e) => {
    const newLength = parseInt(e.target.value);
    setLength(newLength);
  };

  // Auto-generate when options change
  useEffect(() => {
    generatePassword();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [length, options]);

  return (
    <div className="bg-white dark:bg-gray-900 p-6 rounded-lg">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Password Manager</h2>
      
      {/* Generated Password Display */}
      <div className="mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <input
            type="text"
            value={password}
            readOnly
            className="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-900 dark:text-white font-mono"
          />
          <button
            onClick={copyToClipboard}
            className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-md text-sm font-medium transition-colors"
          >
            Copy Password
          </button>
        </div>
        <div className="flex items-center justify-between">
          <span className={`text-sm font-medium ${getStrengthColor()}`}>
            {strength}
          </span>
          <button
            onClick={generatePassword}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
          >
            <span className="material-symbols-rounded text-sm mr-1">refresh</span>
            Generate New
          </button>
        </div>
      </div>

      {/* Password Length Slider */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Password Length:
          </label>
          <span className="text-sm text-gray-600 dark:text-gray-400">{length}</span>
        </div>
        <input
          type="range"
          min="4"
          max="128"
          value={length}
          onChange={handleLengthChange}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>4 characters</span>
          <span>*Between 4 and 128 characters</span>
        </div>
      </div>

      {/* Character Options */}
      <div className="space-y-3">
        <div className="flex items-center">
          <input
            id="uppercase"
            type="checkbox"
            checked={options.uppercase}
            onChange={() => handleOptionChange('uppercase')}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="uppercase" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Uppercase (A-Z)
          </label>
        </div>

        <div className="flex items-center">
          <input
            id="lowercase"
            type="checkbox"
            checked={options.lowercase}
            onChange={() => handleOptionChange('lowercase')}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="lowercase" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Lowercase (a-z)
          </label>
        </div>

        <div className="flex items-center">
          <input
            id="numbers"
            type="checkbox"
            checked={options.numbers}
            onChange={() => handleOptionChange('numbers')}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="numbers" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Number (0-9)
          </label>
        </div>

        <div className="flex items-center">
          <input
            id="symbols"
            type="checkbox"
            checked={options.symbols}
            onChange={() => handleOptionChange('symbols')}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="symbols" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            Symbols (!@#$%^&*)
          </label>
        </div>
      </div>
    </div>
  );
};

export default PasswordGenerator;
