import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import RevisionTypeList from '../../pages/revisiontype/RevisionTypeList'; // Updated import to TaskTypeList

const RevisionType = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-revision-type" buttonName="Add Revision Type" />
        <RevisionTypeList /> 
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default RevisionType;
