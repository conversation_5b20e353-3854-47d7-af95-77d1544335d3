import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent'; // Keep the TableContent import
import EditResourceStatus from './EditResourceStatus'; // Make sure to create and import the EditResourceStatus component

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ResourceStatusList = () => {
    const [resourceStatuses, setResourceStatuses] = useState([]); // Store resource statuses here
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedResourceStatusId, setSelectedResourceStatusId] = useState(null);
    const [error, setError] = useState(null);

    // Define column names for resource statuses (adjust if necessary)
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Resource Status Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchResourceStatuses = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');

    
            try {
                const response = await fetch(`${API_URL}/resource_statuses`, { // Adjusted API endpoint for resource statuses
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
               
    
                // Access resource statuses with bracket notation (adjusted for resource statuses)
                if (Array.isArray(data.resource_status)) {
                    setResourceStatuses(data.resource_status.map(status => ({
                        id: status.id,
                        name: status.name,
                        created_by: status.created_by,
                        updated_by: status.updated_by,
                    })));
                } else {
                    setError('Invalid data format: resource statuses is not an array.');
                }
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchResourceStatuses();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/resource_statuses/${id}`, { // Updated endpoint for resource statuses
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete resource status: ' + response.statusText);
            }

            // Update the resource statuses list after deletion
            setResourceStatuses(prevStatuses => prevStatuses.filter(status => status.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedResourceStatusId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={resourceStatuses}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedResourceStatusId} // Updated for resource status
            />
            {modalVisible && (
                <EditResourceStatus
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    resourceStatusId={selectedResourceStatusId} // Updated for resource status
                />
            )}
        </div>
    );
};

export default ResourceStatusList;
