import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useFetchApiData from '../../../common/fetchData/useFetchApiData';
import { alertMessage } from '../../../common/coreui';
import { API_URL } from '../../../common/fetchData/apiConfig'; 

const AddReviewRelease = ({ isVisible, setVisible }) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [reviewReleaseName, setReviewReleaseName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) setLoggedInUser(userId);
    }, []);

    const token = localStorage.getItem('token');
    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);

    useEffect(() => {
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
    }, [departmentsData]);

    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam('');

        const department = departments.find(dep => dep.name === departmentName);
        if (department?.teams?.length > 0) {
            setTeams(department.teams);
        } else {
            setTeams([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!selectedDepartment || !selectedTeam || !reviewReleaseName) {
            setError('Please fill all fields.');
            return;
        }

        setError('');

        const department = departments.find(dep => dep.name === selectedDepartment);
        const team = teams.find(t => t.name === selectedTeam);

        if (!department || !team) {
            setError('Invalid department or team selection.');
            return;
        }

        try {
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            if (!loggedInUser) {
                setError('User is not logged in.');
                return;
            }

            const response = await fetch(`${API_URL}review`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department_id: department.id,
                    team_id: team.id,
                    name: reviewReleaseName,
                    created_by: loggedInUser,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to add review release.');
            }

            const result = await response.json();
            alertMessage('success');
            setReviewReleaseName('');
            //setSuccessMessage('Review release added successfully!');

            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: 'Review & Release added successfully.',
            });


        } catch (err) {
            alertMessage({
                icon: 'warning',
                title: 'Failed!',
                text: 'Failed to add the Review & Release!',
            });
        }
    };

    if (!isVisible) return null;

    return (
        <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
            <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-auto mt-10">
                <button onClick={() => setVisible(false)} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                    &times;
                </button>
                <h4 className="text-xl font-semibold mb-4 py-4">Add New Review Release</h4>
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                            Select Department
                        </label>
                        <select
                            id="department"
                            value={selectedDepartment}
                            onChange={handleDepartmentChange}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            required
                        >
                            <option value="">Select a Department</option>
                            {departments.length === 0 ? (
                                <option disabled>No departments available</option>
                            ) : (
                                departments.map(dept => (
                                    <option key={dept.id} value={dept.name}>{dept.name}</option>
                                ))
                            )}
                        </select>
                    </div>

                    <div className="mb-4">
                        <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                            Select Team
                        </label>
                        <select
                            id="team"
                            value={selectedTeam}
                            onChange={(e) => setSelectedTeam(e.target.value)}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            required
                        >
                            <option value="">Select a Team</option>
                            {teams.length === 0 ? (
                                <option disabled>No teams available</option>
                            ) : (
                                teams.map(team => (
                                    <option key={team.id} value={team.name}>{team.name}</option>
                                ))
                            )}
                        </select>
                    </div>

                    <div className="mb-4">
                        <label htmlFor="reviewReleaseName" className="block text-sm font-medium text-gray-700 pb-4">
                            Review Release Name
                        </label>
                        <input
                            id="reviewReleaseName"
                            type="text"
                            value={reviewReleaseName}
                            onChange={(e) => setReviewReleaseName(e.target.value)}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            required
                        />
                    </div>

                    <div className="py-4">
                        <button
                            type="submit"
                            className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                        >
                            Add Review Release
                        </button>
                    </div>

                    {error && <p className="text-red-500 text-sm">{error}</p>}
                    {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                </form>
            </div>
        </div>
    );
};

export default AddReviewRelease;
