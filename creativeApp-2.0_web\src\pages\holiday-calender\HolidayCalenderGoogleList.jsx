import React, { useState, useEffect } from "react";
import Select from "react-select";
import moment from "moment";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { format, parse, startOfWeek, getDay } from "date-fns";
import "tailwindcss/tailwind.css";

const locales = {
  "en-US": require("date-fns/locale/en-US"),
};
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const getCurrentYear = () => new Date().getFullYear();

const customStyles = {
  menu: (provided) => ({
    ...provided,
    zIndex: 9999,
  }),
  menuPortal: (base) => ({ ...base, zIndex: 9999 }),
  control: (provided) => ({
    ...provided,
    width: "100%",
    minWidth: "100%",
  }),
};

const HOLIDAY_KEYWORDS = [
  "holiday",
  "new year's day",
  "independence day",
  "eid",
  "christmas",
  "puja",
  "shab",
  "martyrs' day",
  "bengali new year",
  "May Day",
];

const HolidayCalendarGoogleList = () => {
  const [regions, setRegions] = useState([]);
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedYear, setSelectedYear] = useState({
    value: getCurrentYear(),
    label: getCurrentYear().toString(),
  });
  const [holidays, setHolidays] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("calendar");

  const API_KEY =
    process.env.REACT_APP_GOOGLE_API_KEY ||
    "AIzaSyBuRzdDIOljoVutin7ygRZdRbcQSBfJlHY";

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const response = await fetch("https://restcountries.com/v3.1/all");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Check if data is an array
        if (!Array.isArray(data)) {
          console.error("API response is not an array:", data);
          throw new Error("Invalid API response format");
        }

        const countryList = data
          .map((country) => {
            if (!country || !country.cca2 || !country.name) {
              return null; // Skip invalid entries
            }

            let code = country.cca2.toLowerCase();
            if (code === "us") code = "usa";
            if (code === "gb") code = "uk";
            if (code === "au") code = "australian";
            if (code === "at") code = "austrian";
            if (code === "in") code = "indian";
            if (code === "br") code = "brazilian";
            if (code === "cn") code = "china";
            if (code === "ru") code = "russian";
            if (code === "es") code = "spain";

            return {
              value: `en.${code}`,
              label: country.name.common,
            };
          })
          .filter(Boolean) // Remove null entries
          .sort((a, b) => a.label.localeCompare(b.label));

        setRegions([{ value: "en.bd", label: "Bangladesh" }, ...countryList]);
        setSelectedRegion({ value: "en.bd", label: "Bangladesh" });
      } catch (err) {
        console.error("Failed to load countries:", err);
        // Fallback to a basic list if API fails
        const fallbackCountries = [
          { value: "en.bd", label: "Bangladesh" },
          { value: "en.usa", label: "United States" },
          { value: "en.uk", label: "United Kingdom" },
          { value: "en.indian", label: "India" },
          { value: "en.australian", label: "Australia" },
          { value: "en.canadian", label: "Canada" },
          { value: "en.german", label: "Germany" },
          { value: "en.french", label: "France" },
        ];
        setRegions(fallbackCountries);
        setSelectedRegion(fallbackCountries[0]);
      }
    };

    fetchRegions();
  }, []);

  const yearOptions = Array.from({ length: 10 }, (_, i) => {
    const year = getCurrentYear() - i;
    return { value: year, label: year.toString() };
  });

  useEffect(() => {
    if (!selectedRegion || !selectedYear) return;

    const fetchHolidays = async () => {
      setLoading(true);
      setError(null);
      setHolidays([]);

      try {
        if (!API_KEY) {
          throw new Error(
            "Google API Key is missing! Set it in your .env file."
          );
        }

        let calendarId = encodeURIComponent(
          `${selectedRegion.value}#<EMAIL>`
        );

        const calendarUrl = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events?key=${API_KEY}&timeMin=${selectedYear.value}-01-01T00:00:00Z&timeMax=${selectedYear.value}-12-31T23:59:59Z&orderBy=startTime&singleEvents=true`;

        const response = await fetch(calendarUrl);
        const data = await response.json();

        if (data.error) {
          throw new Error(data.error.message);
        }

        const formattedEvents = data.items.map((holiday) => {
          const isHoliday = HOLIDAY_KEYWORDS.some((keyword) =>
            holiday.summary.toLowerCase().includes(keyword)
          );
          return {
            id: holiday.id,
            title: holiday.summary,
            start: new Date(holiday.start.date || holiday.start.dateTime),
            end: new Date(holiday.start.date || holiday.start.dateTime),
            allDay: true,
            isHoliday,
            color: isHoliday ? "#ff6347" : "#32cd32",
          };
        });

        setHolidays(formattedEvents);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHolidays();
  }, [selectedRegion, selectedYear, API_KEY]);

  return (
    <div className="bg-white dark:bg-gray-900 p-4 rounded-xl">
      <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4">
        <div class="w-4/12 md:w-10/12 text-start">
          <h2 class="text-2xl font-bold ">Happenings</h2>
        </div>

        <button
          className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${
            activeTab === "calendar" ? "bg-primary text-white" : "bg-transparent text-primary border-2 border-primary"
          }`}
          onClick={() => setActiveTab("calendar")}
        >
          Calendar View
        </button>

        <button
           className={`w-[190px] h-[40px] text-center justify-center items-center py-2 px-4 text-sm font-medium flex focus:outline-none hover:bg-primary hover:text-white rounded-full border border-gray-200 transition duration-500 ease-in-out hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-slate-200 dark:focus:ring-slate-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 ${
            activeTab === "list" ? "bg-primary text-white" : "bg-transparent text-primary border-2 border-primary"
          }`}
          onClick={() => setActiveTab("list")}
        >
          Full Year Holidays
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="flex mb-4 w-full">
        <div class="w-8/12 flex items-end justify-end gap-1">
          <div className="w-full md:w-1/2">
            <label htmlFor="year" className="font-medium mb-2">
              Select a year:
            </label>
            <Select
              options={yearOptions}
              value={selectedYear}
              onChange={setSelectedYear}
              placeholder="Select a year"
              styles={customStyles}
              menuPortalTarget={document.body}
            />
          </div>

          <div className="w-full md:w-1/2">
            <label htmlFor="region" className="block font-medium mb-2">
              Select a country:
            </label>
            <Select
              options={regions}
              value={selectedRegion}
              onChange={setSelectedRegion}
              placeholder="Select a country"
              styles={customStyles}
              menuPortalTarget={document.body}
            />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center text-slate-500">Loading...</div>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : activeTab === "calendar" ? (
        <div className="bg-white p-4 rounded-lg shadow">
          <Calendar
            localizer={localizer}
            events={holidays}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 500, backgroundColor: "white" }}
            eventPropGetter={(event) => ({
              style: { backgroundColor: event.color, color: "#fff" },
            })}
          />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {holidays.map((holiday) => (
            <div
              key={holiday.id}
              className={`p-4 border rounded shadow-md ${
                holiday.isHoliday ? "bg-red-300" : "bg-green-300"
              }`}
            >
              <h3 className="text-lg font-semibold">{holiday.title}</h3>
              <p className="text-slate-600">{holiday.start.toDateString()}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default HolidayCalendarGoogleList;
