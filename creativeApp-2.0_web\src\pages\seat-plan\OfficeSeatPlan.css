/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  text-align: center;
}

/* Title */
.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

/* Tables Layout */
.tables-container {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

/* Table Box */
.table-box {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  border: 2px solid #ccc;
  width: 250px;
}

/* Table Title */
.table-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #444;
  text-transform: uppercase;
}

/* Seat Layout */
.seat-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

/* Chairs Row */
.chair-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

/* Chair */
.chair {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Chair Icon */
.chair-icon {
  font-size: 32px;
  color: #666;
}

/* Seat Number */
.seat-number {
  font-size: 14px;
  font-weight: bold;
  color: #222;
  margin-top: 4px;
}

/* Chair Text */
.chair-text {
  font-size: 12px;
  color: #555;
  margin-top: 4px;
  text-align: center;
}

/* Table Body (Desk Icon) */
.table-body {
  width: 100%;
  height: 50px;
  border: 4px solid #444;
  background: #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

/* Desk Icon */
.desk-icon {
  font-size: 40px;
  color: #333;
}

/* Desk Text */
.desk-text {
  font-size: 14px;
  font-weight: bold;
  color: #222;
  margin-top: 4px;
}
