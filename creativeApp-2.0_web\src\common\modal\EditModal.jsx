import React, { useEffect, useState } from 'react';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditModal = ({ isVisible, setVisible, teamId }) => {
    const [teamName, setTeamName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchTeam = async () => {
            if (teamId) {
                const token = localStorage.getItem('token');
                try {
                    const response = await fetch(`${API_URL}/teams/${teamId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (!response.ok) {
                        throw new Error('Failed to fetch team: ' + response.statusText);
                    }
        
                    const data = await response.json();

                    setTeamName(data.team.name); // Update this line to access team name correctly
                } catch (error) {
                    setError(error.message);
                }
            }
        };
            

        fetchTeam();
    }, [teamId]); // Re-fetch when teamId changes

    const handleSubmit = async (event) => {
        event.preventDefault();
        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/teams/${teamId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ name: teamName.trim() }),
            });

            if (!response.ok) {
                throw new Error('Failed to update team: ' + response.statusText);
            }

            const result = await response.json();
            setSuccessMessage(`Team "${result.name}" updated successfully!`);

            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);
        } catch (error) {
            setError(error.message);
        }
    };

    if (!isVisible) return null; // Don't render the modal if not visible

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Team</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Name</label>
                        <input
                            type="text"
                            id="name"
                            value={teamName}
                            onChange={(e) => setTeamName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-blue-700 text-white rounded-lg px-4 py-2"
                    >
                        Update Team
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditModal;
