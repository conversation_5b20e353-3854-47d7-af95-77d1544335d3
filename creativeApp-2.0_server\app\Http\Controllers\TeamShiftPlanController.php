<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use App\Models\Team_shift_plan;

class TeamShiftPlanController extends Controller
{
     /**
     * Display a listing of all priorities.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
{
    $teamShiftPlans = Team_shift_plan::all();

    // Log the team shift plans retrieved
    Log::info('All Team Shift Plans Retrieved:', ['team_shift_plans_count' => $teamShiftPlans->count()]);

    return response()->json(['shiftPlans' => $teamShiftPlans], 200);
}


    /**
     * Display the specified priority.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
{
    // Find the team shift plan by ID
    $teamShiftPlan = Team_shift_plan::find($id);

    if (!$teamShiftPlan) {
        return response()->json(['error' => 'Team Shift Plan not found.'], 404);
    }

    // Log the team shift plan retrieved
    Log::info('Team Shift Plan Retrieved:', ['team_shift_plan' => $teamShiftPlan]);

    return response()->json(['shiftPlan' => $teamShiftPlan], 200);
}

    /**
     * Create a new priority.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname,
        ]);

        // Validate the request data
        $request->validate([
            'department' => 'required|integer|max:255',
            'team' => 'required|integer|max:255',
            'week' => 'required|string|max:255',
            'eid' => 'required|integer|max:255',
            'shift' => 'required|integer|max:255',
        ]);

        // Log the request data
        Log::info('Create Team Shift Plan Request:', ['request' => $request->all()]);

        // Check if the team shift plan already exists for the same EID and week
        if (Team_shift_plan::where('eid', $request->eid)->where('week', $request->week)->exists()) {
            return response()->json(['error' => 'Team Shift Plan for this EID and week already exists.'], 409);
        }

        // Create a new team shift plan
        $teamShiftPlan = Team_shift_plan::create([
            'department' => $request->department,
            'team' => $request->team,
            'week' => $request->week,
            'eid' => $request->eid,
            'shift' => $request->shift,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,
        ]);

        // Log the created team shift plan
        Log::info('Team Shift Plan Created:', ['team_shift_plan' => $teamShiftPlan]);

        return response()->json([
            'message' => 'Team Shift Plan created successfully.',
            'team_shift_plan' => $teamShiftPlan,
        ], 201);
    }



    /**
     * Update an existing priority.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname,
        ]);
    
        // Log the update request details
        Log::info('Update Team Shift Plan Request:', ['request' => $request->all(), 'id' => $id]);
    
        // Validate the request data
        $request->validate([
            'department' => 'required|integer|max:255',
            'team' => 'required|integer|max:255',
            'week' => 'required|string|max:255',
            'eid' => 'required|integer|max:255',
            'shift' => 'required|integer|max:255',
        ]);
    
        // Find the team shift plan by ID
        $teamShiftPlan = Team_shift_plan::find($id);
    
        // Check if the team shift plan exists
        if (!$teamShiftPlan) {
            Log::warning('Team Shift Plan Not Found:', ['id' => $id]);
            return response()->json(['error' => 'Team Shift Plan not found.'], 404);
        }
    
        // Check if the updated EID and week combination already exists in another record
        if (Team_shift_plan::where('eid', $request->eid)
            ->where('week', $request->week)
            ->where('id', '!=', $id)
            ->exists()
        ) {
            return response()->json(['error' => 'Another Team Shift Plan for this EID and week already exists.'], 409);
        }
    
        // Update the team shift plan
        $teamShiftPlan->update([
            'department' => $request->department,
            'team' => $request->team,
            'week' => $request->week,
            'eid' => $request->eid,
            'shift' => $request->shift,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,
        ]);
    
        // Log the updated team shift plan
        Log::info('Team Shift Plan Updated:', ['team_shift_plan' => $teamShiftPlan]);
    
        return response()->json([
            'message' => 'Team Shift Plan updated successfully.',
            'team_shift_plan' => $teamShiftPlan,
        ], 200);
    }
    



    /**
     * Delete a priority.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
{
    // Get the authenticated user
    $authUser = request()->user();

    // Check if the user has the appropriate role
    if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
        // Find the team shift plan
        $teamShiftPlan = Team_shift_plan::findOrFail($id);

        // Delete the team shift plan
        $teamShiftPlan->delete();

        Log::info('Team Shift Plan Deleted:', ['team_shift_plan_id' => $id]);

        return response()->json(['message' => 'Team Shift Plan deleted successfully.'], 200);
    }

    // Deny access for other roles
    return response()->json(['error' => 'You do not have permission to delete this team shift plan.'], 403);
}

}
