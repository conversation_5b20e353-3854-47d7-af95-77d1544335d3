import React, { useState } from 'react';

function OneLineText2() {
  const [inputValue, setInputValue] = useState('');
  const [parsedPairs, setParsedPairs] = useState([]);
  const [outputValues, setOutputValues] = useState('');
  const [wordCount, setWordCount] = useState(0);

  const handleChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleProcessInput = () => {
    if (!inputValue.trim()) {
      setParsedPairs([]);
      setOutputValues('');
      setWordCount(0);
      return;
    }

    const lines = inputValue.split('\n');
    const titleCount = {};
    const pairs = [];

    lines.forEach((line) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      const parts = trimmedLine.split(/:(.+)/);
      if (parts.length >= 2) {
        const title = parts[0].trim();
        const rawValue = parts[1].trim();
        const value = rawValue.replace(/\s+/g, ' ');
        titleCount[title] = (titleCount[title] || 0) + 1;
        pairs.push({ title, value });
      }
    });

    const duplicates = new Set(Object.keys(titleCount).filter(t => titleCount[t] > 1));
    const values = pairs.map(p => p.value).join(' ').replace(/\s+/g, ' ').trim();
    const cleaned = values.replace(/[.,!?;:"'`~(){}\[\]]/g, '');
    const wordsArray = cleaned.trim().split(/\s+/).filter(Boolean);

    setParsedPairs(pairs.map(p => ({ ...p, isDuplicate: duplicates.has(p.title) })));
    setOutputValues(values);
    setWordCount(wordsArray.length);
  };

  const handleCopy = () => {
    if (outputValues) {
      navigator.clipboard
        .writeText(outputValues)
        .then(() => alert('Copied to clipboard!'))
        .catch((err) => {
          alert('Failed to copy!');
          console.error(err);
        });
    }
  };

  const handleClear = () => {
    setInputValue('');
    setParsedPairs([]);
    setOutputValues('');
    setWordCount(0);
  };

  return (
    <div className="max-w-xl mx-auto my-10 p-6 font-sans bg-white shadow rounded">
      <label htmlFor="titleValueInput" className="block mb-2 font-medium text-gray-700">
        Paste your <strong>Title: Value</strong> pairs here (Duplicate Title check):
      </label>
      <textarea
        id="titleValueInput"
        rows="6"
        value={inputValue}
        onChange={handleChange}
        placeholder="Example:\nName: John\nAge: 25\nCity: Dhaka"
        className="w-full p-3 text-sm border border-gray-300 rounded mb-4 focus:outline-none focus:ring focus:ring-blue-200"
      />
      <div className="mb-4">
        <button
          onClick={handleProcessInput}
          disabled={!inputValue.trim()}
          className="px-4 py-2 mr-3 text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Process
        </button>
        <button
          onClick={handleClear}
          disabled={!inputValue.trim()}
          className="px-4 py-2 text-white bg-gray-600 rounded hover:bg-gray-700 disabled:opacity-50"
        >
          Clear
        </button>
      </div>

      {parsedPairs.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Parsed Pairs:</h3>
          <ul className="space-y-2">
            {parsedPairs.map((pair, idx) => (
              <li
                key={idx}
                className={`px-4 py-2 rounded text-sm ${
                  pair.isDuplicate
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-200 text-gray-800'
                }`}
              >
                <strong>{pair.title}:</strong> {pair.value} {pair.isDuplicate && '⚠️'}
              </li>
            ))}
          </ul>

          <h3 className="text-lg font-semibold mt-6 mb-2">Output Values:</h3>
          <div className="bg-gray-100 p-3 rounded flex flex-wrap gap-2">
            {outputValues
              .split(' ')
              .filter(Boolean)
              .map((val, idx) => (
                <span
                  key={idx}
                  className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full shadow-sm"
                >
                  {val}
                </span>
              ))}
          </div>

          <p className="mt-3 text-sm">
            <strong>Word Count:</strong> {wordCount}
          </p>

          <button
            onClick={handleCopy}
            className="mt-3 px-4 py-2 text-white bg-green-600 rounded hover:bg-green-700"
          >
            Copy to Clipboard
          </button>
        </div>
      )}
    </div>
  );
}

export default OneLineText2;
