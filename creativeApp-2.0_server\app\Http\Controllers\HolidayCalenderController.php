<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Holiday_calender;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class HolidayCalenderController extends Controller
{
    /**
     * Fetch all holiday calendars with filters, sorting, and pagination.
     */

    public function index()
    {
        $holidayCalenders = Holiday_calender::with(['teams', 'departments'])->get();

        // Log the holiday calender retrieved
        Log::info('All holiday calenders Retrieved:', ['holidayCalenders' => $holidayCalenders->count()]);

        return response()->json(['holidayCalenders' => $holidayCalenders], 200);
    }

    /**
     * Fetch aggregated holiday calendar data.
     */
    public function holidayData(Request $request)
    {
        // Define the query with all necessary relationships
        $query = Holiday_calender::with(['creator', 'updater', 'teams', 'departments', 'locations']);
    
        // Decode input parameters for each field
        $decodedTeam = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedOfficeLocation = $request->filled('location_id') ? urldecode($request->input('location_id')) : null;
        $decodedShift = $request->filled('shift_id') ? urldecode($request->input('shift_id')) : null;
        $decodedUser = $request->filled('user_id') ? urldecode($request->input('user_id')) : null;
        $decodedTicketNumber = $request->filled('ticker') ? urldecode($request->input('ticket')) : null;
        $decodedProductType = $request->filled('product_type_id') ? urldecode($request->input('product_type_id')) : null;
        $decodedHolidayName = $request->filled('holiday_name') ? urldecode($request->input('holiday_name')) : null;
        $decodedHolidayDepartment = $request->filled('holiday_department') ? urldecode($request->input('holiday_department')) : null;
        $decodedHolidayStartdate = $request->filled('holiday_start_date') ? urldecode($request->input('holiday_start_date')) : null;
        $decodedHolidayEnddate = $request->filled('holiday_end_date') ? urldecode($request->input('holiday_end_date')) : null;
        $decodedDayOfWeek = $request->filled('day_of_week') ? urldecode($request->input('day_of_week')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering: Add the logic for each new field
    
        // Filter by team
        if ($decodedTeam) {
            $teams = explode(',', $decodedTeam);
            $query->where(function ($q) use ($teams) {
                foreach ($teams as $team) {
                    $q->orWhere('team_id', '=', trim($team));
                }
            });
        }

        if ($decodedDepartment) {
            $departments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($departments) {
                foreach ($departments as $department) {
                    $q->orWhere('department_id', '=', trim($department));
                }
            });
        }

        if ($decodedOfficeLocation) {
            $locations = explode(',', $decodedOfficeLocation);
            $query->where(function ($q) use ($locations) {
                foreach ($locations as $location) {
                    $q->orWhere('location_id', '=', trim($location));
                }
            });
        }
        
    
        // Filter by task type
        if ($decodedHolidayName) {
            $holidayNames = explode(',', $decodedHolidayName);
            $query->where(function ($q) use ($holidayNames) {
                foreach ($holidayNames as $holidayName) {
                    $q->orWhere('holiday_name', 'like', trim($holidayName));
                }
            });
        }
    
    
        if ($decodedHolidayStartdate) {
            $startDates = explode(',', $decodedHolidayStartdate);
            $query->where(function ($q) use ($startDates) {
                foreach ($startDates as $startDate) {
                    $q->orWhere('holiday_start_date', 'like', trim($startDate));
                }
            });
        }

        if ($decodedHolidayEnddate) {
            $endDates = explode(',', $decodedHolidayEnddate);
            $query->where(function ($q) use ($endDates) {
                foreach ($endDates as $endDate) {
                    $q->orWhere('holiday_end_date', 'like', trim($endDate));
                }
            });
        }

        if ($decodedDayOfWeek) {
            $dayOfWeeks = explode(',', $decodedDayOfWeek);
            $query->where(function ($q) use ($dayOfWeeks) {
                foreach ($dayOfWeeks as $dayOfWeek) {
                    $q->orWhere('day_of_week', 'like', trim($dayOfWeek));
                }
            });
        }
    
        
    
        // Filter by created_by
        if ($decodedCreatedBy) {
            $query->where('created_by', '=', trim($decodedCreatedBy));
        }
    
        // Filter by updated_by
        if ($decodedUpdatedBy) {
            $query->where('updated_by', '=', trim($decodedUpdatedBy));
        }

        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;

        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('holiday_name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('departments', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('teams', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('locations', function ($query) use ($globalSearch) {
                        $query->where('locations_name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhere('holiday_start_date', 'like', '%' . $globalSearch . '%')
                    ->orWhere('holiday_end_date', 'like', '%' . $globalSearch . '%')
                    ->orWhere('day_of_week', 'like', '%' . $globalSearch . '%')

                    ->orWhere('created_at', 'like', '%' . $globalSearch . '%')
                    ->orWhere('updated_at', 'like', '%' . $globalSearch . '%')

                    // Corrected 'creator' and 'updater' relationships
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }

    
        // Sorting logic
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
        $query->orderBy($sortBy, $order);
    
        // Pagination
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $taskrecords = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($taskrecords, 200);
    }
    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Holiday_calender::with([
            'creator', 'updater', 'teams', 'departments', 'locations',
        ]);              

        
        $results->select($column, $column. ' as holiday', \DB::raw("COUNT(*) as total"));

        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'title' parameter from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }
        
        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the 'title' column using a partial match
        $results = Holiday_calender::with([
            'creator', 'updater', 'teams', 'department', 'locations',
        ]); 

        
        if(strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);

            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        }else{
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }
    

    /**
     * Fetch a single holiday calendar.
     */
    public function show($id)
    {
        $holidaycalender = Holiday_calender::find($id);

        if (!$holidaycalender) {
            return response()->json(['error' => 'Holiday calendar not found.'], 404);
        }

        Log::info('Fetched Holiday Calendar:', ['holidaycalender' => $holidaycalender]);

        return response()->json($holidaycalender, 200);
    }

    /**
     * Create a new holiday calendar.
     */
    public function store(Request $request)
    {
        echo "test";
        $authUser = $request->user();
    
        // Validate request first
        $validated = $request->validate([
            'holiday_name' => 'required|string|max:255',
            'holiday_start_date' => 'required|date',
            'holiday_end_date' => 'required|date',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'location_id' => 'nullable|exists:locations,id',
        ]);
    
        // Now, after validation, use the validated department_id and location_id for unique rule
        $validated['holiday_name'] = $request->input('holiday_name');
        $validated['department_id'] = $request->input('department_id');
        $validated['location_id'] = $request->input('location_id');
    
        $validated = $request->validate([
            'holiday_name' => 'required|string|max:255|unique:holiday_calenders,holiday_name,NULL,id,department_id,' . $validated['department_id'] . ',location_id,' . $validated['location_id'],
            'holiday_start_date' => 'required|date',
            'holiday_end_date' => 'required|date',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'location_id' => 'nullable|exists:locations,id',
        ]);
    
        // Initialize Carbon dates
        $start = Carbon::parse($validated['holiday_start_date']);
        $end = Carbon::parse($validated['holiday_end_date']);
    
        $dayNames = [];
        $workingDays = 0;
    
        $current = $start->copy();
    
        while ($current->lte($end)) {
            $dayName = $current->format('l'); // Full day name: Monday, Tuesday, etc.
            $dayNames[] = $dayName;
    
            // Count only weekdays (exclude Saturday and Sunday)
            if (!in_array($dayName, ['Saturday', 'Sunday'])) {
                $workingDays++;
            }
    
            $current->addDay();
        }
    
        // Save the fields
        $validated['day_of_week'] = implode(', ', array_unique($dayNames));
        $validated['days'] = $workingDays;
    
        // Authorization check
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead'])->exists()) {
            Log::warning('Unauthorized Holiday Calendar Creation Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You are not authorized to create a holiday calendar.'], 403);
        }
    
        // Create holiday calendar
        $holidaycalender = Holiday_calender::create(array_merge($validated, [
            'created_by' => $authUser->id,
        ]));
    
        Log::info('Holiday Calendar Created:', ['holidaycalender' => $holidaycalender]);
    
        return response()->json([
            'message' => 'Holiday calendar created successfully.',
            'holidaycalender' => $holidaycalender
        ], 201);
    }
    
    /**
     * Update a holiday calendar.
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Find the holiday calendar by ID
        $holidaycalender = Holiday_calender::find($id);
    
        if (!$holidaycalender) {
            return response()->json(['error' => 'Holiday calendar not found.'], 404);
        }
    
        // Validate request first
        $validated = $request->validate([
            'holiday_name' => 'required|string|max:255',
            'holiday_start_date' => 'required|date',
            'holiday_end_date' => 'required|date',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'location_id' => 'nullable|exists:locations,id',
        ]);
    
        // After validation, use the validated department_id and location_id for unique rule
        $validated['holiday_name'] = $request->input('holiday_name');
        $validated['department_id'] = $request->input('department_id');
        $validated['location_id'] = $request->input('location_id');
    
        // Revalidate with the unique rule, excluding the current holiday calendar's ID
        $validated = $request->validate([
            'holiday_name' => 'required|string|max:255|unique:holiday_calenders,holiday_name,' . $holidaycalender->id . ',id,department_id,' . $validated['department_id'] . ',location_id,' . $validated['location_id'],
            'holiday_start_date' => 'required|date',
            'holiday_end_date' => 'required|date',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
            'location_id' => 'nullable|exists:locations,id',
        ]);
    
        // Initialize Carbon dates
        $start = Carbon::parse($validated['holiday_start_date']);
        $end = Carbon::parse($validated['holiday_end_date']);
    
        $dayNames = [];
        $workingDays = 0;
    
        $current = $start->copy();
    
        while ($current->lte($end)) {
            $dayName = $current->format('l'); // Full day name: Monday, Tuesday, etc.
            $dayNames[] = $dayName;
    
            // Count only weekdays (exclude Saturday and Sunday)
            if (!in_array($dayName, ['Saturday', 'Sunday'])) {
                $workingDays++;
            }
    
            $current->addDay();
        }
    
        // Update the fields
        $validated['day_of_week'] = implode(', ', array_unique($dayNames));
        $validated['days'] = $workingDays;
    
        // Authorization check
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead'])->exists()) {
            Log::warning('Unauthorized Holiday Calendar Update Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You are not authorized to update a holiday calendar.'], 403);
        }
    
        // Update the holiday calendar
        $holidaycalender->update(array_merge($validated, [
            'updated_by' => $authUser->id,  // Set the user who updated the record
        ]));
    
        Log::info('Holiday Calendar Updated:', ['holidaycalender' => $holidaycalender]);
    
        return response()->json([
            'message' => 'Holiday calendar updated successfully.',
            'holidaycalender' => $holidaycalender
        ], 200);
    }
    

    /**
     * Delete a holiday calendar.
     */
    public function destroy($id)
    {
        $authUser = request()->user();
        $holidaycalender = Holiday_calender::find($id);

        if (!$holidaycalender) {
            return response()->json(['error' => 'Holiday calendar not found.'], 404);
        }

        // Authorization check
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            Log::warning('Unauthorized Holiday Calendar Deletion Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'Unauthorized action.'], 403);
        }

        $holidaycalender->delete();
        Log::info('Holiday Calendar Deleted:', ['id' => $id]);

        return response()->json(['message' => 'Holiday calendar deleted successfully.'], 200);
    }
}
