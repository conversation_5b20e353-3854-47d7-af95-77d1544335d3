import React from "react";
import Select from "react-select";
// import {Image} from './index';
import { useGetDepartmentsListQuery, useGetTeamsListQuery,useGetShiftsListQuery, useGetUsersByDefaultTeamListQuery,useGetLocationsListQuery } from "../../features/api";
import { defaultTimeFormat } from "../../utils";

export const DepartmentDropdown = ({ value, onChange, isMulti = false, props={}, className="w-full" }) => {
  const { data, isLoading, isError } = useGetDepartmentsListQuery();

  const options = data?.map((dept) => ({
    value: dept.id,
    label: dept.name
  })) || [];

  return (
    <Select
      options={options}
      isLoading={isLoading}
      isDisabled={isLoading || isError}
      isMulti={isMulti}
      isSearchable={(options.length > 10)}
      value={isSelected(value, options)}
      onChange={onChange}
      className={className}
      {...props}
    />
  );
};

export const TeamsDropdown = ({ value, onChange, dependentField=false, isMulti = false,props={}, className="w-full" }) => {
  const { data, isLoading, isError } = useGetTeamsListQuery();

  const options = data?.map((team) => {
    if(dependentField){
      if(team['department_ids'].includes(dependentField)){
        return {
          value: team.id,
          label: team.name,
        }
      }
    }else {
      return {
        value: team.id,
        label: team.name,
      }
    }
  }).filter(Boolean) || [];

  return (
    <Select
      options={options}
      isLoading={isLoading}
      isDisabled={isLoading || (!dependentField) || isError}
      isMulti={isMulti}
      isSearchable={(options.length > 10)}
      value={isSelected(value, options)}
      onChange={onChange}
      className={className}
      {...props}
    />
  );
};

export const LocationDropdown = ({ value, onChange, isMulti = false, props = {}, className = "w-full" }) => {
  const { data, isLoading, isError } = useGetLocationsListQuery(); // Fetch locations

  const options = data?.map((location) => ({
    value: location.id,
    label: location.locations_name,
  })) || [];

  return (
    <Select
      options={options}
      isLoading={isLoading}
      isDisabled={isLoading || isError}
      isMulti={isMulti}
      isSearchable={options.length > 10}
      value={options.find((opt) => opt.value === value) || null}
      onChange={onChange}
      className={className}
      {...props}
    />
  );
};



export const UsersByDefaultTeamDropdown = ({ value, onChange, dependentField=false, isMulti = false,props={}, className="w-full" }) => {
  const { data, isLoading, isError } = useGetUsersByDefaultTeamListQuery();

  const options = data?.map((user) => {
    if(dependentField){
      if(user['team_id'] === dependentField){
        return {
          value: user.id,
          label: `${user?.fname} ${user?.lname} (EID: ${user?.eid})`,
          // label: (<div  className="flex align-middle items-center w-full text-start ">
          //           <Image className="w-8 h-8 me-2 border border-gray-300 rounded-full" src={user.photo} />
          //           <div className="flex-col align-middle items-center w-full text-start ">
          //           <b>{user?.fname} {user?.lname}</b>
          //           </div>
          //         </div>)
        }
      }
    }else {
      return {
        value: user.id,
        label: `${user?.fname} ${user?.lname} (EID: ${user?.eid})`,
        // label: (<div  className="flex align-middle items-center w-full text-start ">
        //   <Image  src={user.photo} />
        //   <div className="flex-col align-middle items-center w-full text-start ">
        //   <b>{user?.fname} {user?.lname}</b>
        //   </div>
        // </div>)
      }
    }
  }).filter(Boolean) || [];

  

  return (
    <Select
      options={options}
      // getOptionLabel={customLabel} 
      // filterOption={customFilter} 
      isLoading={isLoading}
      isDisabled={isLoading || (dependentField && (dependentField === "")) || isError}
      isMulti={isMulti}
      isSearchable={(options.length > 10)}
      value={isSelected(value, options)}
      onChange={onChange}
      className={className}
      {...props}
    />
  );
};

export const ShiftDropdown = ({ value, onChange, dependentField=false, isMulti = false,props={}, className="w-full" }) => {
  const { data, isLoading, isError } = useGetShiftsListQuery();

  const options = data?.map((shift) => {
    if(dependentField){
      if(shift['team_ids'].includes(dependentField)){
        return {
          value: shift.id,
          label: `${shift?.shift_name} (${defaultTimeFormat(shift?.shift_start)}-${defaultTimeFormat(shift?.shift_end)})`,
        }
      }
    }else {
      return {
        value: shift.id,
        label: `${shift?.shift_name} (${shift?.shift_start}-${shift?.shift_end})`,
      }
    }
  }).filter(Boolean) || [];

  return (
    <Select
      options={options}
      isLoading={isLoading}
      isDisabled={isLoading || (!dependentField) || isError}
      isMulti={isMulti}
      isSearchable={(options.length > 10)}
      value={isSelected(value, options)}
      onChange={onChange}
      className={className}
      {...props}
    />
  );
};


const isSelected = (value, options) => {
  let selectedOptions = []
  if(typeof value === "object" && value?.length){
    selectedOptions = options.find((opt) => opt.value === value.value)
  }else{
    selectedOptions = options.find((opt) => opt.value === value)
  }
  return selectedOptions;
}

// // Custom label rendering
// const customLabel = (option) => {
//   return option.label;
// };

// // Custom filter function
// const customFilter = (option, searchText) => {
//   // Ensure label is a string before calling replace
//   // const labelText = typeof option.label === 'string' ? option.label.replace(/<[^>]*>/g, '') : '';
//   // return labelText.toLowerCase().includes(searchText.toLowerCase());
// };


