<?php

namespace Database\Factories;

use App\Models\Notice;
use Illuminate\Database\Eloquent\Factories\Factory;

class NoticeFactory extends Factory
{
    protected $model = Notice::class;

    public function definition()
    {
        return [
            'title'       => $this->faker->sentence,
            'content'     => $this->faker->paragraph,
            'category'    => $this->faker->randomElement(['News', 'Update', 'Alert']),
            'priority'    => $this->faker->randomElement(['Low', 'Medium', 'High']),
            'department'  => $this->faker->randomElement([
                "HR",
                "IT",
                "Creative",
                "RDC"
            ]),
            'team'        =>  $this->faker->randomElement([
                "Accuweather",
                "Bigtincan",
                "Bloomberg",
                "Boats",
                "CitrusAd",
                "Clipcentric",
                "Expedia",
                "Management",
                "Multiview",
                "Spiceworks"
            ]),
            'published_date' => $this->faker->dateTimeBetween('now', '1 year'),
            'expiry_date' => $this->faker->dateTimeBetween('now', '+1 year'),
            'status' => $this->faker->randomElement(['active', 'inactive','pending','archived']),
            'created_by'  => 1,
            'updated_by'  => 1,
        ];
    }
}
