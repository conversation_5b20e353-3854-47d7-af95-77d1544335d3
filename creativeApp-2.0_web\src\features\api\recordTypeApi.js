import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const recordTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getRecordTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `record-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['RecordTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForRecordType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `record-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['RecordTypeData'],
    }),

    getRecordTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `record-type-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'RecordTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createRecordType: builder.mutation({
      query: (newFormationType) => ({
        url: 'record-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['RecordTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateRecordType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `record-type/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'RecordTypeData', id }, 'RecordTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteRecordType: builder.mutation({
      query: (id) => ({
        url: `record-type/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['RecordTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetRecordTypeDataQuery,
  useLazyFetchDataOptionsForRecordTypeQuery,
  useGetRecordTypeByIdQuery,
  useLazyGetRecordTypeByIdQuery,
  useCreateRecordTypeMutation,
  useUpdateRecordTypeMutation,
  useDeleteRecordTypeMutation,
} = recordTypeApi;
