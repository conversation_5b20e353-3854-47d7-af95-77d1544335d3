import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';
const API_URL = process.env.REACT_APP_BASE_API_URL;
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null; // Additional validation logic can be added here
};

const AddResourceType = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [resourceTypes, setResourceTypes] = useState([]); // Store existing resource types
    const [resourceTypeName, setResourceTypeName] = useState(''); // Store the input value for new resource type
    const [error, setError] = useState(''); // Store error messages
    const [successMessage, setSuccessMessage] = useState(''); // Store success messages
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch existing resource types
    useEffect(() => {
        const fetchResourceTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/resource_types`, { // Adjust API for resource types
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setResourceTypes(data.resource_types || []); // Update with fetched data
            } catch (error) {
                setError(error.message);
            }
        };

        fetchResourceTypes();
    }, []);

    // Handle form submission
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedResourceTypeName = resourceTypeName.trim();

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        // Check if the resource type already exists
        if (Array.isArray(resourceTypes)) {
            const typeExists = resourceTypes.some(type => {
                const typeNameLower = type.name.toLowerCase().trim();
                return typeNameLower === trimmedResourceTypeName.toLowerCase();
            });

            if (typeExists) {
                setError('Resource type already exists. Please add a different type.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }

        setError(''); // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }


            // Create the resource type
            const response = await fetch(`${API_URL}/resource_types`, { // Adjusted API endpoint
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedResourceTypeName,
                    created_by: createdBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save resource type: ' + response.statusText);
            }

            const result = await response.json();
            //setSuccessMessage(`Resource type "${result.name || trimmedResourceTypeName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Responsibility added successfully.',
            });

            setResourceTypeName(''); // Clear input field

            // Refetch the resource types after creation
            const newResourceTypesResponse = await fetch(`${API_URL}/resource_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newResourceTypesResponse.ok) {
                throw new Error('Failed to fetch resource types: ' + newResourceTypesResponse.statusText);
            }

            const newResourceTypesData = await newResourceTypesResponse.json();
            setResourceTypes(newResourceTypesData.resource_types || []);

        } catch (error) {
            alertMessage('error');
        }
    };

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Resource Type</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="resourceTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                Resource Type Name
                            </label>
                            <input
                                type="text"
                                id="resourceTypeName"
                                value={resourceTypeName}
                                onChange={(e) => setResourceTypeName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Resource Type
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddResourceType;
