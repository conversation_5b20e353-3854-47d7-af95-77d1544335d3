import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditMember from './EditMember';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TeamMemberList = ({ searchTerm, currentPage, itemsPerPage, setTotalCount }) => {
    const [users, setUsers] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [filteredUsers, setFilteredUsers] = useState([]);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "EID", key: "eid" },
        { label: "Full Name", key: "fullName" },
        { label: "Designation", key: "designation" },
        { label: "Responsibility Level", key: "resource_type" },
        { label: "Preferred Nickname", key: "nick_name" },
        { label: "Email", key: "email" },
        { label: "Primary Phone Number", key: "primary_contact" },
        { label: "Secondary Phone Number", key: "secondary_contact" },
        { label: "Emergency Contact Number", key: "emergency_contact" },
        { label: "Desk ID", key: "desk_id" },
        { label: "Team", key: "team" },
        { label: "Team Lead/Report to", key: "report_to" },
        { label: "Onsite Status", key: "onsite_status" },
    ];

    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }

                const data = await response.json();

                
                const mappedUsers = data.map(user => ({
                    id: user.id,
                    eid: user.eid || '',
                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),
                    designation: user.designations.length > 0 ? user.designations[0].name : '',
                    resource_type: user.resource_types.length > 0 ? user.resource_types[0].name : '',
                    nick_name: user.nick_name || '',
                    email: user.email || '',
                    primary_contact: user.primary_contact || '',
                    secondary_contact: user.secondary_contact || '',
                    emergency_contact: user.emergency_contact || '',
                    desk_id: user.desk_id || '',
                    team: user.teams.length > 0 ? user.teams[0].name : '',
                    report_to: user.teams.length > 0 ? user.teams[0].team_lead : '',
                    onsite_status: user.onsite_statuses.length > 0 ? user.onsite_statuses[0].name : '',
                }));
                
                setUsers(mappedUsers);
                setFilteredUsers(mappedUsers);
                setTotalCount(totalCount);

            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUsers();
    }, [currentPage, itemsPerPage]);

    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);
    const totalCount = filteredUsers.length;

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm.toLowerCase().trim();

        if (!normalizedSearchTerm) {
        setFilteredUsers(users);
        return;
        }

        const filtered = users.filter(user => {
        return Object.values(user).some(value =>
            value && value.toString().toLowerCase().includes(normalizedSearchTerm)
        );
        });

        setFilteredUsers(filtered);
    }, [searchTerm, users]);

    const handleEdit = (id) => {
        setSelectedUserId(id);
        setModalVisible(true);
    };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit} // Pass the edit handler
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedUserId}
                hideDeleteButton={true}
            />
            {modalVisible && selectedUserId && (
                <EditMember 
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    userId={selectedUserId}
                />
            )}
        </div>
    );
};

export default TeamMemberList;
