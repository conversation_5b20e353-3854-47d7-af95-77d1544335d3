<?php

namespace Database\Factories;


use Carbon\Carbon;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Team;
use App\Models\User;
use App\Models\SchedulePlanner;
use Illuminate\Database\Eloquent\Factories\Factory;

class AttendanceFactory extends Factory
{
    protected $model = Attendance::class;

    public function definition(): array
    {
        return [
            'department_id' => Department::inRandomOrder()->value('id') ?? "",
            'team_id' => Team::inRandomOrder()->value('id') ?? "",
            'user_id' => User::inRandomOrder()->value('id') ?? 1,
            'schedule_planner_id' => SchedulePlanner::inRandomOrder()->value('id') ?? "",
            'date' => $this->faker->dateTimeBetween(
                startDate: Carbon::now()->startOfYear(),
                endDate: Carbon::now()->subDay() // Before today
            )->format('Y-m-d'),

            'start' => function (array $attributes) {
                // Ensuring the start is on the same date as 'date'
                return Carbon::createFromFormat('Y-m-d', $attributes['date'])
                    ->setTime(rand(0, 23), rand(0, 59)) // Random time within the day
                    ->format('Y-m-d H:i');
            },

            'end' => function (array $attributes) {
                return Carbon::createFromFormat('Y-m-d H:i', $attributes['start'])
                    ->addMinutes(rand(240, 600)) // Random duration between 10 minutes and 10 hours
                    ->format('Y-m-d H:i');
            },
            'duration' => function (array $attributes) {
                return date('H:i', strtotime($attributes['end']) - strtotime($attributes['start']));
            },
            'entry_type' => $this->faker->randomElement(['late_entry', 'early_leave', 'break', 'attendance']),
            'details' => $this->faker->sentence(),
            'approval_status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'approval_notes' => $this->faker->optional()->sentence(),
            'created_by'  => User::inRandomOrder()->value('id') ?? "",
            'updated_by'  => User::inRandomOrder()->value('id') ?? ""
        ];
    }
}
