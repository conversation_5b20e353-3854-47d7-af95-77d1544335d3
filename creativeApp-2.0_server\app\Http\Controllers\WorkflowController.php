<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Workflow;
use Illuminate\Support\Facades\Log;

class WorkflowController extends Controller
{
    public function index()
    {
        $workflows = Workflow::all();

        Log::info('All Workflows Retrieved:', ['workflows_count' => $workflows->count()]);

        return response()->json(['workflows' => $workflows], 200);
    }

    public function show($id)
    {
        $workflow = Workflow::find($id);

        if (!$workflow) {
            return response()->json(['error' => 'Workflow not found.'], 404);
        }

        Log::info('Workflow Retrieved:', ['workflow' => $workflow]);

        return response()->json(['workflow' => $workflow], 200);
    }

    public function workflowsData(Request $request)
    {
        $query = Workflow::with(['creator', 'updater', 'team', 'department']);

        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedName = $request->filled('name') ? urldecode($request->input('name')) : null;

        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }

        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }

        if ($decodedName) {
            $names = explode(',', $decodedName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                  ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                  ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }

        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
        $query->orderBy($sortBy, $order);

        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $workflows = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($workflows, 200);
    }

    public function searchByField(Request $request)
    {
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');

        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        $results = Workflow::with(['creator', 'updater', 'team', 'department']);

        if (strpos($column, ".") !== false) {
            [$tblName, $fieldName] = explode('.', $column);
            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        return response()->json($results->get(), 200);
    }

    public function group(Request $request)
    {
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }

        $results = Workflow::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

        return response()->json($results->get(), 200);
    }

    public function store(Request $request)
    {
        $authUser = $request->user();

        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname
        ]);

        Log::info('Create Workflow Request:', ['request' => $request->all()]);

        $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        $workflow = Workflow::create([
            'name' => $request->name,
            'department_id' => $request->department_id,
            'team_id' => $request->team_id,
            'created_by' => $authUser->id,
        ]);

        Log::info('Workflow Created:', ['workflow' => $workflow]);

        return response()->json(['message' => 'Workflow created successfully.', 'workflow' => $workflow], 201);
    }

    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,
            'fname' => $authUser->fname,
            'lname' => $authUser->lname
        ]);

        Log::info('Update Workflow Request:', ['request' => $request->all()]);

        $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        $workflow = Workflow::find($id);

        if (!$workflow) {
            return response()->json(['error' => 'Workflow not found.'], 404);
        }

        $workflow->update([
            'name' => $request->name,
            'department_id' => $request->department_id,
            'team_id' => $request->team_id,
            'updated_by' => $authUser->id,
        ]);

        Log::info('Workflow Updated:', ['workflow' => $workflow]);

        return response()->json(['message' => 'Workflow updated successfully.', 'workflow' => $workflow], 200);
    }

    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $workflow = Workflow::findOrFail($id);
            $workflow->delete();

            return response()->json(['message' => 'Workflow deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this workflow.'], 403);
    }
}
