import React, { useState, useCallback, useEffect, useRef } from "react";

// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";

// Loading spinner component to show while data is loading
import Loading from "../../../common/Loading";

import {confirmationAlert, ManageColumns, SearchFilter, TableView} from '../../../common/coreui';


import { useDispatch } from "react-redux";
import { defaultDateFormat, defaultTimeFormat, defaultDateTimeFormat, getTimezoneDifference, removeKeys, sortByLabel } from "../../../utils";

// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import { reporterDirectoryApi, useDeleteReporterDirectoryMutation, useGetReporterDirectoryDataQuery, useLazyFetchDataOptionsForReporterDirectoryQuery } from "../../../features/api";
import { useNavigate } from "react-router-dom";
import AddReporter from "./AddReporter";
import EditReporter from "./EditReporter";
import { useRoleBasedAccess } from "../../../common/useRoleBasedAccess";
import moment from 'moment-timezone';
// import CurrentTimeByIp from "../../../common/clock/CurrentTimeByIp";
import axios from 'axios';

import { API_URL } from './../../../common/fetchData/apiConfig';
import useFetchApiData from "../../../common/fetchData/useFetchApiData";
import CommonClock from "../../../common/clock/CommonClock";
import { DateTimeFormatDay, DateTimeFormatHour } from "../../../common/DateTimeFormatTable";

const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null;
};

// API endpoint and configuration constants
const MODULE_NAME = "Reporter Directory";

// Main component for listing Reporter Directory List
const ReporterDataList = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [dataItemsId, setDataItemsId] = useState(null);
  const [error, setError] = useState(null);
  const [viewData, setViewData] = useState(null);
  const navigate = useNavigate();
  const [addModalVisible, setAddModalVisible] = useState(false);

  
  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);
  const [currentTime, setCurrentTime] = useState('');
  const [reporters, setReporters] = useState([]);
  const timeDataRef = useRef(null);

  
  const { data: dataItems, isFetching, error: fetchError } = useGetReporterDirectoryDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });

  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForReporterDirectoryQuery();
       
  const [deleteReporterDirectory] = useDeleteReporterDirectoryMutation();

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "")

    setQueryString(q);
  }

  const handleCopy = (data) => {
    const keysToRemove = ["id", "team", "department", "updated_at", "updated_by", "updater", "created_at", "creator", "created_by", "updated_by"];
    const cleanedData = removeKeys(data, keysToRemove);
    setViewData(null)
    setModalVisible(true);
  }

  const handleEdit = (id) => {
    setViewData(null)
    setDataItemsId(id); 
    setModalVisible(true);
  }

  const handleDelete = (id) => {
    confirmationAlert({onConfirm: () => 
      {        
        deleteReporterDirectory(id);
        setViewData(null);
      }});  
  }
 

  const { rolePermissions } = useRoleBasedAccess();

  // Reporters Current Time based on Converted Timezone
  const convertDateTime = (currentDateTime, toTimezone="America/Los_Angeles", fromTimezone="Asia/Dhaka") => {
    if (!currentDateTime || !toTimezone) return null;

    const fromTime = moment.tz(currentDateTime, fromTimezone);
    var convertedTime = fromTime.clone().tz(toTimezone);
    return convertedTime.isValid() ? convertedTime.format("LLL") : null;
  };

  // Fetch reporters data using your custom hook
  const token = localStorage.getItem('token');
  const { data: reportersData } = useFetchApiData(`${API_URL}reporters`, token);

  // Handle Current Time Data and update reporters' time
  const handleCurrentTimeData = async (data) => {
    console.log("Received current time data:", data);

    if (!data.formattedTime) {
      console.error("Invalid time format:", data.formattedTime);
      return;
    }

    // Parse the formatted time
    const formattedTime = moment(data.formattedTime, 'YYYY-MM-DD HH:mm:ss');

    if (!formattedTime.isValid()) {
      console.error("Invalid time format detected:", data.formattedTime);
      return;
    }

    console.log("Before updating current_time:", reporters);

    setCurrentTime(formattedTime.format('YYYY-MM-DD HH:mm:ss'));

    // Fetch reporters data if not already fetched
    if (reportersData && reportersData.reporters) {
      setReporters(reportersData.reporters);
    }

    // Update reporters' current time based on their timezones
    setReporters((prevReporters) => {
      const updatedReporters = prevReporters.map((reporter) => {
        // Find corresponding reporter data from the fetched data
        const reporterFromData = reportersData.reporters.find(r => r.id === reporter.id);

        if (reporterFromData) {
          const reporterTime = formattedTime.tz(reporterFromData.timezone);

          // Convert the time to 12-hour AM/PM format
          const current_time = reporterTime?.isValid()
            ? convertTo12HourFormat(reporterTime.format('HH:mm')) // Convert to AM/PM format
            : "Invalid Time";

          console.log(`Updated Time for ${reporterFromData.name}: ${current_time}`);

          return {
            ...reporterFromData,
            current_time: current_time,  // Update current time based on each reporter's timezone
          };
        }

        return reporter; // Return the original reporter if no matching data is found
      });

      console.log("After updating current_time:", updatedReporters);
      return updatedReporters;
    });
  };

  // Convert to 12-hour format (AM/PM)
  const convertTo12HourFormat = (time24) => {
    return moment(time24, 'HH:mm').format('hh:mm A');
  };

  // UseEffect to handle the initial fetching and update the reporters when the reportersData is available
  useEffect(() => {
    if (reportersData && reportersData.reporters) {
      console.log("Reporters from Data Table:", reportersData.reporters);
      setReporters(reportersData.reporters);
    }
  }, [reportersData]); 

// Handle Local Time from CommonClock (Make sure this is defined correctly)
const handleLocalTimeData = (data) => {
  if (!data.formattedTime) {
    console.error("Invalid time format:", data.formattedTime);
    return;
  }

  if (!timeDataRef.current || timeDataRef.current.formattedTime !== data.formattedTime) {
    timeDataRef.current = data;

    setReporters((prevReporters) => {
      return prevReporters.map((reporter) => {
        // Convert the formatted time to the reporter's local time
        const reporterTime = moment.tz(data.formattedTime, reporter.timezone);
        const localTime = reporterTime.isValid()
          ? convertTo12HourFormat(reporterTime.format('HH:mm'))
          : "Invalid Time";

        return {
          ...reporter,
          local_time: localTime,  // Dynamically set local time
        };
      });
    });
  }
};

const getCurrentTimeInTimezone = (timezone) => {
  return defaultDateTimeFormat(moment().tz(timezone).format('YYYY-MM-DD HH:mm:ss'));
};

const getCurrentDateOnlyInTimezone = (timezone) => {
  return moment().tz(timezone).format('dddd, Do MMMM YYYY');
};

const getCurrentTimeOnlyInTimezone = (timezone) => {
  return moment().tz(timezone).format('hh:mm A');
};

const myTimezone = "Asia/Dhaka"; // Your timezone

const isWithinShift = (start_time, end_time, timezone) => {
  // Get current time in your timezone (Asia/Dhaka)
  const nowInMyTZ = moment.tz(myTimezone);
  
  // Convert the current time from your timezone to the reporter's timezone
  const nowInReporterTZ = nowInMyTZ.clone().tz(timezone);
  
  // Construct reporter's shift start and end moments using the current date in reporter's timezone
  const shiftStart = moment.tz(
    `${nowInReporterTZ.format('YYYY-MM-DD')} ${start_time}`,
    'YYYY-MM-DD HH:mm:ss',
    timezone
  );
  const shiftEnd = moment.tz(
    `${nowInReporterTZ.format('YYYY-MM-DD')} ${end_time}`,
    'YYYY-MM-DD HH:mm:ss',
    timezone
  );
  
  // Check if the current time (in reporter's timezone) is within the shift hours
  return nowInReporterTZ.isBetween(shiftStart, shiftEnd);
};


  let columnSerial = 1;

  const [columns, setColumns]  = useState(
    () => [
        {
          id: columnSerial++,
          name: "Action",
          width: "180px",
          className: "bg-red-300",
          cell: (item) => (
            <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
            
              
              <button
                className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                onClick={() => {
                  setViewData(item);
                }}
              >
                <span className="material-symbols-outlined text-lg ">
                visibility
                </span>
              </button>
  
              {/* Edit Button */}
              
              {rolePermissions.hasShiftLeadRole && (
              <button
                className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                onClick={() => handleEdit(item.id)}
              >
                <span className="material-symbols-outlined text-lg ">
                  stylus_note
                </span>
              </button>
              )}

              {/* Copy Button */}
              {rolePermissions.hasShiftLeadRole && (
              <button
                className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                onClick={() => {
                  handleCopy(item);
                }}
              >
                <span className="material-symbols-outlined text-lg ">
                content_copy
                </span>
              </button>
              )}

              {/* Delete Button */}
              {rolePermissions?.hasTeamLeadRole && (
              <button
                className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                onClick={() => handleDelete(item.id)}
              >
                <span className="material-symbols-outlined text-sm ">
                  delete
                </span>
              </button>
              )}
            </div>
          ),
        },
        {
          id: columnSerial++,
          name: "S.No",
          // Calculate serial number based on current page and rows per page
          selector: (row, index) => (currentPage - 1) * perPage + index + 1,
          width: "80px",
          omit: false,
      },
      {
        id: columnSerial++,
        name: "Department",
        selector: (row) => row.departments?.name  || "N/A",
        db_title_field: "departments.name",
        db_field: "department",
        sortable: true,
        omit: false,
        filterable: true,
      },
      {
        id: columnSerial++,
        name: "Team",
        selector: (row) => row.teams?.name  || "N/A",
        db_title_field: "teams.name",
        db_field: "team",
        sortable: true,
        omit: false,
        filterable: true,
      },
      
      {
          id: columnSerial++,
          name: "Reporter Name",
          db_field: "name",
          selector: (row) => row.name || "N/A",
          omit: false,
          sortable: true,
          filterable: true,
      },
      {
        id: columnSerial++,
        name: "Reporter Location",
        db_field: "location",
        selector: (row) => row.location || "N/A",
        omit: false,
        sortable: true,
        filterable: true,
    },
    {
        id: columnSerial++,
        name: "Reporter Time Zone",
        db_field: "timezone",
        selector: (row) => row.timezone || "N/A",
        omit: false,
        sortable: true,
        filterable: true,
    },
    {
      id: columnSerial++,
      width: "250px",
      name: "Reporter Current Date",
      selector: (row) => {
        // Display only the date part (without time)
        return getCurrentDateOnlyInTimezone(row.timezone);
      },
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      width: "150px",
      name: "Current Time",
      selector: (row) => {
        // Extract only the time part (hh:mm AM/PM) from the full datetime
        return getCurrentTimeOnlyInTimezone(row.timezone);
      },
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Local Time",
      selector: (row) => {
        return getTimezoneDifference(row.timezone, myTimezone);
      },
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
        id: columnSerial++,
        name: "Reporter Office Schedule",
        db_field: "id",
        selector: (row) => row?.start_time ? `${defaultTimeFormat(row?.start_time)} to ${defaultTimeFormat(row?.end_time)}` : "",
        omit: false,
        sortable: true,
        filterable: true,
    },
    {
      id: columnSerial++,
      width: "150px",
      name: "Online/Offline Status",
      db_field: "id",
      // selector: (row) => "",
      cell: (row) => {
       return isWithinShift(row?.start_time, row?.end_time, row?.timezone) ? 
      (<span className="rounded-full px-3 py-2 bg-green-100 border-green-800 border text-green-800 w-full">Online</span>) : 
      (<span className="rounded-full px-3 py-2 bg-red-100 border-red-800 border text-red-800 w-full">Offline</span>)
      },
      omit: false,
      sortable: true,
      filterable: true,
    },
      {
          id: columnSerial++,
          name: "Email",
          db_field: "email",
          selector: (row) => row.email || "N/A",
          omit: false,
          sortable: true,
          filterable: true,
          cell: (row) => (
            <div id="address" className="lowercase" >
                {row.email || "N/A"}
            </div>
          ),
      },
      
      {
          id: columnSerial++,
          name: "Created by",
          selector: (row) => `${row.creator?.fname || ""} ${row.creator?.lname || ""}`,
          db_field: "created_by",
          omit: false,
          sortable: true,
          filterable: true,
      },
      {
        id: columnSerial++,
        name: "Created Date",
        selector: (row) => DateTimeFormatDay(row.created_at),
        db_field: "created_at",
        omit: false,
        sortable: true,
        filterable: true,
      },
      {
          id: columnSerial++,
          name: "Created Time",
          selector: (row) => DateTimeFormatHour(row.created_at),
          db_field: "created_at",
          omit: false,
          sortable: true,
          filterable: false,
        },
        {
          id: columnSerial++,
          name: "Updated by",
          selector: (row) => `${row.updater?.fname || ""} ${row.updater?.lname || "N/A"}`,
          db_field: "updated_by",
          omit: false,
          sortable: true,
          filterable: true,
        },
      {
        id: columnSerial++,
        name: "Updated Date",
        selector: (row) => row.updated_at ? DateTimeFormatDay(row.updated_at) : "N/A",
        db_field: "updated_at",
        omit: false,
        sortable: true,
        filterable: true,
      },
      {
          id: columnSerial++,
          name: "Updated Time",
          selector: (row) => row.updated_at ? DateTimeFormatHour(row.updated_at) : "N/A",
          db_field: "updated_at",
          omit: false,
          sortable: true,
          filterable: false,
      },   
      
    ],
    [currentPage, perPage]
  );
  
  useEffect(() => {
    // Recalculate or update columns if rolePermissions change
    setColumns((prevColumns) => [
      ...prevColumns.map((col) => {
        if (col.name === "Action") {
          // Update the "Action" column dynamically
          return {
            ...col,
            cell: (item) => (
              <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => setViewData(item)}
                >
                  <span className="material-symbols-outlined text-lg">visibility</span>
                </button>
                {rolePermissions.hasShiftLeadRole && (
                  <button
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    onClick={() => handleEdit(item.id)}
                  >
                    <span className="material-symbols-outlined text-lg">stylus_note</span>
                  </button>
                )}

                {rolePermissions.hasShiftLeadRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => handleCopy(item)}
                >
                  <span className="material-symbols-outlined text-lg">content_copy</span>
                </button>
                )}

                {rolePermissions.hasTeamLeadRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                  onClick={() => handleDelete(item.id)}
                >
                  <span className="material-symbols-outlined text-sm">delete</span>
                </button>
                )}
              </div>
            ),
          };
        }
        return col;
      }),
    ]);
  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes
  
  

  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).map((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj })
    }
    setCurrentPage(1);
  };


  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      // Fetch all data items for Excel export
      const result = await dispatch(
        reporterDirectoryApi.endpoints.getReporterDirectoryData.initiate({
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues
          query: queryString,
        })
      ).unwrap(); // Wait for the API response
  
      if (!result?.total || result.total < 1) {
        return false;
      }
  
      var sl = 1;
  
      let prepXlsData = result.data.map((item) => {
        if (columns.length) {
          let obj = {};
          columns.forEach((column) => {
            if (!column.omit && column.selector) {
              obj[column.name] = column.name === "S.No" ? sl++ : column.selector(item) || "";
            }
          });
          return obj;
        }
      });
  
      // Create a worksheet from the JSON data and append to a new workbook
      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  
      // Convert workbook to a buffer and create a Blob to trigger a file download
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(blob, `${MODULE_NAME.replace(/ /g,"_")}_${prepXlsData.length}.xlsx`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };
  

  /**
   * Fetch filter options from API for a specific field.
   */
  const fetchDataOptionsForFilterBy = useCallback(
    async (
      itemObject = {},
      type = "group",
      searching = "",
      fieldType = "select"
    ) => {

      let groupByField = itemObject.db_field || "title";

      try {
        setShowFilterOption(groupByField);
        setFilterOptionLoading(true);

        var groupData = [];

        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });
        
        if (response.data) {
          groupData = response.data;
        }

        if (groupData.length) {

          if (fieldType === "searchable") {
            setFilterOptions((prev) => ({
              ...prev,
              [groupByField]: groupData,
            }));

            return groupData;
          }

          const optionsForFilter = groupData
            .map((item) => {
              if(itemObject.selector){
                let label = itemObject.selector(item);

                if(label){
                  if (item.total && item.total > 1) {
                    label += ` (${item.total})`;
                  }

                  return { label, value: item[groupByField] };
                }

              return null;
              }
            }).filter(Boolean);

          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: sortByLabel(optionsForFilter),
          }));

          return optionsForFilter;
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setFilterOptionLoading(false);
      }
    },
    []
  );

  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      {/* <CurrentTimeByIp currentTimeData={handleCurrentTimeData} />
      <CommonClock onTimeData={handleLocalTimeData} /> */}
      <div className="mx-auto pb-6 ">
        {/* Header section with title and action buttons */}
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div className="w-4/12 md:w-10/12 text-start">
            <h2 className="text-2xl font-bold ">{MODULE_NAME}</h2>
          </div>
          <div className="w-8/12 flex items-end justify-end gap-1">
            {/* Manage Columns dropdown */}
            <ManageColumns columns={columns} setColumns={setColumns} />
            
            {/* Export to Excel button, only shown if data exists */}
            { !isFetching && parseInt(dataItems.total) > 0 && (
              <>
                <button
                  className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={exportToExcel}
                >
                  {isFetching && (
                    <>
                      <span className="material-symbols-outlined animate-spin text-sm me-2">
                        progress_activity
                      </span>
                    </>
                  )}
                  {!isFetching && (
                    <span className="material-symbols-outlined text-sm me-2">
                    file_export
                    </span>
                  )}
                  Export to Excel ({dataItems.total})
                </button>
              </>
            )}
            {/* Button to open modal for adding a new formation */}
            {rolePermissions.hasShiftLeadRole && (
            <button
              className=" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              onClick={() => setAddModalVisible(true)}
            >
              Add New
            </button>
            )}
          </div>
        </div>

        {/* Filter fieldset for global search and field-specific filtering */}
        <SearchFilter
            columns={columns}
            selectedFilterOptions={selectedFilterOptions}
            setSelectedFilterOptions={setSelectedFilterOptions}
            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}
            filterOptions={filterOptions}
            filterOptionLoading={filterOptionLoading}
            showFilterOption={showFilterOption}
            resetPage={resetPage}
            setCurrentPage={setCurrentPage}
            buildQueryParams={buildQueryParams}
        />

        {/* Display error message if any error occurs */}
        {fetchError && <div className="text-red-500">{error}</div>}
        {/* Show loading spinner when data is being fetched */}
        {isFetching && <Loading />}

        {/* If no data is available, display an alert message */}
        
        {/* Render the DataTable with the fetched data */}
        <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
          <DataTable
            columns={columns}
            data={dataItems?.data || []}
            className="p-0 scrollbar-horizontal-10"
            fixedHeader
            
            highlightOnHover
            responsive
            pagination
            paginationServer
            paginationPerPage={perPage}
            paginationTotalRows={dataItems?.total || 0}
            onChangePage={(page) => {
              if (page !== currentPage) {
                setCurrentPage(page);
              }
            }}
            onChangeRowsPerPage={(newPerPage) => {
              if(newPerPage !== perPage){
                setPerPage(newPerPage);
                setCurrentPage(1);
              }
            }}
            paginationComponentOptions={{
              selectAllRowsItem: true,
              selectAllRowsItemText: "ALL",
            }}
            sortServer
            onSort={(column, sortDirection="desc") => {
              if(Object.keys(column).length){
                setSortColumn(column.db_field || column.name || "created_at");
                setSortDirection(sortDirection || "desc");
              }
            }}
          />
        </div>

        {/* Add Modal */}
        {addModalVisible && (
            <AddReporter
                isVisible={addModalVisible}
                setVisible={setAddModalVisible}
            />
        )}

        {/* Conditionally render the Edit modal */}
        {modalVisible && (
          <EditReporter
            isVisible={modalVisible}
            setVisible={setModalVisible}
            dataItemsId={dataItemsId}
          />
        )}

        {viewData && (
          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
        )}
       
      </div>
    </section>
  );
};


export default ReporterDataList;
