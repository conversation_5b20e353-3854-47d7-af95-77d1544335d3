import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const EditSlaAchieve = ({ isVisible, setVisible, slaAchieveId }) => {
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [slaAchieveName, setSlaAchieveName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        if (!isVisible) return;

        const fetchDepartments = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch departments');
                }

                const data = await response.json();

                setDepartments(data.departments);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, [isVisible]);

    // Fetch the sla achieve Details to Edit
    useEffect(() => {
        if (!slaAchieveId || !departments.length) return;

        const fetchSlaAchieve = async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}sla-achieve/${slaAchieveId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch sla achieve details');
                }

                const data = await response.json();
                console.log('SLA Achieve Data:', data);
                
                const slaAchieve = data.slaAchieve;

                // Set the values from the fetched sla achieve
                setSlaAchieveName(slaAchieve.name);
                setSelectedDepartment(slaAchieve.department);
                setSelectedTeam(slaAchieve.team);

                // Fetch teams for the selected department
                const department = departments.find(dep => dep.name === slaAchieve.department);

                if (department && department.teams) {
                    setTeams(department.teams);
    
                    if (!slaAchieve.team && department.teams.length > 0) {
                        setSelectedTeam(department.teams[0].name);
                    }
                } else {
                    setTeams([]);
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchSlaAchieve();
    }, [slaAchieveId, departments]);

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam('');

        if (departmentName) {
            const department = departments.find(dep => dep.name === departmentName);

            if (department && department.teams && department.teams.length > 0) {
                setTeams(department.teams);
                setSelectedTeam(department.teams[0].name);
            } else {
                setTeams([]);
                setSelectedTeam('');
            }
        } else {
            setTeams([]);
            setSelectedTeam('');
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!selectedDepartment || !selectedTeam || !slaAchieveName) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            const response = await fetch(`${API_URL}sla-achieve/${slaAchieveId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department: selectedDepartment,
                    team: selectedTeam,
                    name: slaAchieveName,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update sla achieve.');
            }

            const result = await response.json();
            setSuccessMessage(`sla achieve "${result.slaAchieve.name}" updated successfully!`);
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); 
            }, 2000);
        } catch (error) {
            setError(error.message);
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    return (
        <>
            {isVisible && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Edit sla achieve</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments.length === 0 ? (
                                        <option disabled>No departments available</option>
                                    ) : (
                                        departments.map((department) => (
                                            <option key={department.id} value={department.name}>
                                                {department.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams.length === 0 ? (
                                        <option disabled>No teams available</option>
                                    ) : (
                                        teams.map((team) => (
                                            <option key={team.id} value={team.name}>
                                                {team.name}
                                            </option>
                                        ))
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="slaAchieveName" className="block text-sm font-medium text-gray-700 pb-4">
                                    sla achieve Name
                                </label>
                                <input
                                    id="slaAchieveName"
                                    type="text"
                                    value={slaAchieveName}
                                    onChange={(e) => setSlaAchieveName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Update Sla Achieve
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default EditSlaAchieve;
