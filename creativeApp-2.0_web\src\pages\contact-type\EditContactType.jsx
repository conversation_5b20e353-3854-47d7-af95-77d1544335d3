import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditContactType = ({ isVisible, setVisible, dataItemsId }) => {
    const [contactTypeName, setContactTypeName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchContactType = async () => {
            if (dataItemsId) {
                const token = localStorage.getItem('token');
                try {
                    const response = await fetch(`${API_URL}/contact_types/${dataItemsId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (!response.ok) {
                        throw new Error('Failed to fetch contact type: ' + response.statusText);
                    }

                    const data = await response.json();
                    setContactTypeName(data.contact_type.name); // Update this line to access contact type name correctly
                } catch (error) {
                    setError(error.message);
                }
            }
        };

        fetchContactType();
    }, [dataItemsId]); // Re-fetch when dataItemsId changes

    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior

        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        const token = localStorage.getItem('token');
        
        if (!token) {
            setError('Authentication token is missing.');
            return; // Exit if token is not available
        }
    
        try {
            const response = await fetch(`${API_URL}/contact_types/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: contactTypeName.trim(), // The updated contact type name
                    updated_by: updatedBy,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to update contact type: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Contact type "${result.name}" updated successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Contact type updated successfully.',
            });

    
            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);
            
        } catch (error) {
            alertMessage('error'); // Display error message if something goes wrong
        }
    };

    if (!isVisible) return null; // Don't render the modal if not visible

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Contact Type</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Contact Type Name</label>
                        <input
                            type="text"
                            id="name"
                            value={contactTypeName}
                            onChange={(e) => setContactTypeName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Contact Type
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditContactType;
