import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import SlaAchieveList from '../../pages/task-details/sla-achive/SlaAchiveList';

const SlaAchieve = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-revision-type" buttonName="Add Revision Type" />
        <SlaAchieveList /> 
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default SlaAchieve;
