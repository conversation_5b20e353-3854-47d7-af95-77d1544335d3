<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_details', function (Blueprint $table) {
            $table->id();
            $table->string('department');
            $table->string('team');
            $table->string('ticket_number');
            $table->date('received_date');
            $table->date('due_date');
            $table->integer('unit');
            $table->string('account_name');
            $table->string('campaign_name');
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();

            // $table->unsignedBigInteger('department_id')->nullable();
            // $table->foreign('department_id')->references('id')->on('departments')->onDelete('cascade');

            // $table->unsignedBigInteger('team_id')->nullable();
            // $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade');
        
            // Foreign key for priorities table
            $table->unsignedBigInteger('priority_id')->nullable();
            $table->foreign('priority_id')->references('id')->on('priorities')->onDelete('cascade');
        
            // Foreign key for Task Type table
            $table->unsignedBigInteger('task_type_id')->nullable();
            $table->foreign('task_type_id')->references('id')->on('task_types')->onDelete('cascade');
        
            // Foreign key for Revision Type table
            $table->unsignedBigInteger('revision_type_id')->nullable();
            $table->foreign('revision_type_id')->references('id')->on('revision_types')->onDelete('cascade');
        
            // Foreign key for Product Type table
            $table->unsignedBigInteger('product_type_id')->nullable();
            $table->foreign('product_type_id')->references('id')->on('product_types')->onDelete('cascade');
        
            // Foreign key for Region table
            $table->unsignedBigInteger('region_id')->nullable();
            $table->foreign('region_id')->references('id')->on('regions')->onDelete('cascade');
        
            // Foreign key for Team reporter table
            $table->unsignedBigInteger('reporter_id')->nullable();
            $table->foreign('reporter_id')->references('id')->on('reporters')->onDelete('cascade');
        
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_details');
    }
};
