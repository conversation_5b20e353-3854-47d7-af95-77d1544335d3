import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const isTokenValid = () => {
    return localStorage.getItem('token') !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddGiveFeedback = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [subject, setSubject] = useState('');
    const [message, setMessage] = useState('');
    const [status, setStatus] = useState('');
    const [statuses, setStatuses] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    useEffect(() => {
        const fetchStatuses = async () => {
            try {
                const token = localStorage.getItem('token'); // Get token from local storage
    
                if (!token) {
                    throw new Error("User is not authenticated. Please login.");
                }
    
                const response = await fetch(`${API_URL}/status`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error(`Failed to fetch statuses: ${response.statusText}`);
                }
    
                const data = await response.json();
    
                console.log("Status API Response:", data); // Debugging
    
                // Extract 'status' key from API response
                if (data.status && Array.isArray(data.status)) {
                    setStatuses(data.status); // Set the extracted array
                } else {
                    console.error("Unexpected response format:", data);
                    setStatuses([]);
                }
            } catch (error) {
                console.error("Error fetching statuses:", error.message);
                setError(error.message);
            }
        };
    
        fetchStatuses();
    }, []);
    
    
    
    

    const handleSubmit = async (event) => {
        event.preventDefault();
        setError('');
        setSuccessMessage('');

        if (!isTokenValid()) {
            setError('Authentication token is missing.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/give-feedback/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    subject: subject.trim(),
                    message: message.trim(),
                    status,
                }),
            });

            if (!response.ok) {
                throw new Error(`Failed to save feedback: ${response.statusText}`);
            }

            setSuccessMessage('Feedback added successfully!');
            setSubject('');
            setMessage('');
            setStatus('');
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-feedback';
    const handleClose = () => navigate('/give-feedback');

    return (
        isModalOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative">
                    <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                        &times;
                    </button>
                    <h4 className="text-xl font-semibold mb-4">Add New Feedback</h4>
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                                Subject
                            </label>
                            <input
                                type="text"
                                id="subject"
                                value={subject}
                                onChange={(e) => setSubject(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                                Message
                            </label>
                            <textarea
                                id="message"
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div className="mb-4">
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                                Status
                            </label>
                            <select
    id="status"
    value={status}
    onChange={(e) => setStatus(e.target.value)}
    required
    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
>
    <option value="">Select Status</option>
    {Array.isArray(statuses) && statuses.length > 0 ? (
        statuses.map((statusItem) => (
            <option key={statusItem.id} value={statusItem.name}>
                {statusItem.name}
            </option>
        ))
    ) : (
        <option disabled>Loading statuses...</option>
    )}
</select>

                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Feedback
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        {error && <p className="text-red-500 text-sm">{error}</p>}
                    </form>
                </div>
            </div>
        )
    );
};

export default AddGiveFeedback;
