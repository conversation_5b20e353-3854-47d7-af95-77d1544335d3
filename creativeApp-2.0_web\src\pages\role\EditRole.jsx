import React, { useEffect, useState } from 'react';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditRole = ({ isVisible, setVisible, roleId }) => {
    const [roleName, setRoleName] = useState('');
    const [roles, setRoles] = useState([]); // Store roles fetched from API
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch the role details when the component mounts or roleId changes
    useEffect(() => {
        const fetchRoleName = async () => {
            if (!roleId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/roles`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch roles: ' + response.statusText);
                }

                const data = await response.json();
                const roleArray = data.roles; // Assuming the roles are returned as an array
                if (!Array.isArray(roleArray)) {
                    throw new Error('Expected roles to be an array.');
                }

                const roleData = roleArray.find(role => role.id === roleId);
                if (roleData) {
                    setRoleName(roleData.name);  // Set the name from the matching role
                } else {
                    throw new Error('Role not found. Please check the ID.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchRoleName();
    }, [roleId]);

    // Handle form submission to update the role
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedRoleName = roleName.trim();

        // Check if the role already exists in the current list
        if (Array.isArray(roles)) {
            const roleExists = roles.some(role => {
                const roleNameLower = role.name.toLowerCase().trim();
                return roleNameLower === trimmedRoleName.toLowerCase();
            });

            if (roleExists) {
                setError('Role already exists. Please add a different role.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }

        setError(''); // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return; // Exit if token is not available
            }

            // Assuming the user's first and last name are stored in localStorage
            const firstName = localStorage.getItem('fname');
            const lastName = localStorage.getItem('lname');
            const fullName = `${firstName} ${lastName}`;

            const response = await fetch(`${API_URL}/roles/${roleId}`, {
                method: 'PUT',  // Use PUT for updating
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedRoleName,
                    updated_by: fullName, // Include the full name of the user who updated
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update role: ' + response.statusText);
            }

            const result = await response.json();
            setSuccessMessage(result.message || 'Role updated successfully!');

            setRoleName(''); // Clear the input after success

            // Optionally, refetch roles after the update
            const newRolesResponse = await fetch(`${API_URL}/roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newRolesResponse.ok) {
                throw new Error('Failed to fetch roles: ' + newRolesResponse.statusText);
            }

            const newRolesData = await newRolesResponse.json();
            setRoles(newRolesData.roles || []); // Update the roles list

            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);

        } catch (error) {
            setError(error.message);
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Role</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                {error && <div className="text-red-500">{error}</div>}
                {successMessage && <div className="text-green-500">{successMessage}</div>}
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Role Name</label>
                        <input
                            type="text"
                            id="name"
                            value={roleName}
                            onChange={(e) => setRoleName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Role
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditRole;
