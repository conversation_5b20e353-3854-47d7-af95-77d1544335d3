<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Validation\ValidationException;
use App\Notifications\CustomResetPasswordNotification;
use Illuminate\Support\Facades\Notification;

use Illuminate\Support\Facades\Log;

class PasswordController extends Controller
{
    // Send the reset password email
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);
    
        // Get the user by email
        $user = User::where('email', $request->email)->first();
    
        if (!$user) {
            return response()->json(['error' => 'No user found with that email.'], 404);
        }
    
        // Construct the password reset URL pointing to the React frontend (port 3000)
        $resetUrl = 'http://127.0.0.1:3000/password/reset/{token}?email=' . $user->email;

        // Send the password reset link via notification
        $status = Password::sendResetLink(
            $request->only('email'),
            function ($user, $token) use ($resetUrl) {
                // Replace {token} with the actual token
                $finalResetUrl = str_replace('{token}', $token, $resetUrl);
                $user->notify(new CustomResetPasswordNotification($finalResetUrl));  // Send the notification
            }
        );

    
        if ($status == Password::RESET_LINK_SENT) {
            return response()->json(['message' => 'Password reset link sent!'], 200);
        }
    
        return response()->json(['error' => 'Unable to send reset link.'], 400);
    }
    

    public function reset(Request $request)
    {
        // Validate incoming request parameters
        $request->validate([
            'token' => 'required|string',  // This token comes from the URL (not localStorage)
            'email' => 'required|email',   // Email is also required from the URL
            'password' => 'required|confirmed|min:10',  // Ensure password confirmation
        ]);
    
        // Attempt to reset the password using the provided token and email
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                // Update the user's password securely
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->save();
            }
        );
    
        // Check the result of the reset attempt
        if ($status == Password::PASSWORD_RESET) {
            return response()->json([
                'message' => 'Password has been successfully reset!',
                'status' => 'success',
            ], 200);
        }
    
        // If reset failed, return an appropriate error response
        return response()->json([
            'message' => 'Failed to reset password. The reset token might be invalid or expired.',
            'status' => 'error',
        ], 400);
    }
    
    
}
