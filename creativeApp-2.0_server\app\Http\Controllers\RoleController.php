<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

use Illuminate\Support\Facades\Log;

class RoleController extends Controller
{
     /**
     * Show all role with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $roles = Role::all();
    
        // Log the roles retrieved
        Log::info('All Roles Retrieved:', ['roles_count' => $roles->count()]);
    
        return response()->json(['roles' => $roles], 200);
    }

    /**
     * Display the specified role.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the role by ID
        $role = Role::find($id);

        if (!$role) {
            return response()->json(['error' => 'role not found.'], 404);
        }

        // Log the role retrieved
        Log::info('Role Retrieved:', ['role' => $role]);

        return response()->json(['role' => $role], 200);
    }


        /**
     * Create a new role by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createRole(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        // Log the request data
        Log::info('Create Role Request:', ['request' => $request->all()]);

        // Check if the role name already exists
        if (Role::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Role already exists.'], 409);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Create a new role with the full name
            $role = Role::create([
                'name' => $request->name,
                'created_by' => $authUser->fname . ' ' . $authUser->lname, // Full name
                'updated_by' => $authUser->fname . ' ' . $authUser->lname  // Full name
            ]);

            Log::info('Role Created:', ['role' => $role]);

            return response()->json(['message' => 'Role created successfully.', 'role' => $role], 201);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Role Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create a role.'], 403);
    }


    /**
     * Update an existing role by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateRole(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        // Log the request data
        Log::info('Update Role Request:', ['request' => $request->all()]);

        // Find the role by ID
        $role = Role::find($id);
        
        if (!$role) {
            return response()->json(['error' => 'Role not found.'], 404);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Check if the role name is being updated and does not already exist
            if ($role->name !== $request->name && Role::where('name', $request->name)->exists()) {
                return response()->json(['error' => 'Role name already exists.'], 409);
            }

            // Update the role
            $role->name = $request->name;
            $role->updated_by = $authUser->fname . ' ' . $authUser->lname; // Full name
            $role->save();

            // Log the updated role
            Log::info('Role Updated:', ['role' => $role]);

            return response()->json(['message' => 'Role updated successfully.', 'role' => $role], 200);
        }

        // Deny access for other roles
        Log::warning('Unauthorized Role Update Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to update this role.'], 403);
    }


    /**
     * Delete a role by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteRole($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the role
            $role = Role::findOrFail($id);

            // Delete the role
            $role->delete();

            return response()->json(['message' => 'Role deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this role.'], 403);
    }

}
