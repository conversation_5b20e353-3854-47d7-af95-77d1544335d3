import React, { useEffect, useState } from 'react';
import EditTrainingCategory from './EditTrainingCategory';
import TableContent from '../../../common/table/TableContent';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const TrainingCategoryList = () => {
    const [trainingCategories, setTrainingCategories] = useState([]);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTrainingCategoryId, setSelectedTrainingCategoryId] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Product Type Name", key: "name" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchTrainingCategories = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/training-categories`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('Training Category', data);

                setTrainingCategories(data.trainingCategories.map(trainingCategory => ({
                    id: trainingCategory.id,
                    name: trainingCategory.name,
                    department: trainingCategory.department,
                    team: trainingCategory.team,
                    created_by: trainingCategory.created_by,
                    updated_by: trainingCategory.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            }
        };

        fetchTrainingCategories();
    }, []);

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/training-category/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete training category: ' + response.statusText);
            }

            // Update the training categories list after deletion
            setTrainingCategories(prevTrainingCategories => prevTrainingCategories.filter(trainingCategory => trainingCategory.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedTrainingCategoryId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={trainingCategories}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTrainingCategoryId}
            />
            {modalVisible && (
                <EditTrainingCategory
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    trainingCategoryId={selectedTrainingCategoryId}
                />
            )}
        </div>
    );
};

export default TrainingCategoryList;
