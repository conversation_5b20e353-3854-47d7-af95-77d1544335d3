import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditBillingStatus from './EditBillingStatus'; // Update import to your EditBillingStatus component

const isTokenValid = () => {
    const token = localStorage.getItem('token');

    return token !== null; // Additional validation logic can be added here
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const BillingStatusList = () => {
    const [billingStatuses, setBillingStatuses] = useState([]); // Updated variable name to billingStatuses
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedBillingStatusId, setSelectedBillingStatusId] = useState(null);
    const [error, setError] = useState(null);

    // Update column names for billing statuses
    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Billing Status Name", key: "name" }, // Updated for billing status
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchBillingStatuses = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }
    
            const token = localStorage.getItem('token');
    
            try {
                const response = await fetch(`${API_URL}/billing_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }
    
                const data = await response.json();
                
    
                // Access billing statuses with bracket notation
                if (Array.isArray(data['billing statuses'])) {
                    setBillingStatuses(data['billing statuses'].map(status => ({
                        id: status.id,
                        name: status.name,
                        created_by: status.created_by,
                        updated_by: status.updated_by,
                    })));
                } else {
                    setError('Invalid data format: billing statuses is not an array.');
                }
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchBillingStatuses();
    }, []);
    

    // Handle Delete
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/billing_statuses/${id}`, { // Updated endpoint for billing statuses
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete billing status: ' + response.statusText);
            }

            // Update the billing statuses list after deletion
            setBillingStatuses(prevStatuses => prevStatuses.filter(status => status.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    // Handle Edit
    const handleEdit = (id) => {
        setSelectedBillingStatusId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={billingStatuses}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible} // Pass modal state functions if needed
                setSelectedServiceId={setSelectedBillingStatusId} // Updated for billing status
            />
            {modalVisible && (
                <EditBillingStatus
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    billingStatusId={selectedBillingStatusId} // Updated for billing status
                />
            )}
        </div>
    );
};

export default BillingStatusList;
