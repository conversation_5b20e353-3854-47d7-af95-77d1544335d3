import { useState } from "react";

export default function TableContent({
    tableContent,
    columnNames,
    onDelete,
    onEdit, // Add onEdit to props
    setModalVisible,
    setSelectedServiceId,
    hideDeleteButton,
    renderCustomCell, // Add renderCustomCell to props
}) {
    const [openDropdown, setOpenDropdown] = useState(null);

    const toggleDropdown = (id) => {
        setOpenDropdown((prevId) => (prevId === id ? null : id));
    };

    return (
        <div className="overflow-x-auto px-4">
            <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        {columnNames.map((col, index) => (
                            <th key={index} scope="col" className="px-4 py-3 whitespace-nowrap">
                                {col.label}
                            </th>
                        ))}
                        <th scope="col" className="px-4 py-3">
                            <span className="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {tableContent.map((tableData) => (
                        <tr key={tableData.id} className="border-b dark:border-gray-700 relative">
                            {columnNames.map((col, index) => (
                                <td key={index} className="px-4 py-3 whitespace-nowrap">
                                    {/* Check if a renderCustomCell function is provided */}
                                    {renderCustomCell 
                                        ? renderCustomCell(col, tableData) // Use custom cell rendering if provided
                                        : (tableData[col.key] || '-')} {/* Default rendering */}
                                </td>
                            ))}
                            <td className="px-4 py-3 flex items-center justify-end">
                                <button
                                    id={`${tableData.id}-dropdown-button`}
                                    onClick={() => toggleDropdown(tableData.id)}
                                    className="inline-flex items-center p-0.5 text-sm font-medium text-center text-gray-500 hover:text-gray-800 rounded-lg focus:outline-none dark:text-gray-400 dark:hover:text-gray-100"
                                    type="button"
                                >
                                    <span className="material-symbols-outlined">more_vert</span>
                                </button>
                                {openDropdown === tableData.id && (
                                    <div
                                        id={`${tableData.id}-dropdown`}
                                        className="z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600 absolute top-0"
                                    >
                                        <div className="relative">
                                            <button
                                                className="absolute right-1 top-1 z-20 cursor-pointer"
                                                onClick={() => toggleDropdown(null)}
                                            >
                                                <span className="material-symbols-outlined">close</span>
                                            </button>
                                            <ul
                                                className="py-1 text-sm text-gray-700 dark:text-gray-200"
                                                aria-labelledby={`${tableData.id}-dropdown-button`}
                                            >
                                                <li>
                                                    <button
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            setSelectedServiceId(tableData.id);
                                                            onEdit(tableData.id); // Use onEdit here
                                                            setModalVisible(true);
                                                        }}
                                                        className="inline-flex items-center w-full p-0.5 text-sm font-medium text-center text-gray-500 hover:text-gray-800 rounded-lg focus:outline-none"
                                                        type="button"
                                                    >
                                                        <span className="text-left w-full block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            Edit
                                                        </span>
                                                    </button>
                                                </li>
                                            </ul>
                                            {!hideDeleteButton && ( // Hide delete button if hideDeleteButton is true
                                                <div className="py-1">
                                                    <button
                                                        onClick={() => onDelete(tableData.id)}
                                                        className="w-full text-left block py-2 px-4 text-sm text-red-600 hover:bg-red-100 dark:hover:bg-red-600 dark:text-red-200"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
