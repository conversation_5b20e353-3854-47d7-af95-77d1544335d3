<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Designation;
use App\Models\Department;
use App\Models\Team;
use App\Models\Role;
use App\Models\Resource_type;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // User::factory(10)->create();

        $departments = Department::all();
        $teams = Team::all();
        $roles = Role::all();
        $designations = Designation::all();
        $resource_types = Resource_type::all();
         // Check if there are designations in the database
         if ($designations->isEmpty()) {
            $this->command->warn('No designations found. Please add designations to the database before seeding users.');
            return; // Stop execution if no designations exist
        }

        if ($resource_types->isEmpty()) {
            $this->command->warn('No resource types found. Please add resource types to the database before seeding users.');
            return; // Stop execution if no resource types exist
        }

        User::factory(30)->create()->each(function ($user) use ($departments, $teams, $roles, $designations, $resource_types) {
            $user->departments()->attach($departments->random(rand(1, 3))->pluck('id')->toArray());
            $user->teams()->attach($teams->random(rand(1, 3))->pluck('id')->toArray());
            $user->roles()->attach($roles->random(rand(1, 3))->pluck('id')->toArray());
            $user->designations()->attach($designations->random(rand(1, 3))->pluck('id')->toArray());
            $user->resourceTypes()->attach($resource_types->random(rand(1, 2))->pluck('id')->toArray());
        });
    }
}