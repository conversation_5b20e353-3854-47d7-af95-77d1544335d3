import React from 'react';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import RoleList from '../../pages/role/RoleList';

const Roles = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      {/* <h4 className="text-xl font-bold text-left">Member Role List</h4> */}
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-role" buttonName="Add New Role" />
        <RoleList />
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default Roles;
