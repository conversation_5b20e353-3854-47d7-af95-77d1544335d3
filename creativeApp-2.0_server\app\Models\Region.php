<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Region extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'department_id',
        'team_id',
        'created_by',
        'updated_by'
    ];
    
    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
    
    public function users()
    {
        return $this->belongsToMany(User::class);
    }

    public function taskDetails()
    {
        return $this->hasMany(taskDetails::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }
}
