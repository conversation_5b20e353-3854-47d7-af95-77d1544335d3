import { useState } from "react";

const TableContentTodo = ({
    tableContent,
    columnNames,
    onDelete,
    onEdit, // Add onEdit to props
    setModalVisible,
    setSelectedServiceId,
    hideDeleteButton,
    renderCustomCell, // Add renderCustomCell to props
}) => {
    const [openDropdown, setOpenDropdown] = useState(null);

    const toggleDropdown = (id) => {
        setOpenDropdown((prevId) => (prevId === id ? null : id));
    };
    return (
        <div className="overflow-x-auto px-4">
            <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        {columnNames.map((col, index) => (
                            <th key={index} scope="col" className="px-4 py-3 whitespace-nowrap">
                                {col.label}
                            </th>
                        ))}
                        <th scope="col" className="px-4 py-3 flex items-center justify-center">
                            <span>Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {tableContent.map((tableData) => (
                        <tr key={tableData.id} className="hover:bg-gray-200 relative">
                            {columnNames.map((col, index) => (
                                <td key={index} className="px-4 py-3 whitespace-nowrap">
                                    {/* Check if a renderCustomCell function is provided */}
                                    {renderCustomCell
                                        ? renderCustomCell(col, tableData) // Use custom cell rendering if provided
                                        : (tableData[col.key] || '-')} {/* Default rendering */}
                                </td>
                            ))}
                            <td className="px-4 py-3 flex items-center justify-center">
                                <button
                                    id={`${tableData.id}-dropdown-button`}
                                    onClick={() => toggleDropdown(tableData.id)}
                                    className="inline-flex items-center p-0.5 gap-4 text-sm font-medium text-center text-gray-500 hover:text-gray-800 rounded-lg focus:outline-none dark:text-gray-400 dark:hover:text-gray-100"
                                    type="button"
                                >
                                    <span className="material-symbols-outlined">draw</span>
                                    <span className="material-symbols-outlined">delete</span>
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}

export default TableContentTodo;