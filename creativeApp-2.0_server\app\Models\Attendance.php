<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Attendance extends Model
{
    use HasFactory;

    protected $table = 'attendances';

    protected $fillable = [
        'department_id',
        'team_id',
        'user_id',
        'schedule_planner_id',
        'date',
        'start',
        'end',
        'duration',
        'entry_type',
        'details',
        'approval_status',
        'approval_notes',
        'created_by',
        'updated_by',
    ];

    protected $dates = ['date']; // Ensure date is cast properly

    /**
     * Relationships
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function schedule_planner()
    {
        return $this->belongsTo(SchedulePlanner::class);
    }

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessors & Mutators
     */
    public function getFormattedDateAttribute()
    {
        return $this->date->format('d M, Y');
    }

    public function getFormattedStartTimeAttribute()
    {
        return $this->start ? date('h:i A', strtotime($this->start)) : null;
    }

    public function getFormattedEndTimeAttribute()
    {
        return $this->end ? date('h:i A', strtotime($this->end)) : null;
    }

    public function getHumanReadableEntryTypeAttribute()
    {
        return ucfirst(str_replace('_', ' ', $this->entry_type));
    }

    public function getHumanReadableApprovalStatusAttribute()
    {
        return ucfirst($this->approval_status);
    }

    /**
     * Scopes for Filtering
     */
    public function scopeOfType(Builder $query, $type): Builder
    {
        return $query->where('entry_type', $type);
    }

    public function scopePendingApproval(Builder $query): Builder
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('approval_status', 'rejected');
    }

    public function scopeForUser(Builder $query, $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
}
