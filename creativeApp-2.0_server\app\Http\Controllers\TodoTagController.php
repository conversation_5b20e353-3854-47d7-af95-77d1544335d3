<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TodoTag;
use Illuminate\Support\Facades\Log;

class TodoTagController extends Controller
{

    /**
     * Display a listing of all todoTag.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $todoTags = TodoTag::all();

        // Log the todoTag retrieved
        Log::info('All todoTag Retrieved:', ['todoTag_count' => $todoTags->count()]);

        return response()->json(['todoTags' => $todoTags], 200);
    }

    /**
     * Display the specified todoTag.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the todoTag by ID
        $todoTag = TodoTag::find($id);

        if (!$todoTag) {
            return response()->json(['error' => 'todoTag not found.'], 404);
        }

        // Log the todoTag retrieved
        Log::info('todoTag Retrieved:', ['todoTag' => $todoTag]);

        return response()->json(['todoTag' => $todoTag], 200);
    }

    /**
     * Create a new todoTag.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id,                     
        ]);
         Log::info('Create todoTag Request:', ['request' => $request->all()]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Log the request data
        Log::info('Create todoTag Request:', ['request' => $request->all()]);

        // Check if the todoTag name already exists
        if (TodoTag::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'todoTag already exists.'], 409);
        }

        // Create a new todoTag
        $todoTag = TodoTag::create([
            'name' => $request->name,
            'creator_id' => $authUser->id,
        ]);

        Log::info('todoTag Created:', ['todoTag' => $todoTag]);

        return response()->json(['message' => 'todoTag created successfully.', 'todoTag' => $todoTag], 201);
    }

    /**
     * Update an existing todoTag.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Log the request data
        Log::info('Update todoTag Request:', ['request' => $request->all()]);

        // Find the todoTag by ID
        $todoTag = TodoTag::find($id);

        if (!$todoTag) {
            return response()->json(['error' => 'todoTag not found.'], 404);
        }

        // Check if the todoTag name is being updated and does not already exist
        if ($todoTag->name !== $request->name && TodoTag::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'TodoTag name already exists.'], 409);
        }

        // Update the todoTag
        $todoTag->update([
            'name' => $request->name,
            'updater_id' => $authUser->id,
        ]);

        // Log the updated todoTag
        Log::info('todoTag Updated:', ['todoTag' => $todoTag]);

        return response()->json(['message' => 'TodoTag updated successfully.', 'todoTag' => $todoTag], 200);
    }


    /**
     * Delete a todoTag.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the todoTag
            $todoTag = TodoTag::findOrFail($id);

            // Delete the todoTag
            $todoTag->delete();

            Log::info('todoTag Deleted:', ['todoTag_id' => $id]);

            return response()->json(['message' => 'TodoTag deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete the TodoTag.'], 403);
    }
}
