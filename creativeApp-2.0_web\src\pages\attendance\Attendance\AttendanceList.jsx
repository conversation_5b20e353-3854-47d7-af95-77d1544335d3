import React, { useState, useEffect } from "react";
// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";
import moment from 'moment';
// Loading spinner component to show while data is loading
import Loading from "../../../common/Loading";

import {
  confirmationAlert,
  ManageColumns,
  SearchFilter,
  TableView,
  FormView,
  Image,
} from "../../../common/coreui";

import {
  useGetAttendanceQuery,
  useCreateAttendanceRecordMutation,
  useUpdateAttendanceRecordMutation,
  useLazyFetchDataOptionsForAttendanceFilterByQuery,
  useDeleteAttendanceRecordMutation,
  attendanceFormationApi,
} from "../../../features/api";

import { useDispatch } from "react-redux";
import {
  allowedRoles,
  haveAccess,
  defaultDateTimeFormat,
  defaultDateFormat,
  durationToMinutes,
  defaultDurationFormat,
  sortByLabel,
  removeKeys,
  defaultTimeFormat,
  defaultStartAndEndDateFromWeekNumber
} from "../../../utils";

// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import { a } from "@react-spring/web";

// API endpoint and configuration constants
let MODULE_NAME = "Attendance";

// Main component for listing Attendance Formation records
const AttendanceFormationList = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [viewData, setViewData] = useState(null);
  const [error, setError] = useState(null);
  const [showAddBtn, setShowAddBtn] = useState(true);
  
  // Role-based access control  
  const canAccess = allowedRoles.admin;
  const canEdit = haveAccess(allowedRoles.shiftLead);
  const canDelete = haveAccess(allowedRoles.hod);

  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);

  const [ActiveAttendanceType, setActiveAttendanceType] =
    useState("attendance");
  const AttendanceType = {
    attendance: `<span class="material-symbols-outlined me-2">co_present</span> <span>Attendance</span>`,
    break: `<span class="material-symbols-outlined  me-2">local_cafe</span> <span>Break</span> `,
    early_leave: `<span class="material-symbols-outlined  me-2">exit_to_app</span> <span>Early Leave</span> `,
    late_entry: `<span class="material-symbols-outlined  me-2">assignment_late</span> <span>Late Entry</span> `,
  };

  const {
    data: dataItems,
    isFetching,
    error: fetchError,
  } = useGetAttendanceQuery({
    entry_type: ActiveAttendanceType,
    sort_by: sortColumn,
    order: sortDirection,
    page: currentPage,
    per_page: perPage,
    query: queryString,
  });

  const [triggerFilterByFetch] =
    useLazyFetchDataOptionsForAttendanceFilterByQuery();

  const [deleteFormationType] = useDeleteAttendanceRecordMutation();

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "");

    setQueryString(q);
  };

  const handleCopy = (data) => {
    const keysToRemove = [
      "id",
      "team",
      "department",
      "updated_at",
      "updated_by",
      "updater",
      "created_at",
      "creator",
      "created_by",
      "updated_by",
    ];
    const cleanedData = removeKeys(data, keysToRemove);
    setError(null);
    setViewData(null);
    setFormData(cleanedData);
    setModalVisible(true);
  };

  const handleEdit = (data) => {
    setError(null);
    setViewData(null);
    setFormData(data);
    setModalVisible(true);
  };

  const handleDelete = (id) => {
    confirmationAlert({
      onConfirm: () => {
        deleteFormationType(id);
        setViewData(null);
      },
    });
  };

  const [formData, setFormData] = useState({});

  const [updateFormationType] = useUpdateAttendanceRecordMutation();
  const [createFormationType] = useCreateAttendanceRecordMutation();

  const handleSubmit = async (formData = []) => {

    let response = null;
    try {
      const user_id = localStorage.getItem("user_id");

      // console.log(formData)

      // if (formData && !formData.start) { return null;}

      formData["entry_type"] = ActiveAttendanceType;
      if (formData && formData.id) {
        formData["updated_by"] = user_id;
        response = await updateFormationType({
          id: formData.id,
          ...formData,
        });
      } else {
        formData["created_by"] = user_id;
        response = await createFormationType({
          ...formData,
        });
      }

      if (response && response?.error) {
        setError(response.error.data);
      } else {
        setError(null);
        setFormData(null);
        setModalVisible(false);
      }
    } catch (error) {}
  };

  let columnSerial = 1;
  // Memoize columns to prevent unnecessary recalculations
  const columnsForAttendance = [
    {
      id: columnSerial++,
      name: "Action",
      width: "180px",
      className: "bg-red-300",
      cell: (item) => (
        <div className="flex gap-1 mx-2 items-center justify-center pl-3">
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => {
              setViewData(item);
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              visibility
            </span>
          </button>

          {/* Edit Button */}
          {canEdit && 
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleEdit(item)}
          >
            <span className="material-symbols-outlined text-lg ">
              stylus_note
            </span>
          </button>
      }
          {/* Copy Button */}
          {/* <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => {
                handleCopy(item);
              }}
            >
              <span className="material-symbols-outlined text-lg ">
              content_copy
              </span>
            </button> */}
          {/* Delete Button */}
          {canDelete &&
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleDelete(item.id)}
          >
            <span className="material-symbols-outlined text-sm ">delete</span>
          </button>
          } 
        </div>
      ),
    },
    {
      id: columnSerial++,
      width: "50px",
      name: "S.No",
      // Calculate serial number based on current page and rows per page
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },

    

    {
      id: columnSerial++,
      name: "Name",
      width: "300px",
      db_field: "user.fname",
      // db_title_field: "user.fname",
      selector: (row) =>
        row?.user?.fname ? `${row?.user?.fname} ${row?.user?.lname}` : "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.user?.photo} />
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.user?.fname && (
              <b>
                {row?.user?.fname} {row?.user?.lname}
              </b>
            )}
            {row?.user?.designations && (
              <div>{row?.user?.designations?.[0]?.name}</div>
            )}
          </div>
        </div>
      ),
      sortable: true,
      omit: false,
      filterable: canEdit && "searchable",
      form: {
        canAccess: canAccess,
        type: "UsersByDefaultTeamDropdown",
        dependentField: "team_id",
        props: {
          isMulti: false,
          isSearchable: true
        }
      }
    },
    {
      id: columnSerial++,
      width: "80px",
      name: "EID",
      db_field: "user_id",
      db_title_field: "user.eid",
      selector: (row) => row?.user?.eid || "",
      sortable: true,
      omit: false,
      filterable: canEdit,
    },

    {
      id: columnSerial++,
      width: "280px",
      name: "Date",
      selector: (row) => defaultDateFormat(row.date),
      db_field: "date",
      omit: false,
      sortable: true,
      // form: {
      //   type: "date",
      // },
    },
    {
      id: columnSerial++,
      name: "Start",
      width: "280px",
      selector: (row) => (row.start ? defaultDateTimeFormat(row.start) : ""),
      cell: (row) =>
        row.start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.start)}
          </span>
        ) : (
          ""
        ),
      db_field: "start",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "End",
      selector: (row) => (row.end ? defaultDateTimeFormat(row.end) : ""),
      cell: (row) =>
        row.end ? (
          <span className="rounded-full px-3 py-2 bg-green-200 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.end)}
          </span>
        ) : (
          ""
        ),
      db_field: "end",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Total Working Time",
      selector: (row) =>
        row.duration ? defaultDurationFormat(row.duration) : "",
      cell: (row) =>
        row.duration ? (
          <span
            className={
              durationToMinutes(row.duration) > 9 * 60
                ? "rounded-full px-3 py-2 bg-yellow-100 border-yellow-600 border text-yellow-800"
                : durationToMinutes(row.duration) < 8 * 60
                ? "rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800"
                : "rounded-full px-3 py-2 bg-blue-100 border-blue-800 border text-blue-800"
            }
          >
            {defaultDurationFormat(row.duration)}
          </span>
        ) : (
          ""
        ),
      db_field: "duration",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      width: '250px',
      name: "Week number",
      selector: (row) => `Week: ${row.schedule_planner?.weeknum}: ${defaultStartAndEndDateFromWeekNumber(row.schedule_planner?.weeknum)}` || "",
      cell: (row) => (<div className="">Week: {row.schedule_planner?.weeknum}<br />{defaultStartAndEndDateFromWeekNumber(row.schedule_planner?.weeknum)}</div>),
      omit: false,
      db_field: "schedule_planner_id",
      sortable: true,
      filterable: true
    },

    {
      id: columnSerial++,
      name: "Shift Name",
      width: "280px",
      selector: (row) => row.schedule_planner?.schedule?.shift_name || "",
      db_field: "schedule_planner_id",
      omit: false,
      sortable: true,
      filterable: false,
    },

    {
      id: columnSerial++,
      name: "Shift Start",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_start
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift End",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_end
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_end ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Department",
      selector: (row) => row?.department?.name || "",
      db_title_field: "department.name",
      db_field: "department_id",
      sortable: true,
      omit: false,
      filterable: canEdit,
    },
    {
      id: columnSerial++,
      name: "Team",
      selector: (row) => row?.team?.name || "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.team?.logo} />
          {/* {row?.user?.photo && <img className="rounded-full w-10 h-10 border border-gray-500 me-2" src={AvatarImg} />} */}
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.team?.name && <span>{row?.team?.name}</span>}
          </div>
        </div>
      ),
      db_title_field: "team.name",
      db_field: "team_id",
      omit: false,
      sortable: true,
      filterable: canEdit,
    },
   
    {
      id: columnSerial++,
      name: "Updated by",
      selector: (row) => row.updater?.fname || "",
      db_field: "updated_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      name: "Created by",
      selector: (row) => row.creator?.fname || "",
      db_field: "created_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Updated At",
      selector: (row) => defaultDateTimeFormat(row.updated_at),
      db_field: "updated_at",
      omit: false,
      sortable: true,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Created At",
      selector: (row) => defaultDateTimeFormat(row.created_at),
      db_field: "created_at",
      omit: false,
      sortable: true,
    },
  ];

  const columnsForBreak = [
    {
      id: columnSerial++,
      name: "Action",
      width: "180px",
      className: "bg-red-300",
      cell: (item) => (
        <div className="flex gap-1 mx-2 items-center justify-center pl-3">
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => {
              setViewData(item);
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              visibility
            </span>
          </button>

          {/* Edit Button */}
          {canEdit && 
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleEdit(item)}
          >
            <span className="material-symbols-outlined text-lg ">
              stylus_note
            </span>
          </button>
          }
          {/* Copy Button */}
          {/* <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => {
                handleCopy(item);
              }}
            >
              <span className="material-symbols-outlined text-lg ">
              content_copy
              </span>
            </button> */}
          {/* Delete Button */}
          {canDelete &&
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleDelete(item.id)}
          >
            <span className="material-symbols-outlined text-sm ">delete</span>
          </button>
        }
        </div>
      ),
    },
    {
      id: columnSerial++,
      width: "50px",
      name: "S.No",
      // Calculate serial number based on current page and rows per page
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },

    {
      id: columnSerial++,
      name: "Name",
      width: "300px",
      db_field: "user_id",
      selector: (row) =>
        row?.user?.fname ? `${row?.user?.fname} ${row?.user?.lname}` : "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.user?.photo} />
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.user?.fname && (
              <b>
                {row?.user?.fname} {row?.user?.lname}
              </b>
            )}
            {row?.user?.designations && (
              <div>{row?.user?.designations?.[0]?.name}</div>
            )}
          </div>
        </div>
      ),
      sortable: true,
      omit: false,
      filterable: "searchable",
      form: {
        canAccess: canAccess,
        type: "UsersByDefaultTeamDropdown",
        dependentField: "team_id",
        props: {
          isMulti: false,
          isSearchable: true
        }
      }
    },
    {
      id: columnSerial++,
      width: "80px",
      name: "EID",
      db_field: "user_id",
      db_title_field: "user.eid",
      selector: (row) => row?.user?.eid || "",
      sortable: true,
      omit: false,
      filterable: true,
    },

    {
      id: columnSerial++,
      width: "280px",
      name: "Date",
      selector: (row) => defaultDateFormat(row.date),
      db_field: "date",
      omit: false,
      sortable: true,
      // form: {
      //   type: "date",
      // },
    },
    {
      id: columnSerial++,
      name: "Break Start",
      width: "280px",
      selector: (row) => (row.start ? defaultDateTimeFormat(row.start) : ""),
      cell: (row) =>
        row.start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.start)}
          </span>
        ) : (
          ""
        ),
      db_field: "start",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Break End",
      selector: (row) => (row.end ? defaultDateTimeFormat(row.end) : ""),
      cell: (row) =>
        row.end ? (
          <span className="rounded-full px-3 py-2 bg-green-200 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.end)}
          </span>
        ) : (
          ""
        ),
      db_field: "end",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },
    {
      id: columnSerial++,
      width: "150px",
      name: "Total Duration",
      selector: (row) =>
        row.duration ? defaultDurationFormat(row.duration) : "",
      cell: (row) =>
        row.duration ? (
          <span
            className={
              durationToMinutes(row.duration) > 60
              ? "rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800 w-full"
              : durationToMinutes(row.duration) > 15
              ? "rounded-full px-3 py-2 bg-yellow-100 border-yellow-600 border text-yellow-800 w-full"
                : "rounded-full px-3 py-2 bg-blue-100 border-blue-800 border text-blue-800 w-full"
            }
          >
            {defaultDurationFormat(row.duration)}
          </span>
        ) : (
          ""
        ),
      db_field: "duration",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Name",
      width: "280px",
      selector: (row) => row.schedule_planner?.schedule?.shift_name || "",

      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Start",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_start
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift End",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_end
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_end ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Department",
      selector: (row) => row?.department?.name || "",
      db_title_field: "department.name",
      db_field: "department_id",
      sortable: true,
      omit: false,
      filterable: true,
      // form: {
      //   type: "DepartmentDropdown",
      // }
    },
    {
      id: columnSerial++,
      name: "Team",
      selector: (row) => row?.team?.name || "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.team?.logo} />
          {/* {row?.user?.photo && <img className="rounded-full w-10 h-10 border border-gray-500 me-2" src={AvatarImg} />} */}
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.team?.name && <span>{row?.team?.name}</span>}
          </div>
        </div>
      ),
      db_title_field: "team.name",
      db_field: "team_id",
      omit: false,
      sortable: true,
      filterable: true,
      // form: {
      //   type: "TeamsDropdown",
      //   dependentField: "department_id",
        
      // }
    },

    {
      id: columnSerial++,
      name: "Approval status",
      
      selector: (row) => row.approval_status || "",
      omit: false,
      db_field: "approval_status",
      sortable: true,
      filterable: true,
      form: {
        canAccess: canAccess,
        type: "select",
        options: [
          { value: "pending", label: "pending" },
          { value: "approved", label: "approved" },
          { value: "rejected", label: "rejected" },
        ],  
      }
    },

    {
      id: columnSerial++,
      name: "Details for Break",
      width: "300px",
      selector: (row) => (row?.details ? row.details.trim() : ""),
      omit: false,
      db_field: "details",
      sortable: true,
      form: {
        type: "textarea",
      }
    },
   
    {
      id: columnSerial++,
      name: "Approval notes",
      selector: (row) => row?.approval_notes || "",
      db_field: "approval_notes",
      omit: false,
      sortable: false,
      filterable: false,
      form: {
        canAccess: canAccess,
        type: "textarea",
      }
    },

    {
      id: columnSerial++,
      name: "Updated by",
      selector: (row) => row.updater?.fname || "",
      db_field: "updated_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      name: "Created by",
      selector: (row) => row.creator?.fname || "",
      db_field: "created_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Updated At",
      selector: (row) => defaultDateTimeFormat(row.updated_at),
      db_field: "updated_at",
      omit: false,
      sortable: true,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Created At",
      selector: (row) => defaultDateTimeFormat(row.created_at),
      db_field: "created_at",
      omit: false,
      sortable: true,
    },
  ];

  const columnsForEarlyLeave = [
    {
      id: columnSerial++,
      name: "Action",
      width: "180px",
      className: "bg-red-300",
      cell: (item) => (
        <div className="flex gap-1 mx-2 items-center justify-center pl-3">
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => {
              setViewData(item);
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              visibility
            </span>
          </button>

          {/* Edit Button */}
          {canEdit && 
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleEdit(item)}
          >
            <span className="material-symbols-outlined text-lg ">
              stylus_note
            </span>
          </button>}
          {/* Copy Button */}
          {/* <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => {
              handleCopy(item);
            }}
          >
            <span className="material-symbols-outlined text-lg ">
            content_copy
            </span>
          </button> */}
          {/* Delete Button */}
          {canDelete &&
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleDelete(item.id)}
          >
            <span className="material-symbols-outlined text-sm ">delete</span>
          </button>}
        </div>
      ),
    },
    {
      id: columnSerial++,
      width: "50px",
      name: "S.No",
      // Calculate serial number based on current page and rows per page
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },

    {
      id: columnSerial++,
      name: "Name",
      width: "300px",
      db_field: "user_id",
      selector: (row) =>
        row?.user?.fname ? `${row?.user?.fname} ${row?.user?.lname}` : "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.user?.photo} />
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.user?.fname && (
              <b>
                {row?.user?.fname} {row?.user?.lname}
              </b>
            )}
            {row?.user?.designations && (
              <div>{row?.user?.designations?.[0]?.name}</div>
            )}
          </div>
        </div>
      ),
      sortable: true,
      omit: false,
      filterable: "searchable",
      form: {
        canAccess: canAccess,
        type: "UsersByDefaultTeamDropdown",
        dependentField: "team_id",
        props: {
          isMulti: false,
          isSearchable: true
        }
      }
    },
    {
      id: columnSerial++,
      width: "80px",
      name: "EID",
      db_field: "user_id",
      db_title_field: "user.eid",
      selector: (row) => row?.user?.eid || "",
      sortable: true,
      omit: false,
      filterable: true,
    },

    {
      id: columnSerial++,
      width: "280px",
      name: "Date",
      selector: (row) => defaultDateFormat(row.date),
      db_field: "date",
      omit: false,
      sortable: true,
      // form: {
      //   type: "date",
      // },
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Early Leaving Time",
      selector: (row) => (row.end ? defaultDateTimeFormat(row.end) : ""),
      cell: (row) =>
        row.end ? (
          <span className="rounded-full px-3 py-2 bg-green-200 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.end)}
          </span>
        ) : (
          ""
        ),
      db_field: "end",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },
    {
      id: columnSerial++,
      width: "200px",
      name: "Total Duration",
      selector: (row) =>
        row.duration ? defaultDurationFormat(row.duration) : "",
      cell: (row) =>
        row.duration ? (
          <span
            className={
              durationToMinutes(row.duration) > 9 * 60
                ? "rounded-full px-3 py-2 bg-yellow-100 border-yellow-600 border text-yellow-800 w-full"
                : durationToMinutes(row.duration) < 8 * 60
                ? "rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800 w-full"
                : "rounded-full px-3 py-2 bg-blue-100 border-blue-800 border text-blue-800 w-full"
            }
          >
            {defaultDurationFormat(row.duration)}
          </span>
        ) : (
          ""
        ),
      db_field: "duration",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Name",
      width: "200px",
      selector: (row) => row.schedule_planner?.schedule?.shift_name || "",

      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Start",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_start
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift End",
      width: "280px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_end
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_end ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Department",
      selector: (row) => row?.department?.name || "",
      db_title_field: "department.name",
      db_field: "department_id",
      sortable: true,
      omit: false,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Team",
      selector: (row) => row?.team?.name || "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.team?.logo} />
          {/* {row?.user?.photo && <img className="rounded-full w-10 h-10 border border-gray-500 me-2" src={AvatarImg} />} */}
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.team?.name && <span>{row?.team?.name}</span>}
          </div>
        </div>
      ),
      db_title_field: "team.name",
      db_field: "team_id",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Approval status",
      selector: (row) => row.approval_status || "",
      omit: false,
      db_field: "approval_status",
      sortable: true,
      filterable: true,
      form: {
        canAccess: canAccess,
        type: "select",
        options: [
          { value: "pending", label: "pending" },
          { value: "approved", label: "approved" },
          { value: "rejected", label: "rejected" },
        ],  
      }
    },
    {
      id: columnSerial++,
      name: "Details of Early Leave",
      width: "350px",
      selector: (row) => (row?.details ? row.details.trim() : ""),
      omit: false,
      db_field: "details",
      sortable: true,
      form: {
        type: "textarea",
      }
    },
   

    {
      id: columnSerial++,
      name: "Approval notes",
      selector: (row) => row?.approval_notes || "",
      db_field: "approval_notes",
      omit: false,
      sortable: false,
      filterable: false,
      form: {
        canAccess: canAccess,
        type: "textarea",
      }
    },

    {
      id: columnSerial++,
      name: "Updated by",
      selector: (row) => row.updater?.fname || "",
      db_field: "updated_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      name: "Created by",
      selector: (row) => row.creator?.fname || "",
      db_field: "created_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Updated At",
      selector: (row) => defaultDateTimeFormat(row.updated_at),
      db_field: "updated_at",
      omit: false,
      sortable: true,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Created At",
      selector: (row) => defaultDateTimeFormat(row.created_at),
      db_field: "created_at",
      omit: false,
      sortable: true,
    },
  ];

  const columnsForLateEntry = [
    {
      id: columnSerial++,
      name: "Action",
      width: "180px",
      className: "bg-red-300",
      cell: (item) => (
        <div className="flex gap-1 mx-2 items-center justify-center pl-3">
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => {
              setViewData(item);
            }}
          >
            <span className="material-symbols-outlined text-lg ">
              visibility
            </span>
          </button>

          {/* Edit Button */}
          {canEdit &&
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleEdit(item)}
          >
            <span className="material-symbols-outlined text-lg ">
              stylus_note
            </span>
          </button>}
          {/* Copy Button */}
          {/* <button
          className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
          onClick={() => {
            handleCopy(item);
          }}
        >
          <span className="material-symbols-outlined text-lg ">
          content_copy
          </span>
        </button> */}
          {/* Delete Button */}
          {canDelete &&
          <button
            className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
            onClick={() => handleDelete(item.id)}
          >
            <span className="material-symbols-outlined text-sm ">delete</span>
          </button>
          } 
        </div>
      ),
    },
    {
      id: columnSerial++,
      width: "50px",
      name: "S.No",
      // Calculate serial number based on current page and rows per page
      selector: (row, index) => (currentPage - 1) * perPage + index + 1,
      width: "80px",
      omit: false,
    },

    {
      id: columnSerial++,
      name: "Name",
      width: "300px",
      db_field: "user_id",
      selector: (row) =>
        row?.user?.fname ? `${row?.user?.fname} ${row?.user?.lname}` : "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.user?.photo} />
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.user?.fname && (
              <b>
                {row?.user?.fname} {row?.user?.lname}
              </b>
            )}
            {row?.user?.designations && (
              <div>{row?.user?.designations?.[0]?.name}</div>
            )}
          </div>
        </div>
      ),
      sortable: true,
      omit: false,
      filterable: "searchable",
      form: {
        canAccess: canAccess,
        type: "UsersByDefaultTeamDropdown",
        dependentField: "team_id",
        props: {
          isMulti: false,
          isSearchable: true
        }
      }
    },
    {
      id: columnSerial++,
      width: "80px",
      name: "EID",
      db_field: "user_id",
      db_title_field: "user.eid",
      selector: (row) => row?.user?.eid || "",
      sortable: true,
      omit: false,
      filterable: true,
    },

    {
      id: columnSerial++,
      width: "280px",
      name: "Date",
      selector: (row) => defaultDateFormat(row.date),
      db_field: "date",
      omit: false,
      sortable: true,
      // form: {
      //   type: "date",
      // },
    },
    {
      id: columnSerial++,
      name: "Arriving Time",
      width: "280px",
      selector: (row) => (row.start ? defaultDateTimeFormat(row.start) : ""),
      cell: (row) =>
        row.start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultDateTimeFormat(row.start)}
          </span>
        ) : (
          ""
        ),
      db_field: "start",
      omit: false,
      sortable: true,
      form: {
        type: "datetime",
        props: {
          minDate: moment().subtract(1, "days").toDate(),
          maxDate: new Date(),
        }
      },
    },

    {
      id: columnSerial++,
      width: "200px",
      name: "Late Duration",
      selector: (row) =>
        row.duration ? defaultDurationFormat(row.duration) : "",
      cell: (row) =>
        row.duration ? (
          <span
            className={
              durationToMinutes(row.duration) > 9 * 60
                ? "rounded-full px-3 py-2 bg-yellow-100 border-yellow-600 border text-yellow-800 w-full"
                : durationToMinutes(row.duration) < 8 * 60
                ? "rounded-full px-3 py-2 bg-red-100 border-red-600 border text-red-800 w-full"
                : "rounded-full px-3 py-2 bg-blue-100 border-blue-800 border text-blue-800 w-full"
            }
          >
            {defaultDurationFormat(row.duration)}
          </span>
        ) : (
          ""
        ),
      db_field: "duration",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Name",
      width: "200px",
      selector: (row) => row.schedule_planner?.schedule?.shift_name || "",

      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift Start",
      width: "200px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_start
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_start ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_start)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Shift End",
      width: "200px",
      selector: (row) =>
        row.schedule_planner?.schedule?.shift_end
          ? defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)
          : "",
      cell: (row) =>
        row.schedule_planner?.schedule?.shift_end ? (
          <span className="rounded-full px-3 py-2 bg-lime-100 border-green-800 border text-green-800 w-full">
            {defaultTimeFormat(row.schedule_planner?.schedule?.shift_end)}
          </span>
        ) : (
          ""
        ),
      db_field: "",
      omit: false,
      sortable: true,
    },

    {
      id: columnSerial++,
      name: "Department",
      selector: (row) => row?.department?.name || "",
      db_title_field: "department.name",
      db_field: "department_id",
      sortable: true,
      omit: false,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Team",
      selector: (row) => row?.team?.name || "",
      cell: (row) => (
        <div className="flex align-middle items-center w-full text-start ">
          <Image src={row?.team?.logo} />
          {/* {row?.user?.photo && <img className="rounded-full w-10 h-10 border border-gray-500 me-2" src={AvatarImg} />} */}
          <div className="flex-col align-middle items-center w-full text-start ">
            {row?.team?.name && <span>{row?.team?.name}</span>}
          </div>
        </div>
      ),
      db_title_field: "team.name",
      db_field: "team_id",
      omit: false,
      sortable: true,
      filterable: true,
    },
    {
      id: columnSerial++,
      name: "Details of Late Arrival",
      width: "300px",
      selector: (row) => (row?.details ? row.details.trim() : ""),
      omit: false,
      db_field: "details",
      sortable: true,
      form: {
        type: "textarea",
      }
    },
    {
      id: columnSerial++,
      name: "Approval status",
      selector: (row) => row.approval_status || "",
      omit: false,
      db_field: "approval_status",
      sortable: true,
      filterable: true,
      form: {
        canAccess: canAccess,
        type: "select",
        options: [
          { value: "pending", label: "pending" },
          { value: "approved", label: "approved" },
          { value: "rejected", label: "rejected" },
        ],  
      }
    },

    {
      id: columnSerial++,
      name: "Approval notes",
      selector: (row) => row?.approval_notes || "",
      db_field: "approval_notes",
      omit: false,
      sortable: false,
      filterable: false,
      form: {
        canAccess: canAccess,
        type: "textarea",
      }
    },

    {
      id: columnSerial++,
      name: "Updated by",
      selector: (row) => row.updater?.fname || "",
      db_field: "updated_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      name: "Created by",
      selector: (row) => row.creator?.fname || "",
      db_field: "created_by",
      omit: false,
      sortable: true,
      filterable: false,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Updated At",
      selector: (row) => defaultDateTimeFormat(row.updated_at),
      db_field: "updated_at",
      omit: false,
      sortable: true,
    },
    {
      id: columnSerial++,
      width: "280px",
      name: "Created At",
      selector: (row) => defaultDateTimeFormat(row.created_at),
      db_field: "created_at",
      omit: false,
      sortable: true,
    },
  ];

  const [columns, setColumns] = useState(columnsForAttendance);

  useEffect(() => {
    if(isFetching) return;
    
    switch (ActiveAttendanceType) {
      case "attendance":
        setColumns(columnsForAttendance);
        break;
      case "break":
        setColumns(columnsForBreak);
        break;
      case "early_leave":
        setColumns(columnsForEarlyLeave);
        break;
      case "late_entry":
        setColumns(columnsForLateEntry);
        break;
      default:
        setColumns(columnsForAttendance);
        break;
    }
  }, [ActiveAttendanceType, isFetching]);

  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).map((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }

        return null;
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj });
    }
    setCurrentPage(1);
  };

  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      // Fetch all data items for Excel export
      const result = await dispatch(
        attendanceFormationApi.endpoints.getAttendance.initiate({
          entry_type: ActiveAttendanceType,
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues
          query: queryString,
        })
      ).unwrap(); // Wait for the API response

      if (!result?.total || result.total < 1) {
        return false;
      }

      var sl = 1;

      let prepXlsData = result.data
        .map((item) => {
          if (columns.length) {
            let obj = {};
            columns.forEach((column) => {
              if (!column.omit && column.selector) {
                obj[column.name] =
                  column.name === "S.No" ? sl++ : column.selector(item) || "";
              }
            });
            return obj;
          }

          return false;
        })
        .filter(Boolean);

      // Create a worksheet from the JSON data and append to a new workbook
      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      // Convert workbook to a buffer and create a Blob to trigger a file download
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      saveAs(
        blob,
        `${MODULE_NAME.replace(/ /g, "_")}_${prepXlsData.length}.xlsx`
      );
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };

  /**
   * Fetch filter options from API for a specific field.
   */
  const fetchDataOptionsForFilterBy = async (
    itemObject = {},
    type = "group",
    searching = "",
    fieldType = "select"
  ) => {
    let groupByField = itemObject.db_field || "title";

    try {
      setShowFilterOption(groupByField);
      setFilterOptionLoading(true);

      var groupData = [];

      const response = await triggerFilterByFetch({
        type: type.trim(),
        column: groupByField.trim(),
        text: searching.trim(),
      });

      if (response.data) {
        groupData = response.data;
      }

      if (groupData.length) {
        if (fieldType === "searchable") {
          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: groupData,
          }));

          return groupData;
        }

        const optionsForFilter = groupData
          .map((item) => {
            if (itemObject.selector) {
              let label = itemObject.selector(item);

              if (label) {
                if (item.total && item.total > 1) {
                  label += ` (${item.total})`;
                }

                return { label, value: item[groupByField] };
              }
            }
            return null;
          })
          .filter(Boolean);

        setFilterOptions((prev) => ({
          ...prev,
          [itemObject.id]: sortByLabel(optionsForFilter),
        }));

        return optionsForFilter;
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setFilterOptionLoading(false);
    }
  };

  return (
    <>
      {isFetching && <Loading />}
      <div className="flex flex-col md:flex-row items-start justify-start space-y-0 p-0 px-1 mb-2 bg-gray-100  rounded-lg ">
        {Object.keys(AttendanceType).map((key) => {
          return (
              <button
                key={"AttendanceType-" + key}
                className={
                  ActiveAttendanceType === key
                    ? " min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-primary text-primary"
                    : " min-w-[190px]  text-center justify-center items-center  py-4 px-8 text-sm font-medium  border-b-4 border-transparent hover:border-primary text-primary "
                }
                onClick={() => {
                  setActiveAttendanceType(key);
                }}
              >
                <span
                  className="flex align-middle items-center "
                  dangerouslySetInnerHTML={{ __html: AttendanceType[key] }}
                ></span>
              </button>
          );
        })}
      </div>
      <div className="w-full flex justify-start items-start flex-wrap border border-gray-200 rounded-lg  mb-10 ">
        <div className="mx-auto p-4 pb-6 w-full">
          {/* Header section with title and action buttons */}

          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 mb-4">
            <div className="w-4/12 md:w-10/12 text-start">
              <h2
                className="text-2xl font-bold flex items-center align-middle"
                dangerouslySetInnerHTML={{
                  __html: AttendanceType[ActiveAttendanceType],
                }}
              ></h2>
            </div>
            <div className="w-8/12 flex items-end justify-end gap-1">
              {/* Manage Columns dropdown */}

              {/* Export to Excel button, only shown if data exists */}
              {parseInt(dataItems?.total) > 0 && (
                <>
                  <ManageColumns columns={columns} setColumns={setColumns} />
                  <button
                    className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    onClick={exportToExcel}
                  >
                    {isFetching && (
                      <>
                        <span className="material-symbols-outlined animate-spin text-sm me-2">
                          progress_activity
                        </span>
                      </>
                    )}
                    {!isFetching && (
                      <span className="material-symbols-outlined text-sm me-2">
                        file_export
                      </span>
                    )}
                    Export to Excel ({dataItems.total})
                  </button>
                </>
              )}
              {/* Button to open modal for adding a new formation */}
              {showAddBtn && (
                <button
                  className=" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={() => {
                    setError(null);
                    setFormData(null);
                    setModalVisible(!modalVisible);
                  }}
                >
                  Add New
                </button>
              )}
            </div>
          </div>

          {/* Filter fieldset for global search and field-specific filtering */}
          {parseInt(dataItems?.total) > 0 && (
            <SearchFilter
              columns={columns}
              selectedFilterOptions={selectedFilterOptions}
              setSelectedFilterOptions={setSelectedFilterOptions}
              fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy} // Need to Update
              filterOptions={filterOptions}
              filterOptionLoading={filterOptionLoading}
              showFilterOption={showFilterOption}
              resetPage={resetPage}
              setCurrentPage={setCurrentPage}
              buildQueryParams={buildQueryParams}
            />
          )}

          {/* Display error message if any error occurs */}
          {fetchError && <div className="text-red-500">{error}</div>}
          {/* Show loading spinner when data is being fetched */}
          {isFetching && <Loading />}

          {/* If no data is available, display an alert message */}

          {/* Render the DataTable with the fetched data */}
          <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
            <DataTable
              columns={columns}
              data={dataItems?.data || []}
              keyField="id"
              className="p-0"
              fixedHeader
              
              highlightOnHover
              responsive
              pagination
              paginationServer
              paginationPerPage={perPage}
              paginationTotalRows={dataItems?.total || 0}
              onChangePage={(page) => {
                if (page !== currentPage) {
                  setCurrentPage(page);
                }
              }}
              onChangeRowsPerPage={(newPerPage) => {
                if (newPerPage !== perPage) {
                  setPerPage(newPerPage);
                  setCurrentPage(1);
                }
              }}
              paginationComponentOptions={{
                selectAllRowsItem: true,
                selectAllRowsItemText: "ALL",
              }}
              sortServer
              onSort={(column, sortDirection = "desc") => {
                if (Object.keys(column).length) {
                  setSortColumn(column.db_field || column.name || "created_at");
                  setSortDirection(sortDirection || "desc");
                }
              }}
            />
          </div>

          {/* Conditionally render the Edit modal */}
          {modalVisible && (
            <FormView
              handleSubmit={handleSubmit}
              item={formData}
              error={error}
              setError={setError}
              grid={2}
              setModalVisible={setModalVisible}
              columns={columns}
              title={AttendanceType[ActiveAttendanceType]+"&nbsp;Form" || ""}
            />
          )}

          {viewData && (
            <TableView
              item={viewData}
              setViewData={setViewData}
              columns={columns}
              handleEdit={canEdit && handleEdit}
              handleDelete={canDelete && handleDelete}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default AttendanceFormationList;
