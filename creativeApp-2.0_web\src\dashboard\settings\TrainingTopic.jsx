import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import TrainingTopicList from '../../pages/training/training-topic/TrainingTopicList';

const TrainingTopic = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-trainingtopic" buttonName="Add Training Topic" />
        <TrainingTopicList />
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default TrainingTopic;
