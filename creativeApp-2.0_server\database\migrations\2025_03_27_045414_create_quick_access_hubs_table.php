<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quick_access_hubs', function (Blueprint $table) {
            $table->id();
            $table->string('hubs_icon')->nullable();
            $table->string('hubs_image')->nullable();
            $table->string('hubs_title');
            $table->string('hubs_details');
            $table->string('hubs_url');
            $table->string('hubs_cta');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quick_access_hubs');
    }
};
