const API_URL = process.env.REACT_APP_BASE_API_URL;

const FetchLogin = async (eid, password) => {
  try {
    // Get the token from localStorage (if available)
    const token = localStorage.getItem('token');

    // Make the login request to the API
    const response = await fetch(`${API_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Include the Authorization header with <PERSON><PERSON> token if available
        'Authorization': token ? `Bearer ${token}` : '',  // Use token if available
      },
      body: JSON.stringify({ eid, password }),  // Send credentials as JSON
    });

    if (!response.ok) {
      throw new Error('Login failed: ' + response.statusText);
    }

    // Parse the response JSON
    const data = await response.json();

    // Assuming the server responds with a token, save it to localStorage
    if (data.token) {
      localStorage.setItem('token', data.token); // Save token for future requests
    }

    return data;
  } catch (error) {
    console.error('Error during login:', error);
    throw error;  // Rethrow to handle it in the component
  }
};

export default FetchLogin;  // Default export
