<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

use App\Models\User;
use App\Models\Role;
use App\Models\Team;
use App\Models\Department;
use App\Models\Schedule;
use Validator;
use Carbon\Carbon;


class ScheduleController extends Controller
{
        /**
     * Show all schedule with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Eager load the relevant relationships
        $schedules = Schedule::with([
            'teams'
            ])->get();

        // Log the teams retrieved
        Log::info('All Teams Retrieved:', ['schedules_count' => $schedules->count()]);

        return response()->json(['schedules' => $schedules], 200);
    }

    // Filter logic for data table
    public function scheduleData(Request $request)
    {
        $query = Schedule::with(['creator', 'updater', 'department', 'team']);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedScheduleName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedUpdatedAt = $request->filled('updated_at') ? urldecode($request->input('updated_at')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering by name
        if ($decodedScheduleName) {
            $names = explode(',', $decodedScheduleName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by created_at
        if ($decodedCreatedAt) {
            $decodedCreatedAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($decodedCreatedAts) {
                foreach ($decodedCreatedAts as $decodedCreatedAt) {
                    $q->orWhere('created_at', '=', trim($decodedCreatedAt));
                }
            });
        }
    
        // Filtering by updated_at
        if ($decodedUpdatedAt) {
            $decodedUpdatedAts = explode(',', $decodedUpdatedAt);
            $query->where(function ($q) use ($decodedUpdatedAts) {
                foreach ($decodedUpdatedAts as $decodedUpdated) {
                    $q->orWhere('updated_at', '=', trim($decodedUpdatedAt));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $productTypes = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($productTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Schedule::with(['creator', 'updater', 'department', 'team']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Schedule::with(['creator','updater', 'department', 'team']);
        $results->select($column, $column. ' as schedule', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }
    
    

    /**
     * Show all schedule with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // Eager load relevant relationships for the user
            $user = Schedule::with([
                'teams'
            ])->findOrFail($id);

            return response()->json($user, 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['message' => 'User not found.'], 404);
        }
    }


    // Create schedules
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
        \Log::info('Authenticated User:', ['user' => $authUser]);
    
        \Log::info('Incoming Request:', ['data' => $request->all()]);
    
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'shift_name' => 'required|string|max:255',
            'shift_start' => [
                'required',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'shift_end' => [
                'required',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'team_id' => 'required|exists:teams,id',
            'department_id' => 'nullable|exists:departments,id',
        ], [
            'shift_start.regex' => 'The shift start time must be in the format h:i A (e.g., 7:00 PM).',
            'shift_end.regex' => 'The shift end time must be in the format h:i A (e.g., 4:00 AM).',
        ]);
    
        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid input data',
                'errors' => $validator->errors()
            ]);
        }
    
        // Convert shift_start and shift_end to 24-hour format
        try {
            // Convert to 24-hour time format
            $shiftStart = Carbon::createFromFormat('h:i A', $request->shift_start)->format('H:i:s');
            $shiftEnd = Carbon::createFromFormat('h:i A', $request->shift_end)->format('H:i:s');
    
            // Check if shift_end is earlier than shift_start, indicating an overnight shift
            if ($shiftEnd <= $shiftStart) {
                // If shift_end is earlier, add 1 day to shift_end to account for overnight shift
                $shiftEnd = Carbon::createFromFormat('H:i:s', $shiftEnd)->addDay()->format('H:i:s');
            }
    
            \Log::info('Shift Start: ' . $shiftStart);
            \Log::info('Shift End: ' . $shiftEnd);
    
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error processing shift times.',
                'error' => $e->getMessage(),
            ]);
        }
    
        // Check the authenticated user's roles
        try {
            if ($authUser->roles()->where('name', 'super-admin')->exists()) {
                \Log::info('User is super-admin. Creating schedule...');
                // Super Admin can create any schedule
                return $this->createSchedule($request, $shiftStart, $shiftEnd);
            }
    
            if ($authUser->roles()->where('name', 'admin')->exists()) {
                \Log::info('User is admin. Checking role permissions...');
                // Admin can create schedules, but not for super-admin or admin
                if (in_array(1, $request->roles) || in_array(2, $request->roles)) {
                    \Log::warning('Admin cannot create super-admin or admin schedules.');
                    return response()->json(['error' => 'Admin cannot create super-admin or admin schedules.'], 403);
                }
    
                \Log::info('Admin can create schedules. Proceeding...');
                return $this->createSchedule($request, $shiftStart, $shiftEnd);
            }
    
            // Deny access for other roles
            \Log::warning('User does not have permission to create schedules.');
            return response()->json(['error' => 'You do not have permission to create schedules!'], 403);
        } catch (\Exception $e) {
            \Log::error('Error while checking roles and permissions.', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Error while checking roles and permissions.', 'details' => $e->getMessage()], 500);
        }
    }
    
    protected function createSchedule(Request $request, $shiftStart, $shiftEnd)
    {
        // Get the authenticated user
        $authUser = $request->user();
        try {
            \Log::info('Creating schedule...');
    
            // Create a new Schedule (assuming you have a Schedule model)
            $schedule = Schedule::create([
                'shift_name' => $request->shift_name,
                'shift_start' => $shiftStart, // Use 24-hour format for shift_start
                'shift_end' => $shiftEnd,     // Use 24-hour format for shift_end
                'department_id' => $request->department_id,     
                'team_id'     => $request->team_id,      
                'created_by'  => $authUser->id,
            ]);
    
            \Log::info('Schedule created.', ['schedule' => $schedule]);

            $schedule->teams()->attach($request->team_id);
            $schedule->departments()->attach($request->department_id);
    
            // Save the schedule (perhaps associate with a user)
            $authUser = $request->user();
            $authUser->schedules()->save($schedule);
    
            \Log::info('Schedule saved to user.', ['user' => $authUser->id, 'schedule_id' => $schedule->id]);
    
            // Attach the single selected team to the schedule (use team_id from request)
            $schedule->teams()->attach($request->team_id);  // Attach only a single team
    
            \Log::info('Team attached.', [
                'team' => $request->team_id,
            ]);
    
            // Generate an API token for the user
            $token = $authUser->createToken('api-token')->plainTextToken;
            \Log::info('API token generated.', ['token' => $token]);
    
            // Return the created schedule details along with the API token
            return response()->json([
                'status' => true,
                'message' => 'Schedule created successfully!',
                'data' => $schedule->load('teams'), // Load relations
                'token' => $token
            ], 201);
    
        } catch (\Exception $e) {
            \Log::error('Error while creating schedule.', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Error while creating schedule.', 'details' => $e->getMessage()], 500);
        }
    }
    

    // Update Schedule
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
        \Log::info('Authenticated User:', ['user' => $authUser]);
    
        \Log::info('Incoming Request:', ['data' => $request->all()]);
    
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'shift_name' => 'required|string|max:255',
            'shift_start' => [
                'required',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'shift_end' => [
                'required',
                'regex:/^([1-9]|1[0-2]):([0-5][0-9]) [APap][Mm]$/',  // Match time in h:i AM/PM format
            ],
            'team_id' => 'required|exists:teams,id', // Ensure only a single team is selected
            'department_id' => 'nullable|exists:departments,id',
        ], [
            'shift_start.regex' => 'The shift start time must be in the format h:i A (e.g., 7:00 PM).',
            'shift_end.regex' => 'The shift end time must be in the format h:i A (e.g., 4:00 AM).',
        ]);
    
        // Check if validation fails
        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid input data',
                'errors' => $validator->errors()
            ]);
        }
    
        // Find the schedule by ID
        $schedule = Schedule::find($id);
    
        // Check if the schedule exists
        if (!$schedule) {
            return response()->json([
                'status' => false,
                'message' => 'Schedule not found.',
            ], 404);
        }
    
        // Check if the authenticated user is allowed to update this schedule
        // Checking for 'super-admin' or 'admin' roles
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            \Log::info('User is super-admin or admin. Proceeding to update schedule...');
        } else {
            \Log::warning('User does not have permission to update this schedule.');
            return response()->json([
                'status' => false,
                'message' => 'You do not have permission to update this schedule.',
            ], 403);
        }
    
        // Convert shift_start and shift_end to 24-hour format
        try {
            $shiftStart = Carbon::createFromFormat('h:i A', $request->shift_start)->format('H:i:s');
            $shiftEnd = Carbon::createFromFormat('h:i A', $request->shift_end)->format('H:i:s');
    
            // If shift_end is earlier than shift_start, account for overnight shift
            if ($shiftEnd <= $shiftStart) {
                $shiftEnd = Carbon::createFromFormat('H:i:s', $shiftEnd)->addDay()->format('H:i:s');
            }
    
            \Log::info('Updated Shift Start: ' . $shiftStart);
            \Log::info('Updated Shift End: ' . $shiftEnd);
    
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error processing shift times.',
                'error' => $e->getMessage(),
            ]);
        }
    
        // Begin updating the schedule
        try {
            \Log::info('Updating schedule...', ['schedule_id' => $id]);
    
            // Update the schedule details
            $schedule->update([
                'shift_name' => $request->shift_name,
                'shift_start' => $shiftStart,
                'shift_end' => $shiftEnd,
                'department_id' => $request->department_id,
                'team_id'  => $request->team_id,
                'updated_by'  => $authUser->id,
            ]);
    
            \Log::info('Schedule updated.', ['schedule' => $schedule]);
    
            // Attach the updated team to the schedule
            $schedule->teams()->sync([$request->team_id]);
            $schedule->departments()->sync([$request->department_id]);
    
            \Log::info('Team updated for the schedule.', [
                'team' => $request->team_id,
            ]);
    
            // Optionally, you can return the updated schedule data
            return response()->json([
                'status' => true,
                'message' => 'Schedule updated successfully!',
                'data' => $schedule->load('teams'), // Load relations
            ], 200);
    
        } catch (\Exception $e) {
            \Log::error('Error while updating schedule.', ['error' => $e->getMessage()]);
            return response()->json([
                'status' => false,
                'message' => 'Error while updating schedule.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    
    
    // Destroy schedule
    public function destroy($id)
    {
        // Find the schedule by ID
        $schedule = Schedule::find($id);

        // If schedule not found
        if (!$schedule) {
            return response()->json([
                'status' => false,
                'message' => 'Schedule not found!',
            ]);
        }

        // Detach teams from the schedule before deletion
        $schedule->teams()->detach();

        // Delete the schedule record
        $schedule->delete();

        return response()->json([
            'status' => true,
            'message' => 'Schedule deleted successfully!',
        ]);
    }

    
    
}
