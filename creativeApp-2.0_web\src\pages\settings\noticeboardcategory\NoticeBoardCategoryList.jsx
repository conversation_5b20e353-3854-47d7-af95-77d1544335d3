import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import EditNoticeBoardCategory from './EditNoticeBoardCategory';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const NoticeBoardCategoryList = () => {
    const [categories, setCategories] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [loading, setLoading] = useState(true);
    const [selectedCategoryId, setSelectedCategoryId] = useState(null);
    const [error, setError] = useState(null);

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Category Name", key: "name" },
        { label: "Created By", key: "created_by" },
        { label: "Updated By", key: "updated_by" },
    ];

    useEffect(() => {
        const fetchCategories = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/notice-board-category`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setCategories(data.categories.map(category => ({
                    id: category.id,
                    name: category.name,
                    created_by: category.created_by,
                    updated_by: category.updated_by,
                })));
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/notice-board-category/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete category: ' + response.statusText);
            }

            setCategories(prevCategories => prevCategories.filter(category => category.id !== id));
        } catch (error) {
            setError(error.message);
        }
    };

    const handleEdit = (id) => {
        setSelectedCategoryId(id);
        setModalVisible(true);
    };

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (categories.length === 0) {
        return <div className="text-gray-500">No data available</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={categories}
                columnNames={columnNames}
                onDelete={handleDelete}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedCategoryId}
            />
            {modalVisible && (
                <EditNoticeBoardCategory
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    categoryId={selectedCategoryId}
                />
            )}
        </div>
    );
};

export default NoticeBoardCategoryList;
