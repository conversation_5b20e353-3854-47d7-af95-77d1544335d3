import Swal from 'sweetalert2'

export const alertMessage = (prams) => {

    if(typeof prams === 'string') {
        prams = {
            icon: prams,
        }
    }

    let title = prams?.title || 'Data loaded successfully.';
    let details = prams?.details || 'Data loaded successfully.';
    let icon = prams?.icon || 'success';
    let showConfirmButton = prams?.showConfirmButton || false;
    let toast = prams?.toast || true;

   
    switch (icon) {
        case 'error':
            title = prams?.title || 'Error!';
            icon = prams?.icon || 'error';
            details = prams?.details || 'An error occurred. Please try again.';
            break;
        
        case 'warning':
            title = prams?.title || 'Warning!';
            icon = prams?.icon || 'warning';
            details = prams?.details || 'Please be cautious.';
            break;
    
        case 'info':
            title = prams?.title || 'Info!';
            icon = prams?.icon || 'info';
            details = prams?.details || 'Here is some important information.';
            break;
    
        case 'success':
            title = prams?.title || 'Success!';
            icon = prams?.icon || 'success';
            details = prams?.details || 'Data loaded successfully.';
            break;

            
        case 'created':
            title = prams?.title || 'Success!';
            icon = prams?.icon || 'success';
            details = prams?.details || 'Successfully created.';
            break;
            
        case 'updated':
            title = prams?.title || 'Success!';
            icon = prams?.icon || 'success';
            details = prams?.details || 'Successfully updated.';
            break;

        case 'deleted':
            title = prams?.title || 'Success!';
            icon = prams?.icon || 'warning';
            details = prams?.details || 'Successfully Deleted.';
            break;

    
        default:
            title = 'Notification';
            icon = 'info';
            details = 'Something happened. Please check.';
            break;
    }
    
    // Optionally, you can handle displaying `title`, `icon`, and `details` here.
    
    Swal.fire({
        position: "bottom-end",
        icon: icon,
        title: toast? details : title,
        text: toast? false : details,
        showConfirmButton: showConfirmButton,
        toast: toast,
        timer: 5000,
        ...prams,
    });
}


export const confirmationAlert = (prams) => {
    Swal.fire({
        title: prams?.title || 'Are you sure?',
        text: prams?.text || "You won't be able to revert this!",
        icon: prams?.icon || 'warning',
        showCancelButton: true,
        confirmButtonColor: prams?.confirmButtonColor || '#3085d6',
        cancelButtonColor: prams?.cancelButtonColor || '#d33',
        confirmButtonText: prams?.confirmButtonText || 'Yes, delete it!',
        cancelButtonText: prams?.cancelButtonText || 'No, cancel!',
    }).then((result) => {
        if (result.isConfirmed) {
            prams?.onConfirm && prams.onConfirm();
        } else {
            prams?.onCancel && prams.onCancel();
        }
    });
}
