import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const locationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getLocationData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `location-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['LocationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForLocation: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `location-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['LocationData'],
    }),

    getLocationById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `locations/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'LocationData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createLocation: builder.mutation({
      query: (newFormationType) => ({
        url: 'location-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['LocationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateLocation: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `locations/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'LocationData', id }, 'LocationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteLocation: builder.mutation({
      query: (id) => ({
        url: `locations/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['LocationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetLocationDataQuery,
  useLazyFetchDataOptionsForLocationQuery,
  useGetLocationByIdQuery,
  useLazyGetLocationByIdQuery,
  useCreateLocationMutation,
  useUpdateLocationMutation,
  useDeleteLocationMutation,
} = locationApi;
