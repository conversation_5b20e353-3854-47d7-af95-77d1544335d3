import { Description, Dialog, DialogPanel, DialogTitle } from '@headlessui/react';
import React, { useState } from 'react';

const Modal = ({ children, isOpen, setIsOpen, ModalTitle, modalWidth }) => {
    //const [isOpen, setIsOpen] = useState(false); // use this on parent component
    return (
        <Dialog open={isOpen} onClose={() => setIsOpen(false)} className="relative z-50">
            <div className="fixed inset-0 w-screen overflow-y-auto p-4" style={{ background: '#00000098' }}>
                <div className="flex min-h-full items-center justify-center">
                    <DialogPanel className={`relative p-4 ${modalWidth ? modalWidth : 'w-full max-w-md'} max-h-full`}>
                        {/* main element */}
                        <div className="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
                            {/* <!-- Modal header --> */}
                            <div className="flex items-center justify-between p-1 md:p-5 border-b rounded-t dark:border-gray-600 border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                    {ModalTitle}
                                </h3>
                                <button type="button" className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" onClick={() => setIsOpen(false)}>
                                    <svg className="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                    </svg>
                                    <span className="sr-only">Close modal</span>
                                </button>
                            </div>

                            {/* Modal body */}
                            {children}

                        </div>
                    </DialogPanel>
                </div>
            </div>
        </Dialog >
    );
};

export default Modal;