import axios from 'axios';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const FetchUserData = async (fname, lname, eid, email ) => {
  try {
    const response = await axios.get(`${API_URL}/users`, {
      fname,
      lname,
      eid,
      email
    });

    return response.data;

  } catch (error) {
    console.error('Error fetch user data:', error);
    throw error; 
  }
};

export default FetchUserData; 
