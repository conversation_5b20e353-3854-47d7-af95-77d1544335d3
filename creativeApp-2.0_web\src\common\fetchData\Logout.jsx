import { useNavigate } from 'react-router-dom';

const Logout = () => {
  const navigate = useNavigate();

  const removeToken = () => {
    // Clear the token from local storage
    localStorage.removeItem('token');
    localStorage.clear();

    
    // Navigate to the login page or home page
    navigate('/login'); // Adjust the path as needed
  };

  return removeToken; // Return the function directly
};

export default Logout;
