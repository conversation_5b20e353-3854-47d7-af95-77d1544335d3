<?php

namespace Database\Factories;

use App\Models\SchedulePlanner;
use App\Models\Schedule;
use App\Models\User;
use App\Models\Department;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class SchedulePlannerFactory extends Factory
{
    protected $model = SchedulePlanner::class;

    public function definition(): array
    {
        $currentYear = now()->year;
        $currentWeek = now()->weekOfYear;

        // Get a random number of user IDs (between 1 and 5 in this example)
        $userCount = $this->faker->numberBetween(1, 5);
        $userIds = User::inRandomOrder()->limit($userCount)->pluck('id')->toArray();

        // Convert the array of user IDs to a comma-separated string
        $userIdsString = implode(',', $userIds);

        return [
            'department_id' => Department::inRandomOrder()->value('id') ?? "",
            'team_id' => Team::inRandomOrder()->value('id') ?? "",
            'weeknum' => $this->faker->numberBetween($currentWeek, 52) . '/' . $currentYear,
            'user_id' => $userIdsString, // Store the comma-separated string
            'schedule_id' => Schedule::inRandomOrder()->value('id') ?? "",
            'created_by' => User::inRandomOrder()->value('id') ?? User::factory(),
            'updated_by' => User::inRandomOrder()->value('id') ?? User::factory(),
        ];
    }
}