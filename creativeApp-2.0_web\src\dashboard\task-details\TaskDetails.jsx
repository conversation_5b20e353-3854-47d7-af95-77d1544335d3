import React, { useState } from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TaskRecordList from '../../pages/task-details/task-record/TaskRecordList';
import TaskRecordDataList from '../../pages/task-details/task-record/TaskRecordDataList';


const TaskDetails = () => {

  const [searchTerm, setSearchTerm] = useState('');


  // Handle search input changes
  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm);
  };

  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      {/* <h4 className="text-xl font-bold text-left">Team List</h4> */}
      <TaskRecordDataList />
      {/* <TableLayoutWrapper2>
        <TableHeader routeName="/add-task" buttonName="Add Task Details" onSearch={handleSearch} />
          <TaskRecordList searchTerm={searchTerm} />
      </TableLayoutWrapper2> */}
    </div>

  );
};

export default TaskDetails;
