import React, { useEffect, useState } from 'react';
import TableContent from '../../common/table/TableContent';
import EditMember from './EditMember';
import TablePagination from '../../common/table/TablePagination';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const MemberIndexList = ({ searchTerm }) => {
    const [users, setUsers] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState(null);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "EID", key: "eid" },
        { label: "Avater", key: "photo" },
        { label: "First Name", key: "fname" },
        { label: "Last Name", key: "lname" },
        { label: "Full Name", key: "fullName" },
        { label: "Designation", key: "designation" },
        { label: "Responsibility Level", key: "resource_type" },
        { label: "Email", key: "email" },
        { label: "About", key: "about" },
        { label: "Birthday", key: "birthday" },
        { label: "Birthday Celebration Date", key: "birthday_celebration" },
        { label: "Gender", key: "gender" },
        { label: "Marital Status", key: "marital_status" },
        { label: "Preferred Pronouns", key: "nick_name" },
        { label: "Primary Phone Number", key: "primary_contact" },
        { label: "Secondary Phone Number", key: "secondary_contact" },
        { label: "Emergency Contact Number", key: "emergency_contact" },
        { label: "Emergency Contact Relationship", key: "relation_contact" },
        { label: "Present Address", key: "present_address" },
        { label: "Permanent Address", key: "permanent_address" },
        { label: "Blood Group", key: "blood" },
        { label: "Previous Designation in the Company", key: "prev_designation" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Team Lead/Report to", key: "report_to" },
        { label: "Desk ID", key: "desk_id" },
        { label: "Joining Date", key: "joining_date" },
        { label: "Termination Date", key: "termination_date" },
        { label: "Employment End Date", key: "employment_end" },
        { label: "Work Location", key: "location" },
        { label: "Office Branch", key: "branch" },
        { label: "Onsite Status", key: "onsite_status" },
        { label: "Team Member Status", key: "member_status" },
        { label: "Billing Status", key: "billing_status" },
        { label: "Resource Status", key: "resource_status" },
        { label: "Contact Type", key: "contact_type" },
        { label: "Availability Status", key: "available_status" },
        { label: "Work Anniversary", key: "work_anniversary" },
    ];

    useEffect(() => {
        const fetchUsers = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }
    
            const token = localStorage.getItem('token');
    
            try {
                const response = await fetch(`${API_URL}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }
    
                const data = await response.json();
                console.log('All User', data);

                const mappedUsers = data.map(user => ({
                    id: user.id,
                    eid: user.eid || '',
                    photo: user.photo || '',
                    fname: user.fname || '',
                    lname: user.lname || '',
                    fullName: `${user.fname || ''} ${user.lname || ''}`.trim(),
                    designation: user.designations.length > 0 ? user.designations[0].name : '',
                    resource_type: user.resource_types.length > 0 ? user.resource_types[0].name : '',
                    email: user.email || '',
                    about: user.about || '',
                    birthday: user.birthday || '',
                    birthday_celebration: user.birthday_celebration || '',
                    gender: user.gender || '',
                    marital_status: user.marital_status || '',
                    nick_name: user.nick_name || '',
                    primary_contact: user.primary_contact || '',
                    secondary_contact: user.secondary_contact || '',
                    emergency_contact: user.emergency_contact || '',
                    relation_contact: user.relation_contact || '',
                    present_address: user.present_address || '',
                    permanent_address: user.permanent_address || '',
                    blood: user.bloods.length > 0 ? user.bloods[0].name : '',
                    prev_designation: user.prev_designation || '',
                    department: user.departments.length > 0 ? user.departments[0].name : '',
                    team: user.teams.length > 0 ? user.teams[0].name : '',
                    report_to: user.teams.length > 0 ? user.teams[0].team_lead : '',
                    desk_id: user.desk_id || '',
                    joining_date: user.joining_date || '',
                    termination_date: user.termination_date || '',
                    employment_end: user.employment_end || '',
                    location: user.branches.length > 0 && user.branches[0].locations.length > 0 ? user.branches[0].locations[0].locations_name : '',
                    branch: user.branches.length > 0 ? user.branches[0].name : '',
                    onsite_status: user.onsite_statuses.length > 0 ? user.onsite_statuses[0].name : '',
                    member_status: user.member_statuses.length > 0 ? user.member_statuses[0].name : '',
                    billing_status: user.billing_statuses.length > 0 ? user.billing_statuses[0].name : '',
                    resource_status: user.resource_statuses.length > 0 ? user.resource_statuses[0].name : '',
                    contact_type: user.contact_types.length > 0 ? user.contact_types[0].name : '',
                    available_status: user.available_statuses.length > 0 ? user.available_statuses[0].name : '',
                    work_anniversary: user.work_anniversary || '',
                }));

                setUsers(mappedUsers);
                setFilteredUsers(mappedUsers);
    
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };
    
        fetchUsers();
    }, [currentPage, itemsPerPage]);
    

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm.toLowerCase().trim();

        const highlightText = (text) => {
            const strText = text ? text.toString() : '';
        
            const regex = new RegExp(`(${normalizedSearchTerm})`, 'gi');
            const parts = strText.split(regex);
        
            return parts.map((part, index) => {
                return regex.test(part) ? (
                    <span key={index} className="bg-yellow-300">{part}</span>
                ) : part;
            });
        };        
        
    
        if (!normalizedSearchTerm) {
            setFilteredUsers(users);
            return;
        }
    
        const filtered = users.filter(user => {
            return Object.values(user).some(value =>
                value && value.toString().toLowerCase().includes(normalizedSearchTerm)
            );
        }).map(user => ({
            id: user.id,
            eid: highlightText(user.eid), // Convert eid to string and highlight
            photo: highlightText(user.photo || ''),
            fname: highlightText(user.fname || ''),
            lname: highlightText(user.lname || ''),
            fullName: highlightText(`${user.fname || ''} ${user.lname || ''}`.trim()),
            designation: highlightText(user.designations && user.designations.length > 0 ? user.designations[0].name : ''),
            resource_type: highlightText(user.resource_types && user.resource_types.length > 0 ? user.resource_types[0].name : ''),
            email: highlightText(user.email || ''),
            about: highlightText(user.about || ''),
            birthday: highlightText(user.birthday || ''),
            birthday_celebration: highlightText(user.birthday_celebration || ''),
            gender: highlightText(user.gender || ''),
            marital_status: highlightText(user.marital_status || ''),
            nick_name: highlightText(user.nick_name || ''),
            primary_contact: highlightText(user.primary_contact || ''),
            secondary_contact: highlightText(user.secondary_contact || ''),
            emergency_contact: highlightText(user.emergency_contact || ''),
            relation_contact: highlightText(user.relation_contact || ''),
            present_address: highlightText(user.present_address || ''),
            permanent_address: highlightText(user.permanent_address || ''),
            blood: highlightText(user.bloods && user.bloods.length > 0 ? user.bloods[0].name : ''),
            prev_designation: highlightText(user.prev_designation || ''),
            department: highlightText(user.departments && user.departments.length > 0 ? user.departments[0].name : ''),
            team: highlightText(user.teams && user.teams.length > 0 ? user.teams[0].name : ''),
            report_to: highlightText(user.teams && user.teams.length > 0 ? user.teams[0].team_lead : ''),
            desk_id: highlightText(user.desk_id || ''),
            joining_date: highlightText(user.joining_date || ''),
            termination_date: highlightText(user.termination_date || ''),
            employment_end: highlightText(user.employment_end || ''),
            location: highlightText(user.branches && user.branches.length > 0 && user.branches[0].locations && user.branches[0].locations.length > 0 
                                   ? user.branches[0].locations[0].locations_name : ''),
            branch: highlightText(user.branches && user.branches.length > 0 ? user.branches[0].name : ''),
            onsite_status: highlightText(user.onsite_statuses && user.onsite_statuses.length > 0 ? user.onsite_statuses[0].name : ''),
            member_status: highlightText(user.member_statuses && user.member_statuses.length > 0 ? user.member_statuses[0].name : ''),
            billing_status: highlightText(user.billing_statuses && user.billing_statuses.length > 0 ? user.billing_statuses[0].name : ''),
            resource_status: highlightText(user.resource_statuses && user.resource_statuses.length > 0 ? user.resource_statuses[0].name : ''),
            contact_type: highlightText(user.contact_types && user.contact_types.length > 0 ? user.contact_types[0].name : ''),
            available_status: highlightText(user.available_statuses && user.available_statuses.length > 0 ? user.available_statuses[0].name : ''),
            work_anniversary: highlightText(user.work_anniversary || ''),
        }));
        
    
        setFilteredUsers(filtered);
    }, [searchTerm, users]);

    
    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);
    const totalCount = filteredUsers.length;

    const handleEdit = (id) => {
        setSelectedUserId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
      };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedUserId}
                hideDeleteButton={true}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredUsers.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
            {modalVisible && selectedUserId && (
                <EditMember 
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    userId={selectedUserId}
                />
            )}
        </div>
    );
};

export default MemberIndexList;
