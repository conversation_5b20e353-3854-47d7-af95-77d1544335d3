import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const priorityApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getPriorityData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `priority-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['PriorityData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForPriority: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `priority-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['PriorityData'],
    }),

    getPriorityById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `priority-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'PriorityData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createPriority: builder.mutation({
      query: (newFormationType) => ({
        url: 'priority-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['PriorityData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updatePriority: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `priority/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'PriorityData', id }, 'PriorityData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deletePriority: builder.mutation({
      query: (id) => ({
        url: `priority/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['PriorityData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetPriorityDataQuery,
  useLazyFetchDataOptionsForPriorityQuery,
  useGetPriorityByIdQuery,
  useLazyGetPriorityByIdQuery,
  useCreatePriorityMutation,
  useUpdatePriorityMutation,
  useDeletePriorityMutation,
} = priorityApi;
