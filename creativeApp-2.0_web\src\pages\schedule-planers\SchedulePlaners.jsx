import React, { useState } from "react";
// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";

// Loading spinner component to show while data is loading
import Loading from "../../common/Loading";

import {confirmationAlert, ManageColumns, SearchFilter, TableView, FormView, Image} from '../../common/coreui';

import { useGetSchedulePlannersQuery,
  useUpdateSchedulePlannerMutation,
  useCreateSchedulePlannerMutation,
  useLazyFetchDataOptionsForFilterBySchedulePlannersQuery,
  useDeleteSchedulePlannerMutation, schedulePlannerApi } from '../../features/api';

import { useDispatch } from "react-redux";
import { defaultDateTimeFormat, removeKeys, defaultTimeFormat, defaultStartAndEndDateFromWeekNumber } from "../../utils";

// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";

// API endpoint and configuration constants
const MODULE_NAME = "Schedule Planers";

// Main component for listing Schedule Planers records
const SchedulePlaners = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [viewData, setViewData] = useState(null);
  const [error, setError] = useState(null);

  
  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("");
  const [sortDirection, setSortDirection] = useState("asc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);

  
  const { data: dataItems, isFetching, error: fetchError } = useGetSchedulePlannersQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });

  const [triggerFilterByFetch] = useLazyFetchDataOptionsForFilterBySchedulePlannersQuery();
       
  const [deleteSchedulePlanners] = useDeleteSchedulePlannerMutation();

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "")

    setQueryString(q);
  }

  const handleCopy = (data) => {
    const keysToRemove = ["id", "team", "department", "updated_at", "updated_by", "updater", "created_at", "creator", "created_by", "updated_by"];
    const cleanedData = removeKeys(data, keysToRemove);
    setError(null)
    setViewData(null)
    setFormData(cleanedData); 
    setModalVisible(true);
  }

  const handleEdit = (data) => {
    setError(null)
    setViewData(null)
    setFormData(data); 
    setModalVisible(true);
  }

  const handleDelete = (id) => {
    confirmationAlert({onConfirm: () => 
      {        
        deleteSchedulePlanners(id);
        setViewData(null);
      }});  
  }


    const [formData, setFormData] = useState({});

    const [updateSchedulePlanners] = useUpdateSchedulePlannerMutation();
    const [createSchedulePlanners] = useCreateSchedulePlannerMutation();
  
    const handleSubmit = async (formData=[]) => {
      let response = null;
      try {
        const user_id = localStorage.getItem("user_id");

        if(formData && formData.user_id){
          let users = formData.user_id.map((item) => item.value);

          formData.user_id = users.toString();

          console.log(formData)
        }

        // return false;
  
        if (formData && formData.id) {
          formData["updated_by"] = user_id;
          response = await updateSchedulePlanners({
            id: formData,
            ...formData,
            type: "attendance",
          });
        } else {
          formData["created_by"] = user_id;
          response = await createSchedulePlanners({
            ...formData,
            type: "attendance",
          });
        }

       

  
        if (response && response?.error) {
          setError(response.error.data);
        } else {
          setError(null);
          setFormData(null);
          setModalVisible(false);
        }
      } catch (error) {}
    };
 

    let columnSerial = 1;
  // Memoize columns to prevent unnecessary recalculations
  const [columns, setColumns]  = useState(
    () => [
      {
        id: columnSerial++,
        name: "Action",
        width: "180px",
        className: "bg-red-300",
        cell: (item) => (
          <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
           
            
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => {
                setViewData(item);
              }}
            >
              <span className="material-symbols-outlined text-lg ">
              visibility
              </span>
            </button>

            {/* Edit Button */}
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => handleEdit(item)}
            >
              <span className="material-symbols-outlined text-lg ">
                stylus_note
              </span>
            </button>
             {/* Copy Button */}
             <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => {
                handleCopy(item);
              }}
            >
              <span className="material-symbols-outlined text-lg ">
              content_copy
              </span>
            </button>
            {/* Delete Button */}
            <button
              className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
              onClick={() => handleDelete(item.id)}
            >
              <span className="material-symbols-outlined text-sm ">
                delete
              </span>
            </button>
          </div>
        ),
      },
      {
        id: columnSerial++,
        name: "S.No",
        // Calculate serial number based on current page and rows per page
        selector: (row, index) => (currentPage - 1) * perPage + index + 1,
        width: "80px",
      },
      {
        id: columnSerial++,
        width: '250px',
        name: "Date Start & End",
        selector: (row) => `Week: ${row.weeknum}: ${defaultStartAndEndDateFromWeekNumber(row.weeknum)}` || "",
        cell: (row) => (<div className="bg-green-300 border border-green-800 text-green-800 rounded-full px-6 py-2">Week: {row.weeknum}<br />{defaultStartAndEndDateFromWeekNumber(row.weeknum)}</div>),
        omit: false,
        db_field: "weeknum",
        sortable: true,
        filterable: true,
        form: {
          type: "WeekPicker",
          props: {
            "showWeekNumbers":true,
            "showWeekPicker":true
          }
        }
      },

      {
        id: columnSerial++,
        name: "Department",
        selector: (row) => row.department?.name  || "",
        db_title_field: "department.name",
        db_field: "department_id",
        sortable: true,
        omit: false,
        filterable: true,
        form: {
          type: "DepartmentDropdown",
          props: {
            // isMulti: true
            //  isSearchable: true
          }
        }
      },
      {
        id: columnSerial++,
        name: "Team",
        selector: (row) => row.team?.name || "",
        db_title_field: "team.name",
        db_field: "team_id",
        omit: false,
        sortable: true,
        filterable: true,
        form: {
          type: "TeamsDropdown",
          dependentField: "department_id",
          
        }
      },
    
      {
        id: columnSerial++,
        name: "Assigned employees",
        width: '250px',
        db_field: "user_id",
        db_title_field: "user.fname",
        selector: (row) => {

          return row.users.map((user) => { 
                            return `${user?.fname} ${user?.lname}, EID: ${user.eid}, ${user?.resource_types?.[0]?.name || ""}`
                          }).join("\r\n");
                        },
        cell: (row) => (
          <div className="flex flex-col align-middle items-center w-full text-start mb-2 ">
          {row.users && row.users.map((user) => {
            return (
              <div  className="flex align-middle items-center w-full text-start py-2 border-b-2 border-gray-100 ">
                <Image src={user?.photo} />
                <div className="flex-col align-middle items-center w-full text-start ">
                {user?.fname && <b>{user?.fname} {user?.lname}</b> }
                {user?.eid && <div>{"EID: "+user?.eid || ""}</div> }
                {user?.resource_types?.[0]?.name && <div>{user?.resource_types?.[0]?.name || ""}</div> }
                </div>
              </div>
            )
          })}
          </div>
        ),
        sortable: true,
        omit: false,
        // filterable: "searchable",
        form: {
          type: "UsersByDefaultTeamDropdown",
          dependentField: "team_id",
          props: {
            isMulti: true,
            isSearchable: true
          }
        }
      },     

      {
        id: columnSerial++,
        name: "Shift",
        width: '150px',
        db_field: "schedule_id",
        db_title_field: "schedule.shift_name",
        selector: (row) => row.schedule?.shift_name  || "",
        sortable: true,
        // filterable: true,
        omit: false,
        form: {
          type: "ShiftDropdown",
          dependentField: "team_id",
          props: {
            isMulti: false,
            isSearchable: true
          }
        }
      },

      {
        id: columnSerial++,
        name: "Shift Start",
        width: '150px',
        db_field: "schedule_id",
        db_title_field: "schedule.shift_start",
        selector: (row) => defaultTimeFormat(row.schedule?.shift_start)  || "",
        sortable: true,
        omit: false,
      },

      {
        id: columnSerial++,
        name: "Shift End",
        width: '150px',
        db_field: "schedule_id",
        db_title_field: "schedule.shift_end",
        selector: (row) => defaultTimeFormat(row.schedule?.shift_end)  || "",
        sortable: true,
        omit: false,
      },
      
     

     
      {
        id: columnSerial++,
        name: "Updated by",
        selector: (row) => row.updated_by?.fname || "",
        db_field: "updated_by",
        omit: false,
        sortable: true,
        filterable: false,
      },
      {
        id: columnSerial++,
        width: "250px",
        name: "Updated At",
        selector: (row) => row.updated_by?.fname? defaultDateTimeFormat(row.updated_at) : "",
        db_field: "updated_at",
        omit: false,
        sortable: true,
      },
      {
        id: columnSerial++,
        name: "Created by",
        selector: (row) => row.created_by?.fname || "",
        db_field: "created_by",
        omit: false,
        sortable: true,
        filterable: false,
      },
      {
        id: columnSerial++,
        width: "250px",
        name: "Created At",
        selector: (row) => defaultDateTimeFormat(row.created_at),
        db_field: "created_at",
        omit: false,
        sortable: true,
      },
    ],
    [currentPage, perPage]
  );

  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).map((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }

        return null;
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj })
    }
    setCurrentPage(1);
  };


  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      // Fetch all data items for Excel export
      const result = await dispatch(
        schedulePlannerApi.endpoints.getSchedulePlanners.initiate({
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues
          query: queryString,
        })
      ).unwrap(); // Wait for the API response
  
      if (!result?.total || result.total < 1) {
        return false;
      }
  
      var sl = 1;
  
      let prepXlsData = result.data.map((item) => {
        if (columns.length) {
          let obj = {};
          columns.forEach((column) => {
            if (!column.omit && column.selector) {
              obj[column.name] = column.name === "S.No" ? sl++ : column.selector(item) || "";
            }
          });
          return obj;
        }

        return false;
      }).filter(Boolean);
  
      // Create a worksheet from the JSON data and append to a new workbook
      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  
      // Convert workbook to a buffer and create a Blob to trigger a file download
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(blob, `${MODULE_NAME.replace(/ /g,"_")}_${prepXlsData.length}.xlsx`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };
  

  /**
   * Fetch filter options from API for a specific field.
   */
  const fetchDataOptionsForFilterBy = 
    async (
      itemObject = {},
      type = "group",
      searching = "",
      fieldType = "select"
    ) => {

      let groupByField = itemObject.db_field || "title";

      try {
        setShowFilterOption(groupByField);
        setFilterOptionLoading(true);

        var groupData = [];

        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });
        
        if (response.data) {
          groupData = response.data;
        }

        if (groupData.length) {

          if (fieldType === "searchable") {
            setFilterOptions((prev) => ({
              ...prev,
              [itemObject.id]: groupData,
            }));

            return groupData;
          }

          const optionsForFilter = groupData
            .map((item) => {
              if(itemObject.selector){
                let label = itemObject.selector(item);

                if(label){
                  if (item.total && item.total > 1) {
                    label += ` (${item.total})`;
                  }

                  return { label, value: item[groupByField] };
                }

              }
              return null;
            }).filter(Boolean);

          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: optionsForFilter,
          }));

          return optionsForFilter;
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setFilterOptionLoading(false);
      }
    };

  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      <div className="mx-auto pb-6 ">
        {/* Header section with title and action buttons */}
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div className="w-4/12 md:w-10/12 text-start">
            <h2 className="text-2xl font-bold ">{MODULE_NAME}</h2>
          </div>
          <div className="w-8/12 flex items-end justify-end gap-1">
            {/* Manage Columns dropdown */}
            
            {/* Export to Excel button, only shown if data exists */}
            { parseInt(dataItems?.total) > 0 && (
              <>
              <ManageColumns columns={columns} setColumns={setColumns} />
                <button
                  className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={exportToExcel}
                >
                  {isFetching && (
                    <>
                      <span className="material-symbols-outlined animate-spin text-sm me-2">
                        progress_activity
                      </span>
                    </>
                  )}
                  {!isFetching && (
                    <span className="material-symbols-outlined text-sm me-2">
                    file_export
                    </span>
                  )}
                  Export to Excel ({dataItems.total})
                </button>
              </>
            )}
            {/* Button to open modal for adding a new formation */}
            <button
              className=" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              onClick={() => {
                setFormData(null);
                setModalVisible(!modalVisible)
              }}
            >
              Add New
            </button>
          </div>
        </div>

        {/* Filter fieldset for global search and field-specific filtering */}
        { parseInt(dataItems?.total) > 0 && (
        <SearchFilter
            columns={columns}
            selectedFilterOptions={selectedFilterOptions}
            setSelectedFilterOptions={setSelectedFilterOptions}
            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy} // Need to Update
            filterOptions={filterOptions}
            filterOptionLoading={filterOptionLoading}
            showFilterOption={showFilterOption}
            resetPage={resetPage}
            setCurrentPage={setCurrentPage}
            buildQueryParams={buildQueryParams}
        />
        )}

        {/* Display error message if any error occurs */}
        {fetchError && <div className="text-red-500">{error}</div>}
        {/* Show loading spinner when data is being fetched */}
        {isFetching && <Loading />}

        {/* If no data is available, display an alert message */}
        
        {/* Render the DataTable with the fetched data */}
        <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
          <DataTable
            columns={columns}
            data={dataItems?.data || []}
            keyField="id"
            className="p-0"
            fixedHeader
            
            highlightOnHover
            responsive
            pagination
            paginationServer
            paginationPerPage={perPage}
            paginationTotalRows={dataItems?.total || 0}
            onChangePage={(page) => {
              if (page !== currentPage) {
                setCurrentPage(page);
              }
            }}
            onChangeRowsPerPage={(newPerPage) => {
              if(newPerPage !== perPage){
                setPerPage(newPerPage);
                setCurrentPage(1);
              }
            }}
            paginationComponentOptions={{
              selectAllRowsItem: true,
              selectAllRowsItemText: "ALL",
            }}
            sortServer
            onSort={(column, sortDirection="desc") => {
              if(Object.keys(column).length){
                setSortColumn(column.db_field || column.name || "created_at");
                setSortDirection(sortDirection || "desc");
              }
            }}
          />
        </div>

        {/* Conditionally render the Edit modal */}
        {modalVisible && (
          <FormView handleSubmit={handleSubmit} item={formData} error={error} grid={2} setModalVisible={setModalVisible} columns={columns} />
        )}

        {viewData && (
          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
        )}
       
      </div>
    </section>
  );
};

export default SchedulePlaners;
