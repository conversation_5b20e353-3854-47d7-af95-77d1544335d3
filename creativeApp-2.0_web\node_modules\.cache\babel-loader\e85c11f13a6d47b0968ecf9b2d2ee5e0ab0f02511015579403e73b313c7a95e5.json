{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\PasswordCardsTable.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport AddPasswordForm from './AddPasswordForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordCardsTable = ({\n  generatedPassword,\n  passwordStrength\n}) => {\n  _s();\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([{\n    id: 1,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }, {\n    id: 2,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'StrongPass123!@#',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Strong Password',\n    strengthColor: 'bg-green-100 text-green-600 border-green-300'\n  }, {\n    id: 3,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'ModeratePass456',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Moderate Password',\n    strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n  }, {\n    id: 4,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'WeakPass',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }, {\n    id: 5,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'AnotherStrongPass789!',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Strong Password',\n    strengthColor: 'bg-green-100 text-green-600 border-green-300'\n  }, {\n    id: 6,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'ModerateSecure123',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Moderate Password',\n    strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n  }, {\n    id: 7,\n    title: 'Platform Name',\n    username: '<EMAIL>',\n    password: '••••••••••••',\n    actualPassword: 'VeryWeakPass',\n    team: 'Team Name',\n    department: 'Department name',\n    strength: 'Weak Password',\n    strengthColor: 'bg-red-100 text-red-600 border-red-300'\n  }]);\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [selectedCards, setSelectedCards] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const togglePasswordVisibility = id => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n  const toggleCardSelection = id => {\n    setSelectedCards(prev => prev.includes(id) ? prev.filter(cardId => cardId !== id) : [...prev, id]);\n  };\n  const toggleSelectAll = () => {\n    if (selectedCards.length === passwordCards.length) {\n      setSelectedCards([]);\n    } else {\n      setSelectedCards(passwordCards.map(card => card.id));\n    }\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n  const handleEdit = id => {\n    console.log('Edit password card:', id);\n    // Navigate to edit form or open modal\n  };\n  const handleDelete = id => {\n    console.log('Delete password card:', id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n  const handleDeleteSelected = () => {\n    if (selectedCards.length > 0) {\n      setPasswordCards(prev => prev.filter(card => !selectedCards.includes(card.id)));\n      setSelectedCards([]);\n    }\n  };\n  const handleAddPasswordCard = newCard => {\n    const cardWithId = {\n      ...newCard,\n      id: Math.max(...passwordCards.map(c => c.id)) + 1,\n      password: '••••••••••••',\n      actualPassword: newCard.password\n    };\n    setPasswordCards(prev => [...prev, cardWithId]);\n    setShowAddForm(false);\n  };\n\n  // Placeholder avatar images\n  const avatarImages = ['https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A', 'https://via.placeholder.com/32x32/10B981/FFFFFF?text=B', 'https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C', 'https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-1/2\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n          children: \"Teams Password Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center justify-center w-10 h-10 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded\",\n            children: \"share\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleDeleteSelected,\n          disabled: selectedCards.length === 0,\n          className: `flex items-center justify-center w-10 h-10 rounded-lg ${selectedCards.length > 0 ? 'text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20' : 'text-gray-300 cursor-not-allowed dark:text-gray-600'}`,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded\",\n            children: \"delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex -space-x-2\",\n          children: avatarImages.map((avatar, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n            src: avatar,\n            alt: `User ${index + 1}`,\n            className: \"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(!showAddForm),\n          className: \"flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded mr-2\",\n            children: \"add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), \"Add Password Card\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n          children: \"Add New Password Card\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(false),\n          className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-rounded\",\n            children: \"close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AddPasswordForm, {\n        onSubmit: handleAddPasswordCard,\n        onCancel: () => setShowAddForm(false),\n        generatedPassword: generatedPassword,\n        passwordStrength: passwordStrength\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"w-full text-sm text-left text-gray-500 dark:text-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: \"checkbox-all\",\n                  type: \"checkbox\",\n                  checked: selectedCards.length === passwordCards.length,\n                  onChange: toggleSelectAll,\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"checkbox-all\",\n                  className: \"sr-only\",\n                  children: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"User Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              scope: \"col\",\n              className: \"px-6 py-3\",\n              children: \"Action\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: passwordCards.map(card => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"w-4 p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  id: `checkbox-table-${card.id}`,\n                  type: \"checkbox\",\n                  checked: selectedCards.includes(card.id),\n                  onChange: () => toggleCardSelection(card.id),\n                  className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `checkbox-table-${card.id}`,\n                  className: \"sr-only\",\n                  children: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3\",\n                  children: \"TG\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-900 dark:text-white\",\n                  children: card.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900 dark:text-white\",\n                  children: card.username\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => copyToClipboard(card.username),\n                  className: \"ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"content_copy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-900 dark:text-white mr-2\",\n                  children: visiblePasswords[card.id] ? card.actualPassword : card.password\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => togglePasswordVisibility(card.id),\n                  className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: visiblePasswords[card.id] ? 'visibility_off' : 'visibility'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => copyToClipboard(card.actualPassword),\n                  className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"content_copy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 text-gray-900 dark:text-white\",\n              children: card.team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 text-gray-900 dark:text-white\",\n              children: card.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`,\n                children: card.strength\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEdit(card.id),\n                  className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"edit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(card.id),\n                  className: \"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"material-symbols-rounded text-sm\",\n                    children: \"delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, card.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddForm(!showAddForm),\n        className: \"flex items-center justify-center px-6 py-3 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2\",\n          children: \"add\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), \"Add New Password Card\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordCardsTable, \"nDB6rGOcba2XnN0vT3CPi9MMk2I=\");\n_c = PasswordCardsTable;\nexport default PasswordCardsTable;\nvar _c;\n$RefreshReg$(_c, \"PasswordCardsTable\");", "map": {"version": 3, "names": ["React", "useState", "AddPasswordForm", "jsxDEV", "_jsxDEV", "PasswordCardsTable", "generatedPassword", "passwordStrength", "_s", "passwordCards", "setPasswordCards", "id", "title", "username", "password", "actualPassword", "team", "department", "strength", "strengthColor", "visiblePasswords", "setVisiblePasswords", "selectedCards", "setSelectedCards", "showAddForm", "setShowAddForm", "togglePasswordVisibility", "prev", "toggleCardSelection", "includes", "filter", "cardId", "toggleSelectAll", "length", "map", "card", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "handleEdit", "console", "log", "handleDelete", "handleDeleteSelected", "handleAddPasswordCard", "newCard", "cardWithId", "Math", "max", "c", "avatarImages", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "avatar", "index", "src", "alt", "onSubmit", "onCancel", "scope", "type", "checked", "onChange", "htmlFor", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/PasswordCardsTable.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport AddPasswordForm from './AddPasswordForm';\n\nconst PasswordCardsTable = ({ generatedPassword, passwordStrength }) => {\n  // Sample data - in real app this would come from API/state management\n  const [passwordCards, setPasswordCards] = useState([\n    {\n      id: 1,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'xSnTB07JGnTdGVjHrCMDquVo5Bb9CHgxHVRUqA4xTDn',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    },\n    {\n      id: 2,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'StrongPass123!@#',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Strong Password',\n      strengthColor: 'bg-green-100 text-green-600 border-green-300'\n    },\n    {\n      id: 3,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'ModeratePass456',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Moderate Password',\n      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n    },\n    {\n      id: 4,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'WeakPass',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    },\n    {\n      id: 5,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'AnotherStrongPass789!',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Strong Password',\n      strengthColor: 'bg-green-100 text-green-600 border-green-300'\n    },\n    {\n      id: 6,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'ModerateSecure123',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Moderate Password',\n      strengthColor: 'bg-yellow-100 text-yellow-600 border-yellow-300'\n    },\n    {\n      id: 7,\n      title: 'Platform Name',\n      username: '<EMAIL>',\n      password: '••••••••••••',\n      actualPassword: 'VeryWeakPass',\n      team: 'Team Name',\n      department: 'Department name',\n      strength: 'Weak Password',\n      strengthColor: 'bg-red-100 text-red-600 border-red-300'\n    }\n  ]);\n\n  const [visiblePasswords, setVisiblePasswords] = useState({});\n  const [selectedCards, setSelectedCards] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  const togglePasswordVisibility = (id) => {\n    setVisiblePasswords(prev => ({\n      ...prev,\n      [id]: !prev[id]\n    }));\n  };\n\n  const toggleCardSelection = (id) => {\n    setSelectedCards(prev => \n      prev.includes(id) \n        ? prev.filter(cardId => cardId !== id)\n        : [...prev, id]\n    );\n  };\n\n  const toggleSelectAll = () => {\n    if (selectedCards.length === passwordCards.length) {\n      setSelectedCards([]);\n    } else {\n      setSelectedCards(passwordCards.map(card => card.id));\n    }\n  };\n\n  const copyToClipboard = (text) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  const handleEdit = (id) => {\n    console.log('Edit password card:', id);\n    // Navigate to edit form or open modal\n  };\n\n  const handleDelete = (id) => {\n    console.log('Delete password card:', id);\n    // Show confirmation dialog and delete\n    setPasswordCards(prev => prev.filter(card => card.id !== id));\n  };\n\n  const handleDeleteSelected = () => {\n    if (selectedCards.length > 0) {\n      setPasswordCards(prev => prev.filter(card => !selectedCards.includes(card.id)));\n      setSelectedCards([]);\n    }\n  };\n\n  const handleAddPasswordCard = (newCard) => {\n    const cardWithId = {\n      ...newCard,\n      id: Math.max(...passwordCards.map(c => c.id)) + 1,\n      password: '••••••••••••',\n      actualPassword: newCard.password\n    };\n    setPasswordCards(prev => [...prev, cardWithId]);\n    setShowAddForm(false);\n  };\n\n  // Placeholder avatar images\n  const avatarImages = [\n    'https://via.placeholder.com/32x32/3B82F6/FFFFFF?text=A',\n    'https://via.placeholder.com/32x32/10B981/FFFFFF?text=B',\n    'https://via.placeholder.com/32x32/F59E0B/FFFFFF?text=C',\n    'https://via.placeholder.com/32x32/EF4444/FFFFFF?text=D'\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-6\">\n        <div className=\"w-full md:w-1/2\">\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Teams Password Card</h2>\n        </div>\n        <div className=\"w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0\">\n          {/* Share Icon */}\n          <button className=\"flex items-center justify-center w-10 h-10 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\">\n            <span className=\"material-symbols-rounded\">share</span>\n          </button>\n          \n          {/* Delete Selected Icon */}\n          <button \n            onClick={handleDeleteSelected}\n            disabled={selectedCards.length === 0}\n            className={`flex items-center justify-center w-10 h-10 rounded-lg ${\n              selectedCards.length > 0 \n                ? 'text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20' \n                : 'text-gray-300 cursor-not-allowed dark:text-gray-600'\n            }`}\n          >\n            <span className=\"material-symbols-rounded\">delete</span>\n          </button>\n          \n          {/* User Avatars */}\n          <div className=\"flex -space-x-2\">\n            {avatarImages.map((avatar, index) => (\n              <img\n                key={index}\n                src={avatar}\n                alt={`User ${index + 1}`}\n                className=\"w-8 h-8 rounded-full border-2 border-white dark:border-gray-800\"\n              />\n            ))}\n          </div>\n          \n          {/* Add Password Card Button */}\n          <button\n            onClick={() => setShowAddForm(!showAddForm)}\n            className=\"flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\"\n          >\n            <span className=\"material-symbols-rounded mr-2\">add</span>\n            Add Password Card\n          </button>\n        </div>\n      </div>\n\n      {/* Add Password Form - Embedded */}\n      {showAddForm && (\n        <div className=\"mb-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Add New Password Card</h3>\n            <button\n              onClick={() => setShowAddForm(false)}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <span className=\"material-symbols-rounded\">close</span>\n            </button>\n          </div>\n          <AddPasswordForm \n            onSubmit={handleAddPasswordCard}\n            onCancel={() => setShowAddForm(false)}\n            generatedPassword={generatedPassword}\n            passwordStrength={passwordStrength}\n          />\n        </div>\n      )}\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full text-sm text-left text-gray-500 dark:text-gray-400\">\n          <thead className=\"text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400\">\n            <tr>\n              <th scope=\"col\" className=\"p-4\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"checkbox-all\"\n                    type=\"checkbox\"\n                    checked={selectedCards.length === passwordCards.length}\n                    onChange={toggleSelectAll}\n                    className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                  />\n                  <label htmlFor=\"checkbox-all\" className=\"sr-only\">checkbox</label>\n                </div>\n              </th>\n              <th scope=\"col\" className=\"px-6 py-3\">Title</th>\n              <th scope=\"col\" className=\"px-6 py-3\">User Name</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Password</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Team</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Department</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Level</th>\n              <th scope=\"col\" className=\"px-6 py-3\">Action</th>\n            </tr>\n          </thead>\n          <tbody>\n            {passwordCards.map((card) => (\n              <tr key={card.id} className=\"bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\">\n                <td className=\"w-4 p-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      id={`checkbox-table-${card.id}`}\n                      type=\"checkbox\"\n                      checked={selectedCards.includes(card.id)}\n                      onChange={() => toggleCardSelection(card.id)}\n                      className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\n                    />\n                    <label htmlFor={`checkbox-table-${card.id}`} className=\"sr-only\">checkbox</label>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3\">\n                      TG\n                    </div>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">{card.title}</span>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-gray-900 dark:text-white\">{card.username}</span>\n                    <button\n                      onClick={() => copyToClipboard(card.username)}\n                      className=\"ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">content_copy</span>\n                    </button>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-gray-900 dark:text-white mr-2\">\n                      {visiblePasswords[card.id] ? card.actualPassword : card.password}\n                    </span>\n                    <button\n                      onClick={() => togglePasswordVisibility(card.id)}\n                      className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">\n                        {visiblePasswords[card.id] ? 'visibility_off' : 'visibility'}\n                      </span>\n                    </button>\n                    <button\n                      onClick={() => copyToClipboard(card.actualPassword)}\n                      className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">content_copy</span>\n                    </button>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 text-gray-900 dark:text-white\">{card.team}</td>\n                <td className=\"px-6 py-4 text-gray-900 dark:text-white\">{card.department}</td>\n                <td className=\"px-6 py-4\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${card.strengthColor}`}>\n                    {card.strength}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => handleEdit(card.id)}\n                      className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">edit</span>\n                    </button>\n                    <button\n                      onClick={() => handleDelete(card.id)}\n                      className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                    >\n                      <span className=\"material-symbols-rounded text-sm\">delete</span>\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Add New Password Card Button (Bottom) */}\n      <div className=\"flex justify-center mt-6\">\n        <button\n          onClick={() => setShowAddForm(!showAddForm)}\n          className=\"flex items-center justify-center px-6 py-3 text-sm font-medium text-white rounded-lg bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800\"\n        >\n          <span className=\"material-symbols-rounded mr-2\">add</span>\n          Add New Password Card\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default PasswordCardsTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACtE;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC,CACjD;IACEU,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,6CAA6C;IAC7DC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,kBAAkB;IAClCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,UAAU;IAC1BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,uBAAuB;IACvCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,iBAAiB;IAC3BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,mBAAmB;IACnCC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,mBAAmB;IAC7BC,aAAa,EAAE;EACjB,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,eAAe;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,QAAQ,EAAE,cAAc;IACxBC,cAAc,EAAE,cAAc;IAC9BC,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,iBAAiB;IAC7BC,QAAQ,EAAE,eAAe;IACzBC,aAAa,EAAE;EACjB,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMyB,wBAAwB,GAAIf,EAAE,IAAK;IACvCU,mBAAmB,CAACM,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAAChB,EAAE,GAAG,CAACgB,IAAI,CAAChB,EAAE;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiB,mBAAmB,GAAIjB,EAAE,IAAK;IAClCY,gBAAgB,CAACI,IAAI,IACnBA,IAAI,CAACE,QAAQ,CAAClB,EAAE,CAAC,GACbgB,IAAI,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,KAAKpB,EAAE,CAAC,GACpC,CAAC,GAAGgB,IAAI,EAAEhB,EAAE,CAClB,CAAC;EACH,CAAC;EAED,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIV,aAAa,CAACW,MAAM,KAAKxB,aAAa,CAACwB,MAAM,EAAE;MACjDV,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,MAAM;MACLA,gBAAgB,CAACd,aAAa,CAACyB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACxB,EAAE,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMyB,eAAe,GAAIC,IAAI,IAAK;IAChCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnC;EACF,CAAC;EAED,MAAMI,UAAU,GAAI9B,EAAE,IAAK;IACzB+B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEhC,EAAE,CAAC;IACtC;EACF,CAAC;EAED,MAAMiC,YAAY,GAAIjC,EAAE,IAAK;IAC3B+B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhC,EAAE,CAAC;IACxC;IACAD,gBAAgB,CAACiB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACK,IAAI,IAAIA,IAAI,CAACxB,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMkC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIvB,aAAa,CAACW,MAAM,GAAG,CAAC,EAAE;MAC5BvB,gBAAgB,CAACiB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACK,IAAI,IAAI,CAACb,aAAa,CAACO,QAAQ,CAACM,IAAI,CAACxB,EAAE,CAAC,CAAC,CAAC;MAC/EY,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMuB,qBAAqB,GAAIC,OAAO,IAAK;IACzC,MAAMC,UAAU,GAAG;MACjB,GAAGD,OAAO;MACVpC,EAAE,EAAEsC,IAAI,CAACC,GAAG,CAAC,GAAGzC,aAAa,CAACyB,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACxC,EAAE,CAAC,CAAC,GAAG,CAAC;MACjDG,QAAQ,EAAE,cAAc;MACxBC,cAAc,EAAEgC,OAAO,CAACjC;IAC1B,CAAC;IACDJ,gBAAgB,CAACiB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEqB,UAAU,CAAC,CAAC;IAC/CvB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAG,CACnB,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,EACxD,wDAAwD,CACzD;EAED,oBACEhD,OAAA;IAAKiD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExClD,OAAA;MAAKiD,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAC9GlD,OAAA;QAAKiD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BlD,OAAA;UAAIiD,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eACNtD,OAAA;QAAKiD,SAAS,EAAC,wIAAwI;QAAAC,QAAA,gBAErJlD,OAAA;UAAQiD,SAAS,EAAC,8KAA8K;UAAAC,QAAA,eAC9LlD,OAAA;YAAMiD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eAGTtD,OAAA;UACEuD,OAAO,EAAEd,oBAAqB;UAC9Be,QAAQ,EAAEtC,aAAa,CAACW,MAAM,KAAK,CAAE;UACrCoB,SAAS,EAAE,yDACT/B,aAAa,CAACW,MAAM,GAAG,CAAC,GACpB,oHAAoH,GACpH,qDAAqD,EACxD;UAAAqB,QAAA,eAEHlD,OAAA;YAAMiD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAGTtD,OAAA;UAAKiD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BF,YAAY,CAAClB,GAAG,CAAC,CAAC2B,MAAM,EAAEC,KAAK,kBAC9B1D,OAAA;YAEE2D,GAAG,EAAEF,MAAO;YACZG,GAAG,EAAE,QAAQF,KAAK,GAAG,CAAC,EAAG;YACzBT,SAAS,EAAC;UAAiE,GAHtES,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMlC,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C6B,SAAS,EAAC,yOAAyO;UAAAC,QAAA,gBAEnPlD,OAAA;YAAMiD,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,qBAE5D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlC,WAAW,iBACVpB,OAAA;MAAKiD,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBACrElD,OAAA;QAAKiD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlD,OAAA;UAAIiD,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9FtD,OAAA;UACEuD,OAAO,EAAEA,CAAA,KAAMlC,cAAc,CAAC,KAAK,CAAE;UACrC4B,SAAS,EAAC,4DAA4D;UAAAC,QAAA,eAEtElD,OAAA;YAAMiD,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNtD,OAAA,CAACF,eAAe;QACd+D,QAAQ,EAAEnB,qBAAsB;QAChCoB,QAAQ,EAAEA,CAAA,KAAMzC,cAAc,CAAC,KAAK,CAAE;QACtCnB,iBAAiB,EAAEA,iBAAkB;QACrCC,gBAAgB,EAAEA;MAAiB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDtD,OAAA;MAAKiD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BlD,OAAA;QAAOiD,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAC1ElD,OAAA;UAAOiD,SAAS,EAAC,gFAAgF;UAAAC,QAAA,eAC/FlD,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,KAAK;cAAAC,QAAA,eAC7BlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBACEO,EAAE,EAAC,cAAc;kBACjByD,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE/C,aAAa,CAACW,MAAM,KAAKxB,aAAa,CAACwB,MAAO;kBACvDqC,QAAQ,EAAEtC,eAAgB;kBAC1BqB,SAAS,EAAC;gBAAqN;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChO,CAAC,eACFtD,OAAA;kBAAOmE,OAAO,EAAC,cAAc;kBAAClB,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDtD,OAAA;cAAI+D,KAAK,EAAC,KAAK;cAACd,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRtD,OAAA;UAAAkD,QAAA,EACG7C,aAAa,CAACyB,GAAG,CAAEC,IAAI,iBACtB/B,OAAA;YAAkBiD,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAC3HlD,OAAA;cAAIiD,SAAS,EAAC,SAAS;cAAAC,QAAA,eACrBlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBACEO,EAAE,EAAE,kBAAkBwB,IAAI,CAACxB,EAAE,EAAG;kBAChCyD,IAAI,EAAC,UAAU;kBACfC,OAAO,EAAE/C,aAAa,CAACO,QAAQ,CAACM,IAAI,CAACxB,EAAE,CAAE;kBACzC2D,QAAQ,EAAEA,CAAA,KAAM1C,mBAAmB,CAACO,IAAI,CAACxB,EAAE,CAAE;kBAC7C0C,SAAS,EAAC;gBAAqN;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChO,CAAC,eACFtD,OAAA;kBAAOmE,OAAO,EAAE,kBAAkBpC,IAAI,CAACxB,EAAE,EAAG;kBAAC0C,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtD,OAAA;cAAIiD,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBAAKiD,SAAS,EAAC,iGAAiG;kBAAAC,QAAA,EAAC;gBAEjH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA;kBAAMiD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAEnB,IAAI,CAACvB;gBAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtD,OAAA;cAAIiD,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBAAMiD,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAAEnB,IAAI,CAACtB;gBAAQ;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtEtD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAACD,IAAI,CAACtB,QAAQ,CAAE;kBAC9CwC,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAE3ElD,OAAA;oBAAMiD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtD,OAAA;cAAIiD,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBlD,OAAA;gBAAKiD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClD,OAAA;kBAAMiD,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EACjDlC,gBAAgB,CAACe,IAAI,CAACxB,EAAE,CAAC,GAAGwB,IAAI,CAACpB,cAAc,GAAGoB,IAAI,CAACrB;gBAAQ;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACPtD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMjC,wBAAwB,CAACS,IAAI,CAACxB,EAAE,CAAE;kBACjD0C,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,eAE3ElD,OAAA;oBAAMiD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC/ClC,gBAAgB,CAACe,IAAI,CAACxB,EAAE,CAAC,GAAG,gBAAgB,GAAG;kBAAY;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACTtD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAACD,IAAI,CAACpB,cAAc,CAAE;kBACpDsC,SAAS,EAAC,4DAA4D;kBAAAC,QAAA,eAEtElD,OAAA;oBAAMiD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLtD,OAAA;cAAIiD,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEnB,IAAI,CAACnB;YAAI;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxEtD,OAAA;cAAIiD,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAEnB,IAAI,CAAClB;YAAU;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9EtD,OAAA;cAAIiD,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBlD,OAAA;gBAAMiD,SAAS,EAAE,qDAAqDlB,IAAI,CAAChB,aAAa,EAAG;gBAAAmC,QAAA,EACxFnB,IAAI,CAACjB;cAAQ;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLtD,OAAA;cAAIiD,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBlD,OAAA;gBAAKiD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAACN,IAAI,CAACxB,EAAE,CAAE;kBACnC0C,SAAS,EAAC,+EAA+E;kBAAAC,QAAA,eAEzFlD,OAAA;oBAAMiD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACTtD,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACT,IAAI,CAACxB,EAAE,CAAE;kBACrC0C,SAAS,EAAC,2EAA2E;kBAAAC,QAAA,eAErFlD,OAAA;oBAAMiD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GA3EEvB,IAAI,CAACxB,EAAE;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4EZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvClD,OAAA;QACEuD,OAAO,EAAEA,CAAA,KAAMlC,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5C6B,SAAS,EAAC,yOAAyO;QAAAC,QAAA,gBAEnPlD,OAAA;UAAMiD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,yBAE5D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAvVIH,kBAAkB;AAAAmE,EAAA,GAAlBnE,kBAAkB;AAyVxB,eAAeA,kBAAkB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}