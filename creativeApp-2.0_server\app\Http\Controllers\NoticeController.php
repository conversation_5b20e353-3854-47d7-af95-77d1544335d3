<?php

namespace App\Http\Controllers;

use App\Models\Notice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NoticeController extends Controller
{
    /**
     * Display a listing of the notices.
     */
    public function index()
    {

        //$notices = OnsiteStatus::all();
        $notices = Notice::where('published_date', '<=', now());
        $notices->where('expiry_date', '>=', now());
        return response()->json($notices->get());

    //     $user = $request->user(); // Get the authenticated user

    // $notices = Notice::query();

    // if (!$user || $user->role !== 'admin') {
    //     // If not admin, show only published notices
    //     $notices->where('published_date', '<=', now())
    //             ->where('expiry_date', '>=', now());
    // }

    // return response()->json($notices->get());
        
    }

    /**
     * Store a newly created notice.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'required|string',
            'priority' => 'required|string',
            'department' => 'required|string',
            'team' => 'required|string',
            'expiry_date' => 'nullable|date',
            'published_date' => 'nullable|date',
            'status' => 'nullable|string',
        ]);

        $notice = Notice::create([
            'title' => $request->title,
            'content' => $request->content,
            'category' => $request->category,
            'priority' => $request->priority,
            'department' => $request->department,
            'team' => $request->team,
            'expiry_date' => $request->expiry_date,
            'published_date' => $request->published_date,
            'status' => $request->status,
            'created_by' => Auth::id(), // Assuming authentication is used
        ]);

        return response()->json(['message' => 'Notice created successfully', 'notice' => $notice], 201);
    }

    /**
     * Display the specified notice.
     */
    public function show($id)
    {
        $notice = Notice::findOrFail($id);
        return response()->json($notice);
    }

    /**
     * Update the specified notice.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'required|string',
            'priority' => 'required|string',
            'department' => 'required|string',
            'team' => 'required|string',
            'expiry_date' => 'nullable|date',
            'published_date' => 'nullable|date',
            'status' => 'nullable|string',
        ]);

        $notice = Notice::findOrFail($id);
        $notice->update([
            'title' => $request->title,
            'content' => $request->content,
            'category' => $request->category,
            'priority' => $request->priority,
            'department' => $request->department,
            'team' => $request->team,
            'expiry_date' => $request->expiry_date,
            'published_date' => $request->published_date,
            'status' => $request->status,
            'updated_by' => Auth::id(), // Tracking the user who updates the notice
        ]);

        return response()->json(['message' => 'Notice updated successfully', 'notice' => $notice]);
    }

    /**
     * Remove the specified notice.
     */
    

    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $aboutTheAppEntry = Notice::findOrFail($id);

            // Delete the entry
            $aboutTheAppEntry->delete();

            Log::info('About The App entry deleted:', ['entry_id' => $id]);

            return response()->json(['message' => 'Entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this entry.'], 403);
    }
}
