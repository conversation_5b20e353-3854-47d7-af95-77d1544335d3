import React, { useState, useEffect, useCallback } from "react";
import moment from "moment-timezone"; // Make sure moment-timezone is installed

const CurrentTimeByIp = ({ currentTimeData }) => {
  const [ipData, setIpData] = useState(null);
  const [currentDateTime, setCurrentDateTime] = useState(null);
  const [defaultTimeZone, setDefaultTimeZone] = useState({});
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch IP address data on component mount
  useEffect(() => {
    const fetchIP = async () => {
      try {
        const response = await fetch(`//ip-api.com/json/`, { method: "GET" });

        if (!response.ok) {
          throw new Error("Failed to fetch IP data: " + response.statusText);
        }

        const data = await response.json();
        setIpData(data);  // Only set ipData if it's new
        fetchCurrentDateTimeByIP(data.query); // Call to fetch current time by IP
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchIP();
  }, []); // Runs once when the component is mounted

  // Fetch current date-time based on IP address
  const fetchCurrentDateTimeByIP = useCallback(async (ip = "") => {
    if (!ip) return;

    try {
      const response = await fetch(
        `https://timeapi.io/api/time/current/ip?ipAddress=${ip}`,
        { method: "GET" }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch current time: " + response.statusText);
      }

      const responseData = await response.json();

      // Update the default timezone and the current date-time state if it's changed
      if (responseData.dateTime !== currentDateTime?.toString()) {
        setDefaultTimeZone((prev) => ({
          ...prev,
          value: responseData.timeZone,
          offset: responseData.utcOffset,
        }));
        setCurrentDateTime(moment(responseData.dateTime)); // Use moment-timezone to set the date-time
      }
    } catch (error) {
      setError(error.message);
    }
  }, [currentDateTime]); // Add currentDateTime to the dependencies to avoid redundant updates

  // Convert the current date-time to a specific timezone
  const convertDateTime = (toTimezone) => {
    if (!currentDateTime || !toTimezone) return null;

    const fromTimezone = defaultTimeZone.value || "UTC";
    const convertedTime = moment.tz(currentDateTime, fromTimezone).clone().tz(toTimezone);
    return convertedTime.isValid() ? convertedTime : null;
  };

  // Sending formatted time and conversion function to parent component whenever currentDateTime changes
  useEffect(() => {
    if (currentDateTime && currentTimeData) {
      currentTimeData({ formattedTime: currentDateTime.format("YYYY-MM-DD HH:mm:ss"), convertDateTime });
    }
  }, [currentDateTime, currentTimeData]); // Only run when currentDateTime or currentTimeData changes

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return null; // No UI rendering as it only provides data to parent
};

export default CurrentTimeByIp;
