{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\Welcome.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport PasswordGenerator from '../components/password-manager/PasswordGenerator';\nimport PasswordCardsTable from '../components/password-manager/PasswordCardsTable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Welcome = () => {\n  _s();\n  const [generatedPassword, setGeneratedPassword] = useState('');\n  const [passwordStrength, setPasswordStrength] = useState('Strong Password');\n  const handlePasswordGenerated = (password, strength) => {\n    setGeneratedPassword(password);\n    setPasswordStrength(strength);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(PasswordGenerator, {\n        onPasswordGenerated: handlePasswordGenerated\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PasswordCardsTable, {\n      generatedPassword: generatedPassword,\n      passwordStrength: passwordStrength\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Welcome, \"gbjoCbBerkSuw9roBuHceFPf/HY=\");\n_c = Welcome;\nexport default Welcome;\nvar _c;\n$RefreshReg$(_c, \"Welcome\");", "map": {"version": 3, "names": ["React", "useState", "PasswordGenerator", "PasswordCardsTable", "jsxDEV", "_jsxDEV", "Welcome", "_s", "generatedPassword", "setGeneratedPassword", "passwordStrength", "setPasswordStrength", "handlePasswordGenerated", "password", "strength", "className", "children", "onPasswordGenerated", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/Welcome.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport PasswordGenerator from '../components/password-manager/PasswordGenerator';\r\nimport PasswordCardsTable from '../components/password-manager/PasswordCardsTable';\r\n\r\nconst Welcome = () => {\r\n  const [generatedPassword, setGeneratedPassword] = useState('');\r\n  const [passwordStrength, setPasswordStrength] = useState('Strong Password');\r\n\r\n  const handlePasswordGenerated = (password, strength) => {\r\n    setGeneratedPassword(password);\r\n    setPasswordStrength(strength);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      {/* Password Generator - Always visible at top */}\r\n      <div className=\"mb-8\">\r\n        <PasswordGenerator onPasswordGenerated={handlePasswordGenerated} />\r\n      </div>\r\n\r\n      {/* Password Cards Table - Below generator */}\r\n      <PasswordCardsTable\r\n        generatedPassword={generatedPassword}\r\n        passwordStrength={passwordStrength}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,kBAAkB,MAAM,mDAAmD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnF,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAC,iBAAiB,CAAC;EAE3E,MAAMW,uBAAuB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IACtDL,oBAAoB,CAACI,QAAQ,CAAC;IAC9BF,mBAAmB,CAACG,QAAQ,CAAC;EAC/B,CAAC;EAED,oBACET,OAAA;IAAKU,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAE5EX,OAAA;MAAKU,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBX,OAAA,CAACH,iBAAiB;QAACe,mBAAmB,EAAEL;MAAwB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAGNhB,OAAA,CAACF,kBAAkB;MACjBK,iBAAiB,EAAEA,iBAAkB;MACrCE,gBAAgB,EAAEA;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACd,EAAA,CAvBID,OAAO;AAAAgB,EAAA,GAAPhB,OAAO;AAyBb,eAAeA,OAAO;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}