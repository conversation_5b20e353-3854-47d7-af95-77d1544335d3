import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const AddSlaAchieve = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [slaAchieveName, setSlaAchieveName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    // Fetch Departments and Teams
    useEffect(() => {
        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                console.log('API Response Data:', data);  // Add this to check the API response structure
                setDepartments(data.departments || []);  // Make sure this matches the response structure
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, []);

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam(''); // Reset team when department changes
    
        if (departmentName) {
            const department = departments.find(dep => dep.name === departmentName);
    
            // Debugging check if department is undefined
            console.log('Selected Department:', department);

            if (department && Array.isArray(department.teams) && department.teams.length > 0) {
                setTeams(department.teams); // Set teams related to the selected department
            } else {
                setTeams([]); // Clear teams if no teams available
            }
        } else {
            setTeams([]); // Clear teams if no department is selected
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
    
        if (!selectedDepartment || !selectedTeam || !slaAchieveName) {
            setError('Please fill all fields.');
            return;
        }
    
        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }
    
            const response = await fetch(`${API_URL}sla-achieve`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department: selectedDepartment,  // Send the name of the department
                    team: selectedTeam,              // Send the name of the team
                    name: slaAchieveName,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to add SLA Achievement.');
            }
    
            const result = await response.json();
            console.log('SLA Achievement added:', result);

            setSuccessMessage(`SLA Achievement "${result.slaAchieve.name}" added successfully!`);
            setSelectedDepartment('');
            setSelectedTeam('');
            setSlaAchieveName('');
        } catch (error) {
            setError(error.message);
        }
    };

    const isModalOpen = location.pathname === '/add-sla-achieve';

    const handleClose = () => {
        navigate('/formation');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-[80vh] mt-10">
                        <button onClick={handleClose} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                            &times;
                        </button>
                        <h4 className="text-xl font-semibold mb-4 py-4">Add New SLA Achievement</h4>
                        <form onSubmit={handleSubmit}>
                            <div className="mb-4">
                                <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Department
                                </label>
                                <select
                                    id="department"
                                    value={selectedDepartment}
                                    onChange={handleDepartmentChange}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Department</option>
                                    {departments && departments.length > 0 ? (
                                        departments.map((department) => (
                                            <option key={department.id} value={department.name}>
                                                {department.name}
                                            </option>
                                        ))
                                    ) : (
                                        <option disabled>No departments available</option>
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                    Select Team
                                </label>
                                <select
                                    id="team"
                                    value={selectedTeam}
                                    onChange={(e) => setSelectedTeam(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                >
                                    <option value="">Select a Team</option>
                                    {teams && teams.length > 0 ? (
                                        teams.map((team) => (
                                            <option key={team.id} value={team.name}>
                                                {team.name}
                                            </option>
                                        ))
                                    ) : (
                                        <option disabled>No teams available</option>
                                    )}
                                </select>
                            </div>

                            <div className="mb-4">
                                <label htmlFor="slaAchieveName" className="block text-sm font-medium text-gray-700 pb-4">
                                    SLA Achievement Name
                                </label>
                                <input
                                    id="slaAchieveName"
                                    type="text"
                                    value={slaAchieveName}
                                    onChange={(e) => setSlaAchieveName(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    required
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add SLA Achievement
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddSlaAchieve;
