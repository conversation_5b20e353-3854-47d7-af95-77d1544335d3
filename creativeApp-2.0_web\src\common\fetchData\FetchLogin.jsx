const API_URL = process.env.REACT_APP_BASE_API_URL;

// FetchLogin method for logging in users
const FetchLogin = async (eid, password) => {
  try {
    const token = localStorage.getItem('token');
    console.log(API_URL)

    const response = await fetch(`${API_URL}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
      },
      body: JSON.stringify({ eid, password }),
    });

    if (!response.ok) {
      const errorData = await response.json(); // Get the error response from the server
      console.error('Login failed:', errorData); // Log the error response

      // Pass the error message to be handled in the Login component
      throw new Error(errorData.error || errorData.message || 'Login failed');
    }

    const data = await response.json();
    // console.log('Login successful:', data);

    if (data.token) {
      let getUserRoles = data.user?.roles.map((role) => role.name) || [];
      localStorage.setItem('token', data.token);
      localStorage.setItem('user_id', data.user_id);
      localStorage.setItem('user', JSON.stringify(data.user));
      localStorage.setItem('roles', JSON.stringify(getUserRoles));
    }

    return data; // Return the response data if login is successful
  } catch (error) {
    console.error('Error during login:', error);
    throw error; // Re-throw the error so it can be caught by the calling function
  }
};


// FetchResetLink to send the reset password email link
const FetchResetLink = async (email) => {
  try {
    const response = await fetch(`${API_URL}/password/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }), // Send the email for password reset
    });

    if (!response.ok) {
      const errorData = await response.json(); // Get error response from server
      console.error('Failed to send reset link:', errorData); // Log the error response
      throw new Error(errorData.message || 'Failed to send reset link: ' + response.statusText);
    }

    const data = await response.json();
    console.log('Password reset link sent:', data);

    return data;
  } catch (error) {
    console.error('Error during reset link request:', error);
    throw error;
  }
};

// FetchResetPassword to reset the password with token
const FetchResetPassword = async (email, password, passwordConfirmation, token) => {
  try {
    console.log('Token:', token);  // Log the token to ensure it's passed correctly
    console.log('Email:', email);  // Log the email

    const response = await fetch(`${API_URL}/password/reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password, password_confirmation: passwordConfirmation, token }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error during password reset:', errorData);
      throw new Error(errorData.message || 'Failed to reset password');
    }

    const data = await response.json();
    console.log('Password reset successful:', data);

    return data;
  } catch (error) {
    console.error('Error during password reset:', error);
    throw error;
  }
};


export { FetchLogin, FetchResetLink, FetchResetPassword };
