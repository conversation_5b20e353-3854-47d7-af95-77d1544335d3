import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import Swal from 'sweetalert2';
import Modal from '../../../common/modal/Modal';
import useFetchApiData from '../../../common/fetchData/useFetchApiData';


const AddStatus = ({ setFetchData }) => {
    const [isOpen, setIsOpen] = useState(false);
    const { register, formState: { errors }, handleSubmit, reset } = useForm();

    const API_URL = process.env.REACT_APP_BASE_API_URL;
    const token = localStorage.getItem('token');

    // fetch all status data for checking unique entry
    const statusData = useFetchApiData(`${API_URL}/status`, token);

    const onSubmit = async (data) => {
        let creator_id = localStorage.getItem('user_id');
        data = { ...data, creator_id }
        console.log(data);
        try {
            if (!token) {
                Swal.fire({
                    title: 'Error',
                    text: 'Authentication token is missing. Please login again.',
                    icon: 'warning',
                    showConfirmButton: true,
                });
                return;
            }
            // check status if available or not, if available trigger a message to user try another        
            const stat = statusData.data.status.find((stat) => stat.name === data.name);
            if (stat) {
                Swal.fire({
                    title: 'Error',
                    text: `This "${data.name}" Status already added, kindly try another one!`,
                    icon: 'warning',
                    showConfirmButton: true,
                    confirmButtonColor: '#DC2626',
                })
                reset();
                return;
            }

            // API trigger
            const response = await fetch(`${API_URL}/status`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                throw new Error('Failed to add stat!');
            }

            const result = await response.json();
            console.log(result);
            //fire success message alert
            Swal.fire({
                title: `${result.message}!`,
                icon: "success",
                showConfirmButton: true,
                confirmButtonColor: '#3085d6',
                // timer: 3000,
            });
            setFetchData(true); //re fetch api data after submit success
            setIsOpen(false); // modal close
            reset();// This resets the form to the default values defined in useForm.
        } catch (error) {
            //fire error message alert
            Swal.fire({
                title: `${error.message}`,
                icon: "error",
                showConfirmButton: true,
                confirmButtonColor: '#DC2626',
                // timer: 3000,
            });
        }
    };
    return (
        <div>
            <button className='w-full md:w-auto flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700'
                onClick={() => setIsOpen(true)}>
                <span className="material-symbols-rounded text-xl">add_circle_outline</span> Add New Status
            </button>

            {/* check is modal open or not */}
            {/* <!-- Main modal --> */}
            <Modal isOpen={isOpen} setIsOpen={setIsOpen} ModalTitle="Add New Status">
                {/* <!-- Modal body --> */}
                <form className="p-4 md:p-5" onSubmit={handleSubmit(onSubmit)}>
                    <div className="grid gap-4 mb-4 grid-cols-2">
                        <div className="col-span-2">
                            <label htmlFor="name" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status Name</label>
                            <input
                                {...register("name", { required: 'Status name is required', maxLength: { value: 20, message: "Status name cannot exceed 20 characters" } })}
                                className={`bg-gray-50 border ${errors.name ? 'border-red-600' : 'border-gray-300'} text-gray-900 text-sm rounded-lg focus:${errors.name ? 'border-red-600' : 'border-primary-600'} block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500`}
                                placeholder="Type Status name" />
                        </div>
                        {errors.name && <p className='text-red-600 col-span-2' role="alert">{errors.name.message}</p>}
                        <div className="col-span-2">
                            <label htmlFor="bg_cls" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Background Tailwind Class</label>
                            <input
                                {...register("bg_cls", { required: 'Status Background Class is required', maxLength: { value: 20, message: "text cannot exceed 20 characters" } })}
                                className={`bg-gray-50 border ${errors.bg_cls ? 'border-red-600' : 'border-gray-300'} text-gray-900 text-sm rounded-lg focus:${errors.bg_cls ? 'border-red-600' : 'border-primary-600'} block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500`}
                                placeholder="Type Status name" />
                        </div>
                        {errors.bg_cls && <p className='text-red-600 col-span-2' role="alert">{errors.bg_cls.message}</p>}
                        <div className="col-span-2">
                            <label htmlFor="text_cls" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Text color Tailwind Class</label>
                            <input
                                {...register("text_cls", { required: 'Status text Class is required', maxLength: { value: 20, message: "text cannot exceed 20 characters" } })}
                                className={`bg-gray-50 border ${errors.text_cls ? 'border-red-600' : 'border-gray-300'} text-gray-900 text-sm rounded-lg focus:${errors.text_cls ? 'border-red-600' : 'border-primary-600'} block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500`}
                                placeholder="Type Status name" />
                        </div>
                        {errors.text_cls && <p className='text-red-600 col-span-2' role="alert">{errors.text_cls.message}</p>}
                    </div>
                    <input type='submit' value='Save' className="text-white inline-flex items-center bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" />
                </form>
            </Modal>
        </div>
    );
};

export default AddStatus;