import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const regionApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getRegionData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `region-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['RegionData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForRegion: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `region-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['RegionData'],
    }),

    getRegionById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `region-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'RegionData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createRegion: builder.mutation({
      query: (newFormationType) => ({
        url: 'region-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['RegionData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateRegion: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `region/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'RegionData', id }, 'RegionData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteRegion: builder.mutation({
      query: (id) => ({
        url: `region/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['RegionData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetRegionDataQuery,
  useLazyFetchDataOptionsForRegionQuery,
  useGetRegionByIdQuery,
  useLazyGetRegionByIdQuery,
  useCreateRegionMutation,
  useUpdateRegionMutation,
  useDeleteRegionMutation,
} = regionApi;
