import React, { useEffect, useState } from "react";
// import moment from "moment";
import moment from "moment-timezone";
import Select from "react-select";
import DynamicTimeCard from "./DynamicTimeCard";
import timezonebg from "../../assets/images/timezonebg.png";

const timeZones = {
  Dubai: "Asia/Dubai",
  Kabul: "Asia/Kabul",
  Yerevan: "Asia/Yerevan",
  Baku: "Asia/Baku",
  Dhaka: "Asia/Dhaka",
  Brunei: "Asia/Brunei",
  Thimphu: "Asia/Thimphu",
  Shanghai: "Asia/Shanghai",
  Urumqi: "Asia/Urumqi",
  Nicosia: "Asia/Nicosia",
  Famagusta: "Asia/Famagusta",
  Tbilisi: "Asia/Tbilisi",
  Hong_Kong: "Asia/Hong_Kong",
  Jakarta: "Asia/Jakarta",
  Pontianak: "Asia/Pontianak",
  Makassar: "Asia/Makassar",
  Jayapura: "Asia/Jayapura",
  Jerusalem: "Asia/Jerusalem",
  Kolkata: "Asia/Kolkata",
  Baghdad: "Asia/Baghdad",
  Tehran: "Asia/Tehran",
  Amman: "Asia/Amman",
  Tokyo: "Asia/Tokyo",
  Bishkek: "Asia/Bishkek",
  Pyongyang: "Asia/Pyongyang",
  Seoul: "Asia/Seoul",
  Almaty: "Asia/Almaty",
  Qyzylorda: "Asia/Qyzylorda",
  Qostanay: "Asia/Qostanay",
  Aqtobe: "Asia/Aqtobe",
  Aqtau: "Asia/Aqtau",
  Atyrau: "Asia/Atyrau",
  Oral: "Asia/Oral",
  Beirut: "Asia/Beirut",
  Colombo: "Asia/Colombo",
  Yangon: "Asia/Yangon",
  Ulaanbaatar: "Asia/Ulaanbaatar",
  Hovd: "Asia/Hovd",
  Choibalsan: "Asia/Choibalsan",
  Macau: "Asia/Macau",
  Kuala_Lumpur: "Asia/Kuala_Lumpur",
  Kuching: "Asia/Kuching",
  Karachi: "Asia/Karachi",
  Gaza: "Asia/Gaza",
  Hebron: "Asia/Hebron",
  Kathmandu: "Asia/Kathmandu",
  Yekaterinburg: "Asia/Yekaterinburg",
  Qatar: "Asia/Qatar",
  Omsk: "Asia/Omsk",
  Novosibirsk: "Asia/Novosibirsk",
  Barnaul: "Asia/Barnaul",
  Tomsk: "Asia/Tomsk",
  Novokuznetsk: "Asia/Novokuznetsk",
  Krasnoyarsk: "Asia/Krasnoyarsk",
  Irkutsk: "Asia/Irkutsk",
  Chita: "Asia/Chita",
  Yakutsk: "Asia/Yakutsk",
  Khandyga: "Asia/Khandyga",
  Vladivostok: "Asia/Vladivostok",
  Ust_Nera: "Asia/Ust-Nera",
  Singapore: "Asia/Singapore",
  Magadan: "Asia/Magadan",
  Sakhalin: "Asia/Sakhalin",
  Srednekolymsk: "Asia/Srednekolymsk",
  Kamchatka: "Asia/Kamchatka",
  Anadyr: "Asia/Anadyr",
  Bangkok: "Asia/Bangkok",
  Dushanbe: "Asia/Dushanbe",
  Taipei: "Asia/Taipei",
  Dili: "Asia/Dili",
  Ashgabat: "Asia/Ashgabat",
  Damascus: "Asia/Damascus",
  Riyadh: "Asia/Riyadh",
  Samarkand: "Asia/Samarkand",
  Tashkent: "Asia/Tashkent",
  Ho_Chi_Minh: "Asia/Ho_Chi_Minh",
  Andorra: "Europe/Andorra",
  Tirane: "Europe/Tirane",
  Vienna: "Europe/Vienna",
  Brussels: "Europe/Brussels",
  Sofia: "Europe/Sofia",
  Minsk: "Europe/Minsk",
  Zurich: "Europe/Zurich",
  Prague: "Europe/Prague",
  Berlin: "Europe/Berlin",
  Copenhagen: "Europe/Copenhagen",
  Tallinn: "Europe/Tallinn",
  Madrid: "Europe/Madrid",
  Helsinki: "Europe/Helsinki",
  Paris: "Europe/Paris",
  London: "Europe/London",
  Gibraltar: "Europe/Gibraltar",
  Athens: "Europe/Athens",
  Budapest: "Europe/Budapest",
  Dublin: "Europe/Dublin",
  Rome: "Europe/Rome",
  Vilnius: "Europe/Vilnius",
  Luxembourg: "Europe/Luxembourg",
  Riga: "Europe/Riga",
  Monaco: "Europe/Monaco",
  Chisinau: "Europe/Chisinau",
  Malta: "Europe/Malta",
  Amsterdam: "Europe/Amsterdam",
  Oslo: "Europe/Oslo",
  Warsaw: "Europe/Warsaw",
  Lisbon: "Europe/Lisbon",
  Bucharest: "Europe/Bucharest",
  Belgrade: "Europe/Belgrade",
  Kaliningrad: "Europe/Kaliningrad",
  Moscow: "Europe/Moscow",
  Simferopol: "Europe/Simferopol",
  Kirov: "Europe/Kirov",
  Astrakhan: "Europe/Astrakhan",
  Volgograd: "Europe/Volgograd",
  Saratov: "Europe/Saratov",
  Ulyanovsk: "Europe/Ulyanovsk",
  Samara: "Europe/Samara",
  Stockholm: "Europe/Stockholm",
  Istanbul: "Europe/Istanbul",
  Kiev: "Europe/Kiev",
  Uzhgorod: "Europe/Uzhgorod",
  Zaporozhye: "Europe/Zaporozhye",
  Casey: "Antarctica/Casey",
  Davis: "Antarctica/Davis",
  DumontDUrville: "Antarctica/DumontDUrville",
  Mawson: "Antarctica/Mawson",
  Palmer: "Antarctica/Palmer",
  Rothera: "Antarctica/Rothera",
  Syowa: "Antarctica/Syowa",
  Troll: "Antarctica/Troll",
  Vostok: "Antarctica/Vostok",
  Macquarie: "Antarctica/Macquarie",
  Buenos_Aires: "America/Argentina/Buenos_Aires",
  Cordoba: "America/Argentina/Cordoba",
  Salta: "America/Argentina/Salta",
  Jujuy: "America/Argentina/Jujuy",
  Tucuman: "America/Argentina/Tucuman",
  Catamarca: "America/Argentina/Catamarca",
  La_Rioja: "America/Argentina/La_Rioja",
  San_Juan: "America/Argentina/San_Juan",
  Mendoza: "America/Argentina/Mendoza",
  San_Luis: "America/Argentina/San_Luis",
  Rio_Gallegos: "America/Argentina/Rio_Gallegos",
  Ushuaia: "America/Argentina/Ushuaia",
  Barbados: "America/Barbados",
  La_Paz: "America/La_Paz",
  Belem: "America/Belem",
  Fortaleza: "America/Fortaleza",
  Recife: "America/Recife",
  Araguaina: "America/Araguaina",
  Maceio: "America/Maceio",
  Bahia: "America/Bahia",
  Sao_Paulo: "America/Sao_Paulo",
  Campo_Grande: "America/Campo_Grande",
  Cuiaba: "America/Cuiaba",
  Porto_Velho: "America/Porto_Velho",
  Boa_Vista: "America/Boa_Vista",
  Manaus: "America/Manaus",
  Eirunepe: "America/Eirunepe",
  Rio_Branco: "America/Rio_Branco",
  Nassau: "America/Nassau",
  Belize: "America/Belize",
  St_Johns: "America/St_Johns",
  Halifax: "America/Halifax",
  Glace_Bay: "America/Glace_Bay",
  Moncton: "America/Moncton",
  Goose_Bay: "America/Goose_Bay",
  Blanc_Sablon: "America/Blanc-Sablon",
  Toronto: "America/Toronto",
  Nipigon: "America/Nipigon",
  Thunder_Bay: "America/Thunder_Bay",
  Iqaluit: "America/Iqaluit",
  Pangnirtung: "America/Pangnirtung",
  Atikokan: "America/Atikokan",
  Winnipeg: "America/Winnipeg",
  Rainy_River: "America/Rainy_River",
  Resolute: "America/Resolute",
  Rankin_Inlet: "America/Rankin_Inlet",
  Regina: "America/Regina",
  Swift_Current: "America/Swift_Current",
  Edmonton: "America/Edmonton",
  Cambridge_Bay: "America/Cambridge_Bay",
  Yellowknife: "America/Yellowknife",
  Inuvik: "America/Inuvik",
  Creston: "America/Creston",
  Dawson_Creek: "America/Dawson_Creek",
  Fort_Nelson: "America/Fort_Nelson",
  Vancouver: "America/Vancouver",
  Whitehorse: "America/Whitehorse",
  Dawson: "America/Dawson",
  Santiago: "America/Santiago",
  Punta_Arenas: "America/Punta_Arenas",
  Bogota: "America/Bogota",
  Costa_Rica: "America/Costa_Rica",
  Havana: "America/Havana",
  Curacao: "America/Curacao",
  Santo_Domingo: "America/Santo_Domingo",
  Guayaquil: "America/Guayaquil",
  Cayenne: "America/Cayenne",
  Godthab: "America/Godthab",
  Danmarkshavn: "America/Danmarkshavn",
  Scoresbysund: "America/Scoresbysund",
  Thule: "America/Thule",
  Guatemala: "America/Guatemala",
  Guyana: "America/Guyana",
  Tegucigalpa: "America/Tegucigalpa",
  Port_au_Prince: "America/Port-au-Prince",
  Jamaica: "America/Jamaica",
  Martinique: "America/Martinique",
  Mexico_City: "America/Mexico_City",
  Cancun: "America/Cancun",
  Merida: "America/Merida",
  Monterrey: "America/Monterrey",
  Matamoros: "America/Matamoros",
  Caracas: "America/Caracas",
  Mazatlan: "America/Mazatlan",
  Chihuahua: "America/Chihuahua",
  Ojinaga: "America/Ojinaga",
  Hermosillo: "America/Hermosillo",
  Tijuana: "America/Tijuana",
  Bahia_Banderas: "America/Bahia_Banderas",
  Managua: "America/Managua",
  Panama: "America/Panama",
  Lima: "America/Lima",
  Miquelon: "America/Miquelon",
  Puerto_Rico: "America/Puerto_Rico",
  El_Salvador: "America/El_Salvador",
  Grand_Turk: "America/Grand_Turk",
  Paramaribo: "America/Paramaribo",
  Asuncion: "America/Asuncion",
  Port_of_Spain: "America/Port_of_Spain",
  New_York: "America/New_York",
  New_Jersey: "America/New_York",
  Detroit: "America/Detroit",
  Louisville: "America/Kentucky/Louisville",
  Monticello: "America/Kentucky/Monticello",
  Indianapolis: "America/Indiana/Indianapolis",
  Vincennes: "America/Indiana/Vincennes",
  Winamac: "America/Indiana/Winamac",
  Marengo: "America/Indiana/Marengo",
  Petersburg: "America/Indiana/Petersburg",
  Vevay: "America/Indiana/Vevay",
  Tell_City: "America/Indiana/Tell_City",
  Knox: "America/Indiana/Knox",
  Chicago: "America/Chicago",
  Menominee: "America/Menominee",
  Denver: "America/Denver",
  Boise: "America/Boise",
  Phoenix: "America/Phoenix",
  Center: "America/North_Dakota/Center",
  New_Salem: "America/North_Dakota/New_Salem",
  Beulah: "America/North_Dakota/Beulah",
  Los_Angeles: "America/Los_Angeles",
  Anchorage: "America/Anchorage",
  Alaska: "America/Anchorage",
  Juneau: "America/Juneau",
  Sitka: "America/Sitka",
  Metlakatla: "America/Metlakatla",
  Yakutat: "America/Yakutat",
  Nome: "America/Nome",
  Adak: "America/Adak",
  Montevideo: "America/Montevideo",
  Pago_Pago: "Pacific/Pago_Pago",
  Rarotonga: "Pacific/Rarotonga",
  Easter: "Pacific/Easter",
  Galapagos: "Pacific/Galapagos",
  Fiji: "Pacific/Fiji",
  Chuuk: "Pacific/Chuuk",
  Pohnpei: "Pacific/Pohnpei",
  Kosrae: "Pacific/Kosrae",
  Guam: "Pacific/Guam",
  Majuro: "Pacific/Majuro",
  Kwajalein: "Pacific/Kwajalein",
  Tarawa: "Pacific/Tarawa",
  Enderbury: "Pacific/Enderbury",
  Kiritimati: "Pacific/Kiritimati",
  Noumea: "Pacific/Noumea",
  Norfolk: "Pacific/Norfolk",
  Nauru: "Pacific/Nauru",
  Niue: "Pacific/Niue",
  Auckland: "Pacific/Auckland",
  Chatham: "Pacific/Chatham",
  Tahiti: "Pacific/Tahiti",
  Marquesas: "Pacific/Marquesas",
  Gambier: "Pacific/Gambier",
  Port_Moresby: "Pacific/Port_Moresby",
  Bougainville: "Pacific/Bougainville",
  Pitcairn: "Pacific/Pitcairn",
  Palau: "Pacific/Palau",
  Guadalcanal: "Pacific/Guadalcanal",
  Fakaofo: "Pacific/Fakaofo",
  Tongatapu: "Pacific/Tongatapu",
  Funafuti: "Pacific/Funafuti",
  Wake: "Pacific/Wake",
  Honolulu: "Pacific/Honolulu",
  Efate: "Pacific/Efate",
  Wallis: "Pacific/Wallis",
  Apia: "Pacific/Apia",
  Lord_Howe: "Australia/Lord_Howe",
  Hobart: "Australia/Hobart",
  Currie: "Australia/Currie",
  Melbourne: "Australia/Melbourne",
  Sydney: "Australia/Sydney",
  Broken_Hill: "Australia/Broken_Hill",
  Brisbane: "Australia/Brisbane",
  Lindeman: "Australia/Lindeman",
  Adelaide: "Australia/Adelaide",
  Darwin: "Australia/Darwin",
  Perth: "Australia/Perth",
  Eucla: "Australia/Eucla",
  Abidjan: "Africa/Abidjan",
  Algiers: "Africa/Algiers",
  Cairo: "Africa/Cairo",
  El_Aaiun: "Africa/El_Aaiun",
  Ceuta: "Africa/Ceuta",
  Accra: "Africa/Accra",
  Bissau: "Africa/Bissau",
  Nairobi: "Africa/Nairobi",
  Monrovia: "Africa/Monrovia",
  Tripoli: "Africa/Tripoli",
  Casablanca: "Africa/Casablanca",
  Maputo: "Africa/Maputo",
  Windhoek: "Africa/Windhoek",
  Lagos: "Africa/Lagos",
  Khartoum: "Africa/Khartoum",
  Juba: "Africa/Juba",
  Sao_Tome: "Africa/Sao_Tome",
  Ndjamena: "Africa/Ndjamena",
  Tunis: "Africa/Tunis",
  Johannesburg: "Africa/Johannesburg",
  Azores: "Atlantic/Azores",
  Bermuda: "Atlantic/Bermuda",
  Madeira: "Atlantic/Madeira",
  Cape_Verde: "Atlantic/Cape_Verde",
  Canary: "Atlantic/Canary",
  Stanley: "Atlantic/Stanley",
  Faroe: "Atlantic/Faroe",
  South_Georgia: "Atlantic/South_Georgia",
  Reykjavik: "Atlantic/Reykjavik",
  Cocos: "Indian/Cocos",
  Christmas: "Indian/Christmas",
  Chagos: "Indian/Chagos",
  Mauritius: "Indian/Mauritius",
  Maldives: "Indian/Maldives",
  Mahe: "Indian/Mahe",
  Reunion: "Indian/Reunion",
  Kerguelen: "Indian/Kerguelen",
};

function TimeZoneConvert() {
  const defaultDateFormat = "dddd, LL";
  const defaultTimeFormat = "hh:mm A";

  const background = {
    backgroundImage: `url(${timezonebg})`,
    backgroundPosition: 'center center',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat'
  }

  const queryParameters = new URLSearchParams(window.location.search);
  const getDateTime = queryParameters.get("datetime");
  const getDefaultTimeZones = queryParameters.get("defaulttimezone");
  const getTimeZones = queryParameters.get("timezones");

  const [defaultTimeZone, setDefaultTimeZone] = useState({
    label: "Dhaka",
    value: "Asia/Dhaka",
  }); // Store team data
  const [currentDateTime, setCurrentDateTime] = useState(
    moment().tz(defaultTimeZone["value"])
  ); // Store team data
  const [error, setError] = useState(null); // Handle errors
  const [loading, setLoading] = useState(true); // Loading state
  // const [storeShowICityList, setStoreShowICityList] = useState({});
  var storeShowICityList = {};
  const [ipData, setIpData] = useState(false);
  const [shareBtnText, setShareBtnText] = useState("Share");
  const [fromDateTime, setFromDateTime] = useState("");
  const [fromtimezone, setFromtimezone] = useState({
    label: "Dhaka",
    value: "Asia/Dhaka",
  });
  const [totimezone, setTotimezone] = useState({
    label: "New York",
    value: "America/New_York",
  });
  const [showCityList, setShowCityList] = useState([
     {
       label: "Dhaka",
       value: "Asia/Dhaka",
       isFixed: true,
     },
     {
       label: "New York",
       value: "America/New_York",
       isFixed: true,
     },
     {
       label: "El Salvador",
       value: "America/El_Salvador",
     },
 
     {
       label: "London",
       value: "Europe/London",
     },
    
     {
       label: "Sydney",
       value: "Australia/Sydney",
     },
    
     {
       label: "Dubai",
       value: "Asia/Dubai",
     },
    
     {
       label: "Tokyo",
       value: "Asia/Tokyo",
     },
   ]);

  const datetimeToISOString = (datetime) => {
    return new Date(datetime).valueOf();
  };

  const generateShareUrl = () => {
    console.log(window.location)
    let url = window.location.href.split("?")[0];
    let params = new URLSearchParams();

    let isoDateTime = fromDateTime
      ? new Date(fromDateTime).toISOString()
      : new Date(currentDateTime).toISOString();
    let dtmz = fromDateTime ? fromtimezone["label"] : defaultTimeZone["label"];
    let timezones = showCityList.map((item) =>
      item["label"].replaceAll(" ", "_")
    );

    params.append("datetime", datetimeToISOString(isoDateTime));
    params.append("defaulttimezone", dtmz);
    params.append("timezones", timezones.join(","));
    url = window.location.origin+"/time-zone-convert-share?" + params.toString(",");
    return url;
  };

  let singleLayoutClasses = "";

  if (window.location.pathname === "/time-zone-convert-share") {
    singleLayoutClasses = " max-w-[1642px] mx-auto ";
  }

  // console.log(window.location.pathname)

  useEffect(() => {
    if (getDateTime) {
      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();
      setFromDateTime(getDateTimeParseData);
    }

    if (getDefaultTimeZones) {
      setDefaultTimeZone({
        label: getDefaultTimeZones,
        value: timeZones[getDefaultTimeZones],
      });
      setFromtimezone({
        label: getDefaultTimeZones,
        value: timeZones[getDefaultTimeZones],
      });
    }

    if (getTimeZones) {
      let timezones = getTimeZones.split(",");
      let timezonesData = timezones.map((item) => {
        return { label: item, value: timeZones[item] };
      });
      setShowCityList(timezonesData);
    }
  }, [getDateTime, getDefaultTimeZones, getTimeZones]);

  const convertDateTime = (totimezonevalue = "") => {
    // let fromtimezonelabel = fromtimezone.label;
    let fromtimezonevalue = fromtimezone.value || defaultTimeZone["value"];
    // let totimezonevalue = totimezone.value;
    var fromConvertDateTime = moment.tz(fromDateTime, fromtimezonevalue);
    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);

    if (moment(retDateTime).isValid()) {
      return retDateTime;
    }

    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);
    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);

    if (moment(retDateTime).isValid()) {
      return retDateTime;
    }
    return false;
  };

  const getTimeZoneSelectOptions = () => {
    let data = [];
    Object.keys(timeZones)
      .sort()
      .map((label) =>
        data.push({
          label: `${label.replaceAll("_", " ")}`,
          value: timeZones[label],
        })
      );

    return data;
  };

  const getLabelByTimezone = (label = "") => {
    return Object.keys(timeZones).find((key) => timeZones[key] === label) || "";
  };

  const calculateDifference = (timezone1, timezone2) => {
    const now = moment().utc(); 
    const time1 = now.clone().tz(timezone1);
    const time2 = now.clone().tz(timezone2);
    const diffInHours = (time1._offset - time2._offset) / 60;

    let fromCityName = moment(fromDateTime).isValid()? fromtimezone["label"].replaceAll("_", " ") : defaultTimeZone["label"].replaceAll("_", " ");

    let returnText = ``;

    if(diffInHours < 0){
      returnText += `${diffInHours*-1} Hours behind from ${fromCityName}`;
    }else if(diffInHours > 0){
      returnText += `${diffInHours} Hours ahead of ${fromCityName}`;
    }else {
      returnText += `Same time as ${fromCityName}`;
    }


    return returnText;
  };


  const getSingleCityData = async (item, key) => {
    try {
      let city = item.label.replaceAll("_", " ");
      let flagResponseData = {};
      let data = {};
      console.log("city", city)
  
      if (city && storeShowICityList[city]) {
        flagResponseData = storeShowICityList[city];
      } else {
        // First fetch weather data to get the country name
        const weatherResponse = await fetch(`https://wttr.in/${city}?format=j1`);
        data = await weatherResponse.json();

        // Get country name from weather data
        const country = data?.nearest_area?.[0]?.country?.[0]?.value;
        console.log("Country extracted from weather data:", country);

        // Then fetch country data using the actual country name
        if (country) {
          const flagResponse = await fetch(`https://restcountries.com/v3.1/name/${country}`);
          flagResponseData = await flagResponse.json();
          console.log("Country data fetched:", flagResponseData);
        }
      }
  
      let retData = {
        ...item,
        city,
        country: data?.nearest_area?.[0]?.country?.[0]?.value || "",
        state: data?.nearest_area?.[0]?.region?.[0]?.value || "",
        flag:
          flagResponseData?.[0]?.flags?.svg ||
          flagResponseData?.[0]?.flags?.png ||
          "",
        capital: flagResponseData?.[0]?.capital?.join(", ") || "",
        googleMapUrl: flagResponseData?.[0]?.maps?.googleMaps || "",
        population: flagResponseData?.[0]?.population || "",
        region: flagResponseData?.[0]?.region || "",
        startOfWeek: flagResponseData?.[0]?.startOfWeek || "",
        // timezones: flagResponseData[0].timezones.join(", ") || "",
        timezones: flagResponseData?.[0]?.timezones?.[0] || "",
        languages:
          flagResponseData?.[0]?.languages ? Object.values(flagResponseData[0].languages).join(", ") : "",
        weather: data?.current_condition?.[0] || "",
        dailyWeather: data?.weather || "",
        condition: data?.current_condition?.[0]?.weatherDesc?.[0]?.value || "",
        weatherIcon: data?.current_condition?.[0]?.weatherIconUrl?.[0]?.value || "",
        fullWeaterData: data,
        fullCountryDetails: flagResponseData,
      };
  
      // Update the showCityList state with the new data
      setShowCityList(prevList => {
        const newArray = [...prevList];
        newArray[key] = retData;
        return newArray;
      });

      return retData;
    } catch (error) {
      console.error("Error fetching city data:", error);

      // Return basic item data if API fails
      const fallbackData = {
        ...item,
        city: item.label.replaceAll("_", " "),
        country: "Unknown",
        state: "Unknown",
        flag: "",
        capital: "Unknown",
        googleMapUrl: "",
        population: "",
        region: "Unknown",
        startOfWeek: "Unknown",
        timezones: "",
        languages: "Unknown",
        weather: {},
        dailyWeather: [],
        condition: "Unknown",
        weatherIcon: "",
        fullWeaterData: {},
        fullCountryDetails: {},
      };

      // Update the showCityList with fallback data
      setShowCityList(prevList => {
        const newArray = [...prevList];
        newArray[key] = fallbackData;
        return newArray;
      });

      return fallbackData;
    }
  };



  const fetchWeather = async () => {
    try {
      const results = await Promise.all(
        showCityList.map(async (item, key) => {
          try {
            let city = item.label.replaceAll("_", " ");
            let flagResponseData = {};
            let data = {};

            if (city && storeShowICityList[city]) {
              flagResponseData = storeShowICityList[city];
            } else {
              const response = await fetch(`https://wttr.in/${city}?format=j1`);
              data = await response.json();

              let country = data?.nearest_area?.[0]?.country?.[0]?.value;

              if (country) {
                const flagResponse = await fetch(
                  `https://restcountries.com/v3.1/name/${country}`
                );
                flagResponseData = await flagResponse.json();
              }
            }

        let retData = {
          ...item,
          city,
          country: data?.nearest_area?.[0]?.country?.[0]?.value || "",
          state: data?.nearest_area?.[0]?.region?.[0]?.value || "",
          flag:
            flagResponseData?.[0]?.flags?.svg ||
            flagResponseData?.[0]?.flags?.png ||
            "",
          capital: flagResponseData?.[0]?.capital?.join(", ") || "",
          googleMapUrl: flagResponseData?.[0]?.maps?.googleMaps || "",
          population: flagResponseData?.[0]?.population || "",
          region: flagResponseData?.[0]?.region || "",
          startOfWeek: flagResponseData?.[0]?.startOfWeek || "",
          // timezones: flagResponseData[0].timezones.join(", ") || "",
          timezones: flagResponseData?.[0]?.timezones?.[0] || "",
          languages:
            flagResponseData?.[0]?.languages ? Object.values(flagResponseData[0].languages).join(", ") : "",
          weather: data?.current_condition?.[0] || "",
          dailyWeather: data?.weather || "",
          condition: data?.current_condition?.[0]?.weatherDesc?.[0]?.value || "",
          weatherIcon: data?.current_condition?.[0]?.weatherIconUrl?.[0]?.value || "",
          fullWeaterData: data,
          fullCountryDetails: flagResponseData,
        };

            return retData;
          } catch (error) {
            console.error(`Error fetching data for ${item.label}:`, error);
            // Return basic item data if API fails
            return {
              ...item,
              city: item.label.replaceAll("_", " "),
              country: "Unknown",
              state: "Unknown",
              flag: "",
              capital: "Unknown",
              googleMapUrl: "",
              population: "",
              region: "Unknown",
              startOfWeek: "Unknown",
              timezones: "",
              languages: "Unknown",
              weather: {},
              dailyWeather: [],
              condition: "Unknown",
              weatherIcon: "",
              fullWeaterData: {},
              fullCountryDetails: {},
            };
          }

          // return storeShowICityList[item];
        })
      );

      // results.map((item) => {
      //   storeShowICityList[item.label] = { ...item };
      // });

      // console.log(storeShowICityList)

      setLoading(false);
      setShowCityList(results);
    } catch (error) {
      console.error("Error in fetchWeather:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWeather();
  }, []);

  return (
    <>
     
        <div
          className={
            "bg-white dark:bg-gray-900 rounded-xl p-4 " + singleLayoutClasses
          }
          
        >
          <div className="flex justify-start items-start flex-row mb-[20px]">
            <div className="text-left w-1/2 ">
              <h4 className="text-xl font-medium ">Time Zone Converter</h4>
            </div>
          </div>

          <div className="flex justify-start items-start flex-row mb-[50px]">
            {/* Current Date Time */}
            <div className="border border-gray-200 rounded-lg  px-6 py-3 me-[10px] w-4/12 h-[210px] bg-[#0b333f] text-[#fff]">
              <DynamicTimeCard />
            </div>

            <div className="border border-gray-200 rounded-lg  mx-[10px] px-6 py-3 w-4/12 h-[210px] bg-[#DFECF1]">
              <form className="text-left">
                <div className=" justify-start items-start">
                  <div className="mb-4 w-full sm:w-full">
                    <label
                      htmlFor="start-time"
                      className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                    >
                      Select Date and Time
                    </label>
                    <div className="relative">
                      <input
                        type="datetime-local"
                        id="shiftStart"
                        disabled={loading}
                        value={(fromDateTime || "").toString().substring(0, 16)}
                        onChange={(e) => {
                          setFromDateTime(e.target.value);
                        }}
                        required
                        className="bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                      />
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="mb-4 w-1/2 sm:w-full">
                      <label
                        htmlFor="team"
                        className="block text-sm font-medium text-gray-700 pb-4"
                      >
                        Select City
                      </label>
                      <Select
                        isClearable={false}
                        isSearchable={true}
                        isDisabled={loading}
                        isMulti={false}
                        required={false}
                        placeholder="Choose timezone"
                        name="fromtimezone"
                        value={fromtimezone}
                        options={getTimeZoneSelectOptions()}
                        onChange={(item) => {
                          setFromtimezone(item);
                        }}
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div className="border border-gray-200   rounded-lg  ms-[10px] px-6 py-3 w-4/12 h-[210px]  bg-[#DFECF1] text-[#000]">
              {totimezone.value && convertDateTime(totimezone.value) && (
                <>
                  <div className="text-[18px] font-bold">
                    {moment(fromDateTime).isValid() &&
                      fromtimezone["label"].replaceAll("_", " ")}
                    {!moment(fromDateTime).isValid() &&
                      defaultTimeZone["label"].replaceAll("_", " ")}
                    <br />
                    {moment(fromDateTime).isValid() && fromtimezone["value"]}
                    {!moment(fromDateTime).isValid() &&
                      defaultTimeZone["value"]}
                  </div>

                  <h1 className="text-[64px] font-bold">
                    {moment(fromDateTime).isValid() &&
                      moment(fromDateTime).format(defaultTimeFormat)}
                    {!moment(fromDateTime).isValid() &&
                      moment(currentDateTime).isValid() &&
                      moment(currentDateTime).format(defaultTimeFormat)}
                  </h1>
                  <div className="text-[20px]  ">
                    {moment(fromDateTime).isValid() &&
                      moment(fromDateTime).format(defaultDateFormat)}
                    {!moment(fromDateTime).isValid() &&
                      moment(currentDateTime).isValid() &&
                      moment(currentDateTime).format(defaultDateFormat)}
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="w-full flex justify-start items-start flex-wrap  mb-10">
            <div className="text-start w-full ">
              <div className="flex items-center rounded-lg gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
                <div className="grid shrink-0 grid-cols-1 focus-within:relative w-11/12 ">
                  {/* <label>Add your city</label> */}
                  <Select
                    closeMenuOnSelect={false}
                    isClearable={true}
                    isSearchable={true}
                    isDisabled={loading}
                    isMulti={true}
                    required={false}
                    placeholder="Choose your city"
                    name="showCityList"
                    value={showCityList}
                    options={getTimeZoneSelectOptions()}
                    onChange={(item) => {
                      setShowCityList(item);

                      // Fetch data for newly added cities
                      if (item && item.length > 0) {
                        item.forEach((city, key) => {
                          if (!city.country && city.label) {
                            // Fetch data for cities that don't have country data yet
                            setTimeout(() => getSingleCityData(city, key), 100);
                          }
                        });
                      }
                    }}
                  />
                </div>
                <button
                  disabled={loading || (shareBtnText === 'Copied')}
                  onClick={() => {
                    navigator.clipboard.writeText(generateShareUrl());
                    setShareBtnText("Copied");
                    setTimeout(function() { setShareBtnText("Share"); }, 5000);
                  }}
                  target="_blank"
                  className="block min-w-0 rounded-lg text-center grow py-1.5 pr-3 pl-1 border border-gray-200 bg-[#076d92] hover:bg-[#0B333F] text-base text-[#fff] placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
                >
                  {shareBtnText}
                </button>
              </div>
            </div>
          </div>

          {loading && <>Loding....</>}
          {!loading && (
          <div className="w-full flex justify-start items-start flex-wrap  " style={background}>
            {/* Contacts Navigation */}

            {/* All Contacts timeZone */}

            {/* {Object.keys(showCityList).sort().map((label) => { */}
            {showCityList.length > 0 &&
              showCityList.map((item, key) => {

                // If city data is not loaded yet, fetch it but still show the city
                if (!item.country) {
                  getSingleCityData(item, key);
                };

                // let label = key, timezone = timeZoneList[key];
                let label = item["label"];
                let timezone = item["value"];

                return (
                  <div
                    key={"timezone-" + timezone+label}
                    className="  w-full px-[20px] "
                  >
                    <div className="border-b-2 border-gray-400 py-4 hover:bg-[#DFECF1]  text-sm  text-start flex capitalize align-middle">
                      <div
                        className="w-[200px] flex-col justify-center text-center items-center"
                        onClick={() =>
                          item?.googleMapUrl &&
                          window.open(item.googleMapUrl, "_blank")
                        }
                      >
                        <div className="flex items-center justify-center text-center ">
                          {item.flag ? (
                            <img
                              src={item.flag}
                              alt={item.country || label}
                              className="w-[100%] max-w-[65px] h-auto shadow-lg border border-1 mb-1"
                            />
                          ) : (
                            <div className="w-[65px] h-[45px] bg-gray-200 border border-1 mb-1 flex items-center justify-center text-xs text-gray-500">
                              Loading...
                            </div>
                          )}
                        </div>
                        <div className=" text-[14px] mb-1">{item.country || "Loading..."}</div>
                        <div className="font-bold text-xl">
                          {label.replaceAll("_", " ")
                            ? label.replaceAll("_", " ")
                            : label}
                        </div>
                      </div>
                      <div className="w-2/12">
                        {item.startOfWeek ? (
                          <div className="truncate">Start Of Week: {item.startOfWeek}</div>
                        ) : (
                          <div className="truncate text-gray-400">Start Of Week: Loading...</div>
                        )}
                        {item.capital ? (
                          <div className="truncate">Capital: {item.capital}</div>
                        ) : (
                          <div className="truncate text-gray-400">Capital: Loading...</div>
                        )}
                        {item.languages ? (
                          <div className="truncate">Language: {item.languages}</div>
                        ) : (
                          <div className="truncate text-gray-400">Language: Loading...</div>
                        )}
                        {item.state ? (
                          <div className="truncate">State/City: {item.state}</div>
                        ) : (
                          <div className="truncate text-gray-400">State/City: Loading...</div>
                        )}
                        {/* {item.region && <div>Region: {item.region}</div>} */}
                      </div>
                      <div className="w-2/12 ">
                        {item.weather?.humidity ? (
                          <div>Humidity: {item.weather.humidity}%</div>
                        ) : (
                          <div className="text-gray-400">Humidity: Loading...</div>
                        )}
                        {item.weather?.visibility ? (
                          <div>Visibility: {item.weather.visibility}km</div>
                        ) : (
                          <div className="text-gray-400">Visibility: Loading...</div>
                        )}
                        {item.weather?.windspeedKmph ? (
                          <div>Wind Speed: {item.weather.windspeedKmph}km/h</div>
                        ) : (
                          <div className="text-gray-400">Wind Speed: Loading...</div>
                        )}
                        {item?.condition ? (
                          <div>Condition: {item.condition}</div>
                        ) : (
                          <div className="text-gray-400">Condition: Loading...</div>
                        )}
                      </div>
                      <div className="w-2/12 mb-1">
                        {item?.dailyWeather?.[0] && (
                          <div className=" ">
                            Sun Hour: {item.dailyWeather[0]?.sunHour}h
                          </div>
                        )}
                        {item?.dailyWeather?.[0]?.astronomy?.[0] && (
                          <div className=" ">
                            Today Sunset:{" "}
                            {item.dailyWeather[0]?.astronomy?.[0]?.sunset}
                          </div>
                        )}
                        {item?.dailyWeather?.[1]?.astronomy?.[0] && (
                          <div className=" ">
                            Tomorrow Sunrise:{" "}
                            {item.dailyWeather[1]?.astronomy?.[0]?.sunrise}
                          </div>
                        )}
                      </div>

                      <div className="w-1/12 mb-1 flex-col align-middle text-end">
                        {item?.weather?.temp_C && (
                          <div className="text-4xl font-bold pb-2 pe-3">
                            {item.weather.temp_C}
                            <sup>°C</sup>
                          </div>
                        )}
                      </div>
                      <div className="w-1/12 mb-1 flex-col align-middle text-start">
                        <div>
                          Feels like: {item?.weather?.FeelsLikeC}
                          <sup>°C</sup>
                        </div>
                        {item?.dailyWeather?.[0] && (
                          <div className=" ">
                            Max Temp: {item?.dailyWeather[0]?.maxtempC}
                            <sup>°C</sup>
                          </div>
                        )}
                        {item?.dailyWeather?.[0] && (
                          <div className=" ">
                            Min Temp: {item?.dailyWeather[0]?.mintempC}
                            <sup>°C</sup>
                          </div>
                        )}
                        {/* {item.dailyWeather[0] && (<div className=" ">Avg. Temp: {item.dailyWeather[0].avgtempC}<sup>°C</sup></div>)} */}
                      </div>
                      <div className="w-2/12 flex-col text-end align-middle">
                        <div className=" text-4xl font-bold ">
                          {convertDateTime(timezone) &&
                            convertDateTime(timezone).format(defaultTimeFormat)}
                        </div>

                        <div className=" text-lg ">
                          {convertDateTime(timezone) &&
                            convertDateTime(timezone).format("dddd, LL")}
                          
                        </div>
                        {item?.timezones && (
                            <div className=" text-xm ">
                              {calculateDifference(item.value, defaultTimeZone["value"])}<br/> 
                              Timezone: {convertDateTime(timezone) && convertDateTime(timezone).format("z")}
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
          )}
        </div>
      
    </>
  );
}

export default TimeZoneConvert;
