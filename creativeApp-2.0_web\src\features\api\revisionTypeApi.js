import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const revisionTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getRevisionTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `revision-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['RevisionTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForRevisionType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `revision-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['RevisionTypeData'],
    }),

    getRevisionTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `revision-type-data/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'RevisionTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createRevisionType: builder.mutation({
      query: (newFormationType) => ({
        url: 'revision-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['RevisionTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateRevisionType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `revision-type/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'RevisionTypeData', id }, 'RevisionTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteRevisionType: builder.mutation({
      query: (id) => ({
        url: `revision-type/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['RevisionTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetRevisionTypeDataQuery,
  useLazyFetchDataOptionsForRevisionTypeQuery,
  useGetRevisionTypeByIdQuery,
  useLazyGetRevisionTypeByIdQuery,
  useCreateRevisionTypeMutation,
  useUpdateRevisionTypeMutation,
  useDeleteRevisionTypeMutation,
} = revisionTypeApi;
