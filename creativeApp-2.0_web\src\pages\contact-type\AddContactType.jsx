import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddContactType = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [contactTypes, setContactTypes] = useState([]);  // Initialize as an empty array
    const [contactTypeName, setContactTypeName] = useState('');  // Contact type name input
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch existing contact types
    useEffect(() => {
        const fetchContactTypes = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/contact_types`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setContactTypes(data.contactTypes || []);  // Ensure it's an array
            } catch (error) {
                setError(error.message);
            }
        };

        fetchContactTypes();
    }, []);

    // Handle the form submission
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedContactTypeName = contactTypeName.trim();

        
        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }

        // Ensure contactTypes is an array before calling .some
        if (!Array.isArray(contactTypes)) {
            setError('Contact types data is not available.');
            return;
        }

        // Check if the contact type already exists
        const contactTypeExists = contactTypes.some((type) => {
            const typeNameLower = type.name.toLowerCase().trim();
            return typeNameLower === trimmedContactTypeName.toLowerCase();
        });

        if (contactTypeExists) {
            setError('Contact type already exists. Please add a different contact type.');
            setTimeout(() => setError(''), 3000);
            return;  // Exit if contact type already exists
        }

        setError('');  // Clear any previous error

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Send the new contact type data along with created_by and updated_by as full name
            const response = await fetch(`${API_URL}/contact_types`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedContactTypeName,
                    created_by: createdBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to save contact type: ' + response.statusText);
            }

            const result = await response.json();
            //setSuccessMessage(`Contact type "${result.name || trimmedContactTypeName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Contact type added successfully.',
            });

            setContactTypeName('');  // Clear the contact type input field

            // Refetch the contact types list after adding the new contact type
            const newContactTypesResponse = await fetch(`${API_URL}/contact_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!newContactTypesResponse.ok) {
                throw new Error('Failed to fetch contact types: ' + newContactTypesResponse.statusText);
            }

            const newContactTypesData = await newContactTypesResponse.json();
            setContactTypes(newContactTypesData.contactTypes || []);  // Update the contact types list

        } catch (error) {
            alertMessage('error');
        }
    };

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Contact Type</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="contactTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                Contact Type Name
                            </label>
                            <input
                                type="text"
                                id="contactTypeName"
                                value={contactTypeName}
                                onChange={(e) => setContactTypeName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Contact Type
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddContactType;
