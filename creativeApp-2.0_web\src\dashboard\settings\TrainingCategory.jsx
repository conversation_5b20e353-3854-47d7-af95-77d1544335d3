import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import TrainingCategoryList from '../../pages/training/training-category/TrainingCategoryList';

const TrainingCategory = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-training-category" buttonName="Add Training Category" />
        <TrainingCategoryList />
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default TrainingCategory;
