import React from 'react';

const DateTimeFormat = ({ data, showTime = true }) => {
    const isoString = data.isoString; // Ensure this is a string
    const strIso = typeof isoString === "string" ? isoString : isoString.toString();
    // Replace microseconds (six digits) with milliseconds (three digits)
    const fixedIsoString = strIso.replace(/(\.\d{3})\d*Z$/, '$1Z');
    const date = new Date(fixedIsoString);

    // Full date and time format (with hour, minute, second)
    const fullDateOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hour12: true,
    };

    // Date format without time (no hour, minute, or second)
    const dateOnlyOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
    };

    // Determine which format to use based on the 'showTime' prop
    const formattedDate = showTime
        ? date.toLocaleString("en-US", fullDateOptions)
        : date.toLocaleString("en-US", dateOnlyOptions);

    return (
        <>
            {formattedDate}
        </>
    );
};

export default DateTimeFormat;
