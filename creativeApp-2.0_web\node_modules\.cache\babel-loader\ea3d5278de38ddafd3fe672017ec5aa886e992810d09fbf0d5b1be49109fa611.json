{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\components\\\\password-manager\\\\AddPasswordCard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport PasswordGenerator from './PasswordGenerator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddPasswordCard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n  const calculatePasswordStrength = password => {\n    if (!password) return 'Weak Password';\n    let score = 0;\n\n    // Length check\n    if (password.length >= 12) score += 2;else if (password.length >= 8) score += 1;\n\n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n\n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      // In a real app, you would send this to your API\n      console.log('Password Card Data:', formData);\n\n      // For now, just log and navigate back\n      alert('Password card saved successfully!');\n      navigate('/welcome');\n    }\n  };\n  const handlePasswordGenerated = (generatedPassword, strength) => {\n    setFormData(prev => ({\n      ...prev,\n      password: generatedPassword,\n      strength: strength\n    }));\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 p-6 rounded-xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n        children: \"Add New Password Card\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/welcome'),\n        className: \"flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-rounded mr-2 text-sm\",\n          children: \"arrow_back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), \"Back\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"title\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Title *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"title\",\n              name: \"title\",\n              value: formData.title,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n              placeholder: \"Enter platform or service name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), errors.title && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"User Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"username\",\n              name: \"username\",\n              value: formData.username,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n              placeholder: \"Enter username or email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? \"text\" : \"password\",\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                className: `w-full px-3 py-2 pr-10 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n                placeholder: \"Enter password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: togglePasswordVisibility,\n                className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-rounded text-sm\",\n                  children: showPassword ? 'visibility_off' : 'visibility'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.password\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 35\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-block px-2 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`,\n                children: formData.strength\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"team\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Team *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"team\",\n              name: \"team\",\n              value: formData.team,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.team ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n              placeholder: \"Enter team name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), errors.team && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.team\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"department\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Department *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"department\",\n              name: \"department\",\n              value: formData.department,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`,\n              placeholder: \"Enter department name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), errors.department && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.department\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"strength\",\n              className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n              children: \"Strength Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-full px-3 py-2 border rounded-md ${getStrengthColor(formData.strength)} cursor-not-allowed`,\n              children: formData.strength\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n              children: \"Strength is automatically calculated based on your password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n              children: \"Save Password Card\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => navigate('/welcome'),\n              className: \"flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(PasswordGenerator, {\n          onPasswordGenerated: handlePasswordGenerated\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(AddPasswordCard, \"6FwayGqtW3GQZWHM7QiXWEkXI1A=\", false, function () {\n  return [useNavigate];\n});\n_c = AddPasswordCard;\nexport default AddPasswordCard;\nvar _c;\n$RefreshReg$(_c, \"AddPasswordCard\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "PasswordGenerator", "jsxDEV", "_jsxDEV", "AddPasswordCard", "_s", "navigate", "formData", "setFormData", "title", "username", "password", "team", "department", "strength", "showPassword", "setShowPassword", "errors", "setErrors", "handleInputChange", "e", "name", "value", "target", "prev", "calculatePasswordStrength", "score", "length", "test", "getStrengthColor", "validateForm", "newErrors", "trim", "Object", "keys", "handleSubmit", "preventDefault", "console", "log", "alert", "handlePasswordGenerated", "generatedPassword", "togglePasswordVisibility", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "type", "id", "onChange", "placeholder", "onPasswordGenerated", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/components/password-manager/AddPasswordCard.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport PasswordGenerator from './PasswordGenerator';\n\nconst AddPasswordCard = () => {\n  const navigate = useNavigate();\n  \n  const [formData, setFormData] = useState({\n    title: '',\n    username: '',\n    password: '',\n    team: '',\n    department: '',\n    strength: 'Weak Password'\n  });\n\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Auto-calculate password strength when password changes\n    if (name === 'password') {\n      const strength = calculatePasswordStrength(value);\n      setFormData(prev => ({\n        ...prev,\n        strength: strength\n      }));\n    }\n  };\n\n  const calculatePasswordStrength = (password) => {\n    if (!password) return 'Weak Password';\n    \n    let score = 0;\n    \n    // Length check\n    if (password.length >= 12) score += 2;\n    else if (password.length >= 8) score += 1;\n    \n    // Character variety checks\n    if (/[a-z]/.test(password)) score += 1;\n    if (/[A-Z]/.test(password)) score += 1;\n    if (/[0-9]/.test(password)) score += 1;\n    if (/[^A-Za-z0-9]/.test(password)) score += 1;\n    \n    // Additional complexity\n    if (password.length >= 16) score += 1;\n    \n    if (score >= 6) return 'Strong Password';\n    if (score >= 4) return 'Moderate Password';\n    return 'Weak Password';\n  };\n\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'Strong Password':\n        return 'bg-green-100 text-green-600 border-green-300';\n      case 'Moderate Password':\n        return 'bg-yellow-100 text-yellow-600 border-yellow-300';\n      case 'Weak Password':\n        return 'bg-red-100 text-red-600 border-red-300';\n      default:\n        return 'bg-gray-100 text-gray-600 border-gray-300';\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.title.trim()) {\n      newErrors.title = 'Title is required';\n    }\n    \n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    \n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    }\n    \n    if (!formData.team.trim()) {\n      newErrors.team = 'Team is required';\n    }\n    \n    if (!formData.department.trim()) {\n      newErrors.department = 'Department is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      // In a real app, you would send this to your API\n      console.log('Password Card Data:', formData);\n      \n      // For now, just log and navigate back\n      alert('Password card saved successfully!');\n      navigate('/welcome');\n    }\n  };\n\n  const handlePasswordGenerated = (generatedPassword, strength) => {\n    setFormData(prev => ({\n      ...prev,\n      password: generatedPassword,\n      strength: strength\n    }));\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-900 p-6 rounded-xl\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Add New Password Card</h2>\n        <button\n          onClick={() => navigate('/welcome')}\n          className=\"flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors\"\n        >\n          <span className=\"material-symbols-rounded mr-2 text-sm\">arrow_back</span>\n          Back\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Form Section */}\n        <div>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Title */}\n            <div>\n              <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Title *\n              </label>\n              <input\n                type=\"text\"\n                id=\"title\"\n                name=\"title\"\n                value={formData.title}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter platform or service name\"\n              />\n              {errors.title && <p className=\"mt-1 text-sm text-red-600\">{errors.title}</p>}\n            </div>\n\n            {/* Username */}\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                User Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"username\"\n                name=\"username\"\n                value={formData.username}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter username or email\"\n              />\n              {errors.username && <p className=\"mt-1 text-sm text-red-600\">{errors.username}</p>}\n            </div>\n\n            {/* Password */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Password *\n              </label>\n              <div className=\"relative\">\n                <input\n                  type={showPassword ? \"text\" : \"password\"}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 pr-10 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                    errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  placeholder=\"Enter password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={togglePasswordVisibility}\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <span className=\"material-symbols-rounded text-sm\">\n                    {showPassword ? 'visibility_off' : 'visibility'}\n                  </span>\n                </button>\n              </div>\n              {errors.password && <p className=\"mt-1 text-sm text-red-600\">{errors.password}</p>}\n              \n              {/* Password Strength Indicator */}\n              {formData.password && (\n                <div className=\"mt-2\">\n                  <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full border ${getStrengthColor(formData.strength)}`}>\n                    {formData.strength}\n                  </span>\n                </div>\n              )}\n            </div>\n\n            {/* Team */}\n            <div>\n              <label htmlFor=\"team\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Team *\n              </label>\n              <input\n                type=\"text\"\n                id=\"team\"\n                name=\"team\"\n                value={formData.team}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.team ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter team name\"\n              />\n              {errors.team && <p className=\"mt-1 text-sm text-red-600\">{errors.team}</p>}\n            </div>\n\n            {/* Department */}\n            <div>\n              <label htmlFor=\"department\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Department *\n              </label>\n              <input\n                type=\"text\"\n                id=\"department\"\n                name=\"department\"\n                value={formData.department}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter department name\"\n              />\n              {errors.department && <p className=\"mt-1 text-sm text-red-600\">{errors.department}</p>}\n            </div>\n\n            {/* Strength Level (Read-only, auto-calculated) */}\n            <div>\n              <label htmlFor=\"strength\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Strength Level\n              </label>\n              <div className={`w-full px-3 py-2 border rounded-md ${getStrengthColor(formData.strength)} cursor-not-allowed`}>\n                {formData.strength}\n              </div>\n              <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\n                Strength is automatically calculated based on your password\n              </p>\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex space-x-4\">\n              <button\n                type=\"submit\"\n                className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n              >\n                Save Password Card\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => navigate('/welcome')}\n                className=\"flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        </div>\n\n        {/* Password Generator Section */}\n        <div>\n          <PasswordGenerator onPasswordGenerated={handlePasswordGenerated} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddPasswordCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCf,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIL,MAAM,CAACI,IAAI,CAAC,EAAE;MAChBH,SAAS,CAACM,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvB,MAAMP,QAAQ,GAAGW,yBAAyB,CAACH,KAAK,CAAC;MACjDd,WAAW,CAACgB,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPV,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMW,yBAAyB,GAAId,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,eAAe;IAErC,IAAIe,KAAK,GAAG,CAAC;;IAEb;IACA,IAAIf,QAAQ,CAACgB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC,CAAC,KACjC,IAAIf,QAAQ,CAACgB,MAAM,IAAI,CAAC,EAAED,KAAK,IAAI,CAAC;;IAEzC;IACA,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,OAAO,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;IACtC,IAAI,cAAc,CAACE,IAAI,CAACjB,QAAQ,CAAC,EAAEe,KAAK,IAAI,CAAC;;IAE7C;IACA,IAAIf,QAAQ,CAACgB,MAAM,IAAI,EAAE,EAAED,KAAK,IAAI,CAAC;IAErC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,iBAAiB;IACxC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,mBAAmB;IAC1C,OAAO,eAAe;EACxB,CAAC;EAED,MAAMG,gBAAgB,GAAIf,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,iBAAiB;QACpB,OAAO,8CAA8C;MACvD,KAAK,mBAAmB;QACtB,OAAO,iDAAiD;MAC1D,KAAK,eAAe;QAClB,OAAO,wCAAwC;MACjD;QACE,OAAO,2CAA2C;IACtD;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACxB,QAAQ,CAACE,KAAK,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACtB,KAAK,GAAG,mBAAmB;IACvC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACrB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACpB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAACoB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACnB,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACL,QAAQ,CAACM,UAAU,CAACmB,IAAI,CAAC,CAAC,EAAE;MAC/BD,SAAS,CAAClB,UAAU,GAAG,wBAAwB;IACjD;IAEAK,SAAS,CAACa,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACJ,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMQ,YAAY,GAAIf,CAAC,IAAK;IAC1BA,CAAC,CAACgB,cAAc,CAAC,CAAC;IAElB,IAAIN,YAAY,CAAC,CAAC,EAAE;MAClB;MACAO,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE/B,QAAQ,CAAC;;MAE5C;MACAgC,KAAK,CAAC,mCAAmC,CAAC;MAC1CjC,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;EAED,MAAMkC,uBAAuB,GAAGA,CAACC,iBAAiB,EAAE3B,QAAQ,KAAK;IAC/DN,WAAW,CAACgB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPb,QAAQ,EAAE8B,iBAAiB;MAC3B3B,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrC1B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,oBACEZ,OAAA;IAAKwC,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAEvDzC,OAAA;MAAKwC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDzC,OAAA;QAAIwC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3F7C,OAAA;QACE8C,OAAO,EAAEA,CAAA,KAAM3C,QAAQ,CAAC,UAAU,CAAE;QACpCqC,SAAS,EAAC,qLAAqL;QAAAC,QAAA,gBAE/LzC,OAAA;UAAMwC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,QAE3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN7C,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDzC,OAAA;QAAAyC,QAAA,eACEzC,OAAA;UAAM+C,QAAQ,EAAEf,YAAa;UAACQ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAEjDzC,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEnG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,OAAO;cACVhC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEf,QAAQ,CAACE,KAAM;cACtB6C,QAAQ,EAAEnC,iBAAkB;cAC5BwB,SAAS,EAAE,gLACT1B,MAAM,CAACR,KAAK,GAAG,gBAAgB,GAAG,sCAAsC,EACvE;cACH8C,WAAW,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACD/B,MAAM,CAACR,KAAK,iBAAIN,OAAA;cAAGwC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACR;YAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAGN7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEtG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,UAAU;cACbhC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEf,QAAQ,CAACG,QAAS;cACzB4C,QAAQ,EAAEnC,iBAAkB;cAC5BwB,SAAS,EAAE,gLACT1B,MAAM,CAACP,QAAQ,GAAG,gBAAgB,GAAG,sCAAsC,EAC1E;cACH6C,WAAW,EAAC;YAAyB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,EACD/B,MAAM,CAACP,QAAQ,iBAAIP,OAAA;cAAGwC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACP;YAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAGN7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEtG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cAAKwC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzC,OAAA;gBACEiD,IAAI,EAAErC,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCsC,EAAE,EAAC,UAAU;gBACbhC,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEf,QAAQ,CAACI,QAAS;gBACzB2C,QAAQ,EAAEnC,iBAAkB;gBAC5BwB,SAAS,EAAE,sLACT1B,MAAM,CAACN,QAAQ,GAAG,gBAAgB,GAAG,sCAAsC,EAC1E;gBACH4C,WAAW,EAAC;cAAgB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACF7C,OAAA;gBACEiD,IAAI,EAAC,QAAQ;gBACbH,OAAO,EAAEP,wBAAyB;gBAClCC,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAExHzC,OAAA;kBAAMwC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAC/C7B,YAAY,GAAG,gBAAgB,GAAG;gBAAY;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL/B,MAAM,CAACN,QAAQ,iBAAIR,OAAA;cAAGwC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACN;YAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAGjFzC,QAAQ,CAACI,QAAQ,iBAChBR,OAAA;cAAKwC,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBzC,OAAA;gBAAMwC,SAAS,EAAE,kEAAkEd,gBAAgB,CAACtB,QAAQ,CAACO,QAAQ,CAAC,EAAG;gBAAA8B,QAAA,EACtHrC,QAAQ,CAACO;cAAQ;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,MAAM;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAElG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,MAAM;cACThC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEf,QAAQ,CAACK,IAAK;cACrB0C,QAAQ,EAAEnC,iBAAkB;cAC5BwB,SAAS,EAAE,gLACT1B,MAAM,CAACL,IAAI,GAAG,gBAAgB,GAAG,sCAAsC,EACtE;cACH2C,WAAW,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACD/B,MAAM,CAACL,IAAI,iBAAIT,OAAA;cAAGwC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACL;YAAI;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAGN7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,YAAY;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAExG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXC,EAAE,EAAC,YAAY;cACfhC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAEf,QAAQ,CAACM,UAAW;cAC3ByC,QAAQ,EAAEnC,iBAAkB;cAC5BwB,SAAS,EAAE,gLACT1B,MAAM,CAACJ,UAAU,GAAG,gBAAgB,GAAG,sCAAsC,EAC5E;cACH0C,WAAW,EAAC;YAAuB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACD/B,MAAM,CAACJ,UAAU,iBAAIV,OAAA;cAAGwC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE3B,MAAM,CAACJ;YAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eAGN7C,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAOgD,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAEtG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7C,OAAA;cAAKwC,SAAS,EAAE,sCAAsCd,gBAAgB,CAACtB,QAAQ,CAACO,QAAQ,CAAC,qBAAsB;cAAA8B,QAAA,EAC5GrC,QAAQ,CAACO;YAAQ;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACN7C,OAAA;cAAGwC,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN7C,OAAA;YAAKwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzC,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbT,SAAS,EAAC,4KAA4K;cAAAC,QAAA,EACvL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbH,OAAO,EAAEA,CAAA,KAAM3C,QAAQ,CAAC,UAAU,CAAE;cACpCqC,SAAS,EAAC,+KAA+K;cAAAC,QAAA,EAC1L;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7C,OAAA;QAAAyC,QAAA,eACEzC,OAAA,CAACF,iBAAiB;UAACuD,mBAAmB,EAAEhB;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA5SID,eAAe;EAAA,QACFJ,WAAW;AAAA;AAAAyD,EAAA,GADxBrD,eAAe;AA8SrB,eAAeA,eAAe;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}