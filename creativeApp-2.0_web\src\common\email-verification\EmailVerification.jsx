import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

const EmailVerification = () => {
  const { id, hash } = useParams();
  const navigate = useNavigate();
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Send a GET request to your backend API with the user id and hash
        const response = await axios.get(`${process.env.REACT_APP_BASE_API_URL}/email/verify/${id}/${hash}`);

        // If successful, handle the response
        setMessage(response.data.message);

        // After 2 seconds, navigate to the login page
        setTimeout(() => {
          navigate('/login');
        }, 5000);
      } catch (error) {
        // Handle errors (invalid URL, already verified, etc.)
        if (error.response && error.response.data) {
          setMessage(error.response.data.message || 'There was an error verifying your email.');
        } else {
          setMessage('An unexpected error occurred.');
        }
      } finally {
        setLoading(false);  // Hide the loading message
      }
    };

    verifyEmail();  // Call the email verification function
  }, [id, hash, navigate]);

  return (
    <div className='bg-gray-100 flex items-center justify-center p-8'>
      {loading ? (
        <p>Verifying your email...</p>  // Show loading text while the verification request is in progress
      ) : (
        <div>
          <h2>{message}</h2>  // Show the response message
        </div>
      )}
    </div>
  );
};

export default EmailVerification;
