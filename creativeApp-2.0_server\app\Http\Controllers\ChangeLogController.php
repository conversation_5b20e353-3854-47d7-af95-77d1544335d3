<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Changelog;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class ChangeLogController extends Controller
{
    public function index()
    {
        $changeLogEntries = Changelog::all();
        Log::info('All Change Log entries retrieved:', ['entries_count' => $changeLogEntries->count()]);
        return response()->json(['changeLogs' => $changeLogEntries], 200);
    }

    public function show($id)
    {
        $changeLogEntry = Changelog::find($id);

        if (!$changeLogEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        Log::info('Change Log entry retrieved:', ['entry' => $changeLogEntry]);
        return response()->json(['changeLog' => $changeLogEntry], 200);
    }

    public function store(Request $request)
    {
        $authUser = $request->user();

        Log::info('Create Change Log entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        $request->validate([
            'version' => 'required|string',
            'date' => 'required|date',
            'area' => 'required|string',
            'type' => 'required|string',
            'description' => 'required|string',
            'author' => 'nullable|string',
        ]);

        $changeLogEntry = Changelog::create([
            'version' => $request->version,
            'date' => $request->date,
            'area' => $request->area,
            'type' => $request->type,
            'description' => $request->description,
            'author' => $request->author ?? $authUser->name,
        ]);

        Log::info('Change Log entry created:', ['entry' => $changeLogEntry]);
        return response()->json(['message' => 'Entry created successfully.', 'changeLog' => $changeLogEntry], 201);
    }

    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        Log::info('Update Change Log entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        $request->validate([
            'version' => 'required|string',
            'date' => 'required|date',
            'area' => 'required|string',
            'type' => 'required|string',
            'description' => 'required|string',
            'author' => 'nullable|string',
        ]);

        $changeLogEntry = Changelog::find($id);

        if (!$changeLogEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        $changeLogEntry->update([
            'version' => $request->version,
            'date' => $request->date,
            'area' => $request->area,
            'type' => $request->type,
            'description' => $request->description,
            'author' => $request->author ?? $authUser->name,
        ]);

        Log::info('Change Log entry updated:', ['entry' => $changeLogEntry]);
        return response()->json(['message' => 'Entry updated successfully.', 'changeLog' => $changeLogEntry], 200);
    }

    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $changeLogEntry = Changelog::findOrFail($id);
            $changeLogEntry->delete();

            Log::info('Change Log entry deleted:', ['entry_id' => $id]);
            return response()->json(['message' => 'Entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this entry.'], 403);
    }
}
