import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const holidayCalenderApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getHolidayCalenderData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `holiday-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['HolidayCalenderData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForHolidayCalender: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `holiday-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['HolidayCalenderData'],
    }),

    getHolidayCalenderById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `holidaycalenders/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'HolidayCalenderData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createHolidayCalender: builder.mutation({
      query: (newFormationType) => ({
        url: 'holidaycalenders',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['HolidayCalenderData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateHolidayCalender: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `holidaycalenders/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'HolidayCalenderData', id }, 'HolidayCalenderData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteHolidayCalender: builder.mutation({
      query: (id) => ({
        url: `holidaycalenders/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['HolidayCalenderData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetHolidayCalenderDataQuery,
  useLazyFetchDataOptionsForHolidayCalenderQuery,
  useGetHolidayCalenderByIdQuery,
  useLazyGetHolidayCalenderByIdQuery,
  useCreateHolidayCalenderMutation,
  useUpdateHolidayCalenderMutation,
  useDeleteHolidayCalenderMutation,
} = holidayCalenderApi;
