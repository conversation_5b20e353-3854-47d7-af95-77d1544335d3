<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Training extends Model
{
    use HasFactory;
    protected $fillable = [
        'department',
        'team',
        'title',
        'trainer',
        'arrange_by',
        'category_id',
        'topic_id',
        'date',
        'time',
        'duration',
        'presentation_url',
        'record_url',
        'access_passcode',
        'location',
        'tags',
        'evaluation_form',
        'response',
        'created_by',
        'updated_by'
    ];

    public function training_categories()
    {
        return $this->belongsTo(TrainingCategory::class, 'training_category_id');
    }
}