<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reporter extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'department',
        'team',
        'name',
        'location',
        'timezone',
        'email',
        'start_time',
        'end_time',
        'created_by',
        'updated_by'
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function task_details()
    {
        return $this->hasMany(TaskDetails::class, 'reporter_id');
    }

        /**
     * Relationship to get the team associated with the time card.
     */
    public function teams()
    {
        return $this->belongsTo(Team::class, 'team');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class, 'department');
    }


}
