{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\dashboard\\\\Welcome.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Welcome = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-2xl text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-6xl mb-4\",\n        children: \"\\uD83D\\uDEA7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-2 text-gray-800\",\n        children: \"Page Under Construction\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: \"We're working hard to bring you a better experience. Please check back soon!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => window.history.back(),\n        className: \"px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition\",\n        children: \"Go Backadasd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Welcome;\nexport default Welcome;\nvar _c;\n$RefreshReg$(_c, \"Welcome\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Welcome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/dashboard/Welcome.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Welcome = () => {\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"bg-white p-8 rounded-2xl text-center\">\r\n        <div className=\"text-6xl mb-4\">🚧</div>\r\n        <h1 className=\"text-3xl font-bold mb-2 text-gray-800\">Page Under Construction</h1>\r\n        <p className=\"text-gray-600 mb-6\">\r\n          We're working hard to bring you a better experience. Please check back soon!\r\n        </p>\r\n        <button\r\n          onClick={() => window.history.back()}\r\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition\"\r\n        >\r\n          Go Backadasd\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Welcome;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAC5EH,OAAA;MAAKE,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvCP,OAAA;QAAIE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClFP,OAAA;QAAGE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QACEQ,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;QACrCT,SAAS,EAAC,4EAA4E;QAAAC,QAAA,EACvF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GAlBIX,OAAO;AAoBb,eAAeA,OAAO;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}