import React, { createContext, useState, useEffect, useContext } from 'react';

// Create a context
export const DataContext = createContext();

// Custom hook to use the context
export const useDataContext = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error("useDataContext must be used within a DataProvider");
  }
  return context;
};
const API_URL = process.env.REACT_APP_BASE_API_URL;
export const DataProvider = ({ children }) => {
  const [billingStatuses, setBillingStatuses] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch data once when the provider mounts
  useEffect(() => {
    const fetchBillingStatuses = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No authentication token found.');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`${API_URL}/billing_statuses`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Network response was not ok');
        }

        const data = await response.json();
        setBillingStatuses(data['billing statuses'] || []);
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if there is no data already loaded
    if (billingStatuses.length === 0) {
      fetchBillingStatuses();
    }
  }, [billingStatuses.length]); // Only run once when the component mounts

  return (
    <DataContext.Provider value={{ billingStatuses, error, loading }}>
      {loading ? <div>Loading...</div> : children}
    </DataContext.Provider>
  );
};
