<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RecordType;
use Illuminate\Support\Facades\Log;

class RecordTypeController extends Controller
{
    public function index()
    {
        $recordTypes = RecordType::all();

        // Log the record types retrieved
        Log::info('All record types Retrieved:', ['record_count' => $recordTypes->count()]);

        return response()->json(['recordTypes' => $recordTypes], 200);
    }

    /**
     * Display the specified record  type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the record  type by ID
        $recordType = RecordType::find($id);

        if (!$recordType) {
            return response()->json(['error' => 'record  type not found.'], 404);
        }

        // Log the record  type retrieved
        Log::info('record  Type Retrieved:', ['recordType' => $recordType]);

        return response()->json(['recordType' => $recordType], 200);
    }

    // Filter logic for data table
    public function recordTypesData(Request $request)
    {
        $query = RecordType::with(['creator', 'updater', 'team', 'department']);

        // Decode all input parameters to handle URL-encoded values
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedName = $request->filled('name') ? urldecode($request->input('name')) : null;

        // Filtering by department_id
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }

        // Filtering by team_id
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }

        // Filtering by name
        if ($decodedName) {
            $names = explode(',', $decodedName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }

        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);

        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $recordTypes = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($recordTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = RecordType::with(['creator', 'updater', 'team', 'department']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }


    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = RecordType::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }


    /**
     * Create a new record type.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);
        
        // Log the request data
        Log::info('Create record type Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Create a new record type
        try {
            $recordType = RecordType::create([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'created_by' => $authUser->id,
            ]);
            
            // Log record type creation
            Log::info('record type Created:', ['record_type' => $recordType]);

            return response()->json([
                'message' => 'record type created successfully.',
                'record_type' => $recordType
            ], 201);
        } catch (\Exception $e) {
            // Log error if record type creation fails
            Log::error('record type Creation Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to create record type.'], 500);
        }
    }


    /**
     * Update an existing record type.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
        
        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);

        // Log the request data
        Log::info('Update record type Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Find the existing record type by ID
        $recordType = RecordType::find($id);

        // Check if the record type exists
        if (!$recordType) {
            return response()->json(['error' => 'record type not found.'], 404);
        }

        // Update the record type with the new data
        try {
            $recordType->update([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'updated_by' => $authUser->id, // Track who updated the record type
            ]);

            // Log record type update
            Log::info('record type Updated:', ['record_type' => $recordType]);

            return response()->json([
                'message' => 'record type updated successfully.',
                'record_type' => $recordType
            ], 200);
        } catch (\Exception $e) {
            // Log error if record type update fails
            Log::error('record type Update Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to update record type.'], 500);
        }
    }

    /**
     * Delete a record type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the record type
            $recordType = RecordType::findOrFail($id);

            // Delete the record type
            $recordType->delete();

            return response()->json(['message' => 'record type deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this record type.'], 403);
    }
}
