<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\Quick_access_hub;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class QuickAccessHubController extends Controller
{
    /**
     * Show all hubs.
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function index()
    {
        $qa_hubs = Quick_access_hub::all();
    
        // Log the quick access hubs retrieved
        Log::info('All quick access hubs data Retrieved:', ['qa_hub_count' => $qa_hubs->count()]);
    
        return response()->json(['hubs' => $qa_hubs], 200);
    }

    /**
     * Display the specified hub.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $hub = Quick_access_hub::find($id);

        if (!$hub) {
            return response()->json(['error' => 'Hub not found.'], 404);
        }

        Log::info('Hub Retrieved', ['hub_id' => $id]);

        return response()->json(['hub' => $hub], 200);
    }

    /**
     * Create a new hub.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createHub(Request $request)
    {
        $authUser = $request->user();
    
        // Log the authenticated user performing the hub creation
        Log::info('Authenticated User for Hub Creation', ['user_id' => $authUser->id]);
    
        // Validate the incoming request
        $request->validate([
            'hubs_image' => 'nullable|sometimes|image|mimes:jpeg,png,jpg,gif,svg,webp,avif|max:2000',
            'hubs_icon' => 'nullable|string|max:255',
            'hubs_title' => 'required|string|max:255',
            'hubs_details' => 'required|string|max:255',
            'hubs_url' => 'required|string|max:255|url',
            'hubs_cta' => 'required|string|max:255',
        ]);
    
        // Check if a hub with the same title already exists
        if (Quick_access_hub::where('hubs_title', $request->hubs_title)->exists()) {
            return response()->json(['error' => 'Hub title already exists.'], 409);
        }
    
        // Ensure the authenticated user has the correct role
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            return response()->json(['error' => 'Unauthorized.'], 403);
        }
    
        // Initialize the image path as null
        $imagePath = null;
    
        // Check if the hubs_image is present in the request and attempt to store it
        if ($request->hasFile('hubs_image')) {
            try {
                $imagePath = $request->file('hubs_image')->store('images', 'public');
            } catch (\Exception $e) {
                Log::error('File upload failed', ['error' => $e->getMessage()]);
                return response()->json(['error' => 'Failed to upload files.'], 500);
            }
        }
    
        // Create the new hub
        $hub = Quick_access_hub::create([
            'hubs_image' => $imagePath,
            'hubs_icon' => $request->hubs_icon,
            'hubs_title' => $request->hubs_title,
            'hubs_details' => $request->hubs_details,
            'hubs_url' => $request->hubs_url,
            'hubs_cta' => $request->hubs_cta,
            'created_by' => $authUser->id,
        ]);
    
        // Log hub creation
        Log::info('Hub Created', ['hub_id' => $hub->id, 'hub_title' => $hub->hubs_title]);
    
        // Return a success response
        return response()->json([
            'message' => 'Hub created successfully.',
            'hub' => $hub
        ], 201);
    }
    

    /**
     * Update an existing hub.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateHub(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
        
        // Log request files and input fields
        Log::info('Request Files:', ['files' => $request->files->all()]);
        Log::info('Hubs Title from input:', ['hubs_title' => $request->input('hubs_title')]);
        Log::info('Hubs Details from input:', ['hubs_details' => $request->input('hubs_details')]);
        Log::info('Hubs URL from input:', ['hubs_url' => $request->input('hubs_url')]);
        Log::info('Hubs CTA from input:', ['hubs_cta' => $request->input('hubs_cta')]);
        Log::info('Hubs Icon from input:', ['hubs_icon' => $request->input('hubs_icon')]);
        Log::info('Hubs Image from input:', ['hubs_image' => $request->input('hubs_image')]);  // Base64 encoded image
        
        // Decode the base64 files (if provided)
        $imagePath = $this->decodeBase64Image($request->input('hubs_image'), 'hubs_image');
        
        // Log the decoded file paths
        Log::info('Decoded Hubs Image Path:', ['hubs_image_path' => $imagePath]);
        
        // Validate incoming request data
        $validatedData = $request->validate([
            'hubs_title' => 'nullable|string|max:255',
            'hubs_details' => 'nullable|string',
            'hubs_url' => 'nullable|url',
            'hubs_cta' => 'nullable|string',
            'hubs_icon' => 'nullable|string',
            'hubs_image' => 'nullable|string',  // Base64 encoded image
        ]);
        
        // Find the hub
        $hub = Quick_access_hub::findOrFail($id);
        
        // Log existing hub data before update
        Log::info('Existing Hub Data:', ['hub' => $hub->toArray()]);
        
        // Update fields only if provided
        if ($request->has('hubs_title')) {
            $hub->hubs_title = $validatedData['hubs_title'];
        }
        
        if ($request->has('hubs_details')) {
            $hub->hubs_details = $validatedData['hubs_details'];
        }
        
        if ($request->has('hubs_url')) {
            $hub->hubs_url = $validatedData['hubs_url'];
        }
        
        if ($request->has('hubs_cta')) {
            $hub->hubs_cta = $validatedData['hubs_cta'];
        }
        
        if ($request->has('hubs_icon')) {
            $hub->hubs_icon = $validatedData['hubs_icon'];
        }
        
        // Handle image (base64 or file)
        if ($imagePath) {
            // If image is provided, use base64
            $hub->hubs_image = $imagePath;
        } elseif ($request->hasFile('hubs_image')) {
            // If file is uploaded, store it
            $hub->hubs_image = $request->file('hubs_image')->store('public/images');
        } elseif ($request->input('hubs_image') === null) {
            // If hubs_image is null, delete the existing image
            // Delete the image from storage if it exists
            if ($hub->hubs_image) {
                $existingImagePath = storage_path('app/' . $hub->hubs_image);
                if (file_exists($existingImagePath)) {
                    unlink($existingImagePath);  // Delete the file
                }
            }
            // Set the image field to null
            $hub->hubs_image = null;
        }
        
        // Set updated_by as the authenticated user's ID
        $hub->updated_by = $authUser->id;
        
        // Save the updated hub
        $hub->save();
        
        // Log the updated hub data
        Log::info('Updated Hub:', ['hub' => $hub->toArray()]);
        
        // Return the updated hub as a response
        return response()->json($hub);
    }
    
    // Helper function to decode base64 image data and store it
    protected function decodeBase64Image($base64String, $type)
    {
        if (!$base64String) {
            return null; // No file to handle
        }
    
        // Match and extract the image data and type
        if (preg_match('#^data:image/(\w+);base64,#i', $base64String, $matches)) {
            $imageType = $matches[1]; // Extracted image type (e.g., 'jpeg', 'png')
    
            // Remove the base64 header to get raw image data
            $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64String));
    
            // Check if decoding was successful
            if ($imageData === false) {
                return null; // If base64 decoding fails, return null
            }
    
            // Generate a unique file name with the appropriate extension
            $fileName = $type . '-' . uniqid() . '.' . $imageType; // Use dynamic extension based on image type
            $filePath = storage_path('app/public/images/' . $fileName);
    
            // Save the file to storage
            file_put_contents($filePath, $imageData);
    
            return 'images/' . $fileName;
        }
    
        return null; // If the base64 string does not match the expected format
    }
    

    /**
     * Delete a hub.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteHub(Request $request, $id)
    {
        $authUser = $request->user();
    
        // Log the authenticated user performing the hub deletion
        Log::info('Authenticated User for Hub Deletion', ['user_id' => $authUser->id, 'hub_id' => $id]);
    
        // Ensure the authenticated user has the correct role
        if (!$authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead'])->exists()) {
            return response()->json(['error' => 'Unauthorized.'], 403);
        }
    
        // Find the hub by ID, return 404 if not found
        $hub = Quick_access_hub::find($id);
    
        if (!$hub) {
            return response()->json(['error' => 'Hub not found.'], 404);
        }
    
        // Log hub deletion
        Log::info('Hub Deletion', ['hub_id' => $hub->id, 'hub_title' => $hub->hubs_title]);
    
        // Delete the associated image from storage if it exists
        if ($hub->hubs_image && \Storage::disk('public')->exists($hub->hubs_image)) {
            try {
                \Storage::disk('public')->delete($hub->hubs_image);
                Log::info('Hub Image Deleted', ['image_path' => $hub->hubs_image]);
            } catch (\Exception $e) {
                Log::error('Failed to delete hub image', ['error' => $e->getMessage()]);
            }
        }
    
        // Delete the hub from the database
        $hub->delete();
    
        // Log the deletion
        Log::info('Hub Deleted', ['hub_id' => $hub->id, 'hub_title' => $hub->hubs_title]);
    
        // Return a success response
        return response()->json([
            'message' => 'Hub deleted successfully.'
        ], 200);
    }
    
}
