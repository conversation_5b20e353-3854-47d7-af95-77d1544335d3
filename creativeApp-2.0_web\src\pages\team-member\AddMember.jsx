import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddMemberTest = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [teams, setTeams] = useState([]);
    const [departments, setDepartments] = useState([]);
    const [designations, setDesignations] = useState([]);
    const [resourceTypes, setResourceTypes] = useState([]);
    const [resourceStatuses, setResourceStatuses] = useState([]);
    const [billingStatuses, setBillingStatuses] = useState([]);
    const [contactTypes, setContactTypes] = useState([]);
    const [availableStatuses, setAvailableStatuses] = useState([]);
    const [memberStatuses, setMemberStatuses] = useState([]);
    const [branches, setBranches] = useState([]);
    const [onsiteStatuses, setOnsiteStatuses] = useState([]);
    const [eid, setEid] = useState('');
    const [email, setEmail] = useState('');
    const [selectedRoles, setSelectedRoles] = useState({});
    const [selectedTeams, setSelectedTeams] = useState({});
    const [defaultTeamId, setDefaultTeamId] = useState(null);
    const [hoveredTeamId, setHoveredTeamId] = useState(null);
    const [selectedDepartments, setSelectedDepartments] = useState([]);
    const [selectedDesignations, setSelectedDesignations] = useState('');
    const [selectedResourceTypes, setSelectedResourceTypes] = useState('');
    const [selectedResourceStatuses, setSelectedResourceStatuses] = useState([]);
    const [selectedBillingStatuses, setSelectedBillingStatuses] = useState([]);
    const [selectedContactTypes, setSelectedContactTypes] = useState([]);
    const [selectedAvailableStatuses, setSelectedAvailableStatuses] = useState([]);
    const [selectedMemberStatuses, setSelectedMemberStatuses] = useState([]);
    const [selectedBranches, setSelectedBranches] = useState([]);
    const [selectedOnsiteStatuses, setSelectedOnsiteStatuses] = useState([]);
    const [loggedInUser, setLoggedInUser] = useState(null);

    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');

    const fetchUsers = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        const token = localStorage.getItem('token');

        try {
            const response = await fetch(`${API_URL}/users`, {
                method: 'GET',

                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }

            const data = await response.json();

            setUsers(data);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the roles to select for users
    const fetchRoles = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/roles`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the roles array from the data object
            const rolesData = data.roles; // Get the roles array
    
            const rolesMap = rolesData.reduce((acc, role) => {
                acc[role.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setRoles(rolesData);
            setSelectedRoles(rolesMap);
        } catch (error) {
            setError(error.message);
        }
    };   

    // Fetching all the teams to select for users
    const fetchTeams = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/teams`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const teamsData = data.teams; // Get the teams array
    
            const teamsMap = teamsData.reduce((acc, team) => {
                acc[team.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setTeams(teamsData);
            setSelectedTeams(teamsMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the departments to select for users
    const fetchDepartments = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/departments`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
            const departmentsData = data.departments;
    
            // Ensure `selectedDepartments` is an array, initialized to empty
            const initialSelectedDepartments = [];
            
            // Set both departments and an empty selectedDepartments array
            setDepartments(departmentsData);
            setSelectedDepartments(initialSelectedDepartments); // Initialize as an empty array
    
        } catch (error) {
            setError(error.message);
        }
    };
    

    // Fetching all the designations to select for users
    const fetchDesignations = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/designations`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            // Access the teams array from the data object
            const designationsData = data.designations; // Get the teams array
    
            const designationsMap = designationsData.reduce((acc, designation) => {
                acc[designation.id] = false;
                return acc;
            }, {});
    
            setDesignations(designationsData);
            setSelectedDesignations('');
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the Resource Type (Responsibility Level) to select for users
    const fetchResourceTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/resource_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const resourceTypesData = data['Resource Types']; 
    
            const resourceTypesMap = resourceTypesData.reduce((acc, resourceType) => {
                acc[resourceType.id] = false;
                return acc;
            }, {});
    
            setResourceTypes(resourceTypesData);
            setSelectedResourceTypes('');
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the resourceStatuses to select for users
    const fetchResourceStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/resource_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();

    
            // Access the resourceStatuses array from the data object
            const resourceStatusesData = data.resource_status; // Get the resourceStatuses array
    
            const resourceStatusesMap = resourceStatusesData.reduce((acc, resourceStatus) => {
                acc[resourceStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setResourceStatuses(resourceStatusesData);
            setSelectedResourceStatuses(resourceStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the billing statuses to select for users
    const fetchBillingStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/billing_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const billingStatusesData = data['billing statuses']; 
    
            const billingStatusesMap = billingStatusesData.reduce((acc, billingStatus) => {
                acc[billingStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setBillingStatuses(billingStatusesData);
            setSelectedBillingStatuses(billingStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the contact types to select for users
    const fetchContactTypes = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/contact_types`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const contactTypesData = data.contact_types;
    
            const contactTypesMap = contactTypesData.reduce((acc, contactType) => {
                acc[contactType.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setContactTypes(contactTypesData);
            setSelectedContactTypes(contactTypesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the available status to select for users
    const fetchAvailableStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/available_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const availableStatusesData = data.available_statuses;
    
            const availableStatusesMap = availableStatusesData.reduce((acc, availableStatus) => {
                acc[availableStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setAvailableStatuses(availableStatusesData);
            setSelectedAvailableStatuses(availableStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the team member status to select for users
    const fetchMemberStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/member_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const memberStatusesData = data.member_statuses; 
    
            const memberStatusesMap = memberStatusesData.reduce((acc, memberStatus) => {
                acc[memberStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setMemberStatuses(memberStatusesData);
            setSelectedMemberStatuses(memberStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };
    
    // Fetching all the branch to select for users
    const fetchBranches = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/branches`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const branchesData = data.branches; 
    
            const branchesMap = branchesData.reduce((acc, branch) => {
                acc[branch.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setBranches(branchesData);
            setSelectedBranches(branchesMap);
        } catch (error) {
            setError(error.message);
        }
    };

    // Fetching all the branch to select for users
    const fetchOnsiteStatuses = async () => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        const token = localStorage.getItem('token');
    
        try {
            const response = await fetch(`${API_URL}/onsite_statuses`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Only Super Admin and Admin are permitted to create new user! ' + response.statusText);
            }
    
            const data = await response.json();
    
            const onsiteStatusesData = data.onsite_statuses;
    
            const onsiteStatusesMap = onsiteStatusesData.reduce((acc, onsiteStatus) => {
                acc[onsiteStatus.id] = false; // Initialize all roles as unchecked
                return acc;
            }, {});
    
            setOnsiteStatuses(onsiteStatusesData);
            setSelectedOnsiteStatuses(onsiteStatusesMap);
        } catch (error) {
            setError(error.message);
        }
    };
    
    
    

    useEffect(() => {
        fetchUsers();
        fetchRoles();
        fetchTeams();
        fetchDepartments();
        fetchDesignations();
        fetchResourceTypes();
        fetchResourceStatuses();
        fetchBillingStatuses();
        fetchContactTypes();
        fetchAvailableStatuses();
        fetchMemberStatuses();
        fetchBranches();
        fetchOnsiteStatuses();
    }, []);

    // Retrieving Logged In Users id
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    
    // Filter teams based on selected departments
    const filterTeamsByDepartments = () => {
        if (!selectedDepartments) return teams;
    
        return teams.filter(team =>
        team.departments?.some(department => department.id === selectedDepartments)
        );
    };
    
    // Handle team selection change
    const handleTeamChange = (teamId) => {
        setSelectedTeams((prevSelectedTeams) => {
            const updatedTeams = { ...prevSelectedTeams, [teamId]: !prevSelectedTeams[teamId] };
            return updatedTeams;
        });
    };
    
    const handleDefaultTeamChange = (teamId) => {
        setDefaultTeamId(teamId); // Set selected team as the default
    };

    const handleMouseEnter = (teamId) => {
        setHoveredTeamId(teamId);  // Track hovered team
    };
    
    const handleMouseLeave = () => {
        setHoveredTeamId(null);  // Reset hover state
    };
    

    const filteredTeams = filterTeamsByDepartments();
    
  
    

    const handleSubmit = async (event) => {
        event.preventDefault();

        setLoading(true);

        const trimmedEid = eid.trim();
        const trimmedEmail = email.trim();
    
        if (!trimmedEid || !trimmedEmail) {
            setError('EID and Email are required.');
            return;
        }
    
        const eidExists = users.some(user => typeof user.eid === 'string' && user.eid.toLowerCase().trim() === trimmedEid.toLowerCase());
        const emailExists = users.some(user => typeof user.email === 'string' && user.email.toLowerCase().trim() === trimmedEmail.toLowerCase());
    
        if (eidExists || emailExists) {
            let message = 'The ';
            if (eidExists) message += 'EID ';
            if (emailExists) message += (message.endsWith('The ') ? '' : 'or ') + 'Email ';
            message += 'already exists. Please add a new EID and/or Email.';
            setError(message);
            setTimeout(() => setError(''), 1000);
            return;
        }
    
        setError('');

        // Prepare roles array based on selected roles
        const selectedRoleIds = Object.keys(selectedRoles).filter(roleId => selectedRoles[roleId]);
        const selectedTeamIds = Object.keys(selectedTeams).filter(teamId => selectedTeams[teamId]);
        // Always passed as an array (even if one department is selected)
        const selectedDepartmentIds = Array.isArray(selectedDepartments) ? selectedDepartments : [selectedDepartments];
        // Single select: directly pass the selected value, not in an array
        const selectedDesignationIds = selectedDesignations ? [selectedDesignations] : [];
        const selectedResourceTypeIds = selectedResourceTypes ? [selectedResourceTypes] : [];
        const selectedResourceStatusIds = Array.isArray(selectedResourceStatuses) ? selectedResourceStatuses : [selectedResourceStatuses];
        const selectedBillingStatusIds = Array.isArray(selectedBillingStatuses) ? selectedBillingStatuses : [selectedBillingStatuses];
        const selectedContactTypeIds = Array.isArray(selectedContactTypes) ? selectedContactTypes : [selectedContactTypes];
        const selectedAvailableStatusIds = Array.isArray(selectedAvailableStatuses) ? selectedAvailableStatuses : [selectedAvailableStatuses];
        const selectedMemberStatusIds = Array.isArray(selectedMemberStatuses) ? selectedMemberStatuses : [selectedMemberStatuses];
        const selectedBranchIds = Array.isArray(selectedBranches) ? selectedBranches : [selectedBranches];
        const selectedOnsiteStatusIds = Array.isArray(selectedOnsiteStatuses) ? selectedOnsiteStatuses : [selectedOnsiteStatuses];

        try {
            const token = localStorage.getItem('token');

            // Get user_id from localStorage for 'created_by'
            const createdBy = loggedInUser;

            if (!createdBy) {
                setError('User is not logged in.');
                return;
            }
            
            const response = await fetch(`${API_URL}/users`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    eid: trimmedEid,
                    email: trimmedEmail,
                    roles: selectedRoleIds.map(Number), // Send the roles array as numbers
                    teams: selectedTeamIds.map(Number), // Send the teams array as numbers
                    default_team_id: defaultTeamId,
                    departments: selectedDepartmentIds.map(Number), // Send the departments as numbers
                    designations: selectedDesignationIds.map(Number),
                    resource_types: selectedResourceTypeIds.map(Number),
                    resource_statuses: selectedResourceStatusIds.map(Number),
                    billing_statuses: selectedBillingStatusIds.map(Number),
                    contact_types: selectedContactTypeIds.map(Number),
                    available_statuses: selectedAvailableStatusIds.map(Number),
                    member_statuses: selectedMemberStatusIds.map(Number),
                    branches: selectedBranchIds.map(Number),
                    onsite_statuses: selectedOnsiteStatusIds.map(Number),
                    created_by: createdBy,
                }),
            });
    
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                throw new Error('Failed to save user: ' + response.statusText);
            }
            
    
            const result = await response.json();
            //setSuccessMessage(`User with EID "${trimmedEid}" added successfully!`);
            alertMessage('success')
            setEid('');
            setEmail('');
            setSelectedRoles({});
            setSelectedTeams({});
            setSelectedDepartments([]);
            setSelectedDesignations('');
            setSelectedResourceTypes('');
            setSelectedResourceStatuses([]);
            setSelectedBillingStatuses([]);
            setSelectedContactTypes([]);
            setSelectedAvailableStatuses([]);
            setSelectedMemberStatuses([]);
            setSelectedBranches([]);
            setSelectedOnsiteStatuses([]);
    
            fetchUsers();


        } catch (error) {
            //setError(error.message);
            alertMessage('error');
            setLoading(false);
        }
    };

    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white rounded-lg shadow-md w-full max-w-6xl relative bg-neutral-50">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                        <h4 className="text-base font-medium text-left text-gray-800">Add New Team Member</h4>
                        <button
                            className="text-3xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>

                    <form onSubmit={handleSubmit}>
                        <div className='flex flex-wrap gap-4 p-6 overflow-y-auto max-h-[80vh] scrollbar-vertical'>
                            <div className="mb-4 w-full md:max-w-[23%] text-left">
                                <label htmlFor="eid" className="block pb-2 text-base text-gray-600">
                                    Employee ID <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    type="number"
                                    id="eid"
                                    value={eid}
                                    onChange={(e) => setEid(e.target.value)}
                                    placeholder='Add Team Member ID'
                                    required
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            <div className="mb-4 w-full md:max-w-[23%] text-left">
                                <label htmlFor="email" className="block pb-2 text-base text-gray-600">
                                    Email <span className='text-red-600'>*</span>
                                </label>
                                <input
                                    type="email"
                                    id="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    placeholder='Add Team Member Email'
                                    required
                                    className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                />
                            </div>
                            {/* Select Designations */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left">
                                <label htmlFor="designation" className="block pb-2 text-base text-gray-600">
                                    Designation <span className='text-red-600'>*</span>
                                </label>
                                <div className="">
                                    <select
                                        value={selectedDesignations || ''} // Handle single value
                                        onChange={(e) => setSelectedDesignations(e.target.value)} // Update with selected value
                                        required
                                        className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        <option value="" disabled>Select a designation</option>
                                        {designations.map((designation) => (
                                            <option key={designation.id} value={designation.id}>
                                                {designation.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            {/* Select Responsibility Level (Resource Types) */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left">
                                <label htmlFor="resource-type" className="block pb-2 text-base text-gray-600">
                                    Responsibility Level <span className='text-red-600'>*</span>
                                </label>
                                <div className="">
                                    <select
                                        value={selectedResourceTypes || ''}
                                        onChange={(e) => setSelectedResourceTypes(e.target.value)} // This updates selectedResourceTypes state
                                        required
                                        className="py-2 px-4 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                    >
                                        <option value="" disabled>Select responsibility Level</option>
                                        {resourceTypes.map((resourceType) => (
                                            <option key={resourceType.id} value={resourceType.id}>
                                                {resourceType.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Select Roles */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Roles <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {roles.map(role => (
                                        <label className="inline-flex items-center" key={role.id}>
                                            <input
                                                type="checkbox"
                                                checked={selectedRoles[role.id] || false}
                                                onChange={() => setSelectedRoles(prev => ({ ...prev, [role.id]: !prev[role.id] }))}
                                                className="form-checkbox my-2"
                                            />
                                            <span className="ml-2">{role.name}</span> {/* Assuming role has a 'name' property */}
                                        </label>
                                    ))}
                                </div>
                            </div>
                            
                            {/* Department Selection */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                            <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                Department <span className='text-red-600'>*</span>
                            </label>
                            <div className="flex flex-col p-4">
                                {departments.map(department => (
                                <label className="inline-flex items-center" key={department.id}>
                                    <input
                                    type="radio"
                                    name="department"
                                    value={department.id}
                                    checked={selectedDepartments === department.id}
                                    onChange={() => setSelectedDepartments(department.id)}
                                    required
                                    className="form-radio my-2"
                                    />
                                    <span className="ml-2">{department.name}</span>
                                </label>
                                ))}
                            </div>
                            </div>

                            {/* Teams Selection */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Teams <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {filteredTeams.length > 0 ? (
                                        filteredTeams.map((team) => (
                                            <div
                                                className="flex items-center justify-between"
                                                key={team.id}
                                                onMouseEnter={() => handleMouseEnter(team.id)}
                                                onMouseLeave={handleMouseLeave}
                                            >
                                                <label className="inline-flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedTeams[team.id] || false}
                                                        onChange={() => handleTeamChange(team.id)}
                                                        className="form-checkbox my-2"
                                                    />
                                                    <span className="ml-2">{team.name}</span>
                                                </label>

                                                {/* Show the default team checkbox and hide the popup for default team */}
                                                {defaultTeamId !== team.id && hoveredTeamId === team.id && (
                                                    <div className="relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center">                                          
                                                        <span className="text-[10px] text-gray-500 whitespace-nowrap">Make Default</span>
                                                        <input
                                                            type="radio"
                                                            checked={defaultTeamId === team.id}
                                                            onChange={() => handleDefaultTeamChange(team.id)}
                                                            className="form-radio"
                                                        />
                                                    </div>
                                                )}

                                                {/* Always show checkbox for the default team */}
                                                {defaultTeamId === team.id && (
                                                    <div className="relative flex flex-row gap-1 w-24 border border-gray-200 rounded p-1 justify-center items-center">
                                                        <span className="text-[10px] text-gray-500 whitespace-nowrap">
                                                            Default Team
                                                        </span>
                                                        <input
                                                            type="radio"
                                                            checked={true}
                                                            disabled
                                                            className="form-radio"
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        ))
                                    ) : (
                                        <p>Select a department to show teams</p>
                                    )}
                                </div>
                            </div>

                            {/* Select Resource Statuses */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Resource Statuses <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {resourceStatuses.map(resourceStatus => (
                                        <label className="inline-flex items-center" key={resourceStatus.id}>
                                            <input
                                                type="radio"
                                                name="resourceStatus"
                                                value={resourceStatus.id}
                                                checked={selectedResourceStatuses === resourceStatus.id}
                                                onChange={() => setSelectedResourceStatuses(resourceStatus.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{resourceStatus.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select Billing Statuses */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Billing Statuses <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {billingStatuses.map(billingStatus => (
                                        <label className="inline-flex items-center" key={billingStatus.id}>
                                            <input
                                                type="radio"
                                                name="billingStatus"
                                                value={billingStatus.id}
                                                checked={selectedBillingStatuses === billingStatus.id}
                                                onChange={() => setSelectedBillingStatuses(billingStatus.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{billingStatus.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select Contact Types */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Contact Types <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {contactTypes.map(contactType => (
                                        <label className="inline-flex items-center" key={contactType.id}>
                                            <input
                                                type="radio"
                                                name="contactType"
                                                value={contactType.id}
                                                checked={selectedContactTypes === contactType.id}
                                                onChange={() => setSelectedContactTypes(contactType.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{contactType.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select Available Statuses */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Available Statuses <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {availableStatuses.map(availableStatus => (
                                        <label className="inline-flex items-center" key={availableStatus.id}>
                                            <input
                                                type="radio"
                                                name="availableStatus"
                                                value={availableStatus.id}
                                                checked={selectedAvailableStatuses === availableStatus.id}
                                                onChange={() => setSelectedAvailableStatuses(availableStatus.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{availableStatus.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select Team Member Statuses */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Team Member Statuses <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {memberStatuses.map(memberStatus => (
                                        <label className="inline-flex items-center" key={memberStatus.id}>
                                            <input
                                                type="radio"
                                                name="memberStatus"
                                                value={memberStatus.id}
                                                checked={selectedMemberStatuses === memberStatus.id}
                                                onChange={() => setSelectedMemberStatuses(memberStatus.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{memberStatus.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select Branch */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    Branch <span className='text-red-600'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {branches.map(branch => (
                                        <label className="inline-flex items-center" key={branch.id}>
                                            <input
                                                type="radio"
                                                name="branch"
                                                value={branch.id}
                                                checked={selectedBranches === branch.id}
                                                onChange={() => setSelectedBranches(branch.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{branch.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                            {/* Select On-Site Status */}
                            <div className="mb-4 w-full md:max-w-[23%] text-left border border-gray-200 rounded-xl pb-4">
                                <label className="block text-sm font-medium text-gray-700 p-4 border-b border-gray-200">
                                    On-Site Status <span className='text-red-600 text-base'>*</span>
                                </label>
                                <div className="flex flex-col p-4">
                                    {onsiteStatuses.map(onsiteStatus => (
                                        <label className="inline-flex items-center" key={onsiteStatus.id}>
                                            <input
                                                type="radio"
                                                name="onsiteStatus"
                                                value={onsiteStatus.id}
                                                checked={selectedOnsiteStatuses === onsiteStatus.id}
                                                onChange={() => setSelectedOnsiteStatuses(onsiteStatus.id)}
                                                required
                                                className="form-radio my-2"
                                            />
                                            <span className="ml-2">{onsiteStatus.name}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                        </div>
                        {error && <p className="text-red-500 text-sm pt-4">{error}</p>}
                        <div className='text-left p-6'>
                            <button
                                type="submit"
                                className="w-56 bg-primary hover:bg-secondary text-white py-3 rounded-full flex flex-row gap-4 items-center justify-center m-auto mb-4"
                            >
                                <span class="material-symbols-rounded text-white text-xl font-regular">add_circle</span>
                                {loading ? 'Onboarding...' : 'Send Invitation'}
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
           
        </>
    );
};

export default AddMemberTest;