import React, { Suspense, useState } from 'react';
import { DataProvider } from '../common/utility/DataProvider';

const BillingStatus = React.lazy(() => import('./settings/BillingStatus'));
const ResourceStatus = React.lazy(() => import('./settings/ResourceStatus'));
const ResourceType = React.lazy(() => import('./settings/ResourceType'));
const ContactType = React.lazy(() => import('./settings/ContactType'));
const AvailableStatus = React.lazy(() => import('./settings/AvailableStatus'));
const MemberStatus = React.lazy(() => import('./settings/MemberStatus'));
const Blood = React.lazy(() => import('./settings/Blood'));
const Designation = React.lazy(() => import('./settings/Designation'));
const Location = React.lazy(() => import('./Location'));
const Branch = React.lazy(() => import('./Branch'));
const OnsiteStatus = React.lazy(() => import('./settings/OnsiteStatus'));
const Department = React.lazy(() => import('./settings/Department'));
const Teams = React.lazy(() => import('./settings/Teams'));
const Schedule = React.lazy(() => import('./settings/Schedule'));
const TrainingCategory = React.lazy(() => import('./settings/TrainingCategory'));
const TrainingTopic = React.lazy(() => import('./settings/TrainingTopic'));
const NoticeBoardCategory = React.lazy(() => import('./settings/NoticeBoardCategory'));

const Settings = () => {
  const [activeTab, setActiveTab] = useState('styled-billing-status');

  const handleTabClick = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="bg-white dark:bg-gray-900 rounded-xl pb-10 w-full overflow-hidden">
      <h4 className="text-xl font-bold p-4 lg:p-6 text-left dark:text-gray-400">Application Settings</h4>
      <div className="px-4 lg:px-6 pb-6 bg-white flex flex-row justify-start w-full">
        <div className="border-b border-gray-200 dark:border-gray-700 border border-1 rounded-lg">
          <ul
            className="flex -mb-px text-sm font-medium text-left w-full flex-col overflow-y-auto scrollbar-vertical"
            role="tablist"
          >
            {[
              { id: 'styled-billing-status', label: 'Billing Status' },
              { id: 'styled-resource-status', label: 'Resource Status' },
              { id: 'styled-resource-type', label: 'Responsibility Level' },
              { id: 'styled-contact-type', label: 'Contact Type' },
              { id: 'styled-available-status', label: 'Availability Status' },
              { id: 'styled-member-status', label: 'Team Member Status' },
              { id: 'styled-blood', label: 'Blood Group' },
              { id: 'styled-designation', label: 'Designation' },
              { id: 'styled-location', label: 'Work Location' },
              { id: 'styled-branch', label: 'Office Branch' },
              { id: 'styled-onsite-status', label: 'Onsite Status' },
              { id: 'styled-team', label: 'Creative Team' },
              { id: 'styled-department', label: 'Department' },
              { id: 'styled-schedule', label: 'Office Schedule' },
              { id: 'styled-training-category', label: 'Training Category' },
              { id: 'styled-training-topic', label: 'Training Topics' },
              { id: 'styled-noticeboardcategory', label: 'Notice Board Category' },
            ].map((tab) => (
              <li key={tab.id} className="whitespace-nowrap" role="presentation">
                <button
                  className={`inline-block p-4 border-b-2 w-full text-left rounded-t-lg ${
                    activeTab === tab.id ? 'bg-gray-100 text-black-400' : 'text-gray-500'
                  }`}
                  onClick={() => handleTabClick(tab.id)}
                  role="tab"
                  aria-selected={activeTab === tab.id}
                >
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>

        <div id="default-styled-tab-content" className="w-full px-4 overflow-hidden">
          {/* Render only active tab content */}
          <Suspense fallback={<div>Loading...</div>}>
            {activeTab === 'styled-billing-status' && <BillingStatus />}
            {activeTab === 'styled-resource-status' && <ResourceStatus />}
            {activeTab === 'styled-resource-type' && <ResourceType />}
            {activeTab === 'styled-contact-type' && <ContactType />}
            {activeTab === 'styled-available-status' && <AvailableStatus />}
            {activeTab === 'styled-member-status' && <MemberStatus />}
            {activeTab === 'styled-blood' && <Blood />}
            {activeTab === 'styled-designation' && <Designation />}
            {activeTab === 'styled-location' && <Location />}
            {activeTab === 'styled-branch' && <Branch />}
            {activeTab === 'styled-onsite-status' && <OnsiteStatus />}
            {activeTab === 'styled-department' && <Department />}
            {activeTab === 'styled-team' && <Teams />}
            {activeTab === 'styled-schedule' && <Schedule />}
            {activeTab === 'styled-training-category' && <TrainingCategory />}
            {activeTab === 'styled-training-topic' && <TrainingTopic />}
            {activeTab === 'styled-noticeboardcategory' && <NoticeBoardCategory />}
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default Settings;
