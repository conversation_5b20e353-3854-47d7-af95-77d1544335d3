<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class VerificationController extends Controller
{
    public function verify(Request $request, $id, $hash) 
    {
        // Find the user by ID
        $user = User::findOrFail($id);

        // Validate the hash to ensure the URL is valid
        if (!hash_equals($hash, sha1($user->email))) {
            // If the hash does not match, display an error message on the Blade view
            return view('emails.verify', [
                'message' => 'Invalid verification link.',
            ]);
        }

        // Check if the user has already verified their email
        if ($user->hasVerifiedEmail()) {
            return view('emails.verify', [
                'message' => 'User already verified.',
            ]);
        }

        // Mark the email as verified and trigger the Verified event
        $user->markEmailAsVerified();
        event(new Verified($user));

        // Log successful verification
        Log::info('User verified successfully', ['user_id' => $user->id]);

        // Return the verification success message with a redirect URL
        return view('emails.verify', [
            'message' => 'Email successfully verified. You can create password and log in.',
            'redirect_url' => env('FRONTEND_URL') . '/reset-password', // Frontend URL for redirection
        ]);
    }
}
