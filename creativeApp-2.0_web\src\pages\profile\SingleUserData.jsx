import React, { useEffect, useState } from 'react';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL; // Replace with your actual API URL

const SingleUserData = () => {
    const [user, setUser] = useState(null); // State to store user data
    const [error, setError] = useState(null); // State for error message
    const [loading, setLoading] = useState(true); // State to track loading status

    useEffect(() => {
        const fetchUserData = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            const token = localStorage.getItem('token');

            try {
                // Fetch the currently logged-in user data from the /user endpoint
                const response = await fetch(`${API_URL}/users`, { // This should match your backend route
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch user data: ${response.statusText}`);
                }

                const data = await response.json();


                setUser(data); // Set the matched user data to state
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUserData();
    }, []); // This effect runs once when the component is mounted

    // Loading state UI
    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    // Error state UI
    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    // Render the user data in a simple table
    return (
        <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            {user && (
                <table className="table-auto w-full border-collapse border border-gray-200">
                    <thead>
                        <tr>
                            <th className="border border-gray-300 px-4 py-2">Key</th>
                            <th className="border border-gray-300 px-4 py-2">Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td className="border border-gray-300 px-4 py-2">ID</td>
                            <td className="border border-gray-300 px-4 py-2">{user.id}</td>
                        </tr>
                        <tr>
                            <td className="border border-gray-300 px-4 py-2">Full Name</td>
                            <td className="border border-gray-300 px-4 py-2">
                                {`${user.fname || ''} ${user.lname || ''}`.trim() || 'N/A'}
                            </td>
                        </tr>
                        <tr>
                            <td className="border border-gray-300 px-4 py-2">EID</td>
                            <td className="border border-gray-300 px-4 py-2">{user.eid}</td>
                        </tr>
                        <tr>
                            <td className="border border-gray-300 px-4 py-2">Email</td>
                            <td className="border border-gray-300 px-4 py-2">{user.email}</td>
                        </tr>
                    </tbody>
                </table>
            )}
        </div>
    );
};

export default SingleUserData;
