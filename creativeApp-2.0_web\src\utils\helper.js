import moment from "moment-timezone";

export const allowedRoles = {
  superAdmin: ['super-admin'],
  admin: ['super-admin', 'admin'],
  hod: ['super-admin', 'admin', 'hod'],
  manager: ['super-admin', 'admin', 'hod', 'manager'],
  teamLead: ['super-admin', 'admin', 'hod', 'manager', 'team-lead'],
  coordinator: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator'],
  shiftLead: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead'],
  teamMember: ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member']
};

export const haveAccess = (canAccess=[]) => {
  let userRoles = JSON.parse(localStorage.getItem('roles')) || [];
  const hasAccess = userRoles.some(role => {
    if (canAccess.length === 0){
      return false; // No
    }else if(typeof canAccess === 'string'){
      if(canAccess !== ''){
        canAccess = canAccess.split(',').map(item => item.toLowerCase().trim());
      }else{
        return false; // Yes
      }
    } 
    
    if(role.toLowerCase() && canAccess.length > 0){
      return canAccess.includes(role.toLowerCase())
    }
  });
  return hasAccess;
}


export const sortByLabel = (arr) => {
  return arr.slice().sort((a, b) => {
    // Check if a.label and b.label are strings before using toLowerCase
    const labelA = typeof a.label === 'string' ? a.label.toLowerCase() : '';
    const labelB = typeof b.label === 'string' ? b.label.toLowerCase() : '';
    
    if (labelA < labelB) {
      return -1;
    }
    if (labelA > labelB) {
      return 1;
    }
    return 0;
  });
};


export const flattenObject = (ob) => {
    var toReturn = {};

    for (var i in ob) {
        if (!ob.hasOwnProperty(i)) continue;

        if ((typeof ob[i]) == 'object' && ob[i] !== null) {
            var flatObject = flattenObject(ob[i]);
            for (var x in flatObject) {
                if (!flatObject.hasOwnProperty(x)) continue;

                toReturn[i + '.' + x] = flatObject[x];
            }
        } else {
            toReturn[i] = ob[i];
        }
    }
    return toReturn;
}

export const isWeekday = (date) => {
  const day = date.getDay();
  return day !== 0 && day !== 6; // Disable Sunday (0) and Saturday (6)
};

let currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss');

export const datePickerDateTimeFormat = (dateString) => {
  if (!dateString) return null; // Prevents invalid date errors

  const parsedDate = moment(dateString, "YYYY-MM-DD HH:mm").toDate();
  return isNaN(parsedDate) ? null : parsedDate;
};
export const datePickerTimeFormat = (dateString) => {
  if (!dateString) return null; // Prevents invalid date errors

  const parsedDate = moment(dateString, "HH:mm").toDate();
  return isNaN(parsedDate) ? null : parsedDate;
};

export const dbDateTimeFormat = (datetimeData=currentDateTime) =>  moment(datetimeData).format('YYYY-MM-DD HH:mm:ss');
export const dbTimeFormat = (datetimeData=currentDateTime) =>  moment(datetimeData).format('HH:mm:ss');
export const dbDateFormat = (datetimeData=currentDateTime) =>  moment(datetimeData).format('YYYY-MM-DD');

export const defaultDateTimeFormat = (datetimeData=currentDateTime) =>  moment(datetimeData).format('dddd, Do MMMM YYYY hh:mm A');
export const defaultDateFormat = (dateData=currentDateTime) =>  moment(dateData).format('dddd, Do MMMM YYYY');
export const defaultTimeFormat = (timeData=currentDateTime) =>  moment(timeData, "HH:mm:ss").format("hh:mm A");
export const defaultDurationFormat = (timeStr=currentDateTime) =>  {
  const duration = moment.duration(timeStr);
  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();
  return `${hours}h ${minutes}m`;
};

export const millisecondsToHours =  (ms) => {
  return (ms / (1000 * 60 * 60)).toFixed(2); // returns string with 2 decimals
}

export const secondsToHours = (seconds) => {
  return (seconds / 3600).toFixed(2); // returns string with 2 decimals
}

export const defaultStartAndEndDateFromWeekNumber =  (weekYear) => {
  const [weekNumber, year] = weekYear.split('/').map(Number);

  if(!weekNumber) return 'Invalid Week';

  // Get the first day of the year
  const firstDayOfYear = new Date(year, 0, 1);

  // ISO week starts on Monday, so adjust to the first Monday of the year
  const dayOfWeek = firstDayOfYear.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const firstMondayOffset = dayOfWeek === 0 ? 1 : (dayOfWeek > 1 ? 8 - dayOfWeek : 0);

  // Calculate start date of the given week
  const daysOffset = (weekNumber - 1) * 7;
  const startDate = new Date(year, 0, 1 + firstMondayOffset + daysOffset);
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  const retDate = {
      start: moment(startDate).format("MMM Do"),
      end: moment(endDate).format("MMM Do, YYYY")
  };

  return retDate.start+" - "+retDate.end

}

export const getStartDateFromWeekNumber = (weekYear) => {
  if(!weekYear) return "";
  const [weekNumber, year] = weekYear.split('/').map(Number);

  // Get the first day of the year
  const firstDayOfYear = new Date(year, 0, 1);
  
  // Find the first Monday of the year (ISO weeks start on Monday)
  const dayOfWeek = firstDayOfYear.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const firstMondayOffset = dayOfWeek === 0 ? 1 : (dayOfWeek > 1 ? 8 - dayOfWeek : 0);

  // Calculate the start date of the given week
  const daysOffset = (weekNumber - 1) * 7;
  const startDate = new Date(year, 0, 1 + firstMondayOffset + daysOffset);

  return startDate.toISOString().split('T')[0]; // Format YYYY-MM-DD
}

export const removeKeys = (obj, keysToRemove) => {
  if (Array.isArray(obj)) {
      return obj.map(item => removeKeys(item, keysToRemove));
  } else if (typeof obj === "object" && obj !== null) {
      return Object.fromEntries(
          Object.entries(obj)
              .filter(([key]) => !keysToRemove.includes(key))
              .map(([key, value]) => [key, removeKeys(value, keysToRemove)])
      );
  }
  return obj;
}


/**
 * Calculates the duration between two date/time strings or Moment objects and returns it in "HH:mm:ss" format.
 *
 * @param {string|moment.Moment} startTime - The start time.
 * @param {string|moment.Moment} endTime - The end time.
 * @param {string} [format] - Optional format string if startTime/endTime are strings.
 * @returns {string} - The duration in "HH:mm:ss" format.
 * @throws {Error} - If invalid input is provided.
 */
export const calculateDuration = (startTime, endTime, timeFormat, formatType='HH:mm:ss') => {
  try {
    let startMoment, endMoment;

    if (typeof startTime === 'string' && typeof endTime === 'string') {
      if (!timeFormat) {
        throw new Error("timeFormat is required when start and end times are strings");
      }
      startMoment = moment(startTime, timeFormat);
      endMoment = moment(endTime, timeFormat);
    } else if (moment.isMoment(startTime) && moment.isMoment(endTime)) {
      startMoment = startTime;
      endMoment = endTime;
    } else {
      throw new Error('Invalid input types. startTime and endTime must be strings with a timeFormat, or moment objects.');
    }

    if (!startMoment.isValid() || !endMoment.isValid()) {
      throw new Error('Invalid date/time values.');
    }

    const durationMs = endMoment.diff(startMoment);
    const duration = moment.duration(durationMs);

    switch (formatType) {
      case 'HH:mm:ss':
        const hours = duration.hours().toString().padStart(2, '0');
        const minutes = duration.minutes().toString().padStart(2, '0');
        const seconds = duration.seconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
      case 'minutes':
        return Math.round(duration.asMinutes());
      case 'humanize':
        return duration.humanize();
      default:
        throw new Error('Invalid formatType. Must be "HH:mm:ss", "minutes", or "humanize".');
    }
  } catch (error) {
    console.error('Error calculating duration:', error);
    return 'Invalid duration'; // Or handle the error as needed in your UI
  }
};

export const durationToMinutes = (duration) => {
  const parts = duration.split(":").map(Number);

  if (parts.length === 3) {
      // Format: HH:MM:SS
      const [hours, minutes, seconds] = parts;
      return hours * 60 + minutes + Math.round(seconds / 60);
  } else if (parts.length === 2) {
      // Format: MM:SS
      const [minutes, seconds] = parts;
      return minutes + Math.round(seconds / 60);
  } else if (parts.length === 1) {
      // Format: MM (already in minutes)
      return parts[0];
  }

  return 0; // Default case if format is invalid
};

export const calculateShiftEndTime = (date, shiftStart, shiftEnd) => {
  if (!date || !shiftStart || !shiftEnd) return null; // Handle missing values

  // Create Date objects for start and end times
  const startDateTime = moment(`${date} ${shiftStart}`, "YYYY-MM-DD HH:mm:ss");
  const endDateTime = moment(`${date} ${shiftEnd}`, "YYYY-MM-DD HH:mm:ss");


  if (!startDateTime.isValid()) return null; // Invalid date check
  if (!endDateTime.isValid()) return null; // Invalid date check

  if (startDateTime.isAfter(endDateTime)) {
    return endDateTime.add(1, 'days').format("YYYY-MM-DD HH:mm");
  } else {
    return endDateTime.format("YYYY-MM-DD HH:mm");
  }
};

export const sumDurations = (durationsData) => {
  let totalDuration = moment.duration();
  const currentTime = moment();
  
  durationsData.forEach((dataItem) => {
      let start = moment(dataItem.start, "YYYY-MM-DD HH:mm:ss");
      let end = dataItem.end ? moment(dataItem.end, "YYYY-MM-DD HH:mm:ss") : currentTime;
      
      let duration = moment.duration(end.diff(start));
      totalDuration.add(duration);
  });
  
  return moment.utc(totalDuration.asMilliseconds()).format("HH:mm:ss");
};

/**
 * Calculates duration from start time to end time (if exists) or to the current time.
 * @param {string} startTime - The start time in "YYYY-MM-DD HH:mm:ss" format.
 * @param {string|null} endTime - The end time in "YYYY-MM-DD HH:mm:ss" format (or null if ongoing).
 * @returns {string} - The duration in "HH:mm:ss" format.
 */
export const startEndDuration = (startTime, endTime = null) => {
  if (!startTime) return "00:00:00";

  const startMoment = moment(startTime, "YYYY-MM-DD HH:mm:ss");
  const endMoment = endTime ? moment(endTime, "YYYY-MM-DD HH:mm:ss") : moment();

  const duration = moment.duration(endMoment.diff(startMoment));

  return moment.utc(duration.asMilliseconds()).format("HH:mm:ss");
};

export const getTimezoneDifference = (tz1, tz2) => {
  // Get UTC offsets in minutes for each time zone
  const offsetTz1 = moment.tz(tz1).utcOffset();
  const offsetTz2 = moment.tz(tz2).utcOffset();

  // Calculate the difference in minutes and convert to hours
  const diffInMinutes = offsetTz1 - offsetTz2;
  const diffInHours = diffInMinutes / 60;

  // return { diffInMinutes, diffInHours };

  return diffInHours > 0 ? `+${diffInHours}` : `${diffInHours}`; // Format the output
};

