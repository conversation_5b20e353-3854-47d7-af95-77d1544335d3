<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $exception)
    {
        // Check if it's a validation exception
        if ($exception instanceof ValidationException) {
            return $this->handleValidationException($exception);
        }

        // Check if the exception is a custom success response type or condition
        // For example, a successful response from a controller method
        // if ($this->isSuccessfulRequest($request)) {
        //     return $this->handleSuccessResponse();
        // }

        // Handle other exceptions here...
        return parent::render($request, $exception);
    }

    /**
     * Handle validation exception globally and format the response.
     */
    protected function handleValidationException(ValidationException $exception)
    {
        $errors = $exception->errors();  // Get the validation error messages

        // Format the response for validation errors
        $response = [
            'status' => 'error',
            'message' => 'Validation failed',
            'errors' => $errors,
        ];

        // Return a custom JSON response with HTTP status 422 (unprocessable entity)
        return new JsonResponse($response, 422);
    }

    /**
     * Handle success response globally.
     */
    protected function handleSuccessResponse()
    {
        // Customize your success response structure
        $response = [
            'status' => 'success',
            'message' => 'Operation completed successfully',
        ];

        // Return a custom JSON response with HTTP status 200 (OK)
        return new JsonResponse($response, 200);
    }

    /**
     * Check if the request is a successful request.
     * This can be modified based on your needs (e.g., POST, PUT, PATCH methods).
     */
    protected function isSuccessfulRequest(Request $request)
    {
        // For demonstration, consider a request successful if it's a POST or PUT request that does not cause an error
        // This can be customized to be more specific based on the action being taken
        return $request->isMethod('post') || $request->isMethod('put') || $request->isMethod('patch');
    }


}
