import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from './EditorToolbar';
import 'react-quill/dist/quill.snow.css';

const API_URL = `${process.env.REACT_APP_BASE_API_URL}/`;
const isTokenValid = () => localStorage.getItem('token') !== null;

const EditChangeLog = ({ isVisible, setVisible, changeLogId }) => {
    const [formData, setFormData] = useState({
        version: '',
        date: '',
        area: '',
        type: '',
        author: '',
        description: '',
    });

    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Fetch existing data
    useEffect(() => {
        const fetchChangeLog = async () => {
            if (!isTokenValid()) {
                setError('Authentication token is missing.');
                return;
            }

            if (!changeLogId) {
                setError('Invalid Change Log ID.');
                return;
            }

            try {
                const token = localStorage.getItem('token');
                const response = await fetch(`${API_URL}change-log/${changeLogId}`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) throw new Error('Failed to fetch Change Log data.');

                const data = await response.json();
                const log = data.changeLog || {};

                setFormData({
                    version: log.version || '',
                    date: log.date || '',
                    area: log.area || '',
                    type: log.type || '',
                    author: log.author || '',
                    description: log.description || '',
                });
            } catch (err) {
                setError(err.message);
            }
        };

        if (isVisible) {
            fetchChangeLog();
        }
    }, [changeLogId, isVisible]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        setError('');
        setSuccessMessage('');

        const { version, date, area, type, description } = formData;

        if (!version || !date || !area || !type || !description) {
            setError('Please fill all required fields.');
            return;
        }

        setIsLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}change-log/${changeLogId}`, {
                method: 'PUT',
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to update Change Log.');
            }

            setSuccessMessage('Change Log updated successfully!');
        } catch (err) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        setFormData({
            version: '',
            date: '',
            area: '',
            type: '',
            author: '',
            description: '',
        });
        setError('');
        setSuccessMessage('');
        setVisible(false);
    };

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="relative bg-white p-6 rounded-lg shadow-md w-full max-w-lg h-[80vh] overflow-y-auto">
                <button
                    onClick={handleClose}
                    className="absolute top-2 right-2 text-gray-400 hover:text-gray-900 text-2xl font-bold"
                    aria-label="Close"
                >
                    &times;
                </button>

                <h4 className="text-xl font-semibold mb-4 py-4">Edit Change Log</h4>
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Version</label>
                        <input
                            type="text"
                            name="version"
                            value={formData.version}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Date</label>
                        <input
                            type="date"
                            name="date"
                            value={formData.date}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Area</label>
                        <input
                            type="text"
                            name="area"
                            value={formData.area}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Type</label>
                        <input
                            type="text"
                            name="type"
                            value={formData.type}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                            required
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Author</label>
                        <input
                            type="text"
                            name="author"
                            value={formData.author}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                        />
                    </div>
                    <div className="mb-4">
                        <label className="block font-semibold mb-1">Description</label>
                        <EditorToolbar />
                        <ReactQuill
                            value={formData.description}
                            onChange={(value) => setFormData((prev) => ({ ...prev, description: value }))}
                            className="bg-white border border-gray-300 rounded-md shadow-sm"
                            theme="snow"
                            modules={modules}
                            formats={formats}
                        />
                    </div>

                    <div className="py-4">
                        <button
                            type="submit"
                            className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            disabled={isLoading}
                        >
                            {isLoading ? 'Updating...' : 'Update Change Log'}
                        </button>
                    </div>

                    {error && <p className="text-red-500 text-sm">{error}</p>}
                    {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                </form>
            </div>
        </div>
    );
};

export default EditChangeLog;
