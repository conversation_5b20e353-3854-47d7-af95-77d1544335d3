import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const scheduleApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getScheduleData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `schedule-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ScheduleData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForSchedule: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `schedule-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ScheduleData'],
    }),

    getScheduleById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `schedules/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ScheduleData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createSchedule: builder.mutation({
      query: (newFormationType) => ({
        url: 'schedule-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ScheduleData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateSchedule: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `schedules/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ScheduleData', id }, 'ScheduleData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteSchedule: builder.mutation({
      query: (id) => ({
        url: `schedules/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ScheduleData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetScheduleDataQuery,
  useLazyFetchDataOptionsForScheduleQuery,
  useGetScheduleByIdQuery,
  useLazyGetScheduleByIdQuery,
  useCreateScheduleMutation,
  useUpdateScheduleMutation,
  useDeleteScheduleMutation,
} = scheduleApi;
