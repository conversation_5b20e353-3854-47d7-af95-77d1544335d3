<?php

namespace App\Http\Controllers;

use App\Models\TrainingCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TrainingCategoryController extends Controller
{
    /**
     * Display a listing of all training categories.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $trainingCategories = TrainingCategory::all();

        // Log the training categories retrieved
        Log::info('All Product Types Retrieved:', ['count' => $trainingCategories->count()]);

        return response()->json(['trainingCategories' => $trainingCategories], 200);
    }


    /**
     * Display the specified training category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the training category by ID
        $trainingCategory = TrainingCategory::find($id);

        if (!$trainingCategory) {
            return response()->json(['error' => 'Training category not found.'], 404);
        }

        // Log the training category retrieved
        Log::info('Training Category Retrieved:', ['training_category' => $trainingCategory]);

        return response()->json(['trainingCategory' => $trainingCategory], 200);
    }

    /**
     * Create a new training category.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        Log::info('Create Training category Request:', ['request' => $request->all()]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);

        // Log the request data
        Log::info('Create Training category Request:', ['request' => $request->all()]);

        // Check if the Training category name already exists
        if (TrainingCategory::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Training category already exists.'], 409);
        }

        // Create a new Training category
        $trainingCategory = TrainingCategory::create([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('Training category Created:', ['training_category' => $trainingCategory]);

        return response()->json(['message' => 'Training category created successfully.', 'training_category' => $trainingCategory], 201);
    }

    /**
     * Update an existing Training category.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        Log::info('Update Training category Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);
    
        // Find the existing Training category by ID
        $trainingCategory = TrainingCategory::find($id);
    
        // Check if the Training category exists
        if (!$trainingCategory) {
            return response()->json(['error' => 'Training category not found.'], 404);
        }
    
        // Check if the Training category name is being changed and ensure it does not conflict with an existing name
        if ($trainingCategory->name !== $request->name && TrainingCategory::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Training category with the same name already exists.'], 409);
        }
    
        // Update the Training category with the new data
        $trainingCategory->update([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,  // Track who updated the Training category
        ]);
    
        Log::info('Training category Updated:', ['training_category' => $trainingCategory]);
    
        return response()->json(['message' => 'Training category updated successfully.', 'training_category' => $trainingCategory], 200);
    }
    

    /**
     * Delete a Training category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the Training category
            $trainingCategory = TrainingCategory::findOrFail($id);

            // Delete the Training category
            $trainingCategory->delete();

            return response()->json(['message' => 'Training category deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this Training category.'], 403);
    }
}
