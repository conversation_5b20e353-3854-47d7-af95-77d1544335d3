<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('comments_todo', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('todo_id');
            // Foreign key relation
            $table->foreign('todo_id')->references('id')->on('todos')->onDelete('cascade'); 

            $table->string('comment');
            $table->unsignedBigInteger('commenter_id');
            // Foreign key relation
            $table->foreign('commenter_id')->references('id')->on('users')->onDelete('cascade'); 

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comments_todo');
    }
};
