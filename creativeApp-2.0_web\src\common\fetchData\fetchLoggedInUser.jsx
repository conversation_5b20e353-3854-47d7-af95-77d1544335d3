import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom'

// Function to check if the token is present in localStorage
const isTokenValid = () => {
  const token = localStorage.getItem('token');
  return token !== null && token !== ''; // Ensuring that the token is not empty
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const FetchLoggedInUser = () => {

  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  
  // Fetch logged-in user's data from the API
  useEffect(() => {
    
    // Check if the token is valid
    const token = localStorage.getItem('token');
    
    if (!isTokenValid()) {
      setError('No valid authentication token found.');
      setLoading(false);
      navigate('/login'); 
      return; // Return early if no valid token exists
    }

    const user = localStorage.getItem('user');

    if(user) {
      setUserData(JSON.parse(user));
      setLoading(false);
      return; // Return early if user data is already available
    }

    const fetchUserData = async () => {
      try {
        const response = await fetch(`${API_URL}/logged-users`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,  // Pass token in the header
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }

        const data = await response.json();
        setUserData(data);
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // If loading, show a loading message
  if (loading) {
    return <div>Loading...</div>;
  }

  // If there's an error, show the error message
  if (error) {
    return <div>Error: {error}</div>;
  }

  // If user data is fetched successfully, display it
  return (
    <div>
      <h3>Logged-in User Information</h3>
      {userData ? (
        <div>
          <p><strong>Name:</strong> {userData.lname} {userData.fname}</p>
          <p><strong>Email:</strong> {userData.email}</p>
          {/* Display other user-related information */}
          <p><strong>Role:</strong> {userData.roles?.[0]?.name}</p> {/* Example of accessing nested data */}
        </div>
      ) : (
        <div>No user data available</div>
      )}
    </div>
    
  );
};

export default FetchLoggedInUser;
