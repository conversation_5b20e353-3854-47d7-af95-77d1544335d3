import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';

export const dateTimeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCurrentDateTime: builder.query({
      query: () => "/datetime/current",
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
    convertTimezone: builder.mutation({
      query: (data) => ({
        url: "/datetime/convert",
        method: "POST",
        body: data,
      }),
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
    calculateDuration: builder.mutation({
      query: (data) => ({
        url: "/datetime/duration",
        method: "POST",
        body: data,
      }),
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});


export const {
  useGetCurrentDateTimeQuery,
  useConvertTimezoneMutation,
  useCalculateDurationMutation,
} = dateTimeApi;
