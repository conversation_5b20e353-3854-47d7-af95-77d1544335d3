import React, { useState } from 'react';
import TableHeader from '../common/table/TableHeader';
import TablePagination from '../common/table/TablePagination';
import MemberOnboardList from '../pages/team-member/MemberOnboardList';
import TableLayoutWrapper2 from '../common/table/TableLayoutWrapper2';
import NoticeList from '../pages/notice/NoticeList';

const NoticeBoard = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Handle search input changes
  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm); // Update the search term state in the parent
  };


  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-notice" buttonName="Add New Notice" />
        <NoticeList/>
        
      </TableLayoutWrapper2>
    </div>
  );
};

export default NoticeBoard;

