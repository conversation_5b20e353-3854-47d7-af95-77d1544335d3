@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.react-datepicker-wrapper {
  width: 100%;
}

input[type="date"],
input[type="text"]::placeholder {
  color: #90a1b9;
  background: #fafaf9;
}

.ql-container {
  border: none !important;
}
.ql-editor {
  border: none !important;
  background: transparent !important;
}

/* Scrollbar vertical Width: 10px */
.scrollbar-vertical::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Track */
.scrollbar-vertical::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #ddd;
  border-radius: 10px;
}

/* Handle */
.scrollbar-vertical::-webkit-scrollbar-thumb {
  background: #dfecf1;
  border-radius: 10px;
}

/* Scrollbar Width: 10px */
.scrollbar-horizontal-10::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Track */
.scrollbar-horizontal-10::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px #ddd;
  border-radius: 10px;
}

/* Handle */
.scrollbar-horizontal-10::-webkit-scrollbar-thumb {
  background: #dfecf1;
  border-radius: 10px;
}

/* Data table column cell width */
.rdt_TableCell {
  white-space: nowrap;
}

.App .izzals {
  max-height: unset;
}

.filter-search input,
.filter-search select {
  min-width: 150px;
}

/* Tooltip popup */
/* Basic tooltip styling */
.tooltip-text {
  visibility: hidden;
  background-color: #6c757d;
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px;
  position: absolute;
  top: -20px;
  right: 20px;
  z-index: 1;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

/* Show tooltip when hovering */
.relative:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Member Index */
#about-cell,
#address {
  overflow: hidden !important;
}

/* Navbar Toggle */
.navbar-toggle ul li a {
  padding: 10px;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}

.navbar-toggle ul li a .disable-text {
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

/* Manage Column */
.manage-column input {
  background-color: #196d92 !important;
  border-color: #196d92 !important;
}

.team-cell-index {
}
