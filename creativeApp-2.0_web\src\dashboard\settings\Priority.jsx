import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import PriorityList from '../../pages/priority/PriorityList'; // Updated import to TaskTypeList

const Priority = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-priority" buttonName="Add Priority" /> 
        <PriorityList /> 
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default Priority;
