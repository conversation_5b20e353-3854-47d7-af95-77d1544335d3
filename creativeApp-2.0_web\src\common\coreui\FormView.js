import React, { useState } from "react";
import moment from 'moment';
import Select from "react-select";
import DatePicker from "react-datepicker";
import {DepartmentDropdown, TeamsDropdown, UsersByDefaultTeamDropdown, ShiftDropdown, FormError,LocationDropdown} from '../../common/coreui';
import { registerLocale } from "react-datepicker";
import enGB from "date-fns/locale/en-GB"; // English locale with Monday as start
import { dbDateFormat, isWeekday, dbDateTimeFormat, datePickerTimeFormat, dbTimeFormat, datePickerDateTimeFormat, getStartDateFromWeekNumber, haveAccess } from "../../utils";

registerLocale("en-GB", enGB);

export const FormView = ({
  item = {},
  columns,
  setModalVisible=null,
  handleSubmit,
  error=null,
  setError=null,
  grid = 1,
  title="Add New"
}) => {
  const [dataItems, setDataItems] = useState({ ...item });

  return (
    <div
      className="fixed inset-0 z-[500] flex items-center justify-center bg-gray-800 bg-opacity-50"
      // onClick={() => setModalVisible(null)}
    >
      <div
        className="relative bg-white shadow-lg max-w-[65%] w-full   rounded-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            handleSubmit(dataItems);
          }}
          className="w-full"
        >
          <div className="w-full text-sm text-start rtl:text-right text-gray-500 dark:text-gray-400 ">
            <div>
              <div className="h-[60px] bg-gray-200 rounded-t-lg dark:border-gray-700 sentence-case dark:hover:bg-gray-800 dark:hover:text-white">
                <div>
                  <div className="flex justify-start p-4">
                    <span className="text-lg font-semibold flex items-start align-middle" dangerouslySetInnerHTML={{ __html: title }} ></span>
                    <button
                      className="absolute top-0 right-3 flex w-[20px] h-[20px] ms-2 text-center my-4 justify-center items-center  text-sm font-medium focus:outline-none bg-white rounded-lg border border-red-600 text-red-600 hover:bg-red-500 hover:text-white focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                      onClick={() => {
                        if(setError){
                          setError(null); 
                        }
                        if(setModalVisible){
                          setModalVisible(null)
                        }
                      }}
                    >
                      <span className="material-symbols-outlined text-sm">
                        close
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div className=" ">
                <FormError error={error} />
                {/* max-h-[90vh] overflow-y-auto  */}
                <div
                  className={`w-full p-4 pb-20  grid grid-cols-${grid} gap-4 my-3`}
                >
                  {columns.map(
                    (column, index) =>
                      column.form &&
                    column.name !== "S.No" &&
                    (column.form?.canAccess? haveAccess(column.form.canAccess) : true) &&
                      column.form.type && (
                        <fieldset key={column.id} className="w-full  border-0 text-start rounded-lg py-4 relative  ">
                          <legend className="font-semibold text-sm mx-4  absolute mt-[-20px] py-0 px-0 flex bg-white">
                            {column.name}
                          </legend>
                          <div className="w-auto px-3 mt-2">
                            {/* {column.selector(item) || ""} */}
                            {/* DepartmentDropdown */}
                            {column.form.type &&
                            column.form.type === "DepartmentDropdown" ? (
                              <>
                              <DepartmentDropdown 
                              value={dataItems[column.db_field]}
                              onChange={(items) => {
                                setDataItems({
                                  ...dataItems,
                                  [column.db_field]: items.value,
                                })
                              }}
                              props={column.form.props}
                              className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ) : column.form.type === "LocationDropdown" ? (
                              <>
                                <LocationDropdown
                                  value={dataItems[column.db_field]}
                                  onChange={(items) => {
                                    setDataItems({
                                      ...dataItems,
                                      [column.db_field]: items.value,
                                    });
                                  }}
                                  props={column.form.props}
                                  className={error && error.errors && error.errors[column.db_field] ? 
                                    "border border-red-600 rounded-lg w-full" : "w-full"}
                                />
                                <FormError error={error} field={column.db_field} />
                              </>
                            )
                            : column.form.type === "TeamsDropdown" ? (
                              <>
                              <TeamsDropdown 
                              value={dataItems[column.db_field]}
                              onChange={(items) => {
                                setDataItems({
                                  ...dataItems,
                                  [column.db_field]: items.value,
                                })
                              }}
                              // dependentField={column.form.dependentField}
                              dependentField={dataItems[column.form.dependentField]}
                              props={column.form.props}
                              className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "ShiftDropdown" ? (
                              <>
                              <ShiftDropdown 
                              value={dataItems[column.db_field]}
                              onChange={(items) => {
                                if(column.form.props.isMulti){
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: items,
                                  })
                                }else{
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: items.value,
                                  })
                                }
                                
                              }}
                              // dependentField={column.form.dependentField}
                              dependentField={dataItems[column.form.dependentField]}
                              props={column.form.props}
                              className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>                            
                            ): column.form.type === "UsersByDefaultTeamDropdown" ? (
                              <>
                              <UsersByDefaultTeamDropdown 
                              value={dataItems[column.db_field]}
                              onChange={(items) => {
                                if(column.form.props.isMulti){
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: items,
                                  })
                                }else{
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: items.value,
                                  })
                                }
                                
                              }}
                              // dependentField={column.form.dependentField}
                              dependentField={dataItems[column.form.dependentField]}
                              props={column.form.props}
                              className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>                            
                            ): column.form.type === "WeekPicker" ? (
                              <>
                              <div className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}>
                              <DatePicker
                                selected={getStartDateFromWeekNumber(dataItems[column.db_field])}
                                todayButton="Today"
                                minDate={new Date()}
                                startDate={new Date()}

                                onChange={(date) => {
                                  if (date) {
                                    const weekNumber = moment(date).isoWeek();
                                    const year = moment(date).year();
                                    const weekYear = `${weekNumber}/${year}`;
                                    setDataItems({
                                      ...dataItems,
                                      [column.db_field]: weekYear,
                                    })
                                  }
                                }}

                                dateFormat="I/R"
                                locale="en-GB"
                                showWeekNumbers
                                showWeekPicker
                                props={column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}
                              />
                              </div>
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "datetime" ? (
                              <>
                              <div className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg "}>
                              <DatePicker
                                selected={datePickerDateTimeFormat(dataItems[column.db_field]) || ""}
                                todayButton="Today"

                                onChange={(date) => {
                                  if (date) {
                                    setDataItems({
                                      ...dataItems,
                                      [column.db_field]: dbDateTimeFormat(date),
                                    })
                                  }
                                }}

                                locale="en-GB"
                                filterDate={isWeekday}
                                showWeekNumbers
                                showTimeSelect
                                timeFormat="hh:mm aa" // AM/PM format
                                dateFormat="yyyy-MM-dd hh:mm aa" // Full date-time with AM/PM
                                timeIntervals={1} 
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg border border-gray-300"}
                              />
                              </div>
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "time" ? (
                              <>
                              <div className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}>
                              <DatePicker
                                selected={datePickerTimeFormat(dataItems[column.db_field]) || ""}
                                onChange={(time) => {
                                  if (time) {
                                    setDataItems({
                                      ...dataItems,
                                      [column.db_field]: dbTimeFormat(time),
                                    })
                                  }
                                }}

                                showTimeSelect
                                showTimeSelectOnly
                                timeFormat="hh:mm aa" // 12-hour format with AM/PM
                                dateFormat="hh:mm aa" // Display format
                                timeIntervals={1} // Adjust time intervals (e.g., 5, 10, 30 minutes)
                                placeholderText="Select Time"
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg border border-gray-300"}
                              />
                              </div>
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "date" ? (
                              <>
                              <div className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full  rounded-lg"}>
                              <DatePicker
                                selected={datePickerDateTimeFormat(dataItems[column.db_field]) || ""}
                                todayButton="Today"

                                onChange={(date) => {
                                  if (date) {
                                    setDataItems({
                                      ...dataItems,
                                      [column.db_field]: dbDateFormat(date),
                                    })
                                  }
                                }}

                                locale="en-GB"
                                filterDate={isWeekday}
                                showWeekNumbers
                                dateFormat="MMMM d, yyyy" // Display format
                                placeholderText="Select Date"
                                isClearable // Allows clearing the selected date
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full": "w-full rounded-lg border border-gray-300"}
                              />
                              </div>
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "textarea" ?(
                              <>
                              <textarea
                                id={column.id ?? "textarea-id-"+index}
                                value={dataItems[column.db_field]}
                                onChange={(e) =>
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: e.target.value,
                                  })
                                }
                                rows="4" cols="50"
                                style={{minHeight: "100px"}}
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border-red-600 px-4 rounded-lg w-full  h-[40px]  p-2 ": "border-gray-300 px-4 rounded-lg w-full  h-[40px]  p-2 "}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>
                            ): column.form.type === "select" ? (
                              <>
                              <Select
                                closeMenuOnSelect={false}
                                isClearable
                                isSearchable={
                                  column.form.options &&
                                  column.form.options.length > 5
                                }
                                isDisabled={false}
                                name={column.name}
                                value={{
                                  label: dataItems[column.db_field],
                                  value: dataItems[column.db_field],
                                }}
                                options={column.form.options || []}
                                onChange={(items) =>
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: items.value,
                                  })
                                }
                                // onChange={(items) => setSelectedFilterOptions({ ...selectedFilterOptions, [column.db_field]: items })}
                                // className="w-full  h-[40px] focus:z-50 border-gray-200 rounded-lg p-2"
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border border-red-600 rounded-lg w-full capitalize": "w-full capitalize"}
                                />
                                <FormError error={error}  field={column.db_field} />
                                </>
                            ) : (
                              <>
                              <input
                                type={column.form.type}
                                id={column.db_field}
                                placeholder={column.name}
                                value={dataItems[column.db_field]}
                                onChange={(e) =>
                                  setDataItems({
                                    ...dataItems,
                                    [column.db_field]: e.target.value,
                                  })
                                }
                                {...column.form.props}
                                className={error && error.errors && error.errors[column.db_field]? "border-red-600 px-4 rounded-lg w-full  h-[40px]  p-2 ": "border-gray-300 px-4 rounded-lg w-full  h-[40px]  p-2 "}
                              />
                              <FormError error={error}  field={column.db_field} />
                              </>
                            )}
                          </div>
                        </fieldset>
                      )
                  )}
                </div>
              </div>
            </div>
            <div>
              <div className=" ">
                <div className="rounded-b-xl pe-3 py-4 font-medium text-gray-900 whitespace-nowrap bg-gray-200 dark:text-white dark:bg-gray-800">
                  <div className="flex justify-start px-8">
                    <button
                      type="submit"
                      className="flex w-auto h-[30px] text-center my-3 justify-center items-center py-1 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    >
                      <span className="material-symbols-outlined me-1  text-sm">
                        stylus_note
                      </span>
                      {item?.id ? "Update" : "Add New"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};
