<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Region;
use Illuminate\Support\Facades\Log;

class RegionController extends Controller
{
    /**
     * Display a listing of all regions.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $regions = Region::all();

        // Log the regions retrieved
        Log::info('All Regions Retrieved:', ['regions_count' => $regions->count()]);

        return response()->json(['regions' => $regions], 200);
    }

    /**
     * Display the specified region.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $region = Region::find($id);

        if (!$region) {
            return response()->json(['error' => 'Region not found.'], 404);
        }

        // Log the region retrieved
        Log::info('Region Retrieved:', ['region' => $region]);

        return response()->json(['region' => $region], 200);
    }

    // Filter logic for data table
    public function regionsData(Request $request)
    {
        $query = Region::with(['creator', 'updater', 'team', 'department']);

        // Decode all input parameters to handle URL-encoded values
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedDepartment = $request->filled('department_id') ? urldecode($request->input('department_id')) : null;
        $decodedTeams = $request->filled('team_id') ? urldecode($request->input('team_id')) : null;
        $decodedName = $request->filled('name') ? urldecode($request->input('name')) : null;

        // Filtering by department_id
        if ($decodedDepartment) {
            $decodedDepartments = explode(',', $decodedDepartment);
            $query->where(function ($q) use ($decodedDepartments) {
                foreach ($decodedDepartments as $decodedDepartment) {
                    $q->orWhere('department_id', '=', trim($decodedDepartment));
                }
            });
        }

        // Filtering by team_id
        if ($decodedTeams) {
            $decodedTeams = explode(',', $decodedTeams);
            $query->where(function ($q) use ($decodedTeams) {
                foreach ($decodedTeams as $decodedTeam) {
                    $q->orWhere('team_id', '=', trim($decodedTeam));
                }
            });
        }

        // Filtering by name
        if ($decodedName) {
            $names = explode(',', $decodedName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }

        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('department', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('team', function ($query) use ($globalSearch) {
                        $query->where('name', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });
            });
        }

        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');

        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';

        $query->orderBy($sortBy, $order);

        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $regions = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($regions, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Region::with(['creator', 'updater', 'team', 'department']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }


    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Region::with(['creator','updater', 'team', 'department']);
        $results->select($column, $column. ' as title', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }


    /**
     * Create a new region.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);
        
        // Log the request data
        Log::info('Create region Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Create a new region
        try {
            $region = Region::create([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'created_by' => $authUser->id,
            ]);
            
            // Log region creation
            Log::info('region Created:', ['region' => $region]);

            return response()->json([
                'message' => 'region created successfully.',
                'region' => $region
            ], 201);
        } catch (\Exception $e) {
            // Log error if region creation fails
            Log::error('region Creation Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to create region.'], 500);
        }
    }


    /**
     * Update an existing region.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
        
        // Log the authenticated user's details without sensitive data
        Log::info('Authenticated User ID:', ['user_id' => $authUser->id]);

        // Log the request data
        Log::info('Update region Request:', ['request' => $request->only(['name', 'department_id', 'team_id'])]);

        // Validate the request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'department_id' => 'nullable|exists:departments,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        // Find the existing region by ID
        $region = Region::find($id);

        // Check if the region exists
        if (!$region) {
            return response()->json(['error' => 'region not found.'], 404);
        }

        // Update the region with the new data
        try {
            $region->update([
                'name' => $validatedData['name'],
                'department_id' => $validatedData['department_id'],
                'team_id' => $validatedData['team_id'],
                'updated_by' => $authUser->id, // Track who updated the region
            ]);

            // Log region update
            Log::info('Region Updated:', ['region' => $region]);

            return response()->json([
                'message' => 'region updated successfully.',
                'region' => $region
            ], 200);
        } catch (\Exception $e) {
            // Log error if region update fails
            Log::error('region Update Failed:', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to update region.'], 500);
        }
    }

    /**
     * Delete a region.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $region = Region::findOrFail($id);
            $region->delete();

            Log::info('Region Deleted:', ['region_id' => $id]);

            return response()->json(['message' => 'Region deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this region.'], 403);
    }
}
