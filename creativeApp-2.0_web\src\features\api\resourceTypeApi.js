import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const resourceTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getResourceTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `resource-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ResourceTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForResourceType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `resource-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ResourceTypeData'],
    }),

    getResourceTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `resource_types/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ResourceTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createResourceType: builder.mutation({
      query: (newFormationType) => ({
        url: 'resource-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ResourceTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateResourceType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `resource_types/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ResourceTypeData', id }, 'ResourceTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteResourceType: builder.mutation({
      query: (id) => ({
        url: `resource_types/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ResourceTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetResourceTypeDataQuery,
  useLazyFetchDataOptionsForResourceTypeQuery,
  useGetResourceTypeByIdQuery,
  useLazyGetResourceTypeByIdQuery,
  useCreateResourceTypeMutation,
  useUpdateResourceTypeMutation,
  useDeleteResourceTypeMutation,
} = resourceTypeApi;
