import React, { useState } from 'react';

function OneLineText3() {
  const [inputValue, setInputValue] = useState('');
  const [groupedByTitle, setGroupedByTitle] = useState({});
  const [selectedTitles, setSelectedTitles] = useState({});
  const [deletedTitles, setDeletedTitles] = useState([]);
  const [outputValues, setOutputValues] = useState('');
  const [wordCount, setWordCount] = useState(0);

  const handleChange = (event) => setInputValue(event.target.value);

  const handleProcessInput = () => {
    if (!inputValue.trim()) {
      setGroupedByTitle({});
      setSelectedTitles({});
      setDeletedTitles([]);
      setOutputValues('');
      setWordCount(0);
      return;
    }

    const lines = inputValue.split('\n');
    const tempGroup = {};

    lines.forEach((line) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;

      const parts = trimmedLine.split(/:(.+)/);
      if (parts.length >= 2) {
        const title = parts[0].trim();
        const value = parts[1].trim().replace(/\s+/g, ' ');
        if (!tempGroup[title]) tempGroup[title] = [];
        tempGroup[title].push({ title, value });
      }
    });

    setGroupedByTitle(tempGroup);
    setSelectedTitles({});
    setDeletedTitles([]);
    updateOutput(tempGroup, {}, []);
  };

  const updateOutput = (group, selectedMap, deletedList) => {
    const values = [];

    for (const [title, valueArr] of Object.entries(group)) {
      if (deletedList.includes(title)) continue;
      let value = selectedMap[title] || valueArr[0].value;
      values.push(value);
    }

    const joined = values.join(' ').replace(/\s+/g, ' ').trim();
    const cleaned = joined.replace(/[.,!?;:"'`~(){}\[\]]/g, '');
    const wordsArray = cleaned.split(/\s+/).filter(Boolean);

    setOutputValues(joined);
    setWordCount(wordsArray.length);
  };

  const handleRadioChange = (title, value) => {
    const updated = { ...selectedTitles, [title]: value };
    setSelectedTitles(updated);
    updateOutput(groupedByTitle, updated, deletedTitles);
  };

  const handleCheckboxChange = (title) => {
    const updated = deletedTitles.includes(title)
      ? deletedTitles.filter((t) => t !== title)
      : [...deletedTitles, title];
    setDeletedTitles(updated);
    updateOutput(groupedByTitle, selectedTitles, updated);
  };

  const handleSelectAll = () => {
    const allTitles = Object.keys(groupedByTitle);
    setDeletedTitles(allTitles);
    updateOutput(groupedByTitle, selectedTitles, allTitles);
  };

  const handleDeselectAll = () => {
    setDeletedTitles([]);
    updateOutput(groupedByTitle, selectedTitles, []);
  };

  const handleCopy = () => {
    if (outputValues) {
      navigator.clipboard
        .writeText(outputValues)
        .then(() => alert('Copied to clipboard!'))
        .catch((err) => {
          alert('Failed to copy!');
          console.error(err);
        });
    }
  };

  const handleClear = () => {
    setInputValue('');
    setGroupedByTitle({});
    setSelectedTitles({});
    setDeletedTitles([]);
    setOutputValues('');
    setWordCount(0);
  };

  return (
    <div style={{ maxWidth: '700px', margin: '40px auto', padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <label htmlFor="titleValueInput" style={{ display: 'block', marginBottom: '8px' }}>
        Paste your <strong>Title: Value</strong> pairs here (one per line):
      </label>
      <textarea
        id="titleValueInput"
        rows="6"
        value={inputValue}
        onChange={handleChange}
        style={{ width: '100%', padding: '10px', fontSize: '14px', marginBottom: '12px' }}
        placeholder="Example:\nName: John\nAge: 25\nCity: Dhaka"
      />
      <div style={{ marginBottom: '16px' }}>
        <button onClick={handleProcessInput} style={buttonStyle}>Process</button>
        <button onClick={handleClear} style={buttonStyle}>Clear</button>
      </div>

      {Object.keys(groupedByTitle).length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3>Parsed Pairs</h3>
            <div>
              <button onClick={handleSelectAll} style={miniButton}>Select All</button>
              <button onClick={handleDeselectAll} style={miniButton}>Deselect All</button>
            </div>
          </div>

          <div style={{ marginTop: '10px' }}>
            {Object.entries(groupedByTitle).map(([title, values]) => {
              const selectedValue = selectedTitles[title] || values[0].value;
              const isDeleted = deletedTitles.includes(title);
              return (
                <div key={title} style={pairBox}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '6px' }}>
                    <input
                      type="checkbox"
                      checked={isDeleted}
                      onChange={() => handleCheckboxChange(title)}
                    />
                    <strong>{title}</strong>
                  </div>
                  <div style={{ marginLeft: '26px' }}>
                    {values.length > 1 ? (
                      values.map((val, idx) => (
                        <label key={idx} style={{ display: 'block', marginBottom: '4px', cursor: 'pointer' }}>
                          <input
                            type="radio"
                            name={`select-${title}`}
                            value={val.value}
                            checked={selectedTitles[title] === val.value || (!selectedTitles[title] && idx === 0)}
                            onChange={() => handleRadioChange(title, val.value)}
                          />{' '}
                          {val.value}
                        </label>
                      ))
                    ) : (
                      <div>{values[0].value}</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          <h3 style={{ marginTop: '20px' }}>Output Values</h3>
          <div
            style={{
              background: '#f9f9f9',
              padding: '10px',
              borderRadius: '4px',
              display: 'flex',
              flexWrap: 'wrap',
              gap: '8px',
            }}
          >
            {outputValues
              .split(' ')
              .filter(Boolean)
              .map((val, idx) => (
                <span
                  key={idx}
                  style={{
                    background: '#e0e0e0',
                    padding: '6px 10px',
                    borderRadius: '20px',
                  }}
                >
                  {val}
                </span>
              ))}
          </div>
          <p style={{ marginTop: '10px' }}>
            <strong>Word Count:</strong> {wordCount}
          </p>
          <button onClick={handleCopy} style={buttonStyle}>Copy to Clipboard</button>
        </div>
      )}
    </div>
  );
}

const buttonStyle = {
  padding: '8px 16px',
  marginRight: '10px',
  backgroundColor: '#007bff',
  color: '#fff',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
};

const miniButton = {
  padding: '6px 12px',
  marginLeft: '6px',
  backgroundColor: '#6c757d',
  color: '#fff',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
};

const pairBox = {
  border: '1px solid #ccc',
  borderRadius: '6px',
  padding: '10px 15px',
  marginBottom: '12px',
  background: '#fff',
};

export default OneLineText3;
