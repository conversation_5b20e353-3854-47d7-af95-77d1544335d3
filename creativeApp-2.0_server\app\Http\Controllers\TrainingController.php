<?php

namespace App\Http\Controllers;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
use App\Models\Training;
use App\Models\Training_category;
use Illuminate\Support\Facades\Log;

class TrainingController extends Controller
{
    /**
     * Show all Trainings with relevant relationships.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        // Retrieve all branches with their associated locations
        $trainings = Training::all();
        
        // Log the number of branches retrieved
        Log::info('All Training Retrieved:', ['count' => $trainings->count()]);
        
        // Return the branches as a JSON response
        return response()->json(['trainings' => $trainings], 200);
    }
    
    
    public function show($id)
    {
        // Find the training by ID with its associated training category
        $training = Training::find($id);
    
        // Log the training retrieved
        Log::info('Training Retrieved:', ['training' => $training]);
    
        return response()->json(['training' => $training], 200);
    }


    /**
     * Create a new Training by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
    
        // Validate the request data
        $validatedData = $request->validate([
            'department' => 'nullable|integer|max:255',
            'team' => 'nullable|integer|max:255',
            'title' => 'required|string|max:255',
            'trainer' => 'nullable|integer|max:255',
            'arrange_by' => 'nullable|string|max:255',
            'category_id' => 'nullable|exists:training_categories,id', // Ensuring the category exists
            'topic_id' => 'nullable|exists:training_topics,id', // Ensuring the topic exists
            'date' => 'nullable|date',
            'time' => 'nullable|date_format:H:i:s', // Ensure format matches the database time type
            'duration' => 'nullable|date_format:H:i:s', // Ensure format matches the database time type
            'presentation_url' => 'nullable|string|max:255',
            'record_url' => 'nullable|string|max:255',
            'access_passcode' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'tags' => 'nullable|string|max:255',
            'evaluation_form' => 'nullable|string|max:255',
            'response' => 'nullable|string|max:255',
        ]);
    
        // Log the request data
        Log::info('Create Training Request:', ['request' => $request->all()]);
    
        // Check if the training name already exists
        if (Training::where('title', $request->title)->exists()) {
            return response()->json(['error' => 'Training already exists.'], 409);
        }
    
        // Check if the user has the appropriate role
        if (!$authUser->roles()->where('name', 'super-admin')->exists() && !$authUser->roles()->where('name', 'admin')->exists()) {
            Log::warning('Unauthorized Training Creation Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to create a training.'], 403);
        }
    
        // Create the training
        $training = Training::create([
            'department' => $request->department,
            'team' => $request->team,
            'title' => $request->title,
            'trainer' => $request->trainer,
            'arrange_by' => $request->arrange_by,
            'category_id' => $request->category_id,
            'topic_id' => $request->topic_id,
            'date' => $request->date,
            'time' => $request->time,
            'duration' => $request->duration,
            'presentation_url' => $request->presentation_url,
            'record_url' => $request->record_url,
            'access_passcode' => $request->access_passcode,
            'location' => $request->location,
            'tags' => $request->tags,
            'evaluation_form' => $request->evaluation_form,
            'response' => $request->response,
            'created_by' => $authUser->id,
            'updated_by' => $authUser->id,
        ]);
    
        // Log the training creation
        Log::info('Training Created:', ['training' => $training]);
    
        return response()->json(['message' => 'Training created successfully.', 'training' => $training], 201);
    }
    

    /**
     * Update an existing Training by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);
    
        // Validate the request data
        $validatedData = $request->validate([
            'department' => 'nullable|integer|max:255',
            'team' => 'nullable|integer|max:255',
            'title' => 'required|string|max:255',
            'trainer' => 'nullable|integer|max:255',
            'arrange_by' => 'nullable|string|max:255',
            'category_id' => 'nullable|exists:training_categories,id', // Ensuring the category exists
            'topic_id' => 'nullable|exists:training_topics,id', // Ensuring the topic exists
            'date' => 'nullable|date',
            'time' => 'nullable|date_format:H:i:s', // Ensure format matches the database time type
            'duration' => 'nullable|date_format:H:i:s', // Ensure format matches the database time type
            'presentation_url' => 'nullable|string|max:255',
            'record_url' => 'nullable|string|max:255',
            'access_passcode' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'tags' => 'nullable|string|max:255',
            'evaluation_form' => 'nullable|string|max:255',
            'response' => 'nullable|string|max:255',
        ]);
    
        // Log the request data
        Log::info('Update Training Request:', ['request' => $request->all()]);
    
        // Find the training by ID
        $training = Training::find($id);
    
        if (!$training) {
            return response()->json(['error' => 'Training not found.'], 404);
        }
    
        // Check if the user has the appropriate role
        if (!$authUser->roles()->where('name', 'super-admin')->exists() && !$authUser->roles()->where('name', 'admin')->exists()) {
            Log::warning('Unauthorized Training Update Attempt:', ['user_id' => $authUser->id]);
            return response()->json(['error' => 'You do not have permission to update this training.'], 403);
        }
    
        // Check if the training title is being updated and if it already exists
        if ($training->title !== $request->title && Training::where('title', $request->title)->exists()) {
            return response()->json(['error' => 'Training Title already exists.'], 409);
        }
    
        // Update the training
        $training->update([
            'department' => $request->department,
            'team' => $request->team,
            'title' => $request->title,
            'trainer' => $request->trainer,
            'arrange_by' => $request->arrange_by,
            'category_id' => $request->category_id,
            'topic_id' => $request->topic_id,
            'date' => $request->date,
            'time' => $request->time,
            'duration' => $request->duration,
            'presentation_url' => $request->presentation_url,
            'record_url' => $request->record_url,
            'access_passcode' => $request->access_passcode,
            'location' => $request->location,
            'tags' => $request->tags,
            'evaluation_form' => $request->evaluation_form,
            'response' => $request->response,
            'updated_by' => $authUser->id,
        ]);
    
        // Log the training update
        Log::info('Training Updated:', ['training' => $training]);
    
        return response()->json(['message' => 'Training updated successfully.', 'training' => $training], 200);
    }
    

    /**
     * Delete a Training by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $training = Training::find($id);

            if (!$training) {
                return response()->json(['error' => 'Training not found.'], 404);
            }

            $training->delete();

            Log::info('Training Deleted', ['training_id' => $training->id]);

            return response()->json(['message' => 'Training deleted successfully.'], 200);
        }

        Log::warning('Unauthorized Training Deletion Attempt', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to delete this training.'], 403);
    }
}
