import React, { useEffect, useState } from 'react';
import EditAboutTheApp from './EditAboutTheApp';
import { useNavigate } from 'react-router-dom';
import { useRoleBasedAccess } from '../../common/useRoleBasedAccess';
import {API_URL} from './../../common/fetchData/apiConfig.js';

const TOKEN_KEY = 'token';

const getToken = () => localStorage.getItem(TOKEN_KEY);
const isTokenValid = () => !!getToken();

const AboutTheAppList = () => {
    const navigate = useNavigate();
    const [aboutTheApps, setAboutTheApps] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedAboutTheAppId, setSelectedAboutTheAppId] = useState(null);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const { rolePermissions } = useRoleBasedAccess();

    // Fetch "About the App" data
    useEffect(() => {
        const fetchAboutTheApps = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            try {
                const response = await fetch(`${API_URL}about-the-apps`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch data: ${response.statusText}`);
                }

                const data = await response.json();

                if (Array.isArray(data.aboutTheApp || data)) {
                    setAboutTheApps(
                        data.aboutTheApp.map((app) => ({
                            id: app.id,
                            definition: app.definition,
                        }))
                    );
                } else {
                    setError('Unexpected data format received.');
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchAboutTheApps();
    }, []);

    // Handle delete action
    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}about-the-app/${id}`, {
                method: 'DELETE',
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to delete entry: ${response.statusText}`);
            }

            setAboutTheApps((prev) => prev.filter((app) => app.id !== id));
        } catch (error) {
            console.error('Error deleting data:', error);
            setError(error.message);
        }
    };

    // Handle edit action
    const handleEdit = (id) => {
        setSelectedAboutTheAppId(id);
        setModalVisible(true);
    };

    if (loading) return <div>Loading...</div>;
    if (error) return <div className="text-red-500">{error}</div>;

    return (
        <div className="py-6">
            <div className="flex flex-wrap -mx-2 justify-end">
            {rolePermissions.hasAdminRole && (
                <div className="flex justify-end px-4 mt-4">
                    <button
                        onClick={() => navigate('/add-about-the-app')}
                        className="px-4 py-2 border border-gray-300 text-black rounded-md bg-white hover:bg-white hover:text-black-600"
                    >
                        Add New About The App
                    </button>
                </div>
            )}
                {aboutTheApps.map((app) => (
                    <div key={app.id} className="w-full sm:w-1/1 md:w-1/1 p-4">
                        <div className="bg-white shadow-sm rounded-xl border border-gray-200 p-4">
                            <div
                                className="text-gray-600 mb-4 ql-editor ql-snow"
                                dangerouslySetInnerHTML={{ __html: app.definition }}
                            />
                            {rolePermissions.hasAdminRole && (
                                <div className="flex justify-end gap-4">
                                    <button
                                        onClick={() => handleEdit(app.id)}
                                        className="px-4 py-2 border border-gray-300 text-black rounded-md hover:bg-gray-100"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => handleDelete(app.id)}
                                        className="px-4 py-2 border border-gray-300 text-black rounded-md hover:bg-gray-100"
                                    >
                                        Delete
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            

            {modalVisible && (
                <EditAboutTheApp
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    aboutTheAppId={selectedAboutTheAppId}
                />
            )}
        </div>
    );
};

export default AboutTheAppList;
