import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditLocation = ({ isVisible, setVisible, dataItemsId }) => {
    const [locationName, setLocationName] = useState('');
    const [locations, setLocations] = useState([]);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchLocationName = async () => {
            if (!dataItemsId) return;
    
            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }
    
            try {
                const response = await fetch(`${API_URL}/locations`, { // Update endpoint for locations
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch locations: ' + response.statusText);
                }
    
                const data = await response.json();
    
                const locationArray = data['locations']; // Adjusted to use 'locations' data
                if (!Array.isArray(locationArray)) {
                    throw new Error('Expected locations to be an array.');
                }
    
                // Find the location by ID
                const locationData = locationArray.find(location => location.id === dataItemsId);
                if (locationData) {
                    setLocationName(locationData.locations_name); // Set the name from the matching location
                } else {
                    throw new Error('Location not found. Please check the ID.');
                }
            } catch (error) {
                setError(error.message);
            }
        };
    
        fetchLocationName();
    }, [dataItemsId]);
    
    // Update the Location
    const handleSubmit = async (event) => {
        event.preventDefault();
        const trimmedLocationName = locationName.trim();

        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }
    
        if (Array.isArray(locations)) {
            const locationExists = locations.some(location => {
                const locationNameLower = location.locations_name.toLowerCase().trim();
                return locationNameLower === trimmedLocationName.toLowerCase();
            });
    
            if (locationExists) {
                setError('Location already exists. Please add a different location.');
                const timeoutId = setTimeout(() => setError(''), 3000);
                return () => clearTimeout(timeoutId);
            }
        }
    
        setError('');
    
        try {
            const token = localStorage.getItem('token');
    
            const response = await fetch(`${API_URL}/locations`, { // Update endpoint for locations
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    locations_name: trimmedLocationName,
                    updated_by: updatedBy,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save location: ' + response.statusText);
            }
    
            const result = await response.json();
    
            const updatedLocationName = result.location.locations_name; // Adjusted to use the 'location' object
    
            //setSuccessMessage(`Location "${updatedLocationName}" updated successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Location updated successfully.',
            });

            setLocationName('');
    
            // Optionally, refetch locations
            const newLocationsResponse = await fetch(`${API_URL}/locations`, { // Update endpoint for locations
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newLocationsResponse.ok) {
                throw new Error('Failed to fetch locations: ' + newLocationsResponse.statusText);
            }
    
            const newLocationsData = await newLocationsResponse.json();
            setLocations(newLocationsData['locations'] || []);
            // Close the modal after a short delay
            setTimeout(() => {
                setVisible(false);
                setSuccessMessage(''); // Clear the success message
            }, 1000);
            
        } catch (error) {
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full p-5"
                onClick={(e) => e.stopPropagation()} // Prevent click from closing the modal
            >
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Update Location</h3>
                    <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2">Location Name</label>
                        <input
                            type="text"
                            id="name"
                            value={locationName}
                            onChange={(e) => setLocationName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-lg px-4 py-2"
                    >
                        Update Location
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditLocation;
