import { Navigate } from 'react-router-dom';
import FetchLoggedInRole from './../common/fetchData/FetchLoggedInRole';

const ProtectedRoute = ({ children, requiredRoles }) => {
  const { userData, loading, error } = FetchLoggedInRole();

  // Loading state while the user data is being fetched
  if (loading) {
    return <div>Loading...</div>;
  }

  // Error state if fetching user data fails
  if (error) {
    return <div>Error: {error}</div>;
  }

  // If no user data (not logged in), redirect to the login page
  if (!userData) {
    return <Navigate to="/login" />;
  }

  // Check if the user has at least one of the required roles
  const hasAccess = requiredRoles.some(role => userData.roles.includes(role));

  // If the user doesn't have access (doesn't have the required role), redirect to Unauthorized Page
  if (!hasAccess) {
    return <Navigate to="/unauthorized" />;
  }

  // If the user has access, render the child components (protected page content)
  return children;
};

export default ProtectedRoute;
