import React from 'react';
import TableLayoutWrapper2 from '../../common/table/TableLayoutWrapper2';
import TableHeader from '../../common/table/TableHeader';
import TablePagination from '../../common/table/TablePagination';
import RegionList from '../../pages/region/RegionList'; // Updated import to TaskTypeList

const Region = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-region" buttonName="Add Region" /> {/* Updated route and button name */}
        <RegionList /> {/* Updated component to TaskTypeList */}
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default Region;
