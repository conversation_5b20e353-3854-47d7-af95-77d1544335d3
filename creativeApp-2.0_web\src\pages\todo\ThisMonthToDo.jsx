import React, { useEffect, useState } from 'react';

// Check if the token is available and valid
const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const ThisMonthToDo = () => {
    return (
        <>
            <h1>This is ThisMonthToDo Dashboard</h1>
        </>
    );
}

export default ThisMonthToDo;