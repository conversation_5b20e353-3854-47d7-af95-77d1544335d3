import React from 'react';
import TableLayoutWrapper2 from '../common/table/TableLayoutWrapper2';
import TableHeader from '../common/table/TableHeader';
import ReportProblemList from '../pages/report-problem/ReportProblemList';
import { Link } from 'react-router-dom';

const Reportproblem = () => {
  return (
    <div className='bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]'>
      <h2 className="text-2xl font-bold mb-4">Report Problem</h2>
      
            <div className="flex justify-end mb-4">
        <Link
          to="/add-report-problem"
          className="flex items-center justify-center py-2 px-4 text-sm font-medium text-black bg-white hover:bg-gray-100 border border-gray-300 rounded-lg"
        >
          Add New Report Problem
        </Link>
      </div>

        <ReportProblemList />
      
    </div>
  );
};

export default Reportproblem;
