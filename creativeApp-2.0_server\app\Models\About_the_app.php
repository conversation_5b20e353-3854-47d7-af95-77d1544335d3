<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class About_the_app extends Model
{
    use HasFactory;

    protected $fillable = [
        'definition',
        'created_by',
        'updated_by'
    ];
    
    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
    
    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
