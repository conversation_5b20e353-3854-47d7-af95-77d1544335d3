<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Auth\Notifications\VerifyEmail as BaseVerifyEmail;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\URL;

class VerifyEmail extends Notification
{
    public function via($notifiable)
    {
        return ['mail'];
    }

    protected function verificationUrl($notifiable)
    {
        return URL::temporarySignedRoute(
            'verification.verify',
            Carbon::now()->addMinutes(60),
            ['id' => $notifiable->getKey(), 'hash' => sha1($notifiable->getEmailForVerification())]
        );
    }

    public function toMail($notifiable)
    {
        $url = $this->verificationUrl($notifiable);

        return (new MailMessage)
            ->subject('Verify Your Email Address')
            ->greeting('Hello! 👋')
            ->line('To activate your account and verify your email address, please click the button below:')
            ->action('Verify Your Email', $url)
            ->line('If you are having trouble clicking the button, you can also use the following URL in your web browser:')
            ->line($url)
            ->line('If your account is not working after verification, please contact <NAME_EMAIL>.')
            ->line("Regards,\nThe SEBPO Creative Team")
            ->line('')
            ->salutation('This email and any accompanying attachments are intended only to be read or used by the named addressee(s). It is confidential and contains legally privileged information. If you have received this message in error, please notify the sender immediately and delete the message.');
    }
}
