import React, { useEffect, useState } from "react";
import moment from "moment";
import "moment-timezone";

import { fetchWeather<PERSON><PERSON> } from "openmeteo";


const timeZone = [
    {
      label: "BD/Dhaka",
      timeZone: "Asia/Dhaka",
    },
  
    {
      label: "PST/PDT",
      timeZone: "America/Los_Angeles",
    },
  
    {
      label: "EST/EDT",
      timeZone: "America/New_York",
    },
  
    {
      label: "CST/CDT",
      timeZone: "America/Chicago",
    },
    {
      label: "UK/London/BST",
      timeZone: "Europe/London",
    },
  
    {
      label: "ECT",
      timeZone: "Europe/Berlin",
    },
  
    {
      label: "CEST",
      timeZone: "Europe/Riga",
    },
  
    {
      label: "Singapore",
      timeZone: "Asia/Singapore",
    },
  
    {
      label: "Gulf/GST",
      timeZone: "Asia/Dubai",
    },
  
    {
      label: "Middle EST.",
      timeZone: "Asia/Tehran",
    },
  
    {
      label: "Japan/Tokyo",
      timeZone: "Asia/Tokyo",
    },
  
    {
      label: "Australia/Sydney",
      timeZone: "Australia/Sydney",
    },
  ];

function DynamicTimeCard() {

    const defaultDateFormat = "dddd, LL";
  const defaultTimeFormat = "hh:mm A";

    const [defaultTimeZone, setDefaultTimeZone] = useState(timeZone[0]); // Store team data
      const [currentDateTime, setCurrentDateTime] = useState(
        moment().tz(defaultTimeZone["timeZone"])
      ); // Store team data
      const [error, setError] = useState(null); // Handle errors
      const [loading, setLoading] = useState(true); // Loading state
      const [weatherData, setWeatherData] = useState(null);
      const [ipData, setIpData] = useState(false);

      const params = {
        latitude: ipData?.lat,
        longitude: ipData?.lon,
        hourly: [
          "temperature_2m",
          "relative_humidity_2m",
          "apparent_temperature",
          "precipitation_probability",
          "precipitation",
          "rain",
          "visibility",
          "wind_speed_10m",
          "uv_index",
        ],
        daily: ["weather_code", "sunrise", "sunset"],
        timeformat: "unixtime",
        timezone: "auto",
        past_days: 0,
        forecast_days: 6,
      };
    

      // Example usage
        const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp
        const roundedHour = getRoundedHour(unixTime);


         function getRoundedHour(unixTimestamp) {
            // Create a date object from the Unix timestamp (in seconds)
            const date = new Date(unixTimestamp * 1000);
        
            // Get hours, minutes, and seconds
            const hours = date.getUTCHours() + 6;
            const minutes = date.getUTCMinutes();
            const seconds = date.getUTCSeconds();
        
            // Determine the rounded hour
            let roundedHour;
            if (minutes > 31 || seconds > 31) {
              roundedHour = hours + 1; // Round up to the next hour
            } else {
              roundedHour = hours; // Keep the same hour
            }
        
            // Return the rounded hour (in 24-hour format)
            return roundedHour % 24; // Ensure it wraps around at 24
          }


           // Fetch teams data
            useEffect(() => {
              const fetchIP = async () => {
                try {
                  const response = await fetch(`//ip-api.com/json/`, { method: "GET" });
          
                  if (!response.ok) {
                    throw new Error("Failed to fetch teams: " + response.statusText);
                  }
          
                  const data = await response.json();
                  setIpData(data);
                  fetchCurrentDateTimeByIP(data["query"]);
                } catch (error) {
                  setError(error.message);
                } finally {
                  setLoading(false);
                }
              };
          
              fetchIP();
            }, []);
          
            const fetchCurrentDateTimeByIP = async (ip = "") => {
              try {
                if (ip) {
                  const response = await fetch(
                    `https://timeapi.io/api/time/current/ip?ipAddress=${ip}`,
                    { method: "GET" }
                  );
          
                  setLoading(false);
          
                  if (!response.ok) {
                    throw new Error("Failed to fetch teams: " + response.statusText);
                  }
          
                  const responseData = await response.json();
          
                  if (responseData) {
                    setDefaultTimeZone({ ...defaultTimeZone, responseData });
                    setCurrentDateTime(moment(responseData["dateTime"]));
                  }
                }
              } catch (error) {
                setError(error.message);
              } finally {
                setLoading(false);
              }
            };
          
            useEffect(() => {
              if (!ipData) return;
              const fetchWeather = async () => {
                try {
                  const url = "https://api.open-meteo.com/v1/forecast";
                  const responses = await fetchWeatherApi(url, params);
                  const range = (start, stop, step) =>
                    Array.from(
                      { length: (stop - start) / step },
                      (_, i) => start + i * step
                    );
                  const response = responses[0];
          
                  const utcOffsetSeconds = response.utcOffsetSeconds();
          
                  const hourly = response.hourly();
                  const daily = response.daily();
          
                  const weather = {
                    hourly: {
                      time: range(
                        Number(hourly.time()),
                        Number(hourly.timeEnd()),
                        hourly.interval()
                      ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                      temperature2m: hourly.variables(0).valuesArray(),
                      relativeHumidity2m: hourly.variables(1).valuesArray(),
                      apparentTemperature: hourly.variables(2).valuesArray(),
                      precipitationProbability: hourly.variables(3).valuesArray(),
                      precipitation: hourly.variables(4).valuesArray(),
                      rain: hourly.variables(5).valuesArray(),
                      visibility: hourly.variables(6).valuesArray(),
                      windSpeed10m: hourly.variables(7).valuesArray(),
                      uvIndex: hourly.variables(8).valuesArray(),
                    },
                    daily: {
                      time: range(
                        Number(daily.time()),
                        Number(daily.timeEnd()),
                        daily.interval()
                      ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                      weatherCode: daily.variables(0).valuesArray(),
                      sunrise: daily.variables(1).valuesArray(),
                      sunset: daily.variables(2).valuesArray(),
                    },
                  };
          
                  setWeatherData(weather);
                } catch (err) {
                  setError(err);
                } finally {
                  setLoading(false);
                }
              };
              fetchWeather();
            }, [ipData]);
          
            const updateTime = () => {
              // let updateTime = moment(defaultTimeZone["dateTime"]);
              // let formateDateTime = updateTime.tz(defaultTimeZone["timeZone"]).format(defaultTimeZone["timeFormat"])
              setCurrentDateTime(moment().tz(defaultTimeZone["timeZone"]));
            };
          
            // updateTime()
            setInterval(updateTime, 1000 * 1);
          


  return (
    <>
          {loading && <p>Loading...</p>}
          {!loading && (
            <>
              <h1 className=" text-[48px] font-bold mt-[5px]">
                {currentDateTime.tz(defaultTimeZone["timeZone"]).format("LTS")}
              </h1>

              <div className="flex justify-start items-start flex-row mt-[15px]">
                <div className="w-3/5 text-start ">
                  <h1 className="text-2xl   font-semibold ">{`${ipData?.city}, ${ipData?.country}`}</h1>
                  {/* <h1 className="text-2xl font-bold">{defaultTimeZone["label"]}</h1> */}
                  {/* <p className="font-bold">{defaultTimeZone["label"]}</p> */}
                  <p className="font-normal">{defaultTimeZone["timeZone"]}</p>
                  {/* <p>{currentDateTime.tz(defaultTimeZone["timeZone"]).format("dddd") || ""}</p> */}
                  <p className="font-normal">
                    {currentDateTime
                      .tz(defaultTimeZone["timeZone"])
                      .format(defaultDateFormat) || ""}
                  </p>
                </div>
                {weatherData && (
                  <div className="w-2/5 text-end">
                    <h1 className=" text-2xl font-semibold ">
                      {Math.round(
                        weatherData.hourly.temperature2m[roundedHour]
                      )}
                      <sup>°C</sup>
                    </h1>
                    <p className="font-normal">
                      Feels like:{" "}
                      {Math.round(
                        weatherData.hourly.apparentTemperature[roundedHour]
                      )}
                      <sup>°C</sup>
                    </p>
                    <p className="font-normal">
                      Humidity:{" "}
                      {weatherData.hourly.relativeHumidity2m[roundedHour]}%
                    </p>
                    
                  </div>
                )}
              </div>
            </>
          )}
        </>
  )
}

export default DynamicTimeCard