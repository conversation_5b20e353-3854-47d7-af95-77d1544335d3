import { Link } from "react-router-dom";
import AddTag from "../tag/AddTag";
import AddTodo from "./AddTodo";

const TodoHeader = ({ title, setFetchData }) => {


    return (
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
            <div className="w-full md:w-1/2">
                <h2 className="px-2 w-full text-left font-bold text-3xl text-gray-700 pb-2 capitalize">{title} To-Do</h2>
            </div>
            <div className="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                <div className="flex items-center space-x-3 w-full md:w-auto">
                    {/* Modal toggle */}
                    <AddTag setFetchData={setFetchData} />
                    <AddTodo />
                </div>
            </div>
        </div>
    );
}

export default TodoHeader;