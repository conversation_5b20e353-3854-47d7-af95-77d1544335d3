import { baseApi } from './baseApi';
import { alertMessage } from '../../common/coreui';

export const userDataApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get user data
    getUserData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `user-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        return queryString;
      },
      providesTags: ['UserData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    // Get options for user data groups (e.g., filtering options)
    fetchDataOptionsForUserData: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '', table = '' }) => {
        let queryString = `user-data-${type}?column=${column}&table=${table}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['UserData'],
    }),

    // Get user data by ID
    getUserDataById: builder.query({
      query: (id) => {
        if (id == null || id == undefined) {
          id = '';
        }
        return `users/${id}`;
      },
      providesTags: (result, error, id) => [{ type: 'UserData', id }],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    // Create a new user
    createUserData: builder.mutation({
      query: (newUserData) => ({
        url: 'users',
        method: 'POST',
        body: newUserData,
      }),
      invalidatesTags: ['UserData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    // Update user data by ID
    updateUserData: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `users/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'UserData', id }, 'UserData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    // Delete user data by ID
    deleteUserData: builder.mutation({
      query: (id) => ({
        url: `users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['UserData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetUserDataQuery,
  useLazyFetchDataOptionsForUserDataQuery,
  useGetUserDataByIdQuery,
  useLazyGetUserDataByIdQuery,
  useCreateUserDataMutation,
  useUpdateUserDataMutation,
  useDeleteUserDataMutation,
} = userDataApi;
