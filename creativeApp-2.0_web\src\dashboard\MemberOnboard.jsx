import React, { useState } from 'react';
import MemberOnboardDataList from '../pages/team-member/MemberOnboardDataList';

const MemberOnboard = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Handle search input changes
  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm); // Update the search term state in the parent
  };


  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <MemberOnboardDataList />
    </div>
  );
};

export default MemberOnboard;
