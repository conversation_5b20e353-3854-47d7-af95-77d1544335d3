<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TeamController; 
use App\Http\Controllers\RoleController; 
use App\Http\Controllers\DepartmentController; 
use App\Http\Controllers\BillingStatusController; 
use App\Http\Controllers\ResourceStatusController; 
use App\Http\Controllers\ResourceTypeController; 
use App\Http\Controllers\DesignationController; 
use App\Http\Controllers\BloodGroupController; 
use App\Http\Controllers\AvailableStatusController; 
use App\Http\Controllers\ContactTypeController; 
use App\Http\Controllers\MemberStatusController; 
use App\Http\Controllers\OnsiteStatusController; 
use App\Http\Controllers\ScheduleController; 
use App\Http\Controllers\LocationController; 
use App\Http\Controllers\BranchController; 
use App\Http\Controllers\TaskDetailsController; 
use App\Http\Controllers\TimeCardController; 
use App\Http\Controllers\CurrentTimeController; 

/*----------------------Abdur Rahman ----------------------------*/

use App\Http\Controllers\HolidayCalenderController; 
use App\Http\Controllers\QuickAccessHubController; 
use App\Http\Controllers\TrainingController; 
use App\Http\Controllers\TrainingCategoryController;
use App\Http\Controllers\TrainingTopicsController;
use App\Http\Controllers\TaskTypeController;
use App\Http\Controllers\SlaAchieveController;
use App\Http\Controllers\RevisionTypeController;
use App\Http\Controllers\ProductTypeController;
use App\Http\Controllers\RegionController;
use App\Http\Controllers\ReviewRleaseController;
use App\Http\Controllers\ReporterController;
use App\Http\Controllers\PriorityController;
use App\Http\Controllers\RecordTypeController;
use App\Http\Controllers\PasswordController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\TeamShiftPlanController;
use App\Http\Controllers\AboutTheAppController;
use App\Http\Controllers\ChangeLogController;
use App\Http\Controllers\AppSupportController;
use App\Http\Controllers\GiveFeedbackController;
use App\Http\Controllers\ReportProblemController;
use App\Http\Controllers\NoticeBoardCategoryController;
use App\Http\Controllers\NoticeController;
use App\Http\Controllers\SeatController;



/*----------------------Imran Ahmed ----------------------------*/
use App\Http\Controllers\TodoController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\TodoTagController;

/*----------------------Razib Hossain ----------------------------*/
use App\Http\Controllers\FormationTypeController;
use App\Http\Controllers\ListDataController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\SchedulePlannerController;
use App\Http\Controllers\DateTimeController;



/*
|----------------------------------------------------------------------
| API Routes
|----------------------------------------------------------------------
*/

Route::post('/login', [AuthController::class, 'login']);
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');

// Route for email verification
Route::get('email/verify/{id}/{hash}', [App\Http\Controllers\Auth\VerificationController::class, 'verify'])
    ->middleware(['signed']) // Only signed middleware, no auth needed
    ->name('verification.verify');


// For API Routes
Route::post('password/email', [PasswordController::class, 'sendResetLinkEmail']);
Route::post('password/reset', [PasswordController::class, 'reset'])->name('password.update');

// Route in your Laravel backend (routes/web.php or routes/api.php)
// Laravel backend route
Route::get('/password/reset/{token}', function ($token) {
    // Here, you can verify the token in your database if necessary
    // For now, just return a successful response
    return response()->json(['message' => 'Password reset link is valid'], 200);
});



Route::get('/datetime/current', [DateTimeController::class, 'getCurrentDateTime']);
Route::post('/datetime/convert', [DateTimeController::class, 'convertTimezone']);
Route::post('/datetime/duration', [DateTimeController::class, 'calculateDuration']);




// Protected Routes (Requires Authentication)
Route::middleware(['auth:sanctum', 'cors', 'verified'])->group(function () {

    // ** Admin and Super Admin Routes **
    Route::middleware('role:super-admin|admin')->group(function () {

        Route::get('/logged-users', [AuthController::class, 'loggedInUser']);

        // User Management (Admin and Super-Admin)
        Route::post('/users', [AuthController::class, 'createUser']);
        Route::get('/users', [AuthController::class, 'index']);
        Route::get('/user-data', [AuthController::class, 'usersData']);
        Route::get('/user-data-group', [AuthController::class, 'group']);
        Route::get('/user-data-field', [AuthController::class, 'searchByField']);
        Route::get('/users/{id}', [AuthController::class, 'show']);
        Route::put('/users/{id}', [AuthController::class, 'update']);
        Route::delete('/users/{id}', [AuthController::class, 'delete']);

        // Roles Management
        Route::post('/roles', [RoleController::class, 'createRole']);
        Route::get('/roles', [RoleController::class, 'index']);
        Route::get('/roles/{id}', [RoleController::class, 'show']);
        Route::put('/roles/{id}', [RoleController::class, 'updateRole']);
        Route::delete('/roles/{id}', [RoleController::class, 'deleteRole']);

        // Team Management
        Route::post('/teams', [TeamController::class, 'createTeam']);
        Route::get('/team-data', [TeamController::class, 'teamData']);
        Route::get('/team-data-group', [TeamController::class, 'group']);
        Route::get('/team-data-field', [TeamController::class, 'searchByField']);
        Route::get('/teams', [TeamController::class, 'index']);
        Route::get('/teams/{id}', [TeamController::class, 'show']);
        Route::put('/teams/{id}', [TeamController::class, 'updateTeam']);
        Route::delete('/teams/{id}', [TeamController::class, 'deleteTeam']);

        // Department Management
        Route::post('/departments', [DepartmentController::class, 'createDepartment']);
        Route::get('/department-data', [DepartmentController::class, 'departmentData']);
        Route::get('/department-data-group', [DepartmentController::class, 'group']);
        Route::get('/department-data-field', [DepartmentController::class, 'searchByField']);
        Route::get('/departments', [DepartmentController::class, 'index']);
        Route::get('/departments/{id}', [DepartmentController::class, 'show']);
        Route::put('/departments/{id}', [DepartmentController::class, 'updateDepartment']);
        Route::delete('/departments/{id}', [DepartmentController::class, 'deleteDepartment']);

        // Designation Management
        Route::post('/designations', [DesignationController::class, 'createDesignation']);
        Route::get('/designation-data', [DesignationController::class, 'designationData']);
        Route::get('/designation-data-group', [DesignationController::class, 'group']);
        Route::get('/designation-data-field', [DesignationController::class, 'searchByField']);
        Route::get('/designations', [DesignationController::class, 'index']);
        Route::get('/designations/{id}', [DesignationController::class, 'show']);
        Route::put('/designations/{id}', [DesignationController::class, 'updateDesignation']);
        Route::delete('/designations/{id}', [DesignationController::class, 'deleteDesignation']);

        // Billing Status Management
        Route::post('/billing_statuses', [BillingStatusController::class, 'createBillingStatus']);
        Route::get('/billing-status-data', [BillingStatusController::class, 'billingStatusData']);
        Route::get('/billing-status-data-group', [BillingStatusController::class, 'group']);
        Route::get('/billing-status-data-field', [BillingStatusController::class, 'searchByField']);
        Route::get('/billing_statuses', [BillingStatusController::class, 'index']);
        Route::get('/billing_statuses/{id}', [BillingStatusController::class, 'show']);
        Route::put('/billing_statuses/{id}', [BillingStatusController::class, 'updateBillingStatus']);
        Route::delete('/billing_statuses/{id}', [BillingStatusController::class, 'deleteBillingStatus']);

        // Resource Status Management
        Route::post('/resource_statuses', [ResourceStatusController::class, 'createResourceStatus']);
        Route::get('/resource-status-data', [ResourceStatusController::class, 'resourceStatusData']);
        Route::get('/resource-status-data-group', [ResourceStatusController::class, 'group']);
        Route::get('/resource-status-data-field', [ResourceStatusController::class, 'searchByField']);
        Route::get('/resource_statuses', [ResourceStatusController::class, 'index']);
        Route::get('/resource_statuses/{id}', [ResourceStatusController::class, 'show']);
        Route::put('/resource_statuses/{id}', [ResourceStatusController::class, 'updateResourceStatus']);
        Route::delete('/resource_statuses/{id}', [ResourceStatusController::class, 'deleteResourceStatus']);

        // Resource Type Management
        Route::post('/resource_types', [ResourceTypeController::class, 'createResourceType']);
        Route::get('/resource-type-data', [ResourceTypeController::class, 'resourceTypeData']);
        Route::get('/resource-type-data-group', [ResourceTypeController::class, 'group']);
        Route::get('/resource-type-data-field', [ResourceTypeController::class, 'searchByField']);
        Route::get('/resource_types', [ResourceTypeController::class, 'index']);
        Route::get('/resource_types/{id}', [ResourceTypeController::class, 'show']);
        Route::put('/resource_types/{id}', [ResourceTypeController::class, 'updateResourceType']);
        Route::delete('/resource_types/{id}', [ResourceTypeController::class, 'deleteResourceType']);

        // Availability Status
        Route::post('/available_statuses', [AvailableStatusController::class, 'store']);
        Route::get('/available-status-data', [AvailableStatusController::class, 'availableStatusData']);
        Route::get('/available-status-data-group', [AvailableStatusController::class, 'group']);
        Route::get('/available-status-data-field', [AvailableStatusController::class, 'searchByField']);
        Route::get('/available_statuses', [AvailableStatusController::class, 'index']);
        Route::get('/available_statuses/{id}', [AvailableStatusController::class, 'show']);
        Route::put('/available_statuses/{id}', [AvailableStatusController::class, 'update']);
        Route::delete('/available_statuses/{id}', [AvailableStatusController::class, 'delete']);

        // Blood Group Management
        Route::post('/bloods', [BloodGroupController::class, 'store']);
        Route::get('/blood-data', [BloodGroupController::class, 'bloodData']);
        Route::get('/blood-data-group', [BloodGroupController::class, 'group']);
        Route::get('/blood-data-field', [BloodGroupController::class, 'searchByField']);
        Route::get('/bloods', [BloodGroupController::class, 'index']);
        Route::get('/bloods/{id}', [BloodGroupController::class, 'show']);
        Route::put('/bloods/{id}', [BloodGroupController::class, 'update']);
        Route::delete('/bloods/{id}', [BloodGroupController::class, 'delete']);

        // Contact Types
        Route::post('/contact_types', [ContactTypeController::class, 'store']);
        Route::get('/contact-type-data', [ContactTypeController::class, 'contactTypeData']);
        Route::get('/contact-type-data-group', [ContactTypeController::class, 'group']);
        Route::get('/contact-type-data-field', [ContactTypeController::class, 'searchByField']);
        Route::get('/contact_types', [ContactTypeController::class, 'index']);
        Route::get('/contact_types/{id}', [ContactTypeController::class, 'show']);
        Route::put('/contact_types/{id}', [ContactTypeController::class, 'update']);
        Route::delete('/contact_types/{id}', [ContactTypeController::class, 'delete']);

        // Team Member Status
        Route::post('/member_statuses', [MemberStatusController::class, 'store']);
        Route::get('/member-status-data', [MemberStatusController::class, 'memberStatusData']);
        Route::get('/member-status-data-group', [MemberStatusController::class, 'group']);
        Route::get('/member-status-data-field', [MemberStatusController::class, 'searchByField']);
        Route::get('/member_statuses', [MemberStatusController::class, 'index']);
        Route::get('/member_statuses/{id}', [MemberStatusController::class, 'show']);
        Route::put('/member_statuses/{id}', [MemberStatusController::class, 'update']);
        Route::delete('/member_statuses/{id}', [MemberStatusController::class, 'delete']);

        // On-site Status
        Route::post('/onsite_statuses', [OnsiteStatusController::class, 'store']);
        Route::get('/onsite-status-data', [OnsiteStatusController::class, 'onsiteStatusData']);
        Route::get('/onsite-status-data-group', [OnsiteStatusController::class, 'group']);
        Route::get('/onsite-status-data-field', [OnsiteStatusController::class, 'searchByField']);
        Route::get('/onsite_statuses', [OnsiteStatusController::class, 'index']);
        Route::get('/onsite_statuses/{id}', [OnsiteStatusController::class, 'show']);
        Route::put('/onsite_statuses/{id}', [OnsiteStatusController::class, 'update']);
        Route::delete('/onsite_statuses/{id}', [OnsiteStatusController::class, 'delete']);

        // Resource Type Management
        Route::post('/schedules', [ScheduleController::class, 'store']);
        Route::get('/schedule-data', [ScheduleController::class, 'scheduleData']);
        Route::get('/schedule-data-group', [ScheduleController::class, 'group']);
        Route::get('/schedule-data-field', [ScheduleController::class, 'searchByField']);
        Route::get('/schedules', [ScheduleController::class, 'index']);
        Route::get('/schedules/{id}', [ScheduleController::class, 'show']);
        Route::put('/schedules/{id}', [ScheduleController::class, 'update']);
        Route::delete('/schedules/{id}', [ScheduleController::class, 'destroy']);

          // Locations
        Route::post('/locations', [LocationController::class, 'createLocation']);
        Route::get('/location-data', [LocationController::class, 'locationData']);
        Route::get('/location-data-group', [LocationController::class, 'group']);
        Route::get('/location-data-field', [LocationController::class, 'searchByField']);
        Route::get('/locations', [LocationController::class, 'index']);
        Route::get('/locations/{id}', [LocationController::class, 'show']);
        Route::put('/locations/{id}', [LocationController::class, 'updateLocation']);
        Route::delete('/locations/{id}', [LocationController::class, 'deleteLocation'])->name('locations.delete');

        // Branch
        Route::post('/branches', [BranchController::class, 'createBranch']);
        Route::get('/branch-data', [BranchController::class, 'branchData']);
        Route::get('/branch-data-group', [BranchController::class, 'group']);
        Route::get('/branch-data-field', [BranchController::class, 'searchByField']);
        Route::get('/branches', [BranchController::class, 'index']);
        Route::get('/branches/{id}', [BranchController::class, 'show']);
        Route::put('/branches/{id}', [BranchController::class, 'updateBranch']);
        Route::delete('/branches/{id}', [BranchController::class, 'deleteBranch'])->name('branches.delete');

        // Holiday Calendar Management
        Route::get('/holiday-calenders', [HolidayCalenderController::class, 'index']);
        Route::post('/holidaycalenders', [HolidayCalenderController::class, 'store']);
        Route::get('/holidaycalenders/{id}', [HolidayCalenderController::class, 'show']);
        Route::put('/holidaycalenders/{id}', [HolidayCalenderController::class, 'update']);
        Route::delete('/holidaycalenders/{id}', [HolidayCalenderController::class, 'destroy'])->name('holidaycalenders.delete');

        Route::get('/holiday-data', [HolidayCalenderController::class, 'holidayData']);
        Route::get('/holiday-data-group', [HolidayCalenderController::class, 'group']);
        Route::get('/holiday-data-field', [HolidayCalenderController::class, 'searchByField']);

        // Route::apiResource('holidaycalenders', HolidayCalenderController::class); 
        // Route::get('holidaycalenders-group', [HolidayCalenderController::class, 'group']);  
        // Route::get('holidaycalenders-field', [HolidayCalenderController::class, 'searchByField']);  

        

        // Quick Access Hub Management
        Route::get('/quick-access-hubs', [QuickAccessHubController::class, 'index']);
        Route::post('/quick-access-hubs', [QuickAccessHubController::class, 'createHub']);
        Route::get('/quick-access-hubs/{id}', [QuickAccessHubController::class, 'show']);
        Route::put('/quick-access-hubs/{id}', [QuickAccessHubController::class, 'updateHub']);
        Route::delete('/quick-access-hubs/{id}', [QuickAccessHubController::class, 'deleteHub']);

        // Training Management
        Route::get('/trainings', [TrainingController::class, 'index']);
        Route::get('/training/{id}', [TrainingController::class, 'show']);
        Route::post('/training', [TrainingController::class, 'store']);
        Route::put('/training/{id}', [TrainingController::class, 'update']);
        Route::delete('/training/{id}', [TrainingController::class, 'destroy']);

         // Training Categories
         Route::get('training-categories', [TrainingCategoryController::class, 'index']);
         Route::get('training-category/{id}', [TrainingCategoryController::class, 'show']);
         Route::post('training-category', [TrainingCategoryController::class, 'store']);
         Route::put('training-category/{id}', [TrainingCategoryController::class, 'update']);
         Route::delete('training-category/{id}', [TrainingCategoryController::class, 'delete']);
 
        // Training Topic
        Route::get('training-topics', [TrainingTopicsController::class, 'index']);
        Route::get('training-topic/{id}', [TrainingTopicsController::class, 'show']);
        Route::post('training-topic', [TrainingTopicsController::class, 'store']);
        Route::put('training-topic/{id}', [TrainingTopicsController::class, 'update']);
        Route::delete('training-topic/{id}', [TrainingTopicsController::class, 'delete']);

        // Task Details Routes
        Route::get('task-details', [TaskDetailsController::class, 'index']);
        Route::get('task-filter-dates', [TaskDetailsController::class, 'filterDueDate']);
        Route::get('task-details-data', [TaskDetailsController::class, 'taskRecordsData']);
        Route::get('task-detail/{id}', [TaskDetailsController::class, 'show']);
        Route::post('task-detail', [TaskDetailsController::class, 'store']);
        Route::put('task-detail/{id}', [TaskDetailsController::class, 'update']);
        Route::delete('task-detail/{id}', [TaskDetailsController::class, 'destroy']);
        Route::get('task-details-data-group', [TaskDetailsController::class, 'group']);  
        Route::get('task-details-data-field', [TaskDetailsController::class, 'searchByField']);  

        // Time Cards
        Route::get('time-cards', [TimeCardController::class, 'index']);
        Route::get('time-cards-data', [TimeCardController::class, 'timeCardsData']);
        Route::get('time-cards-data-group', [TimeCardController::class, 'group']);
        Route::get('time-cards-data-field', [TimeCardController::class, 'searchByField']);
        Route::get('time-card/{id}', [TimeCardController::class, 'show']);
        Route::post('time-card', [TimeCardController::class, 'store']);
        Route::put('time-card/{id}', [TimeCardController::class, 'update']);
        Route::delete('time-card/{id}', [TimeCardController::class, 'destroy']);

          // Task Type
        Route::get('task-types', [TaskTypeController::class, 'index']);
        Route::get('task-type-data', [TaskTypeController::class, 'taskTypesData']);
        Route::get('task-type-data-group', [TaskTypeController::class, 'group']);
        Route::get('task-type-data-field', [TaskTypeController::class, 'searchByField']);
        Route::get('task-type/{id}', [TaskTypeController::class, 'show']);
        Route::post('task-type', [TaskTypeController::class, 'store']);
        Route::put('task-type/{id}', [TaskTypeController::class, 'update']);
        Route::delete('task-type/{id}', [TaskTypeController::class, 'delete']);

         // Revision Type
        Route::get('revision-types', [RevisionTypeController::class, 'index']);
        Route::get('revision-type-data', [RevisionTypeController::class, 'revisionTypesData']);
        Route::get('revision-type-data-group', [RevisionTypeController::class, 'group']);
        Route::get('revision-type-data-field', [RevisionTypeController::class, 'searchByField']);
        Route::get('revision-type/{id}', [RevisionTypeController::class, 'show']);
        Route::post('revision-type', [RevisionTypeController::class, 'store']);
        Route::put('revision-type/{id}', [RevisionTypeController::class, 'update']);
        Route::delete('revision-type/{id}', [RevisionTypeController::class, 'destroy']);

        // Product Type Routes
        Route::get('product-types', [ProductTypeController::class, 'index']);
        Route::get('product-type-data', [ProductTypeController::class, 'productTypesData']);
        Route::get('product-type-data-group', [ProductTypeController::class, 'group']);
        Route::get('product-type-data-field', [ProductTypeController::class, 'searchByField']);
        Route::get('product-type/{id}', [ProductTypeController::class, 'show']);
        Route::post('product-type', [ProductTypeController::class, 'store']);
        Route::put('product-type/{id}', [ProductTypeController::class, 'update']);
        Route::delete('product-type/{id}', [ProductTypeController::class, 'delete']);

        // Region Type Routes
        Route::get('regions', [RegionController::class, 'index']);
        Route::get('region-data', [RegionController::class, 'regionsData']);
        Route::get('region-data-group', [RegionController::class, 'group']);
        Route::get('region-data-field', [RegionController::class, 'searchByField']);
        Route::get('region/{id}', [RegionController::class, 'show']);
        Route::post('region', [RegionController::class, 'store']);
        Route::put('region/{id}', [RegionController::class, 'update']);
        Route::delete('region/{id}', [RegionController::class, 'delete']);

        // Reporter Type Routes
        Route::get('reporters', [ReporterController::class, 'index']);
        Route::get('reporter-data', [ReporterController::class, 'reportersData']);
        Route::get('reporter-data-group', [ReporterController::class, 'group']);
        Route::get('reporter-data-field', [ReporterController::class, 'searchByField']);
        Route::get('reporter/{id}', [ReporterController::class, 'show']);
        Route::post('reporter', [ReporterController::class, 'store']);
        Route::put('reporter/{id}', [ReporterController::class, 'update']);
        Route::delete('reporter/{id}', [ReporterController::class, 'destroy']);


        // Priority Type Routes
        Route::get('priorities', [PriorityController::class, 'index']);
        Route::get('priority-data', [PriorityController::class, 'priorityData']);
        Route::get('priority-data-group', [PriorityController::class, 'group']);
        Route::get('priority-data-field', [PriorityController::class, 'searchByField']);
        Route::get('priority/{id}', [PriorityController::class, 'show']);
        Route::post('priority', [PriorityController::class, 'store']);
        Route::put('priority/{id}', [PriorityController::class, 'update']);
        Route::delete('priority/{id}', [PriorityController::class, 'delete']);

        // Record Type Routes
        Route::get('record-types', [RecordTypeController::class, 'index']);
        Route::get('record-type-data', [RecordTypeController::class, 'recordTypesData']);
        Route::get('record-type-data-group', [RecordTypeController::class, 'group']);
        Route::get('record-type-data-field', [RecordTypeController::class, 'searchByField']);
        Route::get('record-type/{id}', [RecordTypeController::class, 'show']);
        Route::post('record-type', [RecordTypeController::class, 'store']);
        Route::put('record-type/{id}', [RecordTypeController::class, 'update']);
        Route::delete('record-type/{id}', [RecordTypeController::class, 'delete']);

        // Review & release Routes
        Route::get('reviews', [ReviewRleaseController::class, 'index']);
        Route::get('review-data', [ReviewRleaseController::class, 'reviewsData']);
        Route::get('review-data-group', [ReviewRleaseController::class, 'group']);
        Route::get('review-data-field', [ReviewRleaseController::class, 'searchByField']);
        Route::get('review/{id}', [ReviewRleaseController::class, 'show']);
        Route::post('review', [ReviewRleaseController::class, 'store']);
        Route::put('review/{id}', [ReviewRleaseController::class, 'update']);
        Route::delete('review/{id}', [ReviewRleaseController::class, 'delete']);

        // Workflow Routes
        Route::get('workflows', [WorkflowController::class, 'index']);
        Route::get('workflow-data', [WorkflowController::class, 'workflowsData']);
        Route::get('workflow-data-group', [WorkflowController::class, 'group']);
        Route::get('workflow-data-field', [WorkflowController::class, 'searchByField']);
        Route::get('workflow/{id}', [WorkflowController::class, 'show']);
        Route::post('workflow', [WorkflowController::class, 'store']);
        Route::put('workflow/{id}', [WorkflowController::class, 'update']);
        Route::delete('workflow/{id}', [WorkflowController::class, 'delete']);

        /*----------------------Imran Ahmed ----------------------------*/
        // Todos Routes
        Route::get('todos/{userId}', [TodoController::class, 'index']); // get all todos for logged user id not EID's.
        Route::get('todo/{id}', [TodoController::class, 'show']);
        Route::post('todo', [TodoController::class, 'store']);
        Route::put('todo/{id}', [TodoController::class, 'update']);
        Route::delete('todo/{id}', [TodoController::class, 'delete']);
        // Status Type Routes
        Route::get('status', [StatusController::class, 'index']);
        Route::get('status/{id}', [StatusController::class, 'show']);
        Route::post('status', [StatusController::class, 'store']);
        Route::put('status/{id}', [StatusController::class, 'update']);
        Route::delete('status/{id}', [StatusController::class, 'delete']);
        // TodoTag Type Routes
        Route::get('todo-tags', [TodoTagController::class, 'index']);
        Route::get('todo-tag/{id}', [TodoTagController::class, 'show']);
        Route::post('todo-tag', [TodoTagController::class, 'store']);
        Route::put('todo-tag/{id}', [TodoTagController::class, 'update']);
        Route::delete('todo-tag/{id}', [TodoTagController::class, 'delete']);

        // SlaAchieve Type Routes
        Route::get('sla-achieves', [SlaAchieveController::class, 'index']);
        Route::get('sla-achieve/{id}', [SlaAchieveController::class, 'show']);
        Route::post('sla-achieve', [SlaAchieveController::class, 'store']);
        Route::put('sla-achieve/{id}', [SlaAchieveController::class, 'update']);
        Route::delete('sla-achieve/{id}', [SlaAchieveController::class, 'delete']);

        // Team Shift Plan routes
        Route::get('team-shift-plans', [TeamShiftPlanController::class, 'index']);
        Route::get('team-shift-plan/{id}', [TeamShiftPlanController::class, 'show']);
        Route::post('team-shift-plan', [TeamShiftPlanController::class, 'store']);
        Route::put('team-shift-plan/{id}', [TeamShiftPlanController::class, 'update']);
        Route::delete('team-shift-plan/{id}', [TeamShiftPlanController::class, 'delete']);

        // About The App routes
        Route::get('about-the-apps', [AboutTheAppController::class, 'index']);
        Route::get('about-the-app/{id}', [AboutTheAppController::class, 'show']);
        Route::post('about-the-app', [AboutTheAppController::class, 'store']);
        Route::put('about-the-app/{id}', [AboutTheAppController::class, 'update']);
        Route::delete('about-the-app/{id}', [AboutTheAppController::class, 'delete']);

        // ChangeLog routes
        Route::get('change-logs', [ChangeLogController::class, 'index']);
        Route::get('change-log/{id}', [ChangeLogController::class, 'show']);
        Route::post('change-log', [ChangeLogController::class, 'store']);
        Route::put('change-log/{id}', [ChangeLogController::class, 'update']);
        Route::delete('change-log/{id}', [ChangeLogController::class, 'delete']);


        // AppSupport routes
        Route::get('app-supports', [AppSupportController::class, 'index']);
        Route::get('app-support/{id}', [AppSupportController::class, 'show']);
        Route::post('app-support', [AppSupportController::class, 'store']);
        Route::put('app-support/{id}', [AppSupportController::class, 'update']);
        Route::delete('app-support/{id}', [AppSupportController::class, 'delete']);

        // Give Feedback Routes
        Route::get('give-feedbacks', [GiveFeedbackController::class, 'index']); 
        Route::get('give-feedback/{id}', [GiveFeedbackController::class, 'show']);      
        Route::post('give-feedback', [GiveFeedbackController::class, 'store']);       
        Route::put('/give-feedback/{id}', [GiveFeedbackController::class, 'update']);    
        Route::delete('give-feedback/{id}', [GiveFeedbackController::class, 'delete']); 

        // Report Problem Routes
        Route::get('report-problems', [ReportProblemController::class, 'index']);  
        Route::get('report-problem/{id}', [ReportProblemController::class, 'show']); 
        Route::post('report-problem', [ReportProblemController::class, 'store']); 
        Route::put('report-problem/{id}', [ReportProblemController::class, 'update']); 
        Route::delete('report-problem/{id}', [ReportProblemController::class, 'delete']); 
        
        // Notice Board Category Routes
        Route::get('notice-board-category', [NoticeBoardCategoryController::class, 'index']);  
        Route::get('notice-board-category/{id}', [NoticeBoardCategoryController::class, 'show']); 
        Route::post('notice-board-category', [NoticeBoardCategoryController::class, 'store']); 
        Route::put('notice-board-category/{id}', [NoticeBoardCategoryController::class, 'update']); 
        Route::delete('notice-board-category/{id}', [NoticeBoardCategoryController::class, 'delete']); 


        // Notice  Routes
        Route::get('notices', [NoticeController::class, 'index']);  
        Route::get('notice/{id}', [NoticeController::class, 'show']); 
        Route::post('notice', [NoticeController::class, 'store']); 
        Route::put('notice/{id}', [NoticeController::class, 'update']); 
        Route::delete('notice/{id}', [NoticeController::class, 'delete']); 

        // seat-plan  Routes

        Route::get('/seat-plan', [SeatController::class, 'index']);
        Route::post('/seat-plan', [SeatController::class, 'store']);
        Route::put('/seat-plan/{id}', [SeatController::class, 'update']);
        Route::delete('/seat-plan/{id}', [SeatController::class, 'destroy']);









        /*----------------------Razib Hossain ----------------------------*/

        // Attendance Formation Route Start
        Route::apiResource('formation-types', FormationTypeController::class); 
        Route::get('formation-types-group', [FormationTypeController::class, 'group']);  
        Route::get('formation-types-field', [FormationTypeController::class, 'searchByField']);  
        // Attendance Formation Route End


        // Attendance Route Start
        Route::apiResource('attendance', AttendanceController::class);
        Route::get('attendance-by-user', [AttendanceController::class, 'attendanceByUserId']);  
        Route::get('attendance-by-user-date', [AttendanceController::class, 'getUserAttendanceByDate']);  
        Route::get('attendance-by-user-id', [AttendanceController::class, 'getUserAttendanceByID']); 
        Route::get('attendance-group', [AttendanceController::class, 'group']);  
        Route::get('attendance-field', [AttendanceController::class, 'searchByField']);  

        // Schedule Planner Route Start
        Route::apiResource('schedule-planners', SchedulePlannerController::class);
        Route::get('schedule-planners-group', [SchedulePlannerController::class, 'group']);
        Route::get('schedule-planners-search', [SchedulePlannerController::class, 'searchByField']);


        // 
        Route::prefix('list')->group(function () {
            Route::get('departments', [ListDataController::class, 'getDepartments']);
            Route::get('teams', [ListDataController::class, 'getTeams']);
            Route::get('shifts', [ListDataController::class, 'getShifts']);
            Route::get('users-by-default-team', [ListDataController::class, 'getUsersByDefaultTeam']);
            Route::get('designations', [ListDataController::class, 'getDesignations']);
            Route::get('office_location', [ListDataController::class, 'getOfficelocation']);
        });
      
        //Get server current time
        Route::get('/current-time', [CurrentTimeController::class, 'getCurrentTime']); 
         
    });

    // ** Regular User Routes ** - Access only for the logged-in user
    Route::middleware('role:user')->group(function () {
        // Regular user can update their own data
        Route::put('/users/{id}', [AuthController::class, 'update']);  // Regular user update (only self-update)
        
        // View their own details
        Route::get('/users/{id}', [AuthController::class, 'show']);  // View user details
    });

    // Common Routes (Accessible to all authenticated users)
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/teams', [TeamController::class, 'index']);  // View teams
    Route::post('/users', [AuthController::class, 'createUser']);

    
    
});

// Welcome Route
Route::get('/welcome', function () {
    return response()->json([
        'message' => 'Deployment successful! Laravel is up and running!',
        'status' => 'success'
    ]);
});