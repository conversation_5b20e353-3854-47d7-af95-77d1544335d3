import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const resourceStatusApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getResourceStatusData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `resource-status-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ResourceStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForResourceStatus: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `resource-status-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ResourceStatusData'],
    }),

    getResourceStatusById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `resource_statuses/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ResourceStatusData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createResourceStatus: builder.mutation({
      query: (newFormationType) => ({
        url: 'resource-status-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ResourceStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateResourceStatus: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `resource_statuses/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ResourceStatusData', id }, 'ResourceStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteResourceStatus: builder.mutation({
      query: (id) => ({
        url: `resource_statuses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ResourceStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetResourceStatusDataQuery,
  useLazyFetchDataOptionsForResourceStatusQuery,
  useGetResourceStatusByIdQuery,
  useLazyGetResourceStatusByIdQuery,
  useCreateResourceStatusMutation,
  useUpdateResourceStatusMutation,
  useDeleteResourceStatusMutation,
} = resourceStatusApi;
