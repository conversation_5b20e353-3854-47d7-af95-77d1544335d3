import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const timeCardsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTimeCardsData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `time-cards-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['TimeCardsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForTimeCards: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `time-cards-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['TimeCardsData'],
    }),

    getTimeCardById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `time-card/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'TimeCardsData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createTimeCard: builder.mutation({
      query: (newFormationType) => ({
        url: 'time-card',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['TimeCardsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateTimeCard: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `time-card/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'TimeCardsData', id }, 'TimeCardsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteTimeCard: builder.mutation({
      query: (id) => ({
        url: `time-card/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TimeCardsData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetTimeCardsDataQuery,
  useLazyFetchDataOptionsForTimeCardsQuery,
  useGetTimeCardByIdQuery,
  useLazyGetTimeCardByIdQuery,
  useCreateTimeCardMutation,
  useUpdateTimeCardMutation,
  useDeleteTimeCardMutation,
} = timeCardsApi;
