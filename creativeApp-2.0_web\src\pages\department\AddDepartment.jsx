import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { alertMessage } from '../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const API_URL = process.env.REACT_APP_BASE_API_URL;

const AddDepartment = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [departmentName, setDepartmentName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);
    
    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    useEffect(() => {
        const fetchDepartments = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                return;
            }

            const token = localStorage.getItem('token');

            try {
                const response = await fetch(`${API_URL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.statusText);
                }

                const data = await response.json();
                setDepartments(data.departments);
            } catch (error) {
                setError(error.message);
            }
        };

        fetchDepartments();
    }, []);

    const handleSubmit = async (event) => {
        event.preventDefault(); // Prevent default form submission behavior

        const createdBy = loggedInUser;

        if (!createdBy) {
            setError('User is not logged in.');
            return;
        }
    
        const trimmedDepartmentName = departmentName.trim();
    
        // Check if the department already exists
        const departmentExists = departments.some((department) => {
            const departmentNameLower = department.name.toLowerCase().trim();
            return departmentNameLower === trimmedDepartmentName.toLowerCase();
        });
    
        if (departmentExists) {
            setError('Department already exists. Please add a different department.');
            setTimeout(() => setError(''), 3000);
            return; // Exit if the department already exists
        }
    
        setError(''); // Clear any previous error
    
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return; // Exit if token is not available
            }
    
            // Send the department data along with created_by and updated_by as fullName
            const response = await fetch(`${API_URL}/departments`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: trimmedDepartmentName,
                    created_by: createdBy,
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to save department: ' + response.statusText);
            }
    
            const result = await response.json();
            //setSuccessMessage(`Department "${result.name || trimmedDepartmentName}" added successfully!`);

            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Department added successfully.',
            });

            setDepartmentName(''); // Clear the department name input field
    
            // Refetch the departments list after adding the new department
            const newDepartmentsResponse = await fetch(`${API_URL}/departments`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });
    
            if (!newDepartmentsResponse.ok) {
                throw new Error('Failed to fetch departments: ' + newDepartmentsResponse.statusText);
            }
    
            const newDepartmentsData = await newDepartmentsResponse.json();
            setDepartments(newDepartmentsData.departments); // Update the departments list
    
        } catch (error) {
            alertMessage('error'); // Set the error message if something fails
        }
    };
    

    // Check if the current location is for the modal
    if (!isVisible) return null;

    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                <div className="bg-white rounded-lg shadow-md w-full max-w-lg relative">
                    <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                        <h3 className="text-base text-left font-medium text-gray-800">Add Department</h3>
                        <button
                            className="text-2xl text-gray-500 hover:text-gray-800"
                            onClick={() => setVisible(false)}
                        >
                            &times;
                        </button>
                    </div>
                    <form onSubmit={handleSubmit} className='p-6'>
                        <div className="mb-4">
                            <label htmlFor="departmentName" className="block text-sm font-medium text-gray-700 pb-4">
                                Department Name
                            </label>
                            <input
                                type="text"
                                id="departmentName"
                                value={departmentName}
                                onChange={(e) => setDepartmentName(e.target.value)}
                                required
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                            />
                            {error && <p className="text-red-500 text-sm">{error}</p>}
                        </div>
                        <div className='py-4'>
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Department
                            </button>
                        </div>
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddDepartment;
