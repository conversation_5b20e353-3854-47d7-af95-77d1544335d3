<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\About_the_app;
use Illuminate\Support\Facades\Log; // Import Log facade
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AboutTheAppController extends Controller
{
    /**
     * Display a listing of all "About the App" records.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $aboutTheAppEntries = About_the_app::all();

        // Log the entries retrieved
        Log::info('All About The App entries retrieved:', ['entries_count' => $aboutTheAppEntries->count()]);

        return response()->json(['aboutTheApp' => $aboutTheAppEntries], 200);
    }

    /**
     * Display the specified "About the App" entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the entry by ID
        $aboutTheAppEntry = About_the_app::find($id);

        if (!$aboutTheAppEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        // Log the entry retrieved
        Log::info('About The App entry retrieved:', ['entry' => $aboutTheAppEntry]);

        return response()->json(['aboutTheApp' => $aboutTheAppEntry], 200);
    }

    /**
     * Create a new "About the App" entry.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $authUser = $request->user();

        // Log the request and authenticated user
        Log::info('Create About The App entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        // Validate the request
        $request->validate([
            'definition' => 'required|string',
        ]);

        // Create the new entry
        $aboutTheAppEntry = About_the_app::create([
            'definition' => $request->definition,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('About The App entry created:', ['entry' => $aboutTheAppEntry]);

        return response()->json(['message' => 'Entry created successfully.', 'aboutTheApp' => $aboutTheAppEntry], 201);
    }

    /**
     * Update an existing "About the App" entry.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $authUser = $request->user();

        // Log the request and authenticated user
        Log::info('Update About The App entry request:', ['request' => $request->all()]);
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'name' => $authUser->name]);

        // Validate the request
        $request->validate([
            'definition' => 'required|string',
        ]);

        // Find the entry by ID
        $aboutTheAppEntry = About_the_app::find($id);

        if (!$aboutTheAppEntry) {
            return response()->json(['error' => 'Entry not found.'], 404);
        }

        // Update the entry
        $aboutTheAppEntry->update([
            'definition' => $request->definition,
            'updated_by' => $authUser->name,
            
        ]);

        Log::info('About The App entry updated:', ['entry' => $aboutTheAppEntry]);

        return response()->json(['message' => 'Entry updated successfully.', 'aboutTheApp' => $aboutTheAppEntry], 200);
    }

    /**
     * Delete an "About the App" entry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $authUser = request()->user();

        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            $aboutTheAppEntry = About_the_app::findOrFail($id);

            // Delete the entry
            $aboutTheAppEntry->delete();

            Log::info('About The App entry deleted:', ['entry_id' => $id]);

            return response()->json(['message' => 'Entry deleted successfully.'], 200);
        }

        return response()->json(['error' => 'You do not have permission to delete this entry.'], 403);
    }
}
