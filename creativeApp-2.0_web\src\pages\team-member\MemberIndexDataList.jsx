import React, { useState, useCallback, useEffect } from "react";

// DataTable component for rendering tabular data with features like pagination and sorting
import DataTable from "react-data-table-component";

// Loading spinner component to show while data is loading
import Loading from "./../../common/Loading";

import {confirmation<PERSON>lert, ManageColumns, SearchFilter, TableView, Image} from './../../common/coreui';


import { useDispatch } from "react-redux";
import { defaultDateFormat, defaultDateTimeFormat, removeKeys, sortByLabel } from "./../../utils";

// Libraries for exporting data to Excel
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import { userDataApi, useDeleteUserDataMutation, useGetUserDataQuery, useLazyFetchDataOptionsForUserDataQuery, useGetUserDataByIdQuery } from "./../../features/api/userDataApi";
import { useNavigate } from "react-router-dom";
import EditMember from "./EditMember";
import AddMember from "./AddMember";
import { DateTimeFormatDay, DateTimeFormatHour, DateTimeFormatTable } from "./../../common/DateTimeFormatTable";
import { useRoleBasedAccess } from "../../common/useRoleBasedAccess";



// API endpoint and configuration constants
const MODULE_NAME = "Member Index";

// Main component for listing Product Type List
const MemberIndexDataList = () => {
  // State variables for data items, filters, search text, modals, and loading status
  const [filterOptions, setFilterOptions] = useState({});
  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});
  const [showFilterOption, setShowFilterOption] = useState("");
  const [queryString, setQueryString] = useState("");
  const [modalVisible, setModalVisible] = useState(false);
  const [filterOptionLoading, setFilterOptionLoading] = useState(false);
  const [dataItemsId, setDataItemsId] = useState(null);
  const [error, setError] = useState(null);
  const [viewData, setViewData] = useState(null);
  const navigate = useNavigate();
  const [addModalVisible, setAddModalVisible] = useState(false);

  const { rolePermissions } = useRoleBasedAccess();

  
  // Sorting and pagination state
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");
  const [perPage, setPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);

  
  const { data: dataItems, isFetching, error: fetchError } = useGetUserDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });

  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForUserDataQuery();
       
  const [deleteUserData] = useDeleteUserDataMutation();

  // Build query parameters from selected filters
  const buildQueryParams = (selectedFilters) => {
    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {
      if (typeof value === "string") {
        return acc + `&${key}=${value}`;
      }
      if (Array.isArray(value)) {
        const vals = value.map((i) => i.value).join(",");
        return acc + `&${key}=${vals}`;
      }
      return acc;
    }, "")

    setQueryString(q);
  }

  const handleCopy = (data) => {
    const keysToRemove = ["id", "team", "department", "updated_at", "updated_by", "updater", "created_at", "creator", "created_by", "updated_by"];
    const cleanedData = removeKeys(data, keysToRemove);
    setViewData(null)
    setModalVisible(true);
  }

  const handleEdit = (id) => {
    setViewData(null)
    setDataItemsId(id); 
    setModalVisible(true);
  }

  const handleDelete = (id) => {
    confirmationAlert({onConfirm: () => 
      {        
        deleteUserData(id);
        setViewData(null);
      }});  
  }

  useEffect(() => {
    // Ensure rolePermissions is being fetched or updated before rendering
    console.log(rolePermissions);
  }, [rolePermissions]);  // Watch for rolePermissions updates
  
 

  let columnSerial = 1;

    const [columns, setColumns]  = useState(
      () => [
          {
            id: columnSerial++,
            name: "Action",
            width: "180px",
            className: "bg-red-300",
            cell: (item) => (
              <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
              
                
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                  onClick={() => {
                    setViewData(item);
                  }}
                >
                  <span className="material-symbols-outlined text-lg ">
                  visibility
                  </span>
                </button>
    
                {/* Edit Button */}
                
                {rolePermissions?.hasManagerRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                  onClick={() => handleEdit(item.id)}
                >
                  <span className="material-symbols-outlined text-lg ">
                    stylus_note
                  </span>
                </button>
                )}

                {/* Copy Button */}
                {rolePermissions?.hasManagerRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3  text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                  onClick={() => {
                    handleCopy(item);
                  }}
                >
                  <span className="material-symbols-outlined text-lg ">
                  content_copy
                  </span>
                </button>
                )}

                {/* Delete Button */}
                {rolePermissions?.hasManagerRole && (
                <button
                  className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200 "
                  onClick={() => handleDelete(item.id)}
                >
                  <span className="material-symbols-outlined text-sm ">
                    delete
                  </span>
                </button>
                )}
              </div>
            ),
          },
          {
            id: columnSerial++,
            name: "S.No",
            // Calculate serial number based on current page and rows per page
            selector: (row, index) => (currentPage - 1) * perPage + index + 1,
            width: "80px",
            omit: false,
        },
        {
            id: columnSerial++,
            name: "EID",
            db_field: "eid",
            selector: (row) => row.eid || "",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
          id: columnSerial++,
          name: "Full Name",
          width:'300px',
          db_field: "id",
          selector: (row) => row?.fname ? `${row?.fname} ${row?.lname}` : "",
            cell: (row) => (
              <span className="flex align-middle items-center w-full text-start gap-2 px-4 py-2">
                {row?.photo ? (
                  <Image
                    src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row?.photo}`}
                    alt="Profile Photo"
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <span className="min-w-10 w-10 h-10 bg-primary text-white text-xs leading-none flex justify-center items-center text-center rounded-full">No <br/>Photo</span>
                )}
            
                <div className="flex flex-col min-w-[80%]">
                  {row?.fname && <b>{row.fname} {row.lname}</b>}
            
                  {row?.designations?.length > 0 ? (
                    <div>{row.designations.map(designation => designation.name).join(', ')}</div>
                  ) : (
                    <div className="text-sm text-gray-400">N/A</div>
                  )}
                </div>
              </span>
            ),          
          sortable: true,
          omit: false,
          filterable: false,
        },    
        {
          id: columnSerial++,
          name: "Preferred Nickname",
          db_field: "nick_name",
          selector: (row) => row.nick_name || "N/A",
          omit: false,
          sortable: true,
          filterable: true,
        },    
        // {
        //   id: columnSerial++,
        //   name: "Avatar",
        //   db_field: "photo",
        //   selector: (row) => row.photo ? <img src={`${process.env.REACT_APP_BASE_STORAGE_URL}/${row.photo}`} alt="Avatar" style={{ width: 30, height: 30, borderRadius: '50%', margin: '0 auto', display: 'block' }} /> : "N/A",
        //   omit: false,
        //   sortable: true,
        //   filterable: false,
        // },     
        // {
        //     id: columnSerial++,
        //     name: "First Name",
        //     db_field: "fname",
        //     selector: (row) => row.fname || "N/A",
        //     omit: false,
        //     sortable: true,
        //     filterable: true,
        // },
        // {
        //     id: columnSerial++,
        //     name: "Last Name",
        //     db_field: "lname",
        //     selector: (row) => row.lname || "N/A",
        //     omit: false,
        //     sortable: true,
        //     filterable: false,
        // },
        // {
        //     id: columnSerial++,
        //     name: "Full Name",
        //     db_field: "fullName",
        //     selector: (row) => `${row.fname} ${row.lname}` || "N/A",
        //     omit: false,
        //     sortable: true,
        //     filterable: false,
        // },
        // {
        //     id: columnSerial++,
        //     name: "Designation",
        //     db_field: "designations",
        //     selector: (row) => row.designations?.map(designation => designation.name).join(', ') || "N/A",
        //     omit: false,
        //     sortable: true,
        //     filterable: true,
        // },

        {
          id: columnSerial++,
          name: "Email",
          db_field: "email",
          selector: (row) => row.email || "",
          cell: (row) => <span className="lowercase">{row.email || ""}</span>,
          omit: false,
          sortable: true,
          filterable: true,
        },      
        {
            id: columnSerial++,
            name: "Responsibility Level",
            db_field: "resource_types",
            selector: (row) => row.resource_types?.map(type => type.name).join(', ') || "N/A",
            selectorForFilter: (row) => row.name || "N/A",
            tableNameForFilter: 'resource_types',
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
          id: columnSerial++,
          name: "About Me",
          db_field: "about",
          selector: (row) => row.about || "N/A",
          omit: false,
          sortable: true,
          filterable: false,
          cell: (row) => (
              <div
                id="about-cell"
                style={{
                  whiteSpace: 'normal',
                  display: '-webkit-box', // Use webkit box model
                  WebkitBoxOrient: 'vertical', // Ensure vertical orientation
                  WebkitLineClamp: 4, // Limit to 4 lines
                  textOverflow: 'ellipsis', // Show ellipsis when text overflows
                  maxHeight: '6.5em', // Make sure the height is sufficient for 3 lines
                }}
              >
                  {row.about || "N/A"}
              </div>
          ),
      },
        {
            id: columnSerial++,
            name: "Birthday",
            db_field: "birthday",
            selector: (row) => row.birthday ? DateTimeFormatDay(row.birthday) : "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
          id: columnSerial++,
          name: "Would you like to celebrate your birthday?",
          db_field: "birthday_celebration",
          selector: (row) => parseInt(row.birthday_celebration) === 1? 'Yes':'No',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
            id: columnSerial++,
            name: "Birthday Celebration Date",
            db_field: "birthday_celebration_date",
            selector: (row) => row.birthday_celebration_date ? DateTimeFormatTable(row.birthday_celebration_date) : "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Gender",
            db_field: "gender",
            selector: (row) => row.gender || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Marital Status",
            db_field: "marital_status",
            selector: (row) => row.marital_status || "N/A",
            cell: (row) => <span className="capitalize">{row.marital_status || "N/A"}</span>,
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Primary Phone Number",
            db_field: "primary_contact",
            selector: (row) => row.primary_contact || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Secondary Phone Number",
            db_field: "secondary_contact",
            selector: (row) => row.secondary_contact || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Emergency Contact Number",
            db_field: "emergency_contact",
            selector: (row) => row.emergency_contact || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Emergency Contact Relationship",
            db_field: "relation_contact",
            selector: (row) => row.relation_contact || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Present Address",
            db_field: "present_address",
            selector: (row) => row.present_address || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
            cell: (row) => (
              <div id="address" >
                  {row.present_address || "N/A"}
              </div>
            ),
        },
        {
            id: columnSerial++,
            name: "Permanent Address",
            db_field: "permanent_address",
            selector: (row) => row.permanent_address || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
            cell: (row) => (
              <div id="address" >
                  {row.permanent_address || "N/A"}
              </div>
            ),
        },
        {
          id: columnSerial++,
          name: "Blood Group",
          db_field: "bloods",
          selector: (row) => row.bloods?.map(blood => blood.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'bloods',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Would you like to permit blood donation?",
          db_field: "blood_donate",
          selector: (row) => row.blood_donate || "N/A",
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
            id: columnSerial++,
            name: "Previous Designation in the Company",
            db_field: "prev_designation",
            selector: (row) => row.prev_designation || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },               
        {
          id: columnSerial++,
          name: "Department",
          db_field: "departments",
          selector: (row) => row.departments?.map(department => department.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'departments',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Team",
          db_field: "teams",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'teams',
          omit: false,
          sortable: true,
          filterable: true,
          className: 'team-column',
          cell: (row) => (
            <div className="team-cell-index">
              {row.team_names?.join(', ') || "N/A"}
            </div>
          ),
        },          
        {
          id: columnSerial++,
          name: "Team Lead/Report to",
          db_field: "team_lead",
          selector: (row) => {
              // Access the team_lead string directly from the first team in the teams array
              const teamLead = row.teams && row.teams[0] ? row.teams[0].team_lead : null;
              return teamLead || "N/A"; // If there's no team_lead, show "N/A"
          },
          omit: false,
          sortable: true,
          filterable: true,
        },   
        {
            id: columnSerial++,
            name: "Desk ID",
            db_field: "desk_id",
            selector: (row) => row.desk_id || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Joining Date",
            db_field: "joining_date",
            selector: (row) => row.joining_date ? defaultDateFormat(row.joining_date) : "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        // {
        //   id: columnSerial++,
        //   name: "Work Anniversary",
        //   db_field: "work_anniversary",
        //   selector: (row) => defaultDateTimeFormat(row.work_anniversary),
        //   omit: false,
        //   sortable: true,
        //   filterable: true,
        // },
        {
          id: columnSerial++,
          name: "Termination Date",
          db_field: "termination_date",
          selector: (row) => {
              const date = new Date(row.termination_date);
              // Check if the date is invalid or empty
              if (isNaN(date.getTime()) || !row.termination_date) {
                  return "N/A";
              }
              return defaultDateFormat(row.termination_date);  // Use the defaultDateFormat if valid
          },
          omit: false,
          sortable: true,
          filterable: true,
      },
      {
          id: columnSerial++,
          name: "Employment End Date",
          db_field: "employment_end",
          selector: (row) => {
              const date = new Date(row.employment_end);
              // Check if the date is invalid or empty
              if (isNaN(date.getTime()) || !row.employment_end) {
                  return "N/A";
              }
              return defaultDateFormat(row.employment_end);  // Use the defaultDateFormat if valid
          },
          omit: false,
          sortable: true,
          filterable: true,
      },      
        {
          id: columnSerial++,
          name: "Billing Status",
          db_field: "billing_statuses",
          selector: (row) => row.billing_statuses?.map(status => status.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'billing_statuses',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Resource Status",
          db_field: "resource_statuses",
          selector: (row) => row.resource_statuses?.map(status => status.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'resource_statuses',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Contract Type",
          db_field: "contact_types",
          selector: (row) => row.contact_types?.map(contact => contact.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'contact_types',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Available Status",
          db_field: "available_statuses",
          selector: (row) => row.available_statuses?.map(status => status.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'available_statuses',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "Work Location",
          db_field: "locations", // Assuming "locations" is part of the user data
          selector: (row) => {
            // Ensure `row.branches` exists and is an array
            const locations = row?.branches?.flatMap(branch => 
              branch.locations?.map(location => location.locations_name) // Access locations_name instead of name
            ) || [];
            return locations.join(', ') || "";  // Join location names with a comma
          },
          selectorForFilter: (row) => row.locations_name || "N/A",
          tableNameForFilter: 'locations',
          omit: false,
          sortable: true,
          filterable: true,
        },               
        {
          id: columnSerial++,
          name: "Offices Branch",
          db_field: "branches",
          selector: (row) => row.branches?.map(branch => branch.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'branches',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
          id: columnSerial++,
          name: "On-site Status",
          db_field: "onsite_statuses",
          selector: (row) => row.onsite_statuses?.map(status => status.name).join(', ') || "",
          selectorForFilter: (row) => row.name || "N/A",
          tableNameForFilter: 'onsite_statuses',
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
            id: columnSerial++,
            name: "Team Member Status",
            db_field: "member_statuses",
            selector: (row) => row.member_statuses?.map(status => status.name).join(', ') || "",
            selectorForFilter: (row) => row.name || "N/A",
            tableNameForFilter: 'member_statuses',
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
            id: columnSerial++,
            name: "Created Date",
            db_field: "created_at",
            selector: (row) => DateTimeFormatDay(row.created_at),
            omit: false,
            sortable: true,
            filterable: false,
        },
        {
            id: columnSerial++,
            name: "Created At",
            db_field: "created_at",
            selector: (row) => DateTimeFormatHour(row.created_at),
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
          id: columnSerial++,
          name: "Updated At",
          db_field: "updated_at",
          selector: (row) => DateTimeFormatDay(row.updated_at),
          omit: false,
          sortable: true,
          filterable: true,
        },
        {
            id: columnSerial++,
            name: "Created By",
            db_field: "created_by",
            selector: (row) => row.creator?.fname || "N/A",
            omit: false,
            sortable: true,
            filterable: true,
        },
        {
          id: columnSerial++,
          name: "Updated By",
          db_field: "updated_by",
          selector: (row) => row.updater?.fname || "N/A",
          omit: false,
          sortable: true,
          filterable: true,
      },
        
      ],
      [currentPage, perPage]
    );

    useEffect(() => {
      // Recalculate or update columns if rolePermissions change
      setColumns((prevColumns) => [
        ...prevColumns.map((col) => {
          if (col.name === "Action") {
            // Update the "Action" column dynamically
            return {
              ...col,
              cell: (item) => (
                <div className="flex gap-1 mx-2 !min-w-[200px] pl-3">
                  <button
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    onClick={() => setViewData(item)}
                  >
                    <span className="material-symbols-outlined text-lg">visibility</span>
                  </button>
                  {rolePermissions?.hasManagerRole && (
                    <button
                      className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                      onClick={() => handleEdit(item.id)}
                    >
                      <span className="material-symbols-outlined text-lg">stylus_note</span>
                    </button>
                  )}

                  {rolePermissions?.hasManagerRole && (
                  <button
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    onClick={() => handleCopy(item)}
                  >
                    <span className="material-symbols-outlined text-lg">content_copy</span>
                  </button>
                  )}

                  {rolePermissions?.hasManagerRole && (
                  <button
                    className="w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200"
                    onClick={() => handleDelete(item.id)}
                  >
                    <span className="material-symbols-outlined text-sm">delete</span>
                  </button>
                  )}
                </div>
              ),
            };
          }
          return col;
        }),
      ]);
    }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes
  
  // Resets the pagination and clear-all filter state
  const resetPage = () => {
    if (Object.keys(selectedFilterOptions).length) {
      let newObj = {};
      Object.keys(selectedFilterOptions).map((key) => {
        if (typeof selectedFilterOptions[key] === "string") {
          newObj[key] = "";
        } else {
          newObj[key] = [];
        }
      });
      setSelectedFilterOptions({ ...newObj });
      buildQueryParams({ ...newObj })
    }
    setCurrentPage(1);
  };


  // Export the fetched data into an Excel file
  const dispatch = useDispatch();
  const exportToExcel = async () => {
    try {
      // Fetch all data items for Excel export
      const result = await dispatch(
        userDataApi.endpoints.getUserData.initiate({
          sort_by: sortColumn,
          order: sortDirection,
          page: currentPage,
          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues
          query: queryString,
        })
      ).unwrap(); // Wait for the API response
  
      if (!result?.total || result.total < 1) {
        return false;
      }
  
      var sl = 1;
  
      let prepXlsData = result.data.map((item) => {
        if (columns.length) {
          let obj = {};
          columns.forEach((column) => {
            if (!column.omit && column.selector) {
              obj[column.name] = column.name === "S.No" ? sl++ : column.selector(item) || "";
            }
          });
          return obj;
        }
      });
  
      // Create a worksheet from the JSON data and append to a new workbook
      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  
      // Convert workbook to a buffer and create a Blob to trigger a file download
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], { type: "application/octet-stream" });
      saveAs(blob, `${MODULE_NAME.replace(/ /g,"_")}_${prepXlsData.length}.xlsx`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
    }
  };
  

  /**
   * Fetch filter options from API for a specific field.
   */
  const fetchDataOptionsForFilterBy = useCallback(
    async (
      itemObject = {},
      type = "group",
      searching = "",
      fieldType = "select"
    ) => {

      let groupByField = itemObject.db_field || "title";
      let tableNameForFilter = itemObject.tableNameForFilter || "";

      try {
        setShowFilterOption(groupByField);
        setFilterOptionLoading(true);

        var groupData = [];

        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), table: tableNameForFilter.trim(), text: searching.trim() });
        
        if (response.data) {
          groupData = response.data;
        }

        if (groupData.length) {

          if (fieldType === "searchable") {
            setFilterOptions((prev) => ({
              ...prev,
              [groupByField]: groupData,
            }));

            return groupData;
          }

          const optionsForFilter = groupData
            .map((item) => {
              if(itemObject.selector){
                let label = "";
                let value = item[groupByField];

                if(itemObject?.selectorForFilter){
                  label = itemObject.selectorForFilter(item);
                  value = item.id;
                }else{
                  label = itemObject.selector(item);
                }

                if(label){
                  if (item.total && item.total > 1) {
                    label += ` (${item.total})`;
                  }

                  return { label, value };
                }

              return null;
              }
            }).filter(Boolean);

          setFilterOptions((prev) => ({
            ...prev,
            [itemObject.id]: sortByLabel(optionsForFilter),
          }));

          return optionsForFilter;
        }
      } catch (error) {
        setError(error.message);
      } finally {
        setFilterOptionLoading(false);
      }
    },
    []
  );

  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      <div className="mx-auto pb-6 ">
        {/* Header section with title and action buttons */}
        <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
          <div className="w-4/12 md:w-10/12 text-start">
            <h2 className="text-2xl font-bold ">{MODULE_NAME}</h2>
          </div>
          <div className="w-8/12 flex items-end justify-end gap-1">
            {/* Manage Columns dropdown */}
            <ManageColumns columns={columns} setColumns={setColumns} />
            
            {/* Export to Excel button, only shown if data exists */}
            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (
              <>
                <button
                  className="w-[190px] h-[40px]  text-center justify-center items-center  py-2 px-4 text-sm font-medium flex text-gray-900 focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                  onClick={exportToExcel}
                >
                  {isFetching && (
                    <>
                      <span className="material-symbols-outlined animate-spin text-sm me-2">
                        progress_activity
                      </span>
                    </>
                  )}
                  {!isFetching && (
                    <span className="material-symbols-outlined text-sm me-2">
                    file_export
                    </span>
                  )}
                  Export to Excel ({dataItems.total})
                </button>
              </>
            )}
            {/* Button to open modal for adding a new formation */}
            {rolePermissions.hasManagerRole && (
            <button
              className=" h-[40px] w-[190px]  text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
              onClick={() => setAddModalVisible(true)}
            >
              Add New
            </button>
            )}
          </div>
        </div>

        {/* Filter fieldset for global search and field-specific filtering */}
        <SearchFilter
            columns={columns}
            selectedFilterOptions={selectedFilterOptions}
            setSelectedFilterOptions={setSelectedFilterOptions}
            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}
            filterOptions={filterOptions}
            filterOptionLoading={filterOptionLoading}
            showFilterOption={showFilterOption}
            resetPage={resetPage}
            setCurrentPage={setCurrentPage}
            buildQueryParams={buildQueryParams}
        />

        {/* Display error message if any error occurs */}
        {fetchError && <div className="text-red-500">{error}</div>}
        {/* Show loading spinner when data is being fetched */}
        {isFetching && <Loading />}

        {/* If no data is available, display an alert message */}
        
        {/* Render the DataTable with the fetched data */}
        <div className="border border-gray-200 p-0 pb-1 rounded-lg my-5 ">
          <DataTable
            columns={columns}
            data={dataItems?.data || []}
            className="p-0 scrollbar-horizontal-10"
            fixedHeader
            
            highlightOnHover
            responsive
            pagination
            paginationServer
            paginationPerPage={perPage}
            paginationTotalRows={dataItems?.total || 0}
            onChangePage={(page) => {
              if (page !== currentPage) {
                setCurrentPage(page);
              }
            }}
            onChangeRowsPerPage={(newPerPage) => {
              if(newPerPage !== perPage){
                setPerPage(newPerPage);
                setCurrentPage(1);
              }
            }}
            paginationComponentOptions={{
              selectAllRowsItem: true,
              selectAllRowsItemText: "ALL",
            }}
            sortServer
            onSort={(column, sortDirection="desc") => {
              if(Object.keys(column).length){
                setSortColumn(column.db_field || column.name || "created_at");
                setSortDirection(sortDirection || "desc");
              }
            }}
          />
        </div>

        {/* Add Modal */}
        {addModalVisible && (
            <AddMember
                isVisible={addModalVisible}
                setVisible={setAddModalVisible}
            />
        )}

        {/* Conditionally render the Edit modal */}
        {modalVisible && (
          <EditMember
            isVisible={modalVisible}
            setVisible={setModalVisible}
            dataItemsId={dataItemsId}
          />
        )}

        {viewData && (
          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />
        )}
       
      </div>
    </section>
  );
};

export default MemberIndexDataList;
