import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const contactTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getContactTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `contact-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ContactTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForContactType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `contact-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ContactTypeData'],
    }),

    getContactTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `contact_types/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ContactTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createContactType: builder.mutation({
      query: (newFormationType) => ({
        url: 'contact-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ContactTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateContactType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `contact_types/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ContactTypeData', id }, 'ContactTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteContactType: builder.mutation({
      query: (id) => ({
        url: `contact_types/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ContactTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetContactTypeDataQuery,
  useLazyFetchDataOptionsForContactTypeQuery,
  useGetContactTypeByIdQuery,
  useLazyGetContactTypeByIdQuery,
  useCreateContactTypeMutation,
  useUpdateContactTypeMutation,
  useDeleteContactTypeMutation,
} = contactTypeApi;
