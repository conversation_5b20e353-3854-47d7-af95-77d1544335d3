<?php

namespace App\Http\Controllers;

use App\Models\Todo;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\TodoTag;



class TodoController extends Controller
{
    /**
     * Display a listing of all todos.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index($user)
    {
        $userId = $user;
        Log::info('UserId:', [$userId]);

        // Retrieve todos where creator_id matches or user_id exists in assignees array
        $todos = Todo::with(['priority', 'status', 'creator'])
            ->where(function ($query) use ($userId) {
                $query->where('creator_id', (int) $userId) // Check if the user is the creator and // Explicitly cast to integer
                    ->orWhereJsonContains('assignees', (int) $userId); // Or if the user exists in the assignees array and // Cast userId to integer for JSON comparison
            })
            ->get();

        // Fetch related tags for each todo
        foreach ($todos as $todo) {
            $todo->tags = TodoTag::whereIn('id', $todo->tag_id)->get(['id', 'name']);
        }

        // Log the retrieved todos
        Log::info('All Todos Retrieved:', ['Todos_count' => $todos->count()]);

        return response()->json(['todos' => $todos], 200);
    }


    /**
     * Display the specified todo.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the todo by ID
        $todo = Todo::with(['priority', 'status',/*'tags', 'assignees',*/ 'creator'])->findOrFail($id);

        if (!$todo) {
            return response()->json(['error' => 'Todo not found.'], 404);
        }

        // Retrieve the tags
        $todo->tags = TodoTag::whereIn('id', $todo->tag_id)->get(['id', 'name']);
        
        // Retrieve the assignees
        $todo->assignees = User::whereIn('id', $todo->assignees)->get(['id', 'photo']);

        // Log the Todo retrieved
        Log::info('Todo Retrieved:', ['Todo' => $todo]);

        return response()->json(['todo' => $todo], 200);
    }

    /**
     * Create a new todo.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'title' => $request->title, 
            'creator_id' => $request->creator_id, 
            'assignees' => $request->assignees, 
            'priority_id' => $request->priority_id, 
            'due_date' => $request->due_date, 
            'status_id' => $request->status_id, 
            'details' => $request->details, 
            'tag_id' => $request->tag_id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

         // Validate incoming request
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'creator_id' => 'required|integer|exists:users,id',
            'assignees' => 'array',
            'priority_id' => 'required|exists:priorities,id',
            'due_date' => 'nullable|date',
            'status_id' => 'required|exists:statuses,id',
            'details' => 'nullable|string',
            'tag_id' => 'array',
        ]);

        // Log the request data
        Log::info('Create todo Request:', ['request' => $request->all()]);

        // Check if the todo name already exists
       /* if (Todo::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'todoTag already exists.'], 409);
        }*/

        // Create a new todoTag
        $todo = Todo::create([
            'title' => $request->title,
            'creator_id' => $authUser->id,
            'assignees' => $request->assignees,
            'priority_id' => $request->priority_id,
            'due_date' => $request->due_date,
            'status_id' => $request->status_id,
            'details' => $request->details,
            'tag_id' => $request->tag_id,
        ]);

        Log::info('todo Created:', ['todo' => $todo]);

        return response()->json(['message' => 'Todo created successfully.', 'todo' => $todo], 201);
    }

    /**
     * Update an existing todo.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate incoming request
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'creator_id' => 'sometimes|exists:users,id',
            'assignees' => 'array',
            'priority_id' => 'sometimes|exists:priorities,id',
            'due_date' => 'nullable|date',
            'status_id' => 'sometimes|exists:statuses,id',
            'details' => 'nullable|string',
            'tag_id' => 'array',
        ]);

        // Log the request data
        Log::info('Update todo Request:', ['request' => $request->all()]);

        // Find the todoTag by ID
        $todo = Todo::findOrFail($id);

        if (!$todo) {
            return response()->json(['error' => 'Todo not found.'], 404);
        }

        // Check if the todo is being updated and does not already exist
        /*if ($todo->name !== $request->name && Todo::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Todo name already exists.'], 409);
        }*/

        // Update the todo
        $todo->update([
            'title' => $request->title ?? $todo->title, // Only update if a new title is provided
            'assignees' => $request->assignees ?? $todo->assignees, // Only update if assignees are provided
            'priority_id' => $request->priority_id ?? $todo->priority_id,
            'due_date' => $request->due_date ?? $todo->due_date,
            'status_id' => $request->status_id ?? $todo->status_id,
            'details' => $request->details ?? $todo->details,
            'tag_id' => $request->tag_id ?? $todo->tag_id,
            'updater_id' => $authUser->id,
        ]);

        // Log the updated todo
        Log::info('Todo Updated:', ['Todo' => $todo]);

        return response()->json(['message' => 'Todo updated successfully.', 'todo' => $todo], 200);
    }


    /**
     * Delete a todo.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Find the todoTag
        $todo = Todo::findOrFail($id);

        // Check if the user has the created this todo and permit to delete
        if ($authUser->id === $todo->creator_id) {           

            // Delete the todoTag
            $todo->delete();

            Log::info('Todo Deleted:', ['Todo_id' => $id]);

            return response()->json(['message' => 'Todo deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete the Todo. Only the creator can delete the todo'], 403);
    }

}
