{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\holiday-calender\\\\HolidayCalenderList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useEffect } from \"react\";\n\n// DataTable component for rendering tabular data with features like pagination and sorting\nimport DataTable from \"react-data-table-component\";\n\n// Loading spinner component to show while data is loading\nimport Loading from \"./../../common/Loading\";\nimport { confirmationAlert, ManageColumns, SearchFilter, TableView } from './../../common/coreui';\nimport { useDispatch } from \"react-redux\";\nimport { defaultDateTimeFormat, defaultDateFormat, removeKeys, sortByLabel } from \"./../../utils\";\n\n// Libraries for exporting data to Excel\nimport { saveAs } from \"file-saver\";\nimport * as XLSX from \"xlsx\";\nimport { holidayCalender<PERSON>pi, useDeleteHolidayCalenderMutation, useGetHolidayCalenderDataQuery, useLazyFetchDataOptionsForHolidayCalenderQuery } from \"./../../features/api\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\nimport AddHolidayCalender from \"./AddHolidayCalender\";\nimport EditHolidayCalender from \"./EditHolidayCalender\";\nimport { DateTimeFormatDay } from \"../../common/DateTimeFormatTable\";\n\n// API endpoint and configuration constants\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MODULE_NAME = \"Holiday Calender\";\n\n// Main component for listing Product Type List\nconst HolidayCalenderList = () => {\n  _s();\n  // State variables for data items, filters, search text, modals, and loading status\n  const [filterOptions, setFilterOptions] = useState({});\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\n  const [queryString, setQueryString] = useState(\"\");\n  const [modalVisible, setModalVisible] = useState(false);\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\n  const [dataItemsId, setDataItemsId] = useState(null);\n  const [error, setError] = useState(null);\n  const [viewData, setViewData] = useState(null);\n  const navigate = useNavigate();\n  const [addModalVisible, setAddModalVisible] = useState(false);\n\n  // Sorting and pagination state\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\n  const [sortDirection, setSortDirection] = useState(\"desc\");\n  const [perPage, setPerPage] = useState(\"10\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const {\n    data: dataItems,\n    isFetching,\n    error: fetchError\n  } = useGetHolidayCalenderDataQuery({\n    sort_by: sortColumn,\n    order: sortDirection,\n    page: currentPage,\n    per_page: perPage,\n    query: queryString\n  });\n  const [triggerFilterByFetch, {\n    data: groupData,\n    error: groupDataError\n  }] = useLazyFetchDataOptionsForHolidayCalenderQuery();\n  const [deleteHolidayCalender] = useDeleteHolidayCalenderMutation();\n\n  // Build query parameters from selected filters\n  const buildQueryParams = selectedFilters => {\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\n      if (typeof value === \"string\") {\n        return acc + `&${key}=${value}`;\n      }\n      if (Array.isArray(value)) {\n        const vals = value.map(i => i.value).join(\",\");\n        return acc + `&${key}=${vals}`;\n      }\n      return acc;\n    }, \"\");\n    setQueryString(q);\n  };\n  const handleCopy = data => {\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\n    const cleanedData = removeKeys(data, keysToRemove);\n    setViewData(null);\n    setModalVisible(true);\n  };\n  const handleEdit = id => {\n    setViewData(null);\n    setDataItemsId(id);\n    setModalVisible(true);\n  };\n  const handleDelete = id => {\n    confirmationAlert({\n      onConfirm: () => {\n        deleteHolidayCalender(id);\n        setViewData(null);\n      }\n    });\n  };\n  let columnSerial = 1;\n  const {\n    rolePermissions\n  } = useRoleBasedAccess();\n\n  // Define columns dynamically based on rolePermissions\n  const [columns, setColumns] = useState(() => [{\n    id: columnSerial++,\n    name: \"Action\",\n    width: \"180px\",\n    className: \"bg-red-300\",\n    cell: item => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-1 mx-2 !min-w-[200px] pl-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => setViewData(item),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"visibility\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleEdit(item.id),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"stylus_note\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 13\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleCopy(item),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-lg\",\n          children: \"content_copy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 13\n      }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n        onClick: () => handleDelete(item.id),\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined text-sm\",\n          children: \"delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this)\n  }, {\n    id: columnSerial++,\n    name: \"S.No\",\n    selector: (row, index) => (currentPage - 1) * perPage + index + 1,\n    width: \"80px\",\n    omit: false\n  }, {\n    id: columnSerial++,\n    name: \"Department\",\n    selector: row => {\n      var _row$departments;\n      return ((_row$departments = row.departments) === null || _row$departments === void 0 ? void 0 : _row$departments.name) || \"N/A\";\n    },\n    db_title_field: \"departments.name\",\n    db_field: \"department_id\",\n    sortable: true,\n    omit: false,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Team\",\n    selector: row => {\n      var _row$teams;\n      return ((_row$teams = row.teams) === null || _row$teams === void 0 ? void 0 : _row$teams.name) || \"N/A\";\n    },\n    db_title_field: \"teams.name\",\n    db_field: \"team_id\",\n    sortable: true,\n    omit: false,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Office Location\",\n    selector: row => {\n      var _row$locations;\n      return ((_row$locations = row.locations) === null || _row$locations === void 0 ? void 0 : _row$locations.locations_name) || \"N/A\";\n    },\n    db_title_field: \"locations.locations_name\",\n    db_field: \"location_id\",\n    sortable: true,\n    omit: false,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Holiday Name\",\n    db_field: \"holiday_name\",\n    selector: row => row.holiday_name || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Holiday Start Date\",\n    db_field: \"holiday_start_date\",\n    selector: row => defaultDateFormat(row.holiday_start_date) || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Holiday End Date\",\n    db_field: \"holiday_end_date\",\n    selector: row => defaultDateFormat(row.holiday_end_date) || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Holiday Day of Week\",\n    db_field: \"day_of_week\",\n    selector: row => row.day_of_week || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Total Day\",\n    db_field: \"days\",\n    selector: row => row.days || \"\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Created By\",\n    db_field: \"created_by\",\n    selector: row => {\n      var _row$creator, _row$creator2;\n      return (_row$creator = row.creator) !== null && _row$creator !== void 0 && _row$creator.fname && (_row$creator2 = row.creator) !== null && _row$creator2 !== void 0 && _row$creator2.lname ? `${row.creator.fname} ${row.creator.lname}` : \"N/A\";\n    },\n    omit: false,\n    sortable: true,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Updated By\",\n    db_field: \"updated_by\",\n    selector: row => {\n      var _row$updater, _row$updater2;\n      return (_row$updater = row.updater) !== null && _row$updater !== void 0 && _row$updater.fname && (_row$updater2 = row.updater) !== null && _row$updater2 !== void 0 && _row$updater2.lname ? `${row.updater.fname} ${row.updater.lname}` : \"N/A\";\n    },\n    omit: false,\n    sortable: true,\n    filterable: false\n  }, {\n    id: columnSerial++,\n    name: \"Created At\",\n    width: '250px',\n    selector: row => defaultDateTimeFormat(row.created_at),\n    db_field: \"created_at\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }, {\n    id: columnSerial++,\n    name: \"Updated At\",\n    width: '250px',\n    selector: row => defaultDateTimeFormat(row.updated_at),\n    db_field: \"updated_at\",\n    omit: false,\n    sortable: true,\n    filterable: true\n  }]);\n  useEffect(() => {\n    // Recalculate or update columns if rolePermissions change\n    setColumns(prevColumns => [...prevColumns.map(col => {\n      if (col.name === \"Action\") {\n        // Update the \"Action\" column dynamically\n        return {\n          ...col,\n          cell: item => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-1 mx-2 !min-w-[200px] pl-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => setViewData(item),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"visibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleEdit(item.id),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"stylus_note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleCopy(item),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-lg\",\n                children: \"content_copy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), (rolePermissions === null || rolePermissions === void 0 ? void 0 : rolePermissions.hasManagerRole) && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\",\n              onClick: () => handleDelete(item.id),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm\",\n                children: \"delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        };\n      }\n      return col;\n    })]);\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\n\n  // Resets the pagination and clear-all filter state\n  const resetPage = () => {\n    if (Object.keys(selectedFilterOptions).length) {\n      let newObj = {};\n      Object.keys(selectedFilterOptions).map(key => {\n        if (typeof selectedFilterOptions[key] === \"string\") {\n          newObj[key] = \"\";\n        } else {\n          newObj[key] = [];\n        }\n      });\n      setSelectedFilterOptions({\n        ...newObj\n      });\n      buildQueryParams({\n        ...newObj\n      });\n    }\n    setCurrentPage(1);\n  };\n\n  // Export the fetched data into an Excel file\n  const dispatch = useDispatch();\n  const exportToExcel = async () => {\n    try {\n      // Fetch all data items for Excel export\n      const result = await dispatch(holidayCalenderApi.endpoints.getHolidayCalenderData.initiate({\n        sort_by: sortColumn,\n        order: sortDirection,\n        page: currentPage,\n        per_page: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 10,\n        // Fallback value to avoid undefined issues\n        query: queryString\n      })).unwrap(); // Wait for the API response\n\n      if (!(result !== null && result !== void 0 && result.total) || result.total < 1) {\n        return false;\n      }\n      var sl = 1;\n      let prepXlsData = result.data.map(item => {\n        if (columns.length) {\n          let obj = {};\n          columns.forEach(column => {\n            if (!column.omit && column.selector) {\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\n            }\n          });\n          return obj;\n        }\n      });\n\n      // Create a worksheet from the JSON data and append to a new workbook\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\n      const workbook = XLSX.utils.book_new();\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\n\n      // Convert workbook to a buffer and create a Blob to trigger a file download\n      const excelBuffer = XLSX.write(workbook, {\n        bookType: \"xlsx\",\n        type: \"array\"\n      });\n      const blob = new Blob([excelBuffer], {\n        type: \"application/octet-stream\"\n      });\n      saveAs(blob, `${MODULE_NAME.replace(/ /g, \"_\")}_${prepXlsData.length}.xlsx`);\n    } catch (error) {\n      console.error(\"Error exporting to Excel:\", error);\n    }\n  };\n\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\n  const fetchDataOptionsForFilterBy = useCallback(async (itemObject = {}, type = \"group\", searching = \"\", fieldType = \"select\") => {\n    let groupByField = itemObject.db_field || \"title\";\n    try {\n      setShowFilterOption(groupByField);\n      setFilterOptionLoading(true);\n      var groupData = [];\n      const response = await triggerFilterByFetch({\n        type: type.trim(),\n        column: groupByField.trim(),\n        text: searching.trim()\n      });\n      if (response.data) {\n        groupData = response.data;\n      }\n      if (groupData.length) {\n        if (fieldType === \"searchable\") {\n          setFilterOptions(prev => ({\n            ...prev,\n            [groupByField]: groupData\n          }));\n          return groupData;\n        }\n        const optionsForFilter = groupData.map(item => {\n          if (itemObject.selector) {\n            let label = itemObject.selector(item);\n            if (label) {\n              if (item.total && item.total > 1) {\n                label += ` (${item.total})`;\n              }\n              return {\n                label,\n                value: item[groupByField]\n              };\n            }\n            return null;\n          }\n        }).filter(Boolean);\n        setFilterOptions(prev => ({\n          ...prev,\n          [itemObject.id]: sortByLabel(optionsForFilter)\n        }));\n        return optionsForFilter;\n      }\n    } catch (error) {\n      setError(error.message);\n    } finally {\n      setFilterOptionLoading(false);\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-auto pb-6 \",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4/12 md:w-10/12 text-start\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold \",\n            children: MODULE_NAME\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8/12 flex items-end justify-end gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(ManageColumns, {\n            columns: columns,\n            setColumns: setColumns\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), !isFetching && dataItems && parseInt(dataItems.total) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-[190px] h-[40px] text-center justify-center items-center  py-2 px-4 text-sm font-medium flex hover:shadow-lg  text-primary transition duration-500 ease-in-out focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-primarySeafoam focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-slate-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\",\n              onClick: exportToExcel,\n              children: [isFetching && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-symbols-outlined animate-spin text-sm me-2\",\n                  children: \"progress_activity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false), !isFetching && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"material-symbols-outlined text-sm me-2\",\n                children: \"file_export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), \"Export to Excel (\", dataItems.total, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)\n          }, void 0, false), rolePermissions.hasManagerRole && /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \" h-[40px] w-[190px] text-center justify-center items-center py-2 px-8 text-sm font-medium transition duration-500 ease-in-out focus:outline-none bg-primary text-white rounded-full hover:bg-secondaryTeal hover:text-primary-700 hover:shadow-md focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700\",\n            onClick: () => setAddModalVisible(true),\n            children: \"Add New Holiday\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchFilter, {\n        columns: columns,\n        selectedFilterOptions: selectedFilterOptions,\n        setSelectedFilterOptions: setSelectedFilterOptions,\n        fetchDataOptionsForFilterBy: fetchDataOptionsForFilterBy,\n        filterOptions: filterOptions,\n        filterOptionLoading: filterOptionLoading,\n        showFilterOption: showFilterOption,\n        resetPage: resetPage,\n        setCurrentPage: setCurrentPage,\n        buildQueryParams: buildQueryParams\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), fetchError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-500\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 24\n      }, this), isFetching && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 24\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 p-0 pb-1 rounded-lg my-5 \",\n        children: /*#__PURE__*/_jsxDEV(DataTable, {\n          columns: columns,\n          data: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.data) || [],\n          className: \"p-0 scrollbar-horizontal-10\",\n          fixedHeader: true,\n          highlightOnHover: true,\n          responsive: true,\n          pagination: true,\n          paginationServer: true,\n          paginationPerPage: perPage,\n          paginationTotalRows: (dataItems === null || dataItems === void 0 ? void 0 : dataItems.total) || 0,\n          onChangePage: page => {\n            if (page !== currentPage) {\n              setCurrentPage(page);\n            }\n          },\n          onChangeRowsPerPage: newPerPage => {\n            if (newPerPage !== perPage) {\n              setPerPage(newPerPage);\n              setCurrentPage(1);\n            }\n          },\n          paginationComponentOptions: {\n            selectAllRowsItem: true,\n            selectAllRowsItemText: \"ALL\"\n          },\n          sortServer: true,\n          onSort: (column, sortDirection = \"desc\") => {\n            if (Object.keys(column).length) {\n              setSortColumn(column.db_field || column.name || \"created_at\");\n              setSortDirection(sortDirection || \"desc\");\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), addModalVisible && /*#__PURE__*/_jsxDEV(AddHolidayCalender, {\n        isVisible: addModalVisible,\n        setVisible: setAddModalVisible\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 13\n      }, this), modalVisible && /*#__PURE__*/_jsxDEV(EditHolidayCalender, {\n        isVisible: modalVisible,\n        setVisible: setModalVisible,\n        dataItemsId: dataItemsId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 11\n      }, this), viewData &&\n      /*#__PURE__*/\n      // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\n      _jsxDEV(TableView, {\n        item: viewData,\n        setViewData: setViewData,\n        columns: columns,\n        handleEdit: handleEdit,\n        handleDelete: handleDelete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 462,\n    columnNumber: 5\n  }, this);\n};\n_s(HolidayCalenderList, \"vT0+uBm1gKDh4F+qG67cK24eYGY=\", false, function () {\n  return [useNavigate, useGetHolidayCalenderDataQuery, useLazyFetchDataOptionsForHolidayCalenderQuery, useDeleteHolidayCalenderMutation, useRoleBasedAccess, useDispatch];\n});\n_c = HolidayCalenderList;\nexport default HolidayCalenderList;\nvar _c;\n$RefreshReg$(_c, \"HolidayCalenderList\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "DataTable", "Loading", "<PERSON><PERSON><PERSON><PERSON>", "ManageColumns", "SearchFilter", "TableView", "useDispatch", "defaultDateTimeFormat", "defaultDateFormat", "<PERSON><PERSON><PERSON><PERSON>", "sortByLabel", "saveAs", "XLSX", "holidayCalenderApi", "useDeleteHolidayCalenderMutation", "useGetHolidayCalenderDataQuery", "useLazyFetchDataOptionsForHolidayCalenderQuery", "useNavigate", "useRoleBasedAccess", "AddHolidayCalender", "EditHolidayCalender", "DateTimeFormatDay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MODULE_NAME", "HolidayCalenderList", "_s", "filterOptions", "setFilterOptions", "selectedFilterOptions", "setSelectedFilterOptions", "showFilterOption", "setShowFilterOption", "queryString", "setQueryString", "modalVisible", "setModalVisible", "filterOptionLoading", "setFilterOptionLoading", "dataItemsId", "setDataItemsId", "error", "setError", "viewData", "setViewData", "navigate", "addModalVisible", "setAddModalVisible", "sortColumn", "setSortColumn", "sortDirection", "setSortDirection", "perPage", "setPerPage", "currentPage", "setCurrentPage", "data", "dataItems", "isFetching", "fetchError", "sort_by", "order", "page", "per_page", "query", "triggerFilterByFetch", "groupData", "groupDataError", "deleteHolidayCalender", "buildQueryParams", "selectedFilters", "q", "Object", "entries", "reduce", "acc", "key", "value", "Array", "isArray", "vals", "map", "i", "join", "handleCopy", "keysToRemove", "cleanedData", "handleEdit", "id", "handleDelete", "onConfirm", "columnSerial", "rolePermissions", "columns", "setColumns", "name", "width", "className", "cell", "item", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasManagerRole", "selector", "row", "index", "omit", "_row$departments", "departments", "db_title_field", "db_field", "sortable", "filterable", "_row$teams", "teams", "_row$locations", "locations", "locations_name", "holiday_name", "holiday_start_date", "holiday_end_date", "day_of_week", "days", "_row$creator", "_row$creator2", "creator", "fname", "lname", "_row$updater", "_row$updater2", "updater", "created_at", "updated_at", "prevColumns", "col", "resetPage", "keys", "length", "newObj", "dispatch", "exportToExcel", "result", "endpoints", "getHolidayCalenderData", "initiate", "total", "unwrap", "sl", "prepXlsData", "obj", "for<PERSON>ach", "column", "worksheet", "utils", "json_to_sheet", "workbook", "book_new", "book_append_sheet", "excelBuffer", "write", "bookType", "type", "blob", "Blob", "replace", "console", "fetchDataOptionsForFilterBy", "itemObject", "searching", "fieldType", "groupByField", "response", "trim", "text", "prev", "optionsForFilter", "label", "filter", "Boolean", "message", "parseInt", "fixedHeader", "highlightOnHover", "responsive", "pagination", "paginationServer", "paginationPerPage", "paginationTotalRows", "onChangePage", "onChangeRowsPerPage", "newPerPage", "paginationComponentOptions", "selectAllRowsItem", "selectAllRowsItemText", "sortServer", "onSort", "isVisible", "setVisible", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/holiday-calender/HolidayCalenderList.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from \"react\";\r\n\r\n// DataTable component for rendering tabular data with features like pagination and sorting\r\nimport DataTable from \"react-data-table-component\";\r\n\r\n// Loading spinner component to show while data is loading\r\nimport Loading from \"./../../common/Loading\";\r\n\r\nimport {confirmation<PERSON><PERSON>t, ManageColumns, SearchFilter, TableView} from './../../common/coreui';\r\n\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { defaultDateTimeFormat, defaultDateFormat, removeKeys, sortByLabel } from \"./../../utils\";\r\n\r\n// Libraries for exporting data to Excel\r\nimport { saveAs } from \"file-saver\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { holidayCalenderApi, useDeleteHolidayCalenderMutation, useGetHolidayCalenderDataQuery, useLazyFetchDataOptionsForHolidayCalenderQuery } from \"./../../features/api\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useRoleBasedAccess } from \"./../../common/useRoleBasedAccess\";\r\nimport AddHolidayCalender from \"./AddHolidayCalender\";\r\nimport EditHolidayCalender from \"./EditHolidayCalender\";\r\nimport { DateTimeFormatDay } from \"../../common/DateTimeFormatTable\";\r\n\r\n// API endpoint and configuration constants\r\nconst MODULE_NAME = \"Holiday Calender\";\r\n\r\n// Main component for listing Product Type List\r\nconst HolidayCalenderList = () => {\r\n  // State variables for data items, filters, search text, modals, and loading status\r\n  const [filterOptions, setFilterOptions] = useState({});\r\n  const [selectedFilterOptions, setSelectedFilterOptions] = useState({});\r\n  const [showFilterOption, setShowFilterOption] = useState(\"\");\r\n  const [queryString, setQueryString] = useState(\"\");\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [filterOptionLoading, setFilterOptionLoading] = useState(false);\r\n  const [dataItemsId, setDataItemsId] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [viewData, setViewData] = useState(null);\r\n  const navigate = useNavigate();\r\n  const [addModalVisible, setAddModalVisible] = useState(false);\r\n\r\n  \r\n  // Sorting and pagination state\r\n  const [sortColumn, setSortColumn] = useState(\"created_at\");\r\n  const [sortDirection, setSortDirection] = useState(\"desc\");\r\n  const [perPage, setPerPage] = useState(\"10\");\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  \r\n  const { data: dataItems, isFetching, error: fetchError } = useGetHolidayCalenderDataQuery({ sort_by: sortColumn, order: sortDirection, page: currentPage, per_page: perPage, query: queryString });\r\n\r\n  const [triggerFilterByFetch, { data: groupData, error: groupDataError }] = useLazyFetchDataOptionsForHolidayCalenderQuery();\r\n       \r\n  const [deleteHolidayCalender] = useDeleteHolidayCalenderMutation();\r\n\r\n  // Build query parameters from selected filters\r\n  const buildQueryParams = (selectedFilters) => {\r\n    let q = Object.entries(selectedFilters).reduce((acc, [key, value]) => {\r\n      if (typeof value === \"string\") {\r\n        return acc + `&${key}=${value}`;\r\n      }\r\n      if (Array.isArray(value)) {\r\n        const vals = value.map((i) => i.value).join(\",\");\r\n        return acc + `&${key}=${vals}`;\r\n      }\r\n      return acc;\r\n    }, \"\")\r\n\r\n    setQueryString(q);\r\n  }\r\n\r\n  const handleCopy = (data) => {\r\n    const keysToRemove = [\"id\", \"team\", \"department\", \"updated_at\", \"updated_by\", \"updater\", \"created_at\", \"creator\", \"created_by\", \"updated_by\"];\r\n    const cleanedData = removeKeys(data, keysToRemove);\r\n    setViewData(null)\r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleEdit = (id) => {\r\n    setViewData(null)\r\n    setDataItemsId(id); \r\n    setModalVisible(true);\r\n  }\r\n\r\n  const handleDelete = (id) => {\r\n    confirmationAlert({onConfirm: () => \r\n      {        \r\n        deleteHolidayCalender(id);\r\n        setViewData(null);\r\n      }});  \r\n  }\r\n \r\n\r\n  let columnSerial = 1;\r\n\r\n  const { rolePermissions } = useRoleBasedAccess();\r\n\r\n  // Define columns dynamically based on rolePermissions\r\n  const [columns, setColumns] = useState(() => [\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Action\",\r\n      width: \"180px\",\r\n      className: \"bg-red-300\",\r\n      cell: (item) => (\r\n        <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n          {/* View Button */}\r\n          <button\r\n            className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n            onClick={() => setViewData(item)}\r\n          >\r\n            <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n          </button>\r\n  \r\n          {/* Conditionally render Edit Button based on rolePermissions */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleEdit(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Copy Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleCopy(item)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n            </button>\r\n          )}\r\n  \r\n          {/* Delete Button */}\r\n          {rolePermissions?.hasManagerRole && (\r\n            <button\r\n              className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n              onClick={() => handleDelete(item.id)}\r\n            >\r\n              <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"S.No\",\r\n      selector: (row, index) => (currentPage - 1) * perPage + index + 1,\r\n      width: \"80px\",\r\n      omit: false,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Department\",\r\n      selector: (row) => row.departments?.name || \"N/A\",\r\n      db_title_field: \"departments.name\",\r\n      db_field: \"department_id\",\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Team\",\r\n      selector: (row) => row.teams?.name || \"N/A\",\r\n      db_title_field: \"teams.name\",\r\n      db_field: \"team_id\",\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Office Location\",\r\n      selector: (row) => row.locations?.locations_name || \"N/A\",\r\n      db_title_field: \"locations.locations_name\",\r\n      db_field: \"location_id\",\r\n      sortable: true,\r\n      omit: false,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Holiday Name\",\r\n      db_field: \"holiday_name\",\r\n      selector: (row) => row.holiday_name || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Holiday Start Date\",\r\n      db_field: \"holiday_start_date\",\r\n      selector: (row) => defaultDateFormat(row.holiday_start_date) || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Holiday End Date\",\r\n      db_field: \"holiday_end_date\",\r\n      selector: (row) => defaultDateFormat(row.holiday_end_date) || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Holiday Day of Week\",\r\n      db_field: \"day_of_week\",\r\n      selector: (row) => row.day_of_week || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Total Day\",\r\n      db_field: \"days\",\r\n      selector: (row) => row.days || \"\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created By\",\r\n      db_field: \"created_by\",\r\n      selector: (row) => row.creator?.fname && row.creator?.lname ? `${row.creator.fname} ${row.creator.lname}` : \"N/A\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: false,\r\n    },    \r\n  {\r\n    id: columnSerial++,\r\n    name: \"Updated By\",\r\n    db_field: \"updated_by\",\r\n    selector: (row) => row.updater?.fname && row.updater?.lname ? `${row.updater.fname} ${row.updater.lname}` : \"N/A\",\r\n    omit: false,\r\n    sortable: true,\r\n    filterable: false,\r\n  },  \r\n    {\r\n      id: columnSerial++,\r\n      name: \"Created At\",\r\n      width: '250px',\r\n      selector: (row) => defaultDateTimeFormat(row.created_at),\r\n      db_field: \"created_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n    {\r\n      id: columnSerial++,\r\n      name: \"Updated At\",\r\n      width: '250px',\r\n      selector: (row) => defaultDateTimeFormat(row.updated_at),\r\n      db_field: \"updated_at\",\r\n      omit: false,\r\n      sortable: true,\r\n      filterable: true,\r\n    },\r\n  ]);\r\n  \r\n  useEffect(() => {\r\n    // Recalculate or update columns if rolePermissions change\r\n    setColumns((prevColumns) => [\r\n      ...prevColumns.map((col) => {\r\n        if (col.name === \"Action\") {\r\n          // Update the \"Action\" column dynamically\r\n          return {\r\n            ...col,\r\n            cell: (item) => (\r\n              <div className=\"flex gap-1 mx-2 !min-w-[200px] pl-3\">\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-green-600 hover:bg-green-600 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => setViewData(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">visibility</span>\r\n                </button>\r\n                {rolePermissions?.hasManagerRole && (\r\n                  <button\r\n                    className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-primary hover:bg-primary hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                    onClick={() => handleEdit(item.id)}\r\n                  >\r\n                    <span className=\"material-symbols-outlined text-lg\">stylus_note</span>\r\n                  </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-teal-900 hover:bg-teal-900 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleCopy(item)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-lg\">content_copy</span>\r\n                </button>\r\n                )}\r\n\r\n                {rolePermissions?.hasManagerRole && (\r\n                <button\r\n                  className=\"w-full md:w-auto flex items-center justify-center py-1 px-3 text-red-700 hover:bg-red-700 hover:text-white rounded-lg text-sm focus:outline-none focus:ring-4 focus:ring-gray-200\"\r\n                  onClick={() => handleDelete(item.id)}\r\n                >\r\n                  <span className=\"material-symbols-outlined text-sm\">delete</span>\r\n                </button>\r\n                )}\r\n              </div>\r\n            ),\r\n          };\r\n        }\r\n        return col;\r\n      }),\r\n    ]);\r\n  }, [rolePermissions]); // Dependency array ensures this is updated whenever rolePermissions changes\r\n  \r\n  \r\n\r\n  // Resets the pagination and clear-all filter state\r\n  const resetPage = () => {\r\n    if (Object.keys(selectedFilterOptions).length) {\r\n      let newObj = {};\r\n      Object.keys(selectedFilterOptions).map((key) => {\r\n        if (typeof selectedFilterOptions[key] === \"string\") {\r\n          newObj[key] = \"\";\r\n        } else {\r\n          newObj[key] = [];\r\n        }\r\n      });\r\n      setSelectedFilterOptions({ ...newObj });\r\n      buildQueryParams({ ...newObj })\r\n    }\r\n    setCurrentPage(1);\r\n  };\r\n\r\n\r\n  // Export the fetched data into an Excel file\r\n  const dispatch = useDispatch();\r\n  const exportToExcel = async () => {\r\n    try {\r\n      // Fetch all data items for Excel export\r\n      const result = await dispatch(\r\n        holidayCalenderApi.endpoints.getHolidayCalenderData.initiate({\r\n          sort_by: sortColumn,\r\n          order: sortDirection,\r\n          page: currentPage,\r\n          per_page: dataItems?.total || 10, // Fallback value to avoid undefined issues\r\n          query: queryString,\r\n        })\r\n      ).unwrap(); // Wait for the API response\r\n  \r\n      if (!result?.total || result.total < 1) {\r\n        return false;\r\n      }\r\n  \r\n      var sl = 1;\r\n  \r\n      let prepXlsData = result.data.map((item) => {\r\n        if (columns.length) {\r\n          let obj = {};\r\n          columns.forEach((column) => {\r\n            if (!column.omit && column.selector) {\r\n              obj[column.name] = column.name === \"S.No\" ? sl++ : column.selector(item) || \"\";\r\n            }\r\n          });\r\n          return obj;\r\n        }\r\n      });\r\n  \r\n      // Create a worksheet from the JSON data and append to a new workbook\r\n      const worksheet = XLSX.utils.json_to_sheet(prepXlsData);\r\n      const workbook = XLSX.utils.book_new();\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, \"Sheet1\");\r\n  \r\n      // Convert workbook to a buffer and create a Blob to trigger a file download\r\n      const excelBuffer = XLSX.write(workbook, {\r\n        bookType: \"xlsx\",\r\n        type: \"array\",\r\n      });\r\n      const blob = new Blob([excelBuffer], { type: \"application/octet-stream\" });\r\n      saveAs(blob, `${MODULE_NAME.replace(/ /g,\"_\")}_${prepXlsData.length}.xlsx`);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    }\r\n  };\r\n  \r\n\r\n  /**\r\n   * Fetch filter options from API for a specific field.\r\n   */\r\n  const fetchDataOptionsForFilterBy = useCallback(\r\n    async (\r\n      itemObject = {},\r\n      type = \"group\",\r\n      searching = \"\",\r\n      fieldType = \"select\"\r\n    ) => {\r\n\r\n      let groupByField = itemObject.db_field || \"title\";\r\n\r\n      try {\r\n        setShowFilterOption(groupByField);\r\n        setFilterOptionLoading(true);\r\n\r\n        var groupData = [];\r\n\r\n        const response = await triggerFilterByFetch({ type: type.trim(), column: groupByField.trim(), text: searching.trim() });\r\n        \r\n        if (response.data) {\r\n          groupData = response.data;\r\n        }\r\n\r\n        if (groupData.length) {\r\n\r\n          if (fieldType === \"searchable\") {\r\n            setFilterOptions((prev) => ({\r\n              ...prev,\r\n              [groupByField]: groupData,\r\n            }));\r\n\r\n            return groupData;\r\n          }\r\n\r\n          const optionsForFilter = groupData\r\n            .map((item) => {\r\n              if(itemObject.selector){\r\n                let label = itemObject.selector(item);\r\n\r\n                if(label){\r\n                  if (item.total && item.total > 1) {\r\n                    label += ` (${item.total})`;\r\n                  }\r\n\r\n                  return { label, value: item[groupByField] };\r\n                }\r\n\r\n              return null;\r\n              }\r\n            }).filter(Boolean);\r\n\r\n          setFilterOptions((prev) => ({\r\n            ...prev,\r\n            [itemObject.id]: sortByLabel(optionsForFilter),\r\n          }));\r\n\r\n          return optionsForFilter;\r\n        }\r\n      } catch (error) {\r\n        setError(error.message);\r\n      } finally {\r\n        setFilterOptionLoading(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return (\r\n    <section className=\"bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]\">\r\n      <div className=\"mx-auto pb-6 \">\r\n        {/* Header section with title and action buttons */}\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 py-4\">\r\n          <div className=\"w-4/12 md:w-10/12 text-start\">\r\n            <h2 className=\"text-2xl font-bold \">{MODULE_NAME}</h2>\r\n          </div>\r\n          <div className=\"w-8/12 flex items-end justify-end gap-1\">\r\n            {/* Manage Columns dropdown */}\r\n            <ManageColumns columns={columns} setColumns={setColumns} />\r\n            \r\n            {/* Export to Excel button, only shown if data exists */}\r\n            { !isFetching && dataItems && parseInt(dataItems.total) > 0 && (\r\n              <>\r\n                <button\r\n                  className=\"w-[190px] h-[40px] text-center justify-center items-center  py-2 px-4 text-sm font-medium flex hover:shadow-lg  text-primary transition duration-500 ease-in-out focus:outline-none bg-white rounded-full border border-primary text-primary-100 hover:bg-primarySeafoam focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-slate-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700\"\r\n                  onClick={exportToExcel}\r\n                >\r\n                  {isFetching && (\r\n                    <>\r\n                      <span className=\"material-symbols-outlined animate-spin text-sm me-2\">\r\n                        progress_activity\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                  {!isFetching && (\r\n                    <span className=\"material-symbols-outlined text-sm me-2\">\r\n                    file_export\r\n                    </span>\r\n                  )}\r\n                  Export to Excel ({dataItems.total})\r\n                </button>\r\n              </>\r\n            )}\r\n            {/* Button to open modal for adding a new formation */}\r\n            {rolePermissions.hasManagerRole && (\r\n              <button\r\n                className=\" h-[40px] w-[190px] text-center justify-center items-center py-2 px-8 text-sm font-medium transition duration-500 ease-in-out focus:outline-none bg-primary text-white rounded-full hover:bg-secondaryTeal hover:text-primary-700 hover:shadow-md focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700\"\r\n\r\n                onClick={() => setAddModalVisible(true)}\r\n              >\r\n                Add New Holiday\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filter fieldset for global search and field-specific filtering */}\r\n        <SearchFilter\r\n            columns={columns}\r\n            selectedFilterOptions={selectedFilterOptions}\r\n            setSelectedFilterOptions={setSelectedFilterOptions}\r\n            fetchDataOptionsForFilterBy={fetchDataOptionsForFilterBy}\r\n            filterOptions={filterOptions}\r\n            filterOptionLoading={filterOptionLoading}\r\n            showFilterOption={showFilterOption}\r\n            resetPage={resetPage}\r\n            setCurrentPage={setCurrentPage}\r\n            buildQueryParams={buildQueryParams}\r\n        />\r\n\r\n        {/* Display error message if any error occurs */}\r\n        {fetchError && <div className=\"text-red-500\">{error}</div>}\r\n        {/* Show loading spinner when data is being fetched */}\r\n        {isFetching && <Loading />}\r\n\r\n        {/* If no data is available, display an alert message */}\r\n        \r\n        {/* Render the DataTable with the fetched data */}\r\n        <div className=\"border border-gray-200 p-0 pb-1 rounded-lg my-5 \">\r\n          <DataTable\r\n            columns={columns}\r\n            data={dataItems?.data || []}\r\n            className=\"p-0 scrollbar-horizontal-10\"\r\n            fixedHeader\r\n            \r\n            highlightOnHover\r\n            responsive\r\n            pagination\r\n            paginationServer\r\n            paginationPerPage={perPage}\r\n            paginationTotalRows={dataItems?.total || 0}\r\n            onChangePage={(page) => {\r\n              if (page !== currentPage) {\r\n                setCurrentPage(page);\r\n              }\r\n            }}\r\n            onChangeRowsPerPage={(newPerPage) => {\r\n              if(newPerPage !== perPage){\r\n                setPerPage(newPerPage);\r\n                setCurrentPage(1);\r\n              }\r\n            }}\r\n            paginationComponentOptions={{\r\n              selectAllRowsItem: true,\r\n              selectAllRowsItemText: \"ALL\",\r\n            }}\r\n            sortServer\r\n            onSort={(column, sortDirection=\"desc\") => {\r\n              if(Object.keys(column).length){\r\n                setSortColumn(column.db_field || column.name || \"created_at\");\r\n                setSortDirection(sortDirection || \"desc\");\r\n              }\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Add Modal */}\r\n        {addModalVisible && (\r\n            <AddHolidayCalender\r\n                isVisible={addModalVisible}\r\n                setVisible={setAddModalVisible}\r\n            />\r\n        )}\r\n\r\n        {/* Conditionally render the Edit modal */}\r\n        {modalVisible && (\r\n          <EditHolidayCalender\r\n            isVisible={modalVisible}\r\n            setVisible={setModalVisible}\r\n            dataItemsId={dataItemsId}\r\n          />\r\n        )}\r\n\r\n        {viewData && (\r\n          // <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n          <TableView item={viewData} setViewData={setViewData} columns={columns} handleEdit={handleEdit} handleDelete={handleDelete} />\r\n        )}\r\n       \r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n\r\nexport default HolidayCalenderList;\r\n "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;;AAE/D;AACA,OAAOC,SAAS,MAAM,4BAA4B;;AAElD;AACA,OAAOC,OAAO,MAAM,wBAAwB;AAE5C,SAAQC,iBAAiB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,SAAS,QAAO,uBAAuB;AAG/F,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,qBAAqB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,eAAe;;AAEjG;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,kBAAkB,EAAEC,gCAAgC,EAAEC,8BAA8B,EAAEC,8CAA8C,QAAQ,sBAAsB;AAC3K,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,kCAAkC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAG,kBAAkB;;AAEtC;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtE,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMkD,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;;EAG7D;EACA,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,YAAY,CAAC;EAC1D,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAGjD,MAAM;IAAE6D,IAAI,EAAEC,SAAS;IAAEC,UAAU;IAAEjB,KAAK,EAAEkB;EAAW,CAAC,GAAG9C,8BAA8B,CAAC;IAAE+C,OAAO,EAAEZ,UAAU;IAAEa,KAAK,EAAEX,aAAa;IAAEY,IAAI,EAAER,WAAW;IAAES,QAAQ,EAAEX,OAAO;IAAEY,KAAK,EAAE/B;EAAY,CAAC,CAAC;EAElM,MAAM,CAACgC,oBAAoB,EAAE;IAAET,IAAI,EAAEU,SAAS;IAAEzB,KAAK,EAAE0B;EAAe,CAAC,CAAC,GAAGrD,8CAA8C,CAAC,CAAC;EAE3H,MAAM,CAACsD,qBAAqB,CAAC,GAAGxD,gCAAgC,CAAC,CAAC;;EAElE;EACA,MAAMyD,gBAAgB,GAAIC,eAAe,IAAK;IAC5C,IAAIC,CAAC,GAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MACpE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOF,GAAG,GAAG,IAAIC,GAAG,IAAIC,KAAK,EAAE;MACjC;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QACxB,MAAMG,IAAI,GAAGH,KAAK,CAACI,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACL,KAAK,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;QAChD,OAAOR,GAAG,GAAG,IAAIC,GAAG,IAAII,IAAI,EAAE;MAChC;MACA,OAAOL,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAENzC,cAAc,CAACqC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMa,UAAU,GAAI5B,IAAI,IAAK;IAC3B,MAAM6B,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC;IAC7I,MAAMC,WAAW,GAAG/E,UAAU,CAACiD,IAAI,EAAE6B,YAAY,CAAC;IAClDzC,WAAW,CAAC,IAAI,CAAC;IACjBR,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmD,UAAU,GAAIC,EAAE,IAAK;IACzB5C,WAAW,CAAC,IAAI,CAAC;IACjBJ,cAAc,CAACgD,EAAE,CAAC;IAClBpD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqD,YAAY,GAAID,EAAE,IAAK;IAC3BxF,iBAAiB,CAAC;MAAC0F,SAAS,EAAEA,CAAA,KAC5B;QACEtB,qBAAqB,CAACoB,EAAE,CAAC;QACzB5C,WAAW,CAAC,IAAI,CAAC;MACnB;IAAC,CAAC,CAAC;EACP,CAAC;EAGD,IAAI+C,YAAY,GAAG,CAAC;EAEpB,MAAM;IAAEC;EAAgB,CAAC,GAAG5E,kBAAkB,CAAC,CAAC;;EAEhD;EACA,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAGnG,QAAQ,CAAC,MAAM,CAC3C;IACE6F,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,YAAY;IACvBC,IAAI,EAAGC,IAAI,iBACT9E,OAAA;MAAK4E,SAAS,EAAC,qCAAqC;MAAAG,QAAA,gBAElD/E,OAAA;QACE4E,SAAS,EAAC,uLAAuL;QACjMI,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAACuD,IAAI,CAAE;QAAAC,QAAA,eAEjC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,EAGR,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,mLAAmL;QAC7LI,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE;QAAAY,QAAA,eAEnC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACT,EAGA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,qLAAqL;QAC/LI,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACe,IAAI,CAAE;QAAAC,QAAA,eAEhC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACT,EAGA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;QACE4E,SAAS,EAAC,mLAAmL;QAC7LI,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE;QAAAY,QAAA,eAErC/E,OAAA;UAAM4E,SAAS,EAAC,mCAAmC;UAAAG,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEjB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,MAAM;IACZY,QAAQ,EAAEA,CAACC,GAAG,EAAEC,KAAK,KAAK,CAACvD,WAAW,GAAG,CAAC,IAAIF,OAAO,GAAGyD,KAAK,GAAG,CAAC;IACjEb,KAAK,EAAE,MAAM;IACbc,IAAI,EAAE;EACR,CAAC,EACD;IACEtB,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBY,QAAQ,EAAGC,GAAG;MAAA,IAAAG,gBAAA;MAAA,OAAK,EAAAA,gBAAA,GAAAH,GAAG,CAACI,WAAW,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBhB,IAAI,KAAI,KAAK;IAAA;IACjDkB,cAAc,EAAE,kBAAkB;IAClCC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,IAAI;IACdL,IAAI,EAAE,KAAK;IACXM,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,MAAM;IACZY,QAAQ,EAAGC,GAAG;MAAA,IAAAS,UAAA;MAAA,OAAK,EAAAA,UAAA,GAAAT,GAAG,CAACU,KAAK,cAAAD,UAAA,uBAATA,UAAA,CAAWtB,IAAI,KAAI,KAAK;IAAA;IAC3CkB,cAAc,EAAE,YAAY;IAC5BC,QAAQ,EAAE,SAAS;IACnBC,QAAQ,EAAE,IAAI;IACdL,IAAI,EAAE,KAAK;IACXM,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,iBAAiB;IACvBY,QAAQ,EAAGC,GAAG;MAAA,IAAAW,cAAA;MAAA,OAAK,EAAAA,cAAA,GAAAX,GAAG,CAACY,SAAS,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,cAAc,KAAI,KAAK;IAAA;IACzDR,cAAc,EAAE,0BAA0B;IAC1CC,QAAQ,EAAE,aAAa;IACvBC,QAAQ,EAAE,IAAI;IACdL,IAAI,EAAE,KAAK;IACXM,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,cAAc;IACpBmB,QAAQ,EAAE,cAAc;IACxBP,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACc,YAAY,IAAI,EAAE;IACzCZ,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,oBAAoB;IAC1BmB,QAAQ,EAAE,oBAAoB;IAC9BP,QAAQ,EAAGC,GAAG,IAAKtG,iBAAiB,CAACsG,GAAG,CAACe,kBAAkB,CAAC,IAAI,EAAE;IAClEb,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,kBAAkB;IACxBmB,QAAQ,EAAE,kBAAkB;IAC5BP,QAAQ,EAAGC,GAAG,IAAKtG,iBAAiB,CAACsG,GAAG,CAACgB,gBAAgB,CAAC,IAAI,EAAE;IAChEd,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,qBAAqB;IAC3BmB,QAAQ,EAAE,aAAa;IACvBP,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACiB,WAAW,IAAI,EAAE;IACxCf,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,WAAW;IACjBmB,QAAQ,EAAE,MAAM;IAChBP,QAAQ,EAAGC,GAAG,IAAKA,GAAG,CAACkB,IAAI,IAAI,EAAE;IACjChB,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBmB,QAAQ,EAAE,YAAY;IACtBP,QAAQ,EAAGC,GAAG;MAAA,IAAAmB,YAAA,EAAAC,aAAA;MAAA,OAAK,CAAAD,YAAA,GAAAnB,GAAG,CAACqB,OAAO,cAAAF,YAAA,eAAXA,YAAA,CAAaG,KAAK,KAAAF,aAAA,GAAIpB,GAAG,CAACqB,OAAO,cAAAD,aAAA,eAAXA,aAAA,CAAaG,KAAK,GAAG,GAAGvB,GAAG,CAACqB,OAAO,CAACC,KAAK,IAAItB,GAAG,CAACqB,OAAO,CAACE,KAAK,EAAE,GAAG,KAAK;IAAA;IACjHrB,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACH;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBmB,QAAQ,EAAE,YAAY;IACtBP,QAAQ,EAAGC,GAAG;MAAA,IAAAwB,YAAA,EAAAC,aAAA;MAAA,OAAK,CAAAD,YAAA,GAAAxB,GAAG,CAAC0B,OAAO,cAAAF,YAAA,eAAXA,YAAA,CAAaF,KAAK,KAAAG,aAAA,GAAIzB,GAAG,CAAC0B,OAAO,cAAAD,aAAA,eAAXA,aAAA,CAAaF,KAAK,GAAG,GAAGvB,GAAG,CAAC0B,OAAO,CAACJ,KAAK,IAAItB,GAAG,CAAC0B,OAAO,CAACH,KAAK,EAAE,GAAG,KAAK;IAAA;IACjHrB,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACC;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,OAAO;IACdW,QAAQ,EAAGC,GAAG,IAAKvG,qBAAqB,CAACuG,GAAG,CAAC2B,UAAU,CAAC;IACxDrB,QAAQ,EAAE,YAAY;IACtBJ,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,EACD;IACE5B,EAAE,EAAEG,YAAY,EAAE;IAClBI,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,OAAO;IACdW,QAAQ,EAAGC,GAAG,IAAKvG,qBAAqB,CAACuG,GAAG,CAAC4B,UAAU,CAAC;IACxDtB,QAAQ,EAAE,YAAY;IACtBJ,IAAI,EAAE,KAAK;IACXK,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;EAEFvH,SAAS,CAAC,MAAM;IACd;IACAiG,UAAU,CAAE2C,WAAW,IAAK,CAC1B,GAAGA,WAAW,CAACxD,GAAG,CAAEyD,GAAG,IAAK;MAC1B,IAAIA,GAAG,CAAC3C,IAAI,KAAK,QAAQ,EAAE;QACzB;QACA,OAAO;UACL,GAAG2C,GAAG;UACNxC,IAAI,EAAGC,IAAI,iBACT9E,OAAA;YAAK4E,SAAS,EAAC,qCAAqC;YAAAG,QAAA,gBAClD/E,OAAA;cACE4E,SAAS,EAAC,uLAAuL;cACjMI,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAACuD,IAAI,CAAE;cAAAC,QAAA,eAEjC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACR,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAC9BrF,OAAA;cACE4E,SAAS,EAAC,mLAAmL;cAC7LI,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACY,IAAI,CAACX,EAAE,CAAE;cAAAY,QAAA,eAEnC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACT,EAEA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAChCrF,OAAA;cACE4E,SAAS,EAAC,qLAAqL;cAC/LI,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACe,IAAI,CAAE;cAAAC,QAAA,eAEhC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACP,EAEA,CAAAb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,cAAc,kBAChCrF,OAAA;cACE4E,SAAS,EAAC,mLAAmL;cAC7LI,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACU,IAAI,CAACX,EAAE,CAAE;cAAAY,QAAA,eAErC/E,OAAA;gBAAM4E,SAAS,EAAC,mCAAmC;gBAAAG,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC;MACH;MACA,OAAOiC,GAAG;IACZ,CAAC,CAAC,CACH,CAAC;EACJ,CAAC,EAAE,CAAC9C,eAAe,CAAC,CAAC,CAAC,CAAC;;EAIvB;EACA,MAAM+C,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAInE,MAAM,CAACoE,IAAI,CAAC/G,qBAAqB,CAAC,CAACgH,MAAM,EAAE;MAC7C,IAAIC,MAAM,GAAG,CAAC,CAAC;MACftE,MAAM,CAACoE,IAAI,CAAC/G,qBAAqB,CAAC,CAACoD,GAAG,CAAEL,GAAG,IAAK;QAC9C,IAAI,OAAO/C,qBAAqB,CAAC+C,GAAG,CAAC,KAAK,QAAQ,EAAE;UAClDkE,MAAM,CAAClE,GAAG,CAAC,GAAG,EAAE;QAClB,CAAC,MAAM;UACLkE,MAAM,CAAClE,GAAG,CAAC,GAAG,EAAE;QAClB;MACF,CAAC,CAAC;MACF9C,wBAAwB,CAAC;QAAE,GAAGgH;MAAO,CAAC,CAAC;MACvCzE,gBAAgB,CAAC;QAAE,GAAGyE;MAAO,CAAC,CAAC;IACjC;IACAvF,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAGD;EACA,MAAMwF,QAAQ,GAAG3I,WAAW,CAAC,CAAC;EAC9B,MAAM4I,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF;MACA,MAAMC,MAAM,GAAG,MAAMF,QAAQ,CAC3BpI,kBAAkB,CAACuI,SAAS,CAACC,sBAAsB,CAACC,QAAQ,CAAC;QAC3DxF,OAAO,EAAEZ,UAAU;QACnBa,KAAK,EAAEX,aAAa;QACpBY,IAAI,EAAER,WAAW;QACjBS,QAAQ,EAAE,CAAAN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4F,KAAK,KAAI,EAAE;QAAE;QAClCrF,KAAK,EAAE/B;MACT,CAAC,CACH,CAAC,CAACqH,MAAM,CAAC,CAAC,CAAC,CAAC;;MAEZ,IAAI,EAACL,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEI,KAAK,KAAIJ,MAAM,CAACI,KAAK,GAAG,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;MAEA,IAAIE,EAAE,GAAG,CAAC;MAEV,IAAIC,WAAW,GAAGP,MAAM,CAACzF,IAAI,CAACyB,GAAG,CAAEkB,IAAI,IAAK;QAC1C,IAAIN,OAAO,CAACgD,MAAM,EAAE;UAClB,IAAIY,GAAG,GAAG,CAAC,CAAC;UACZ5D,OAAO,CAAC6D,OAAO,CAAEC,MAAM,IAAK;YAC1B,IAAI,CAACA,MAAM,CAAC7C,IAAI,IAAI6C,MAAM,CAAChD,QAAQ,EAAE;cACnC8C,GAAG,CAACE,MAAM,CAAC5D,IAAI,CAAC,GAAG4D,MAAM,CAAC5D,IAAI,KAAK,MAAM,GAAGwD,EAAE,EAAE,GAAGI,MAAM,CAAChD,QAAQ,CAACR,IAAI,CAAC,IAAI,EAAE;YAChF;UACF,CAAC,CAAC;UACF,OAAOsD,GAAG;QACZ;MACF,CAAC,CAAC;;MAEF;MACA,MAAMG,SAAS,GAAGlJ,IAAI,CAACmJ,KAAK,CAACC,aAAa,CAACN,WAAW,CAAC;MACvD,MAAMO,QAAQ,GAAGrJ,IAAI,CAACmJ,KAAK,CAACG,QAAQ,CAAC,CAAC;MACtCtJ,IAAI,CAACmJ,KAAK,CAACI,iBAAiB,CAACF,QAAQ,EAAEH,SAAS,EAAE,QAAQ,CAAC;;MAE3D;MACA,MAAMM,WAAW,GAAGxJ,IAAI,CAACyJ,KAAK,CAACJ,QAAQ,EAAE;QACvCK,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,WAAW,CAAC,EAAE;QAAEG,IAAI,EAAE;MAA2B,CAAC,CAAC;MAC1E5J,MAAM,CAAC6J,IAAI,EAAE,GAAG9I,WAAW,CAACgJ,OAAO,CAAC,IAAI,EAAC,GAAG,CAAC,IAAIhB,WAAW,CAACX,MAAM,OAAO,CAAC;IAC7E,CAAC,CAAC,OAAOpG,KAAK,EAAE;MACdgI,OAAO,CAAChI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;;EAGD;AACF;AACA;EACE,MAAMiI,2BAA2B,GAAG9K,WAAW,CAC7C,OACE+K,UAAU,GAAG,CAAC,CAAC,EACfN,IAAI,GAAG,OAAO,EACdO,SAAS,GAAG,EAAE,EACdC,SAAS,GAAG,QAAQ,KACjB;IAEH,IAAIC,YAAY,GAAGH,UAAU,CAACzD,QAAQ,IAAI,OAAO;IAEjD,IAAI;MACFlF,mBAAmB,CAAC8I,YAAY,CAAC;MACjCxI,sBAAsB,CAAC,IAAI,CAAC;MAE5B,IAAI4B,SAAS,GAAG,EAAE;MAElB,MAAM6G,QAAQ,GAAG,MAAM9G,oBAAoB,CAAC;QAAEoG,IAAI,EAAEA,IAAI,CAACW,IAAI,CAAC,CAAC;QAAErB,MAAM,EAAEmB,YAAY,CAACE,IAAI,CAAC,CAAC;QAAEC,IAAI,EAAEL,SAAS,CAACI,IAAI,CAAC;MAAE,CAAC,CAAC;MAEvH,IAAID,QAAQ,CAACvH,IAAI,EAAE;QACjBU,SAAS,GAAG6G,QAAQ,CAACvH,IAAI;MAC3B;MAEA,IAAIU,SAAS,CAAC2E,MAAM,EAAE;QAEpB,IAAIgC,SAAS,KAAK,YAAY,EAAE;UAC9BjJ,gBAAgB,CAAEsJ,IAAI,KAAM;YAC1B,GAAGA,IAAI;YACP,CAACJ,YAAY,GAAG5G;UAClB,CAAC,CAAC,CAAC;UAEH,OAAOA,SAAS;QAClB;QAEA,MAAMiH,gBAAgB,GAAGjH,SAAS,CAC/Be,GAAG,CAAEkB,IAAI,IAAK;UACb,IAAGwE,UAAU,CAAChE,QAAQ,EAAC;YACrB,IAAIyE,KAAK,GAAGT,UAAU,CAAChE,QAAQ,CAACR,IAAI,CAAC;YAErC,IAAGiF,KAAK,EAAC;cACP,IAAIjF,IAAI,CAACkD,KAAK,IAAIlD,IAAI,CAACkD,KAAK,GAAG,CAAC,EAAE;gBAChC+B,KAAK,IAAI,KAAKjF,IAAI,CAACkD,KAAK,GAAG;cAC7B;cAEA,OAAO;gBAAE+B,KAAK;gBAAEvG,KAAK,EAAEsB,IAAI,CAAC2E,YAAY;cAAE,CAAC;YAC7C;YAEF,OAAO,IAAI;UACX;QACF,CAAC,CAAC,CAACO,MAAM,CAACC,OAAO,CAAC;QAEpB1J,gBAAgB,CAAEsJ,IAAI,KAAM;UAC1B,GAAGA,IAAI;UACP,CAACP,UAAU,CAACnF,EAAE,GAAGhF,WAAW,CAAC2K,gBAAgB;QAC/C,CAAC,CAAC,CAAC;QAEH,OAAOA,gBAAgB;MACzB;IACF,CAAC,CAAC,OAAO1I,KAAK,EAAE;MACdC,QAAQ,CAACD,KAAK,CAAC8I,OAAO,CAAC;IACzB,CAAC,SAAS;MACRjJ,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC,EACD,EACF,CAAC;EAED,oBACEjB,OAAA;IAAS4E,SAAS,EAAC,+DAA+D;IAAAG,QAAA,eAChF/E,OAAA;MAAK4E,SAAS,EAAC,eAAe;MAAAG,QAAA,gBAE5B/E,OAAA;QAAK4E,SAAS,EAAC,iGAAiG;QAAAG,QAAA,gBAC9G/E,OAAA;UAAK4E,SAAS,EAAC,8BAA8B;UAAAG,QAAA,eAC3C/E,OAAA;YAAI4E,SAAS,EAAC,qBAAqB;YAAAG,QAAA,EAAE5E;UAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACNpF,OAAA;UAAK4E,SAAS,EAAC,yCAAyC;UAAAG,QAAA,gBAEtD/E,OAAA,CAACpB,aAAa;YAAC4F,OAAO,EAAEA,OAAQ;YAACC,UAAU,EAAEA;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAGzD,CAAC/C,UAAU,IAAID,SAAS,IAAI+H,QAAQ,CAAC/H,SAAS,CAAC4F,KAAK,CAAC,GAAG,CAAC,iBACzDhI,OAAA,CAAAE,SAAA;YAAA6E,QAAA,eACE/E,OAAA;cACE4E,SAAS,EAAC,sbAAsb;cAChcI,OAAO,EAAE2C,aAAc;cAAA5C,QAAA,GAEtB1C,UAAU,iBACTrC,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,eACE/E,OAAA;kBAAM4E,SAAS,EAAC,qDAAqD;kBAAAG,QAAA,EAAC;gBAEtE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC,gBACP,CACH,EACA,CAAC/C,UAAU,iBACVrC,OAAA;gBAAM4E,SAAS,EAAC,wCAAwC;gBAAAG,QAAA,EAAC;cAEzD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,EAAC,mBACe,EAAChD,SAAS,CAAC4F,KAAK,EAAC,GACpC;YAAA;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC,gBACT,CACH,EAEAb,eAAe,CAACc,cAAc,iBAC7BrF,OAAA;YACE4E,SAAS,EAAC,wYAAwY;YAElZI,OAAO,EAAEA,CAAA,KAAMtD,kBAAkB,CAAC,IAAI,CAAE;YAAAqD,QAAA,EACzC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpF,OAAA,CAACnB,YAAY;QACT2F,OAAO,EAAEA,OAAQ;QACjBhE,qBAAqB,EAAEA,qBAAsB;QAC7CC,wBAAwB,EAAEA,wBAAyB;QACnD4I,2BAA2B,EAAEA,2BAA4B;QACzD/I,aAAa,EAAEA,aAAc;QAC7BU,mBAAmB,EAAEA,mBAAoB;QACzCN,gBAAgB,EAAEA,gBAAiB;QACnC4G,SAAS,EAAEA,SAAU;QACrBpF,cAAc,EAAEA,cAAe;QAC/Bc,gBAAgB,EAAEA;MAAiB;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,EAGD9C,UAAU,iBAAItC,OAAA;QAAK4E,SAAS,EAAC,cAAc;QAAAG,QAAA,EAAE3D;MAAK;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAEzD/C,UAAU,iBAAIrC,OAAA,CAACtB,OAAO;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAK1BpF,OAAA;QAAK4E,SAAS,EAAC,kDAAkD;QAAAG,QAAA,eAC/D/E,OAAA,CAACvB,SAAS;UACR+F,OAAO,EAAEA,OAAQ;UACjBrC,IAAI,EAAE,CAAAC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAED,IAAI,KAAI,EAAG;UAC5ByC,SAAS,EAAC,6BAA6B;UACvCwF,WAAW;UAEXC,gBAAgB;UAChBC,UAAU;UACVC,UAAU;UACVC,gBAAgB;UAChBC,iBAAiB,EAAE1I,OAAQ;UAC3B2I,mBAAmB,EAAE,CAAAtI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4F,KAAK,KAAI,CAAE;UAC3C2C,YAAY,EAAGlI,IAAI,IAAK;YACtB,IAAIA,IAAI,KAAKR,WAAW,EAAE;cACxBC,cAAc,CAACO,IAAI,CAAC;YACtB;UACF,CAAE;UACFmI,mBAAmB,EAAGC,UAAU,IAAK;YACnC,IAAGA,UAAU,KAAK9I,OAAO,EAAC;cACxBC,UAAU,CAAC6I,UAAU,CAAC;cACtB3I,cAAc,CAAC,CAAC,CAAC;YACnB;UACF,CAAE;UACF4I,0BAA0B,EAAE;YAC1BC,iBAAiB,EAAE,IAAI;YACvBC,qBAAqB,EAAE;UACzB,CAAE;UACFC,UAAU;UACVC,MAAM,EAAEA,CAAC5C,MAAM,EAAEzG,aAAa,GAAC,MAAM,KAAK;YACxC,IAAGsB,MAAM,CAACoE,IAAI,CAACe,MAAM,CAAC,CAACd,MAAM,EAAC;cAC5B5F,aAAa,CAAC0G,MAAM,CAACzC,QAAQ,IAAIyC,MAAM,CAAC5D,IAAI,IAAI,YAAY,CAAC;cAC7D5C,gBAAgB,CAACD,aAAa,IAAI,MAAM,CAAC;YAC3C;UACF;QAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL3D,eAAe,iBACZzB,OAAA,CAACJ,kBAAkB;QACfuL,SAAS,EAAE1J,eAAgB;QAC3B2J,UAAU,EAAE1J;MAAmB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACJ,EAGAtE,YAAY,iBACXd,OAAA,CAACH,mBAAmB;QAClBsL,SAAS,EAAErK,YAAa;QACxBsK,UAAU,EAAErK,eAAgB;QAC5BG,WAAW,EAAEA;MAAY;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACF,EAEA9D,QAAQ;MAAA;MACP;MACAtB,OAAA,CAAClB,SAAS;QAACgG,IAAI,EAAExD,QAAS;QAACC,WAAW,EAAEA,WAAY;QAACiD,OAAO,EAAEA,OAAQ;QAACN,UAAU,EAAEA,UAAW;QAACE,YAAY,EAAEA;MAAa;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC7H;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC/E,EAAA,CArjBID,mBAAmB;EAAA,QAWNV,WAAW,EAW+BF,8BAA8B,EAEdC,8CAA8C,EAEzFF,gCAAgC,EA0CpCI,kBAAkB,EAqP7BZ,WAAW;AAAA;AAAAsM,EAAA,GAzTxBjL,mBAAmB;AAwjBzB,eAAeA,mBAAmB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}