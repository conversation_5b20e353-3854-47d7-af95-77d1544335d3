<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Seat;

class SeatFactory extends Factory
{
    protected $model = Seat::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'row' => $this->faker->numberBetween(1, 5), // Generates rows from 1 to 5
            'seat' => $this->faker->numberBetween(1, 6), // Each row has up to 6 seats
        ];
    }
}

