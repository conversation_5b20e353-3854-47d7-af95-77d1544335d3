<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Role;
use App\Models\Resource_status;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

use Illuminate\Support\Facades\Log;

class ResourceStatusController extends Controller
{
    /**
     * Show all resource_status with relevant relationships.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $resource_status = Resource_status::all();
    
        // Log the resource_status retrieved
        Log::info('All resource_status Retrieved:', ['resource_status_count' => $resource_status->count()]);
    
        return response()->json(['resource_status' => $resource_status], 200);
    }

    // Filter logic for data table
    public function resourceStatusData(Request $request)
    {
        $query = Resource_status::with(['creator', 'updater']);
    
        // Decode all input parameters to handle URL-encoded values
        $decodedStatusName = $request->filled('name') ? urldecode($request->input('name')) : null;
        $decodedCreatedAt = $request->filled('created_at') ? urldecode($request->input('created_at')) : null;
        $decodedUpdatedAt = $request->filled('updated_at') ? urldecode($request->input('updated_at')) : null;
        $decodedCreatedBy = $request->filled('created_by') ? urldecode($request->input('created_by')) : null;
        $decodedUpdatedBy = $request->filled('updated_by') ? urldecode($request->input('updated_by')) : null;
    
        // Filtering by name
        if ($decodedStatusName) {
            $names = explode(',', $decodedStatusName);
            $query->where(function ($q) use ($names) {
                foreach ($names as $name) {
                    $q->orWhere('name', 'like', '%' . trim($name) . '%');
                }
            });
        }

        // Filtering by created_at
        if ($decodedCreatedAt) {
            $decodedCreatedAts = explode(',', $decodedCreatedAt);
            $query->where(function ($q) use ($decodedCreatedAts) {
                foreach ($decodedCreatedAts as $decodedCreatedAt) {
                    $q->orWhere('created_at', '=', trim($decodedCreatedAt));
                }
            });
        }
    
        // Filtering by updated_at
        if ($decodedUpdatedAt) {
            $decodedUpdatedAts = explode(',', $decodedUpdatedAt);
            $query->where(function ($q) use ($decodedUpdatedAts) {
                foreach ($decodedUpdatedAts as $decodedUpdated) {
                    $q->orWhere('updated_at', '=', trim($decodedUpdatedAt));
                }
            });
        }

        // Filtering by created_by
        if ($decodedCreatedBy) {
            $decodedCreateds = explode(',', $decodedCreatedBy);
            $query->where(function ($q) use ($decodedCreateds) {
                foreach ($decodedCreateds as $decodedCreated) {
                    $q->orWhere('created_by', '=', trim($decodedCreated));
                }
            });
        }
    
        // Filtering by updated_by
        if ($decodedUpdatedBy) {
            $decodedUpdateds = explode(',', $decodedUpdatedBy);
            $query->where(function ($q) use ($decodedUpdateds) {
                foreach ($decodedUpdateds as $decodedUpdated) {
                    $q->orWhere('updated_by', '=', trim($decodedUpdated));
                }
            });
        }
    
        // Global search logic
        $globalSearch = $request->filled('globalsearch') ? urldecode($request->input('globalsearch')) : null;
        if ($globalSearch) {
            $query->where(function ($q) use ($globalSearch) {
                $q->orWhere('name', 'like', '%' . $globalSearch . '%')
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('creator', function ($query) use ($globalSearch) {
                        $query->where('lname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    })
                    ->orWhereHas('updater', function ($query) use ($globalSearch) {
                        $query->where('fname', 'like', '%' . $globalSearch . '%');
                    });

            });
        }
    
        // Sorting: Use query parameters 'sort_by' and 'order'
        $sortBy = $request->query('sort_by', 'created_at');
        $order = $request->query('order', 'desc');
    
        // Validate order parameter
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
    
        $query->orderBy($sortBy, $order);
    
        // Pagination: Accept a 'per_page' parameter, defaulting to 15 if not provided
        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);
        $productTypes = $query->paginate($perPage, ['*'], 'page', $page);
    
        return response()->json($productTypes, 200);
    }

    public function searchByField(Request $request)
    {
        // Retrieve and decode the 'column' and 'text' parameters from the URL
        $encodedColumn = $request->query('column');
        $encodedText = $request->query('text');
        if (!$encodedColumn) {
            return response()->json(['error' => 'The parameter is required.'], 400);
        }

        $column = urldecode($encodedColumn);
        $text = urldecode($encodedText);

        // Perform the search on the specified column
        $results = Resource_status::with(['creator', 'updater']);

        if (strpos($column, ".") !== false) {
            $columnExp = explode('.', $column);
            $tblName = $columnExp[0];
            $fieldName = $columnExp[1];

            $results->whereHas($tblName, function ($query) use ($text, $fieldName) {
                $query->where($fieldName, 'like', '%' . $text . '%');
            });
        } else {
            $results->where($column, 'like', '%' . $text . '%');
        }

        // Return the search results as a JSON response
        return response()->json($results->get(), 200);
    }

    
    public function group(Request $request)
    {
        // Retrieve the dynamic column name from query parameters.
        $column = $request->query('column');
        if (!$column) {
            return response()->json(['error' => 'The group_by parameter is required.'], 400);
        }
        
        // Build the query: Select the group column and the count of records in each group.
        $results = Resource_status::with(['creator','updater']);
        $results->select($column, $column. ' as status', \DB::raw("COUNT(*) as total"));
        $results->groupBy($column)->orderBy($column);

            
        return response()->json($results->get(), 200);
    }
    

    /**
     * Display the specified Resource status.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the resource status by ID
        $resource_status = Resource_status::find($id);

        if (!$resource_status) {
            return response()->json(['error' => 'Resource status not found.'], 404);
        }

        // Log the resource_status retrieved
        Log::info('Resource status Retrieved:', ['Resource status' => $resource_status]);

        return response()->json(['Resource status' => $resource_status], 200);
    }


        /**
     * Create a new resource_status by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createResourceStatus(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', ['user_id' => $authUser->id, 'fname' => $authUser->fname, 'lname' => $authUser->lname]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        // Log the request data
        Log::info('Create resource status Request:', ['request' => $request->all()]);

        // Check if the resource status name already exists
        if (Resource_status::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'Resource status already exists.'], 409);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Create a new resource status with the full name
            $resource_status = Resource_status::create([
                'name' => $request->name,
                'created_by' => $authUser->id
            ]);

            Log::info('Resource status Created:', ['Resource status' => $resource_status]);

            return response()->json(['message' => 'Resource status created successfully.', 'Resource status' => $resource_status], 201);
        }

        // Deny access for other roles
        Log::warning('Unauthorized resource status Creation Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to create a Resource status.'], 403);
    }


    /**
     * Update an existing resource status by Super Admin or Admin.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateResourceStatus(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255'
        ]);

        // Log the request data
        Log::info('Update resource status Request:', ['request' => $request->all()]);

        // Find the resource status by ID
        $resource_status = Resource_status::find($id);
        
        if (!$resource_status) {
            return response()->json(['error' => 'Resource status not found.'], 404);
        }

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Check if the resource status name is being updated and does not already exist
            if ($resource_status->name !== $request->name && Resource_status::where('name', $request->name)->exists()) {
                return response()->json(['error' => 'Resource status name already exists.'], 409);
            }

            // Update the Resource status
            $resource_status->name = $request->name;
            $resource_status->updated_by = $authUser->id;
            $resource_status->save();

            // Log the updated Resource status
            Log::info('Resource status Updated:', ['Resource status' => $resource_status]);

            return response()->json(['message' => 'Resource status updated successfully.', 'Resource status' => $resource_status], 200);
        }

        // Deny access for other roles
        Log::warning('Unauthorized resource status Update Attempt:', ['user_id' => $authUser->id]);
        return response()->json(['error' => 'You do not have permission to update this Resource status.'], 403);
    }


    /**
     * Delete a resource status by Super Admin or Admin.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteResourceStatus($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the Resource status
            $resource_status = Resource_status::findOrFail($id);

            // Delete the Resource status
            $resource_status->delete();

            return response()->json(['message' => 'Resource status deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this Resource status.'], 403);
    }

}
