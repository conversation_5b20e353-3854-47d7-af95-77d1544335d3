import React, { useEffect, useState } from "react";
import moment from "moment-timezone";
import DynamicTimeCard from "./DynamicTimeCard";
import { Calendar, momentLocalizer } from 'react-big-calendar'
import 'react-big-calendar/lib/css/react-big-calendar.css';
import data from "./data";
import AttendanceList from "./AttendanceList";
import {AttendanceBtn, BreakBtn} from '../../../common/coreui';
import { useGetUserAttendanceByDateQuery, useGetUserAttendanceByIDQuery } from "../../../features/api";
import { startEndDuration, sumDurations, dbDateFormat, defaultDateTimeFormat, defaultDurationFormat } from "../../../utils";

function Attendance() {
  const defaultDateFormat = "dddd, LL";
  const defaultTimeFormat = "hh:mm A";

  const localizer = momentLocalizer(moment)

  const [defaultTimeZone, setDefaultTimeZone] = useState({
    label: "Dhaka",
    value: "Asia/Dhaka",
  }); // Store team data
  const [currentDateTime, setCurrentDateTime] = useState(
    moment().tz(defaultTimeZone["value"])
  ); // Store team data
  const [error, setError] = useState(null); // Handle errors
  const [loading, setLoading] = useState(true); // Loading state
  const [showCalendar, setShowCalendar] = useState(false);
  const [userDataForCalendar, setUserDataForCalendar] = useState({});

  const [userId, setUserId] = useState(localStorage.getItem("user_id"));
  const [date, setDate] = useState(dbDateFormat());
  const [totalTodaysBreak, setTotalTodaysBreak] = useState("00:00:00");
  const [totalTodaysWork, setTotalTodaysWork] = useState("00:00:00");

  // const { data:attendanceByUserId, error:fetchErrorAttendanceByUserId, isFetching: isFetchingAttendanceByUserId, refetch: refetchAttendanceByUserId } = useGetUserAttendanceByIDQuery({
  //   user_id: userId,
  //   date:date,
  // });
  
  const { data:attendanceTodayData, error:fetchError, isFetching, refetch } = useGetUserAttendanceByDateQuery({
    user_id: userId,
    date:date,
  });

  const { data:attendanceUserData } = useGetUserAttendanceByIDQuery({
    user_id: userId,
  });

  useEffect(() => {
    if (Array.isArray(attendanceUserData)) {
      console.log("Attendance User Data: ", attendanceUserData);
  
      const userData = attendanceUserData.map((item) => {
        if (item.duration === null || item.duration === undefined || item.duration === "") {
          return null;
        }
  
        let date = item.date ? moment(item.date).format("YYYY-MM-DD") : null;
        let start = item.start ? moment(item.start).format("YYYY-MM-DDTHH:mm:ss") : null;
        let end = item.end ? moment(item.end).format("YYYY-MM-DDTHH:mm:ss") : null;
        let title = "";
        let colorBg = '#bbf7d0';
        let color = '#4d7c0f';
  
        switch (item.entry_type) {
          case "attendance":
            title = `Present: ${defaultDateTimeFormat(item.start)}`;
            if (item.end) {
              title += ` to ${defaultDateTimeFormat(item.end)}`;
            }
            break;
          case "break":
            title = `Break: ${defaultDurationFormat(item.duration)}`;
            colorBg = '#fdf6b2';
            color = '#723b13';
            break;
          case "leave":
            title = `Leave`;
            start = null;
            end = null;
            break;
          case "late_entry":
            title = `Late Entry: ${defaultDateTimeFormat(item.start)}`;
            colorBg = '#f78ddd';
            color = '#000';
            break;
          case "early_leave":
            title = `Early Leave: ${defaultDateTimeFormat(item.end)}`;
            colorBg = '#ffd7d7';
            color = '#600101';
            break;
          default:
            title = `${item.entry_type.replaceAll("_", " ")}: ${item.duration}`;
        }
  
        return {
          colorBg,
          color,
          title,
          start: start ? new Date(start) : new Date(date),
          end: end ? new Date(end) : new Date(date),
          allDay: start && end ? false : true,
        };
      }).filter(Boolean);
  
      setUserDataForCalendar(userData);
      setShowCalendar(true);
      console.log("User Data: ", userData);
    } else {
      console.warn("attendanceUserData is not an array:", attendanceUserData);
    }
  }, [attendanceUserData]);
  


  const calculateTodaysAttendance = () => {
    if (attendanceTodayData) {
      if(attendanceTodayData?.break && attendanceTodayData.break.length > 0){
        setTotalTodaysBreak(sumDurations(attendanceTodayData.break));
      }
      
      if(attendanceTodayData?.attendance && attendanceTodayData.attendance.length > 0 && attendanceTodayData.attendance[0].start){
        setTotalTodaysWork(startEndDuration(attendanceTodayData.attendance[0].start, attendanceTodayData.attendance[0].end));
      }
    }
  }

  useEffect(() => {
    if (attendanceTodayData && !isFetching) {
      const interval = setInterval(calculateTodaysAttendance, 1000 * 1);
      return () => clearInterval(interval);
    } else if (fetchError) {
      setError(fetchError);
    } 
  }, [attendanceTodayData, isFetching, fetchError]);


  
  return (
    <section className="bg-white dark:bg-gray-900 px-4 py-2 rounded-xl text-[#0F172A]">
      <div className="flex justify-start items-start flex-row mb-[20px]">
        <div className="text-left w-1/2 ">
          <h4 className="text-xl font-medium ">
            Attendance
          </h4>
        </div>
      </div>

      <section className="bg-[#0b333f] text-[#fff] dark:bg-gray-900 p-4 rounded-xl mb-[50px]">
          <div className="flex justify-center items-center flex-row  gap-2 px-3 py-5   border border-gray-200 rounded-xl   ">
            {/* Current Date Time */}
            <div className="w-3/12 ">
                <DynamicTimeCard />
            </div>
          
            <div className="w-5/12 flex flex-row  gap-1">
                {isFetching && !data && <div className="w-full text-white">Loding...</div>}
                {data && !isFetching && <>
                <div className="w-6/12  ">
                    <h6 className="font-normal text-lg ">Total Working Hour Today</h6>
                    <h1 className="text-[48px] font-bold leading-none">{totalTodaysWork}</h1>
                </div>
                
                <div className="w-6/12 ">
                    <h6 className="font-normal text-lg ">Total Break Hour Today</h6>
                    <h1 className="text-[48px] font-bold leading-none">{totalTodaysBreak}</h1>
                </div>
                </>}
            </div>
            
            <div className="  w-4/12  ">
            <div className=" w-auto h-full   rounded-xl flex justify-center items-center flex-row  gap-3 ">
                {/* <span className="font-semibold text-sm">Add Your Attendance</span> */}
                <AttendanceBtn />
                <BreakBtn />
                {/* <AttendanceBtn entry_type="break"  /> */}
                
            </div>
            </div>
          </div>
      </section>

      <div className="w-full flex justify-start items-start flex-wrap border border-gray-200 rounded-xl p-4 mb-10">
              <div className="w-full flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-2">
                 <div className="w-8/12  text-start">
                   <h2 className="text-lg font-semibold flex items-center align-middle">
                   <span className="material-symbols-outlined me-2">
                    calendar_month
                    </span>
                    Attendance Calendar
                    </h2>
                 </div>
                 <div className="w-4/12 flex items-end justify-end text-end gap-1">
                   {/* Button to open modal for adding a new formation */}
                   <button
                     className="text-center justify-center items-center  py-2 px-8 text-sm font-medium focus:outline-none bg-primary text-white rounded-full border border-gray-200 hover:bg-black-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                     onClick={() => {
                      setShowCalendar(!showCalendar)
                     }}
                   >
                     {showCalendar? "Hide" : "Show"}
                   </button>
                 </div>
               </div>

       {showCalendar && <div className="w-full flex justify-start items-start flex-wrap  my-5">
            <Calendar
              localizer={localizer}
              events={userDataForCalendar}
              startAccessor="start"
              endAccessor="end"
              style={{ height: 750, width: '100%' }}
              defaultView='week'
              culture="en-GB"
              scrollToTime={new Date()}
              eventPropGetter={(myEventsList) => {
                const backgroundColor = myEventsList.colorBg ? myEventsList.colorBg : '#e7e7e7';
                const color = myEventsList.color ? myEventsList.color : '#727272';
                return { style: { backgroundColor ,color, fontWeight: 400, fontSize: "11px"} }
              }}
              tileDisabled={({ date, view }) =>
                view === "month" && (date.getDay() === 0 || date.getDay() === 6)
              }
              
            />
        </div>}
      </div>
      
       <AttendanceList />
    </section>
  );
}

export default Attendance;
