import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FetchLogin } from './../common/fetchData/FetchLogin';
import BgImg from './../assets/images/login-bg.png';
import { alertMessage } from './coreui'; // Import alertMessage

export default function Login() {
  const [eid, setEid] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false); // State to toggle password visibility
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Check if the user is already logged in by looking for both user_id and token in localStorage
    const userId = localStorage.getItem('user_id');
    const token = localStorage.getItem('token');
    if (userId && token) {
      navigate('/'); // Redirect to the dashboard or another page if already logged in
    }
  }, [navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
  
    // Client-side validation
    if (!eid || !password) {
      alertMessage({
        icon: 'error',
        title: 'Invalid Input',
        details: 'Please fill in both fields.',
      });
      return;
    }
  
    setLoading(true);
  
    try {
      const data = await FetchLogin(eid, password);
  
      // On successful login
      navigate('/'); // Redirect to dashboard or home
  
      setLoading(false);
  
      alertMessage({
        icon: 'success',
        title: 'Login Successful',
        details: 'Welcome back! You have successfully logged in.',
      });
    } catch (err) {
      setLoading(false);
  
      const errorMessage = err.message;
  
      if (errorMessage.includes('The provided credentials are incorrect')) {
        alertMessage({
          icon: 'error',
          title: 'Invalid Credentials',
          details: 'The Employee ID or password is incorrect. Please try again.',
        });
      } else if (errorMessage.includes('Please verify your identity address to login')) {
        alertMessage({
          icon: 'error',
          title: 'Email Not Verified',
          details: 'Your email is not verified. Please check your inbox and verify your email to log in.',
        });
      } else if (errorMessage.includes('Your account is not active')) {
        alertMessage({
          icon: 'error',
          title: 'Account Inactive',
          details: 'Your account is currently inactive. Please contact your manager for assistance.',
        });
      } else {
        alertMessage({
          icon: 'error',
          title: 'Login Failed',
          details: errorMessage || 'Login failed. Please check your credentials and try again.',
        });
      }
    }
  };
  

  return (
    <section className="bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
        <div className="w-full bg-white rounded-3xl shadow dark:border md:mt-0 sm:max-w-6xl xl:p-0 dark:bg-gray-800 dark:border-gray-700 flex flex-col sm:flex-row items-center justify-center gap-12">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8 w-full sm:w-1/2 text-left">
            <h6 className="text-base text-gray-600 pb-1">Welcome back! 👋</h6>
            <h1 className="text-2xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white mt-0">
              Resume Your Journey
            </h1>
            <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
              <div className="relative">
                <label htmlFor="eid" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Employee ID</label>
                <input
                  type="number"
                  id="eid"
                  value={eid}
                  onChange={(e) => setEid(e.target.value)}
                  className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  placeholder="Enter your EID"
                  required
                />
                <span className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400">dialpad</span>
              </div>

              <div className="relative">
                <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Password</label>
                <input
                  type={isPasswordVisible ? "text" : "password"} // Toggle password visibility
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  className="bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  required
                />
                {/* Visibility icon */}
                <span
                  onClick={() => setIsPasswordVisible(!isPasswordVisible)} // Toggle the password visibility
                  className="material-symbols-rounded text-xl absolute right-2 bottom-2 text-gray-400 cursor-pointer"
                >
                  {isPasswordVisible ? 'visibility_off' : 'visibility'} {/* Toggle icon based on visibility state */}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="remember"
                      type="checkbox"
                      className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                className="w-full text-white bg-secondaryOrange hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-xl text-sm px-5 py-3 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              >
                {loading ? 'Login...' : 'Access Your World'}
              </button>

              <p className="text-sm font-light text-gray-500 dark:text-gray-400">
                Lost your key? <Link to="/reset-password" className="font-medium text-primary hover:underline dark:text-primary-500">Let’s get you a new one!</Link>
              </p>
            </form>
          </div>

          <div className="bg-primary rounded-3xl w-full sm:w-1/2 my-6 mr-6">
            <h2 className="text-white text-4xl font-bold pt-16 px-12 pb-8 leading-normal text-left">Welcome back, Creator! Dive in and bring your best ideas to life!</h2>
            <img src={BgImg} alt="Login background" />
          </div>
        </div>
      </div>
    </section>
  );
}
