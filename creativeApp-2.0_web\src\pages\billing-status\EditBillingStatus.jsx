import React, { useEffect, useState } from 'react';
import { alertMessage } from '../../common/coreui';

const API_URL = process.env.REACT_APP_BASE_API_URL;

const EditBillingStatus = ({ isVisible, setVisible, dataItemsId }) => {
    const [billingStatusName, setBillingStatusName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch billing status by ID
    useEffect(() => {
        const fetchBillingStatus = async () => {
            if (!dataItemsId) return;

            const token = localStorage.getItem('token');
            if (!token) {
                setError('No authentication token found.');
                return;
            }

            try {
                const response = await fetch(`${API_URL}/billing_statuses`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch billing statuses: ' + response.statusText);
                }

                const data = await response.json();
                const statusArray = data['billing statuses'];

                if (!Array.isArray(statusArray)) {
                    throw new Error('Expected billing statuses to be an array.');
                }

                const matchedStatus = statusArray.find(status => status.id === dataItemsId);
                if (matchedStatus) {
                    setBillingStatusName(matchedStatus.name);
                } else {
                    throw new Error('Billing status not found.');
                }
            } catch (error) {
                setError(error.message);
            }
        };

        fetchBillingStatus();
    }, [dataItemsId]);

    // Handle form submission
    const handleSubmit = async (event) => {
        event.preventDefault();

        // Get user_id from localStorage for 'created_by'
        const updatedBy = loggedInUser;

        if (!updatedBy) {
            setError('User is not logged in.');
            return;
        }

        const token = localStorage.getItem('token');
        if (!token) {
            setError('Authentication token is missing.');
            return;
        }

        try {
            const response = await fetch(`${API_URL}/billing_statuses/${dataItemsId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: billingStatusName.trim(),
                    updated_by: updatedBy,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update Billing status: ' + response.statusText);
            }

            const result = await response.json();

            //setSuccessMessage(`Billing status "${result.name}" updated successfully!`);
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'Billing status updated successfully.',
            });

            setTimeout(() => {
                setVisible(false);
                setSuccessMessage('');
            }, 1000);
        } catch (error) {
            alertMessage('error');
        }
    };

    if (!isVisible) return null;

    return (
        <div
            className="fixed top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center bg-gray-800 bg-opacity-50"
            onClick={() => setVisible(false)}
        >
            <div
                className="relative bg-white rounded-lg shadow-lg max-w-md w-full"
                onClick={(e) => e.stopPropagation()}
            >

                <div className="flex justify-between items-center mb-4 bg-gray-100 p-4">
                    <h3 className="text-base text-left font-medium text-gray-800">Update Billing Status</h3>
                    <button
                        className="text-2xl text-gray-500 hover:text-gray-800"
                        onClick={() => setVisible(false)}
                    >
                        &times;
                    </button>
                </div>

                <form onSubmit={handleSubmit} className='p-6'>
                    <div className="mb-4">
                        <label htmlFor="name" className="block mb-2 text-left text-sm text-gray-600">Billing Status Name</label>
                        <input
                            type="text"
                            id="name"
                            value={billingStatusName}
                            onChange={(e) => setBillingStatusName(e.target.value)}
                            className="border rounded w-full p-2"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        className="bg-primary hover:bg-secondary text-white rounded-md px-4 py-2"
                    >
                        Update Billing Status
                    </button>
                </form>
            </div>
        </div>
    );
};

export default EditBillingStatus;
