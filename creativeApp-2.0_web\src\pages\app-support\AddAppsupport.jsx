import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import ReactQuill from 'react-quill';
import EditorToolbar, { modules, formats } from "./EditorToolbar";
import 'react-quill/dist/quill.snow.css';

import {API_URL} from './../../common/fetchData/apiConfig.js';
import { alertMessage } from '../../common/coreui/alertMessage.js';


const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};

const AddAppsupport = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [name, setName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');

    const handleSubmit = async (event) => {
        event.preventDefault();

        if ( !name) {
            setError('Please fill all fields.');
            return;
        }

        setError('');
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            const response = await fetch(`${API_URL}app-support`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to add about the app.');
            }

            const result = await response.json();
            //setSuccessMessage('About the App added successfully!');

            // ✅ Success alert
            alertMessage({
                icon: 'success',
                title: 'Success!',
                text: result?.message || 'About the App added successfully.',
            });

            setName('');
        } catch (error) {
            alertMessage('error');
        }
    };

    const isModalOpen = location.pathname === '/add-app-support';

    const handleClose = () => {
        navigate('/app-support');
    };

    return (
        <>
            {isModalOpen && (
                <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                    <div className="bg-white rounded-lg shadow-md w-full max-w-2xl relative overflow-y-auto max-h-[80vh]">
      
                        <div className="flex justify-between items-center mb-4 bg-gray-100 px-4 py-2">
                            <h4 className="text-base text-left font-medium text-gray-800">Add New App Support</h4>
                            <button
                                className="text-3xl text-gray-500 hover:text-gray-800"
                                onClick={handleClose}
                            >
                                &times;
                            </button>
                        </div>
                        <form onSubmit={handleSubmit} className='p-6'>
                            <div className="mb-4 text-left">
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 pb-4">
                                    Description
                                </label>
                                <EditorToolbar />
                                <ReactQuill
                                    id="name"
                                    value={name}
                                    onChange={setName}
                                    className="bg-white border border-gray-300 rounded-md shadow-sm"
                                    theme="snow"
                                    modules={modules}
                                    formats={formats}
                                />
                            </div>

                            <div className="py-4">
                                <button
                                    type="submit"
                                    className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                                >
                                    Add App Support
                                </button>
                            </div>

                            {error && <p className="text-red-500 text-sm">{error}</p>}
                            {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                        </form>
                    </div>
                </div>
            )}
        </>
    );
};

export default AddAppsupport;
