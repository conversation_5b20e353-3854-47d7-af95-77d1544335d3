<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormationType extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'short_title',
        'details',
        'is_active',
        'type',
        'alocated_leave',
        'year',
        'team_id',
        'department_id',
        'created_by',
        'updated_by',
    ];

     /**
     * Relationship to get the Department record.
     */
    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

     /**
     * Relationship to get the Department record.
     */
    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
