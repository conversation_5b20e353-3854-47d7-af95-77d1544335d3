import React, { useState } from 'react';
import TimeCardDataList from '../../pages/time-card/TimeCardDataList';

const TimeCard = () => {
  const [searchTerm, setSearchTerm] = useState('');


  // Handle search input changes
  const handleSearch = (searchTerm) => {
    setSearchTerm(searchTerm);
  };

  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TimeCardDataList />
    </div>
  );
};

export default TimeCard;
