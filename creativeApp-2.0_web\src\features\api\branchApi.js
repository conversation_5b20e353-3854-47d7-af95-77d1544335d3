import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const branchApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getBranchData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `branch-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['BranchData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForBranch: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `branch-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['BranchData'],
    }),

    getBranchById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `branches/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'BranchData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createBranch: builder.mutation({
      query: (newFormationType) => ({
        url: 'branch-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['BranchData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateBranch: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `branches/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'BranchData', id }, 'BranchData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteBranch: builder.mutation({
      query: (id) => ({
        url: `branches/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BranchData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetBranchDataQuery,
  useLazyFetchDataOptionsForBranchQuery,
  useGetBranchByIdQuery,
  useLazyGetBranchByIdQuery,
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
} = branchApi;
