import React, { useEffect, useState } from 'react';
import TableContent from '../../../common/table/TableContent';
import TablePagination from '../../../common/table/TablePagination';
import EditTaskRecord from './EditTaskRecord';
import { API_URL } from './../../../common/fetchData/apiConfig'; 
import { DateTimeFormatTable, DateTimeFormatHour } from '../../../common/DateTimeFormatTable';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token !== null;
};


const TaskRecordList = ({ searchTerm }) => {
    const [taskDetails, setTaskDetails] = useState([]);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedTaskId, setSelectedTaskId] = useState(null);
    const [filteredTasks, setFilteredTasks] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 20;

    const columnNames = [
        { label: "SL", key: "id" },
        { label: "Department", key: "department" },
        { label: "Team", key: "team" },
        { label: "Date", key: "date" },
        { label: "Task Created At", key: "created_at" },
        { label: "Task Added By", key: "full_name" },
        { label: "Task Ticket Number", key: "ticket_number" },
        { label: "Month", key: "month" },
        { label: "Week", key: "week" },
        { label: "Received Date", key: "received_date" },
        { label: "Due Date", key: "due_date" },
        { label: "Priority", key: "priority" },
        { label: "Task Type", key: "task_type" },
        { label: "Revision Type", key: "revision_type" },
        { label: "Product Type", key: "product_type" },
        { label: "Unit", key: "unit" },
        { label: "Reporter", key: "reporter" },
        { label: "Region", key: "region" },
        { label: "Account Name", key: "account_name" },
        { label: "Campaign Name", key: "campaign_name" },
        { label: "Notes/Comments", key: "notes" },
    ];

    useEffect(() => {
        const fetchTaskDetails = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }
    
            const token = localStorage.getItem('token');
    
            // Function to get the week number from the date
            const getWeekNumber = (dateString) => {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return 'N/A';
                const startDate = new Date(date.getFullYear(), 0, 1);
                const days = Math.floor((date - startDate) / (24 * 60 * 60 * 1000));
                return Math.ceil((days + 1) / 7);
            };
    
            try {
                const response = await fetch(`${API_URL}task-details`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error('Failed to fetch users: ' + response.statusText);
                }
    
                const data = await response.json();
                console.log('Task Details:', data);
    
                // Sort tasks in descending order based on `created_at` date
                const sortedTasks = data.taskDetails.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
                // Map through sorted tasks
                const mappedTasks = sortedTasks.map((task, index) => ({
                    id: index + 1, // Replacing the ID with the task number (1-based)
                    department: task.department || 'N/A',
                    team: task.team || 'N/A',
                    date: DateTimeFormatTable(task.created_at) || 'N/A', // Format date
                    created_at: DateTimeFormatHour(task.created_at) || 'N/A', // Format date
                    full_name: task.created_by || 'N/A',
                    ticket_number: task.ticket_number || 'N/A',
                    month: task.created_at ? new Date(task.created_at).toLocaleString('en-GB', { month: 'long' }) : 'N/A', // Month name from date
                    week: task.due_date ? getWeekNumber(task.due_date) : 'N/A', // Week number from date
                    received_date: DateTimeFormatTable(task.received_date) || 'N/A',
                    due_date: DateTimeFormatTable(task.due_date) || 'N/A',
                    priority: task.priorities?.name || 'N/A',
                    task_type: task.task_types?.name || 'N/A',
                    revision_type: task.revision_types?.name || 'N/A',
                    product_type: task.product_types?.name || 'N/A',
                    unit: task.unit || 'N/A',
                    reporter: task.reporters?.name || 'N/A',
                    region: task.regions?.name || 'N/A',
                    account_name: task.account_name || 'N/A',
                    campaign_name: task.campaign_name || 'N/A',
                    notes: task.notes || 'N/A',
                }));
    
                setTaskDetails(mappedTasks);
                setFilteredTasks(mappedTasks);
    
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };
    
        fetchTaskDetails();
    }, [currentPage, itemsPerPage]);
    

    // Filter search
    useEffect(() => {
        const normalizedSearchTerm = searchTerm ? searchTerm.toLowerCase().trim() : '';

        const highlightText = (text) => {
            const strText = text ? text.toString() : ''; // Ensuring it's always a string

            const regex = new RegExp(`(${normalizedSearchTerm})`, 'gi');
            const parts = strText.split(regex);

            return parts.map((part, index) => {
                return regex.test(part) ? (
                    <span key={index} className="bg-yellow-300">{part}</span>
                ) : part;
            });
        };

        if (!normalizedSearchTerm) {
            setFilteredTasks(taskDetails);
            return;
        }

        const filtered = taskDetails.filter(task => {
            return Object.values(task).some(value => {
                const valueStr = value ? value.toString().toLowerCase() : ''; // Safeguard for undefined/null
                return valueStr.includes(normalizedSearchTerm);
            });
        }).map(task => ({
            id: task.id,
            ticket_number: highlightText(task.ticket_number),
            month: highlightText(task.month),
            week: highlightText(task.week),
            received_date: highlightText(task.received_date),
            due_date: highlightText(task.due_date),
            unit: highlightText(task.unit),
            account_name: highlightText(task.account_name),
            campaign_name: highlightText(task.campaign_name),
            notes: highlightText(task.notes),
            product_type_id: highlightText(task.product_type_id),
        }));

        setFilteredTasks(filtered);
    }, [searchTerm, taskDetails]);

    // Pagination logic
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentPageUsers = filteredTasks.slice(startIndex, startIndex + itemsPerPage);

    const handleEdit = (id) => {
        setSelectedTaskId(id);
        setModalVisible(true);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    if (loading) {
        return <div className="text-gray-500">Loading...</div>;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    // Show "No data found" message if filteredTasks is empty
    if (filteredTasks.length === 0) {
        return <div className="text-center text-lg text-gray-500">No data found, please add data to see in the list.</div>;
    }

    return (
        <div>
            <TableContent
                tableContent={currentPageUsers}
                columnNames={columnNames}
                onEdit={handleEdit}
                setModalVisible={setModalVisible}
                setSelectedServiceId={setSelectedTaskId}
                hideDeleteButton={true}
            />
            <TablePagination
                currentPage={currentPage}
                totalItems={filteredTasks.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
            />
            {modalVisible && selectedTaskId && (
                <EditTaskRecord 
                    isVisible={modalVisible}
                    setVisible={setModalVisible}
                    taskId={selectedTaskId}
                />
            )}
        </div>
    );
};

export default TaskRecordList;
