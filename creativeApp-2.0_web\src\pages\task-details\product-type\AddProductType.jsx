import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useFetchApiData from '../../../common/fetchData/useFetchApiData'; // Importing custom hook
import { alertMessage } from '../../../common/coreui';

const isTokenValid = () => {
    const token = localStorage.getItem('token');
    return token && token !== 'null';
};

const API_URL = process.env.REACT_APP_BASE_API_URL+'/';

const AddProductType = ({isVisible, setVisible}) => {
    const location = useLocation();
    const navigate = useNavigate();
    const [departments, setDepartments] = useState([]);
    const [teams, setTeams] = useState([]);
    const [selectedDepartment, setSelectedDepartment] = useState('');
    const [selectedTeam, setSelectedTeam] = useState('');
    const [productTypeName, setProductTypeName] = useState('');
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loggedInUser, setLoggedInUser] = useState(null);

    // Fetch logged-in user data (user_id)
    useEffect(() => {
        const userId = localStorage.getItem('user_id');
        if (userId) {
            setLoggedInUser(userId);
        }
    }, []);

    // Fetch departments using the custom hook
    const token = localStorage.getItem('token');

    const { data: departmentsData } = useFetchApiData(`${API_URL}departments`, token);

    useEffect(() => {
        if (departmentsData) {
            setDepartments(departmentsData.departments || []);
        }
    }, [departmentsData]);

    // Handle Department Change and Fetch Teams
    const handleDepartmentChange = (e) => {
        const departmentName = e.target.value;
        setSelectedDepartment(departmentName);
        setSelectedTeam(''); // Reset team when department changes
    
        if (departmentName) {
            // Find the department object using the selected name
            const department = departments.find(dep => dep.name === departmentName);
    
            if (department && department.teams && department.teams.length > 0) {
                setTeams(department.teams); // Set teams related to the selected department
            } else {
                setTeams([]); // Clear teams if no teams available
            }
        } else {
            setTeams([]); // Clear teams if no department is selected
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
    
        if (!selectedDepartment || !selectedTeam || !productTypeName) {
            setError('Please fill all fields.');
            return;
        }
    
        setError('');

        const department = departments.find(dep => dep.name === selectedDepartment);
        const team = teams.find(t => t.name === selectedTeam);

        if (!department || !team) {
            setError('Invalid department or team selection.');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Authentication token is missing.');
                return;
            }

            // Get user_id from localStorage for 'created_by'
            const createdBy = loggedInUser;
            
            if (!createdBy) {
                setError('User is not logged in.');
                return;
            }

            const response = await fetch(`${API_URL}product-type`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    department_id: department.id,  // Send department ID
                    team_id: team.id,              // Send team ID
                    name: productTypeName,
                    created_by: createdBy,         // Send the logged-in user ID as 'created_by'
                }),
            });
    
            if (!response.ok) {
                throw new Error('Failed to add product type.');
            }
    
            const result = await response.json();
            //setSuccessMessage(`Product Type "${result.product_type.name}" added successfully!`);
            alertMessage('success');
            // setSelectedDepartment('');
            // setSelectedTeam('');
            setProductTypeName('');
        } catch (error) {
            //setError(error.message);
            alertMessage('error');
        }
    };


    if (!isVisible) return null;


    return (
        <>
            
            <div className="fixed top-0 left-0 right-0 bottom-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-hidden">
                <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-lg relative overflow-y-auto h-auto mt-10">
                    <button onClick={() => setVisible(false)} className="absolute top-2 right-2 text-gray-400 hover:text-gray-900">
                        &times;
                    </button>
                    <h4 className="text-xl font-semibold mb-4 py-4">Add New Product Type</h4>
                    <form onSubmit={handleSubmit}>
                        <div className="mb-4">
                            <label htmlFor="department" className="block text-sm font-medium text-gray-700 pb-4">
                                Select Department
                            </label>
                            <select
                                id="department"
                                value={selectedDepartment}
                                onChange={handleDepartmentChange}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select a Department</option>
                                {departments.length === 0 ? (
                                    <option disabled>No departments available</option>
                                ) : (
                                    departments.map((department) => (
                                        <option key={department.id} value={department.name}>
                                            {department.name}
                                        </option>
                                    ))
                                )}
                            </select>
                        </div>

                        <div className="mb-4">
                            <label htmlFor="team" className="block text-sm font-medium text-gray-700 pb-4">
                                Select Team
                            </label>
                            <select
                                id="team"
                                value={selectedTeam}
                                onChange={(e) => setSelectedTeam(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            >
                                <option value="">Select a Team</option>
                                {teams.length === 0 ? (
                                    <option disabled>No teams available</option>
                                ) : (
                                    teams.map((team) => (
                                        <option key={team.id} value={team.name}>
                                            {team.name}
                                        </option>
                                    ))
                                )}
                            </select>
                        </div>

                        <div className="mb-4">
                            <label htmlFor="productTypeName" className="block text-sm font-medium text-gray-700 pb-4">
                                Product Type Name
                            </label>
                            <input
                                id="productTypeName"
                                type="text"
                                value={productTypeName}
                                onChange={(e) => setProductTypeName(e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                                required
                            />
                        </div>

                        <div className="py-4">
                            <button
                                type="submit"
                                className="w-full bg-primary hover:bg-secondary text-white rounded-md py-3"
                            >
                                Add Product Type
                            </button>
                        </div>

                        {error && <p className="text-red-500 text-sm">{error}</p>}
                        {successMessage && <p className="text-green-500 text-sm">{successMessage}</p>}
                    </form>
                </div>
            </div>
            
        </>
    );
};

export default AddProductType;
