<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('formation_types', function (Blueprint $table) {
              $table->id();
              $table->string('title');
              $table->string('short_title')->nullable();
              $table->text('details')->nullable();
              $table->string('is_active')->default("active");
              $table->string('type');
              $table->string('year');
              $table->string('alocated_leave');
              $table->unsignedBigInteger('department_id');
              $table->unsignedBigInteger('team_id');
              $table->unsignedBigInteger('created_by')->nullable();
              $table->unsignedBigInteger('updated_by')->nullable();
              $table->timestamps();

            //   $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            //   $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null'); 
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('formation_types');
    }
};
