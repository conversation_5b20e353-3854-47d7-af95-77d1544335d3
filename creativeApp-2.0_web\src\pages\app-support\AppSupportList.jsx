import React, { useEffect, useState, useCallback } from 'react';
import EditAppSupport from './EditAppSupport';
import { useNavigate } from 'react-router-dom';
import { useRoleBasedAccess } from '../../common/useRoleBasedAccess';

import {API_URL} from './../../common/fetchData/apiConfig.js';

// Use an environment variable if available; fallback to the default URL.

const TOKEN_KEY = 'token';

const getToken = () => localStorage.getItem(TOKEN_KEY);
const isTokenValid = () => Boolean(getToken());

const AppSupportList = () => {
  const navigate = useNavigate();
  const [appSupports, setAppSupports] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedAppSupportId, setSelectedAppSupportId] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const { rolePermissions } = useRoleBasedAccess();

  // Fetch "App Support" data
  const fetchAppSupports = useCallback(async () => {
    setLoading(true);
    setError('');
    if (!isTokenValid()) {
      setError('No authentication token found.');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_URL}app-supports`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.statusText}`);
      }

      const data = await response.json();

      // Validate API response structure
      if (Array.isArray(data.appSupport)) {
        setAppSupports(
          data.appSupport.map((app) => ({
            id: app.id,
            name: app.name || 'No content available',
          }))
        );
      } else {
        throw new Error('Unexpected data format received.');
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAppSupports();
  }, [fetchAppSupports]);

  // Handle delete action
  const handleDelete = async (id) => {
    if (!isTokenValid()) {
      setError('No authentication token found.');
      return;
    }

    try {
      const response = await fetch(`${API_URL}app-support/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete entry: ${response.statusText}`);
      }

      // Remove deleted entry from state
      setAppSupports((prev) => prev.filter((app) => app.id !== id));
    } catch (err) {
      console.error('Error deleting data:', err);
      setError(err.message);
    }
  };

  // Handle edit action
  const handleEdit = (id) => {
    setSelectedAppSupportId(id);
    setModalVisible(true);
  };

  // Callback to close the modal
  const closeModal = () => {
    setModalVisible(false);
    setSelectedAppSupportId(null);
  };

  if (loading) return <div className="text-center py-4">Loading...</div>;
  if (error) return <div className="text-red-500 text-center py-4">{error}</div>;

  return (
    <div className="py-6">
            <div className="flex flex-wrap -mx-2 justify-end">
            {rolePermissions.hasAdminRole && (
                <div className="flex justify-end px-4 mt-4">
                    <button
                        onClick={() => navigate('/add-app-support')}
                        className="px-4 py-2 border border-gray-300 text-black rounded-md bg-white hover:bg-white hover:text-black-600"
                    >
                        Add New App Support
                    </button>
                </div>
            )}
                {appSupports.map((app) => (
                    <div key={app.id} className="w-full sm:w-1/1 md:w-1/1 p-4">
                        <div className="bg-white shadow-sm rounded-xl border border-gray-200 p-4">
                            <div
                                className="text-gray-600 mb-4 ql-editor ql-snow"
                                dangerouslySetInnerHTML={{ __html: app.name }}
                            />
                            {rolePermissions.hasAdminRole && (
                                <div className="flex justify-end gap-4">
                                    <button
                                        onClick={() => handleEdit(app.id)}
                                        className="px-4 py-2 border border-gray-300 text-black rounded-md hover:bg-gray-100"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => handleDelete(app.id)}
                                        className="px-4 py-2 border border-gray-300 text-black rounded-md hover:bg-gray-100"
                                    >
                                        Delete
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {modalVisible && (
        <EditAppSupport
          isVisible={modalVisible}
          setVisible={closeModal}
          appSupportId={selectedAppSupportId}
          // Optional: allow EditAppSupport to refresh the list after an edit
          refreshData={fetchAppSupports}
        />
      )}
    </div>
  );
};

export default AppSupportList;
