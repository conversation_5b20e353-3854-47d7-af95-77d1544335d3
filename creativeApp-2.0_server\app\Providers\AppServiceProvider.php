<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Carbon\Carbon;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //

        $ip = request()->ip(); // Get user's IP address
        $response = file_get_contents("http://ip-api.com/json");
        $data = json_decode($response, true);
        $timezone = $data['timezone'] ?? null;

        if($timezone){
            date_default_timezone_set($timezone); // Set PHP timezone
            Carbon::setTestNow(Carbon::now()->setTimezone($timezone)); 
        }
        

    }
}
