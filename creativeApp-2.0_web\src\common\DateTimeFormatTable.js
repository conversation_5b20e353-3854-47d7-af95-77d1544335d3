// utils/dateFormatter.js

// Format the date into a readable format (e.g., February 18, 2025)
export const DateTimeFormatTable = (dateString) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A'; // Handle invalid date

    // Format: February 18, 2025
    return new Intl.DateTimeFormat('en-US', {
        month: 'long',
        day: '2-digit',
        year: 'numeric',
    }).format(date);
};

// New method to format the time into HH:mm (24-hour format)
export const DateTimeFormatHour = (dateString) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A';

    // Format: 04:30 AM or 04:30 PM (12-hour format with AM/PM)
    return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,       // 12-hour format with AM/PM
    }).format(date);
};


export const DateTimeFormatDay = (dateString) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A'; // Handle invalid date

    // Format: Wednesday, 19 March 2025
    const formattedDate = new Intl.DateTimeFormat('en-GB', {
        weekday: 'long',   // Full weekday name (e.g., "Wednesday")
        day: '2-digit',    // Day in 2 digits (e.g., "19")
        month: 'long',     // Full month name (e.g., "March")
        year: 'numeric',   // Full year (e.g., "2025")
    }).format(date);

    // Add comma after the weekday
    const [weekday, day, month, year] = formattedDate.split(' ');
    return `${weekday}, ${day} ${month} ${year}`;
};



