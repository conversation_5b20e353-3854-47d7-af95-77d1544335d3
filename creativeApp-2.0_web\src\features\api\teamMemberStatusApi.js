import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const teamMemberStatusApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getMemberStatusData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `member-status-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['MemberStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForMemberStatus: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `member-status-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['MemberStatusData'],
    }),

    getMemberStatusById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `member_statuses/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'MemberStatusData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createMemberStatus: builder.mutation({
      query: (newFormationType) => ({
        url: 'member-status-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['MemberStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateMemberStatus: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `member_statuses/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'MemberStatusData', id }, 'MemberStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteMemberStatus: builder.mutation({
      query: (id) => ({
        url: `member_statuses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['MemberStatusData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetMemberStatusDataQuery,
  useLazyFetchDataOptionsForMemberStatusQuery,
  useGetMemberStatusByIdQuery,
  useLazyGetMemberStatusByIdQuery,
  useCreateMemberStatusMutation,
  useUpdateMemberStatusMutation,
  useDeleteMemberStatusMutation,
} = teamMemberStatusApi;
