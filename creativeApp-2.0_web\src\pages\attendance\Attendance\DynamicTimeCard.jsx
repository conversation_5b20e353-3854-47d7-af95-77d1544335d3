import React, { useEffect, useState } from "react";
import moment from "moment";
import "moment-timezone";

import {
  useGetCurrentDateTimeQuery 
} from "../../../features/api";

function DynamicTimeCard() {
  const defaultDateFormat = "dddd, LL";
  const defaultTimeFormat = "hh:mm A";

  // const [loading, setLoading] = useState(false); // Loading state
  const [defaultTimeZone, setDefaultTimeZone] = useState("Asia/Dhaka"); // Store team data
  const [currentDateTime, setCurrentDateTime] = useState(
    moment().tz(defaultTimeZone)
  ); // Store team data

  const { data, error, isLoading:loading } = useGetCurrentDateTimeQuery();

  // Fetch teams data
  useEffect(() => {
    if (!loading && data && !error) {
      setDefaultTimeZone(data["timezone"]);
    }
  }, [loading, data]);
 

  const updateTime = () => {
    setCurrentDateTime(moment().tz(defaultTimeZone));
  };

  // updateTime()
  useEffect(() => {
  const interval = setInterval(updateTime, 1000 * 1);
    return () => clearInterval(interval);
  }, []);

  return (
    <>
      {loading && <p>Loading...</p>}
      {!loading && (
        <>
          <h1 className=" text-[48px] font-bold leading-none">
            {currentDateTime.tz(defaultTimeZone).format("LTS")}
          </h1>
          <p className="font-normal text-xl">
            {currentDateTime
              .tz(defaultTimeZone)
              .format(defaultDateFormat) || ""}
          </p>
        </>
      )}
    </>
  );
}

export default DynamicTimeCard;
