import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AddQuickAccessHub from './AddQuickAccessHub';
import EditQuickAccessHub from './EditQuickAccessHub';
import { useRoleBasedAccess } from '../../common/useRoleBasedAccess';
import Loading from '../../common/Loading';
import { alertMessage, confirmationAlert } from '../../common/coreui';
import { API_URL, ASSET_URL } from './../../common/fetchData/apiConfig'; 

const getToken = () => localStorage.getItem('token');
const isTokenValid = () => getToken() !== null;

const QuickAccessHubview = () => {
    const [quickAccessHubs, setQuickAccessHubs] = useState([]);
    const [error, setError] = useState(null);
    const [selectedHubId, setSelectedHubId] = useState(null);
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [loading, setLoading] = useState(true);
    const [filterOptionLoading, setFilterOptionLoading] = useState(false);

    const { rolePermissions } = useRoleBasedAccess();

    // Fetching data from the API
    useEffect(() => {
        const fetchQuickAccessHubs = async () => {
            if (!isTokenValid()) {
                setError('No authentication token found.');
                setLoading(false);
                return;
            }

            setFilterOptionLoading(true);
    
            try {
                const response = await fetch(`${API_URL}quick-access-hubs`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${getToken()}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (!response.ok) {
                    throw new Error(`Network response was not ok: ${response.statusText}`);
                }
    
                const data = await response.json();
                
                // Corrected this part: Check for 'hubs' array, not 'hub'
                if (data.hubs && Array.isArray(data.hubs)) {
                    setQuickAccessHubs(data.hubs);  // Update the state with the 'hubs' array
                } else {
                    setError("Unexpected data format received.");
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setFilterOptionLoading(false);
            }
        };
    
        fetchQuickAccessHubs();
    }, []);
    

    const handleEdit = (id) => {
        setSelectedHubId(id);
        setEditModalVisible(true);
    };

    const handleDelete = async (id) => {
        if (!isTokenValid()) {
            setError('No authentication token found.');
            return;
        }
    
        // Show confirmation alert before proceeding with deletion
        confirmationAlert({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            onConfirm: async () => {
                try {
                    const response = await fetch(`${API_URL}quick-access-hubs/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${getToken()}`,
                            'Content-Type': 'application/json',
                        },
                    });
    
                    if (!response.ok) {
                        throw new Error('Failed to delete the Hub');
                    }
    
                    setQuickAccessHubs((prev) => prev.filter((hub) => hub.id !== id));

                    alertMessage('success', 'Hub deleted successfully!');
                    
                } catch (error) {
                    alertMessage('error', 'Failed to delete the Hub!');
                }
            },
            onCancel: () => {
                alertMessage('info', 'Deletion cancelled.');
            }
        });
    };
    

    // If loading users or filter options, show loading component
    if (filterOptionLoading) {
        return <Loading />;
    }

    if (error) {
        return <div className="text-red-500">{error}</div>;
    }

    return (
        <>
            <div className="flex flex-wrap -mx-2 py-6">
                {quickAccessHubs.map((hub) => (
                    <div key={hub.id} className="w-full sm:w-1/2 md:w-1/3 p-4">
                        <div className="bg-white shadow-sm rounded-xl border border-gray-200 dark:bg-gray-800 dark:border-gray-600">
                            <div className="flex items-center p-6">
                                <div className="me-3 text-white dark:text-gray:400 rounded-lg dark:text-gray-400">
                                    <div className="w-16">
                                    {hub.hubs_image ? (
                                        <img
                                            src={`${ASSET_URL}/${hub.hubs_image}`}
                                            alt="Hub Image"
                                            className="w-full h-full object-contain"
                                        />
                                    ) : (
                                        <span className="material-symbols-rounded w-12 h-12 rounded-lg bg-primary text-white dark:text-gray:400 text-2xl flex items-center justify-center">
                                            {hub.hubs_icon || 'help_outline'}
                                        </span>
                                    )}
                                                                        </div>
                                </div>
                                <div className='text-left'>
                                    <div className="text-lg font-bold text-black-600 pb-2 dark:text-gray-300">{hub.hubs_title}</div>
                                    <div className="text-sm text-gray-400">{hub.hubs_details}</div>
                                </div>
                            </div>
                            <div className="p-4 border-t border-gray-200 bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                                <div className="flex items-center justify-between flex-row">
                                    <a className="font-medium btn btn-sm bg-primarySeafoam dark:text-gray-700 hover:text-black-700 text-primary px-6 py-3 rounded-xl" href={hub.hubs_url} target="_blank" rel="noopener noreferrer">
                                        {hub.hubs_cta || 'Go to Hub'}
                                    </a>
                                    {rolePermissions.hasTeamLeadRole && (
                                    <div className='flex flex-row gap-4'>
                                        <button onClick={() => handleEdit(hub.id)} className="font-medium btn btn-sm bg-secondaryTeal hover:text-black-700 text-white dark:text-gray:400 px-6 py-3 rounded-xl">
                                            Edit
                                        </button>
                                        <button onClick={() => handleDelete(hub.id)} className="font-medium btn btn-sm bg-red-600 hover:text-black-700 text-white dark:text-gray:400 px-6 py-3 rounded-xl">
                                            Delete
                                        </button>
                                    </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}

                {rolePermissions.hasTeamLeadRole && (
                <div className="w-full sm:w-1/2 md:w-1/3 px-4 mt-4">
                    <div className="bg-gray-50 dark:bg-gray-700 shadow-sm rounded-xl border border-gray-200 dark:border-gray-600 cursor-pointer h-48 flex items-center justify-center flex-row" onClick={() => setAddModalVisible(true)}>
                        <span className="material-symbols-rounded text-gray-300 dark:text-gray-500 text-6xl hover:text-primaryBlue">add</span>
                    </div>
                </div>
                )}
            </div>

            {addModalVisible && (
                <AddQuickAccessHub
                    isVisible={addModalVisible}
                    setVisible={setAddModalVisible}
                />
            )}

            {editModalVisible && (
                <EditQuickAccessHub
                    isVisible={editModalVisible}
                    setVisible={setEditModalVisible}
                    hubId={selectedHubId}
                />
            )}
        </>
    );
};

export default QuickAccessHubview;
