import React, { useState, useEffect, useMemo } from "react";
import useFetchApiData from "../../common/fetchData/useFetchApiData.jsx";
import { API_URL } from "../../common/fetchData/apiConfig.js";

const TeamArea = () => {
  const token = localStorage.getItem("token");
  const { data: usersData, error } = useFetchApiData(`${API_URL}users`, token);

  useEffect(() => {
    if (error) {
      console.error("API Error:", error);
    }
  }, [error]);

  // Utility function to format time into 12-hour format
  const formatTime12Hour = (timeString) => {
    if (!timeString) return "";
    const [hours, minutes] = timeString.split(":");
    const date = new Date();
    date.setHours(hours, minutes);
    return date.toLocaleTimeString("en-US", { hour: "2-digit", minute: "2-digit", hour12: true });
  };

  // Memoized Team Data Calculation
  const teamData = useMemo(() => {
    if (!Array.isArray(usersData) || usersData.length === 0) return {};

    const teamCounts = {};

    usersData.forEach((user) => {
      if (!Array.isArray(user.teams)) return;

      user.teams.forEach((team) => {
        const teamKey = team.name;

        if (!teamCounts[teamKey]) {
          teamCounts[teamKey] = {
            logo: team.logo ? `${API_URL}${team.logo}` : "/assets/images/default-logo.png", 
            teamLead: team.poc || "Not Assigned",
            members: 0,
            liveMembers: 0,
            benchMembers: 0,
            resourceCounts: {},
            shifts: {},
          };
        }

        teamCounts[teamKey].members++;

    // Ensure member_statuses is an array and check if it contains "Live"
    const isLiveMember = user.member_statuses?.some(status => status.name === "Live");

    if (isLiveMember) {
        teamCounts[teamKey].liveMembers++;
    } else {
        teamCounts[teamKey].benchMembers++;
    }


        if (!Array.isArray(user.schedules)) return;

        user.schedules.forEach((schedule) => {
          const shiftKey = schedule.shift_name;

          if (!teamCounts[teamKey].shifts[shiftKey]) {
            teamCounts[teamKey].shifts[shiftKey] = {
              time: schedule.shift_start && schedule.shift_end
                ? `${formatTime12Hour(schedule.shift_start)} - ${formatTime12Hour(schedule.shift_end)}`
                : "Shift time not available",
              resourceCounts: {},
            };
          }

          if (!Array.isArray(user.resource_types)) return;

          user.resource_types.forEach((resourceType) => {
            const role = resourceType.name || "Other";
            teamCounts[teamKey].shifts[shiftKey].resourceCounts[role] =
              (teamCounts[teamKey].shifts[shiftKey].resourceCounts[role] || 0) + 1;
            teamCounts[teamKey].resourceCounts[role] =
              (teamCounts[teamKey].resourceCounts[role] || 0) + 1;
          });
        });
      });
    });

    return teamCounts;
  }, [usersData]);

  return (
    <div className="p-6">
      {Object.keys(teamData).length === 0 ? (
        <p className="text-center text-gray-500">No teams available</p>
      ) : (
        Object.keys(teamData).map((teamName, index) => (
          <TeamSection key={index} title={teamName} {...teamData[teamName]} />
        ))
      )}
    </div>
  );
};

const TeamSection = ({ title, teamLead, members, liveMembers, benchMembers, resourceCounts, shifts, logo }) => (
  <div className="p-6 rounded-lg mb-8">
    <div className="flex items-center gap-4 whitespace-nowrap">
      <p className="font-normal text-lg">Team: <span className="text-primary">{title}</span></p>
      <span className="font-normal text-lg">|</span>
      <p className="font-normal text-lg">Team Lead: <span className="text-primary">{teamLead}</span></p>
      <span className="font-normal text-lg">|</span>
      <p className="font-normal text-lg">Total Members: <span className="text-primary">{members}</span></p>
      <p className="font-normal text-lg">Live Members: <span className="text-primary">{liveMembers}</span></p>
      <p className="font-normal text-lg">Bench Members: <span className="text-primary">{benchMembers}</span></p>
      <p className="font-normal text-lg">Total Designer: <span className="text-primary">{resourceCounts["Designer"] || 0}</span></p>
      <p className="font-normal text-lg">Total Developer: <span className="text-primary">{resourceCounts["Developer"] || 0}</span></p>
      <p className="font-normal text-lg">Total QA: <span className="text-primary">{resourceCounts["QA"] || 0}</span></p>
    </div>
    <div className="flex flex-row gap-2 mt-2">
  <div className="w-[220px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md flex flex-col items-center justify-center border">
    <h5 className="text-lg">{title}</h5>
    <img src={logo} alt={title} className="w-16 h-16 rounded-full my-2" />
    <p className="font-normal text-lg">
      Total Members: <span className="text-primary">{members}</span>
    </p>
  </div>
  <div className="flex flex-col w-full gap-2 mt-2">
    {Object.keys(shifts).map((shiftKey, index) => (
      <ShiftDetailCard key={index} title={shiftKey} {...shifts[shiftKey]} />
    ))}
  </div>
</div>

  </div>
);

const ShiftDetailCard = ({ title, time, resourceCounts }) => (
  <div className="p-4">
    <div className="flex items-center gap-4">
      <div className="w-[209px] h-[77px] p-4 rounded-lg text-center font-bold text-gray-700 shadow-md border">
        <h5 className="text-sm">{title}</h5>
        <p className="text-xs font-normal text-gray-600">⏰ {time}</p>
      </div>
      <div className="grid grid-cols-4 gap-4 w-full">
        {Object.entries(resourceCounts || {}).map(([role, count]) => (
          <Card key={role} title={`Total ${role}`} count={count} />
        ))}
      </div>
    </div>
  </div>
);

const Card = ({ title, count }) => (
  <div className="h-[78px] p-4 rounded-lg text-center shadow-md border">
    <span className="text-sm">{title}</span>
    <p className="text-base font-bold"> 👥 {count}</p>
  </div>
);

export default TeamArea;
