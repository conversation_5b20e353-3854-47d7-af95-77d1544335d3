import React, { useState, useRef } from 'react';

function OneLineText() {
  const [inputValue, setInputValue] = useState('');
  const [outputValues, setOutputValues] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const hiddenTextAreaRef = useRef(null);

  const handleChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleProcessInput = () => {
    if (!inputValue.trim()) {
      setOutputValues('');
      setWordCount(0);
      return;
    }

    const lines = inputValue.split('\n');
    const values = lines
      .map((line) => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return null;

        const parts = trimmedLine.split(/:(.+)/);
        if (parts.length >= 2) {
          const cleanedValue = parts[1].trim().replace(/\s+/g, ' ');
          return cleanedValue;
        }
        return null;
      })
      .filter(Boolean)
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim();

    setOutputValues(values);

    const cleaned = values.replace(/[.,!?;:"'`~(){}\[\]]/g, '');
    const wordsArray = cleaned.trim().split(/\s+/).filter(Boolean);
    setWordCount(wordsArray.length);
  };

  const handleCopy = () => {
    if (outputValues && hiddenTextAreaRef.current) {
      hiddenTextAreaRef.current.select();
      document.execCommand('copy');
      alert('Copied to clipboard!');
    }
  };

  const handleClear = () => {
    setInputValue('');
    setOutputValues('');
    setWordCount(0);
  };

  return (
    <div className="max-w-xl mx-auto my-10 p-6 font-sans bg-white shadow rounded">
      <label htmlFor="titleValueInput" className="block mb-2 font-medium text-gray-700">
        Paste your <strong>Title: Value</strong> pairs here (one per line):
      </label>
      <textarea
        id="titleValueInput"
        rows="6"
        value={inputValue}
        onChange={handleChange}
        placeholder="Example:\nName: John\nAge: 25\nCity: Dhaka"
        aria-label="Input area for title-value pairs"
        className="w-full p-3 text-sm border border-gray-300 rounded mb-4 focus:outline-none focus:ring focus:ring-blue-200"
      />
      <div className="mb-4">
        <button
          onClick={handleProcessInput}
          disabled={!inputValue.trim()}
          className="px-4 py-2 mr-3 text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Process
        </button>
        <button
          onClick={handleClear}
          disabled={!inputValue.trim()}
          className="px-4 py-2 text-white bg-gray-600 rounded hover:bg-gray-700 disabled:opacity-50"
        >
          Clear
        </button>
      </div>

      {outputValues && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Output Values:</h3>
          <textarea
            value={outputValues}
            readOnly
            className="w-full p-3 text-sm border border-gray-300 rounded mb-3 bg-gray-100"
          />

          {/* Hidden textarea for copy operation */}
          <textarea
            ref={hiddenTextAreaRef}
            value={outputValues}
            readOnly
            className="absolute left-[-9999px] opacity-0"
            aria-hidden="true"
          />

          <div className="bg-gray-100 p-3 rounded flex flex-wrap gap-2">
            {outputValues
              .split(' ')
              .filter(Boolean)
              .map((val, idx) => (
                <span
                  key={idx}
                  className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full shadow-sm"
                >
                  {val}
                </span>
              ))}
          </div>

          <p className="mt-3 text-sm">
            <strong>Word Count:</strong> {wordCount}
          </p>

          <button
            onClick={handleCopy}
            disabled={!outputValues.trim()}
            className="mt-3 px-4 py-2 text-white bg-green-600 rounded hover:bg-green-700 disabled:opacity-50"
          >
            Copy to Clipboard
          </button>
        </div>
      )}
    </div>
  );
}

export default OneLineText;
