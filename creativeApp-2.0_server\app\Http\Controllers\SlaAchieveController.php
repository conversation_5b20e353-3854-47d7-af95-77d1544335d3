<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\SlaAchieve;
use Illuminate\Support\Facades\Log;

class SlaAchieveController extends Controller
{
    /**
     * Display a listing of all SLA Achievements.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $slaAchieves = SlaAchieve::all();

        // Log the SLA Achievements retrieved
        Log::info('All SLA Achievements Retrieved:', ['sla_achievements_count' => $slaAchieves->count()]);

        return response()->json(['slaAchieves' => $slaAchieves], 200);
    }

    /**
     * Display the specified SLA Achievement.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        // Find the SLA Achievement by ID
        $slaAchieve = SlaAchieve::find($id);

        if (!$slaAchieve) {
            return response()->json(['error' => 'SLA Achievement not found.'], 404);
        }

        // Log the SLA Achievement retrieved
        Log::info('SLA Achievement Retrieved:', ['sla_achievement' => $slaAchieve]);

        return response()->json(['slaAchieve' => $slaAchieve], 200);
    }

    /**
     * Create a new SLA Achievement.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Get the authenticated user
        $authUser = $request->user();

        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);

        Log::info('Create SLA Achievement Request:', ['request' => $request->all()]);

        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);

        // Check if the SLA Achievement name already exists
        if (SlaAchieve::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'SLA Achievement already exists.'], 409);
        }

        // Create a new SLA Achievement
        $slaAchieve = SlaAchieve::create([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'created_by' => $authUser->fname . ' ' . $authUser->lname,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname
        ]);

        Log::info('SLA Achievement Created:', ['sla_achievement' => $slaAchieve]);

        return response()->json(['message' => 'SLA Achievement created successfully.', 'slaAchieve' => $slaAchieve], 201);
    }

    /**
     * Update an existing SLA Achievement.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Get the authenticated user
        $authUser = $request->user();
    
        // Log the authenticated user's details
        Log::info('Authenticated User:', [
            'user_id' => $authUser->id, 
            'fname' => $authUser->fname, 
            'lname' => $authUser->lname
        ]);
    
        Log::info('Update SLA Achievement Request:', ['request' => $request->all()]);
    
        // Validate the request data
        $request->validate([
            'name' => 'required|string|max:255',
            'department' => 'required|string|max:255',
            'team' => 'required|string|max:255',
        ]);
    
        // Find the existing SLA Achievement by ID
        $slaAchieve = SlaAchieve::find($id);
    
        // Check if the SLA Achievement exists
        if (!$slaAchieve) {
            return response()->json(['error' => 'SLA Achievement not found.'], 404);
        }
    
        // Check if the SLA Achievement name is being changed and ensure it does not conflict with an existing name
        if ($slaAchieve->name !== $request->name && SlaAchieve::where('name', $request->name)->exists()) {
            return response()->json(['error' => 'SLA Achievement with the same name already exists.'], 409);
        }
    
        // Update the SLA Achievement with the new data
        $slaAchieve->update([
            'name' => $request->name,
            'department' => $request->department,
            'team' => $request->team,
            'updated_by' => $authUser->fname . ' ' . $authUser->lname,  // Track who updated the SLA Achievement
        ]);
    
        Log::info('SLA Achievement Updated:', ['sla_achievement' => $slaAchieve]);
    
        return response()->json(['message' => 'SLA Achievement updated successfully.', 'slaAchieve' => $slaAchieve], 200);
    }
    

    /**
     * Delete an SLA Achievement.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        // Get the authenticated user
        $authUser = request()->user();

        // Check if the user has the appropriate role
        if ($authUser->roles()->whereIn('name', ['super-admin', 'admin'])->exists()) {
            // Find the SLA Achievement
            $slaAchieve = SlaAchieve::findOrFail($id);

            // Delete the SLA Achievement
            $slaAchieve->delete();

            return response()->json(['message' => 'SLA Achievement deleted successfully.'], 200);
        }

        // Deny access for other roles
        return response()->json(['error' => 'You do not have permission to delete this SLA Achievement.'], 403);
    }
}
