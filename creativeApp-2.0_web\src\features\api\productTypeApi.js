import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const productTypeApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getProductTypeData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `product-type-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['ProductTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForProductType: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `product-type-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['ProductTypeData'],
    }),

    getProductTypeById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `product-type/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'ProductTypeData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createProductType: builder.mutation({
      query: (newFormationType) => ({
        url: 'product-type-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['ProductTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateProductType: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `product-type/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'ProductTypeData', id }, 'ProductTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteProductType: builder.mutation({
      query: (id) => ({
        url: `product-type/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ProductTypeData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetProductTypeDataQuery,
  useLazyFetchDataOptionsForProductTypeQuery,
  useGetProductTypeByIdQuery,
  useLazyGetProductTypeByIdQuery,
  useCreateProductTypeMutation,
  useUpdateProductTypeMutation,
  useDeleteProductTypeMutation,
} = productTypeApi;
