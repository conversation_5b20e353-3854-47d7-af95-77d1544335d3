<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, ...$roles)
    {
        $authUser = Auth::user();
    
        // Check if the user is authenticated
        if (Auth::check()) {
            // Log the user's roles for debugging
            \Log::info('Authenticated user roles:', $authUser->roles()->pluck('name')->toArray());
    
            // Allow super-admin or admin to create users
            if ($authUser->roles()->whereIn('name', ['super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest'])->exists()) {
                return $next($request); // User has the required role
            }
    
            // Check if the user has one of the specific roles passed as arguments
            if ($authUser->roles()->whereIn('name', $roles)->exists()) {
                return $next($request); // User has one of the specified roles
            }
        } else {
            \Log::warning('Unauthorized access attempt without authentication.');
        }
    
        return response()->json(['error' => 'Unauthorized.'], 403);
    }
    
    
    
    
    
}

