import { baseApi } from './baseApi';
import {alertMessage} from '../../common/coreui';


export const designationApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getDesignationData: builder.query({
      query: ({ sort_by = 'created_at', order = 'desc', page = 1, per_page = 10, query }) => {
        let queryString = `designation-data?sort_by=${sort_by}&order=${order}&page=${page}&per_page=${per_page}`;
        if (query) queryString += `&${query}`;
        // if (query) queryString += `&${encodeURIComponent(query)}`;
        return queryString;
      },
      providesTags: ['DesignationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
        //   alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    fetchDataOptionsForDesignation: builder.query({
      query: ({ type = 'group', column = 'team_id', text = '' }) => {
        let queryString = `designation-data-${type}?column=${column}`;
        if (text) queryString += `&text=${encodeURIComponent(text)}`;
        return queryString;
      },
      providesTags: ['DesignationData'],
    }),

    getDesignationById: builder.query({
        query: (id) => {
            if (id == null || id == undefined) {
                id = "";
            //   throw new Error("Invalid ID: ID cannot be null or undefined");
            }
            return `designations/${id}`;
          },
      providesTags: (result, error, id) => [{ type: 'DesignationData', id }],

      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('success');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    createDesignation: builder.mutation({
      query: (newFormationType) => ({
        url: 'designation-data',
        method: 'POST',
        body: newFormationType,
      }),
      invalidatesTags: ['DesignationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('created');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    updateDesignation: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `designations/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'DesignationData', id }, 'DesignationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('updated');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),

    deleteDesignation: builder.mutation({
      query: (id) => ({
        url: `designations/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['DesignationData'],
      async onQueryStarted(args, { queryFulfilled }) {
        try {
          await queryFulfilled;
          alertMessage('deleted');
        } catch (error) {
          alertMessage('error');
        }
      },
    }),
  }),
});

export const {
  useGetDesignationDataQuery,
  useLazyFetchDataOptionsForDesignationQuery,
  useGetDesignationByIdQuery,
  useLazyGetDesignationByIdQuery,
  useCreateDesignationMutation,
  useUpdateDesignationMutation,
  useDeleteDesignationMutation,
} = designationApi;
