<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaskDetails extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_number',
        'month',
        'week',
        'received_date',
        'due_date',
        'unit',
        'account_name',
        'campaign_name',
        'notes',
        'department',
        'team',
        'product_type_id',
        'priority_id',
        'task_type_id',
        'revision_type_id',
        'record_type_id',
        'region_id',
        'reporter_id',
        'created_by',
        'updated_by'
    ];

    /**
     * Relationship to get the user who created this record.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship to get the user who updated this record.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function product_types()
    {
        return $this->belongsTo(ProductType::class, 'product_type_id');
    }

    public function task_types()
    {
        return $this->belongsTo(TaskType::class, 'task_type_id');
    }

    public function revision_types()
    {
        return $this->belongsTo(RevisionType::class, 'revision_type_id');
    }
    public function record_types()
    {
        return $this->belongsTo(RecordType::class, 'record_type_id');
    }
    public function priorities()
    {
        return $this->belongsTo(Priority::class, 'priority_id');
    }
    public function regions()
    {
        return $this->belongsTo(Region::class, 'region_id');
    }

    public function reporters()
    {
        return $this->belongsTo(Reporter::class, 'reporter_id');
    }

    public function teams()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function departments()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

}
