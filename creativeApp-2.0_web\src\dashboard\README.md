# Dashboard template

## Usage

<!-- #default-branch-switch -->

1. Copy the files into your project, or one of the [example projects](https://github.com/mui/material-ui/tree/master/examples).
2. Make sure your project has the required dependencies: @mui/material, @mui/icons-material, @emotion/styled, @emotion/react, @mui/x-charts.
3. Import and use the `Dashboard` component.

## Demo

<!-- #default-branch-switch -->

View the demo at https://mui.com/material-ui/getting-started/templates/dashboard/.
