{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\creativeapp\\\\creativeApp-2.0_web\\\\src\\\\pages\\\\world-time\\\\TimeZoneConvert.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\n// import moment from \"moment\";\nimport moment from \"moment-timezone\";\nimport Select from \"react-select\";\nimport DynamicTimeCard from \"./DynamicTimeCard\";\nimport timezonebg from \"../../assets/images/timezonebg.png\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst timeZones = {\n  Dubai: \"Asia/Dubai\",\n  Kabul: \"Asia/Kabul\",\n  Yerevan: \"Asia/Yerevan\",\n  Baku: \"Asia/Baku\",\n  Dhaka: \"Asia/Dhaka\",\n  Brunei: \"Asia/Brunei\",\n  Thimphu: \"Asia/Thimphu\",\n  Shanghai: \"Asia/Shanghai\",\n  Urumqi: \"Asia/Urumqi\",\n  Nicosia: \"Asia/Nicosia\",\n  Famagusta: \"Asia/Famagusta\",\n  Tbilisi: \"Asia/Tbilisi\",\n  Hong_Kong: \"Asia/Hong_Kong\",\n  Jakarta: \"Asia/Jakarta\",\n  Pontianak: \"Asia/Pontianak\",\n  Makassar: \"Asia/Makassar\",\n  Jayapura: \"Asia/Jayapura\",\n  Jerusalem: \"Asia/Jerusalem\",\n  Kolkata: \"Asia/Kolkata\",\n  Baghdad: \"Asia/Baghdad\",\n  Tehran: \"Asia/Tehran\",\n  Amman: \"Asia/Amman\",\n  Tokyo: \"Asia/Tokyo\",\n  Bishkek: \"Asia/Bishkek\",\n  Pyongyang: \"Asia/Pyongyang\",\n  Seoul: \"Asia/Seoul\",\n  Almaty: \"Asia/Almaty\",\n  Qyzylorda: \"Asia/Qyzylorda\",\n  Qostanay: \"Asia/Qostanay\",\n  Aqtobe: \"Asia/Aqtobe\",\n  Aqtau: \"Asia/Aqtau\",\n  Atyrau: \"Asia/Atyrau\",\n  Oral: \"Asia/Oral\",\n  Beirut: \"Asia/Beirut\",\n  Colombo: \"Asia/Colombo\",\n  Yangon: \"Asia/Yangon\",\n  Ulaanbaatar: \"Asia/Ulaanbaatar\",\n  Hovd: \"Asia/Hovd\",\n  Choibalsan: \"Asia/Choibalsan\",\n  Macau: \"Asia/Macau\",\n  Kuala_Lumpur: \"Asia/Kuala_Lumpur\",\n  Kuching: \"Asia/Kuching\",\n  Karachi: \"Asia/Karachi\",\n  Gaza: \"Asia/Gaza\",\n  Hebron: \"Asia/Hebron\",\n  Kathmandu: \"Asia/Kathmandu\",\n  Yekaterinburg: \"Asia/Yekaterinburg\",\n  Qatar: \"Asia/Qatar\",\n  Omsk: \"Asia/Omsk\",\n  Novosibirsk: \"Asia/Novosibirsk\",\n  Barnaul: \"Asia/Barnaul\",\n  Tomsk: \"Asia/Tomsk\",\n  Novokuznetsk: \"Asia/Novokuznetsk\",\n  Krasnoyarsk: \"Asia/Krasnoyarsk\",\n  Irkutsk: \"Asia/Irkutsk\",\n  Chita: \"Asia/Chita\",\n  Yakutsk: \"Asia/Yakutsk\",\n  Khandyga: \"Asia/Khandyga\",\n  Vladivostok: \"Asia/Vladivostok\",\n  Ust_Nera: \"Asia/Ust-Nera\",\n  Singapore: \"Asia/Singapore\",\n  Magadan: \"Asia/Magadan\",\n  Sakhalin: \"Asia/Sakhalin\",\n  Srednekolymsk: \"Asia/Srednekolymsk\",\n  Kamchatka: \"Asia/Kamchatka\",\n  Anadyr: \"Asia/Anadyr\",\n  Bangkok: \"Asia/Bangkok\",\n  Dushanbe: \"Asia/Dushanbe\",\n  Taipei: \"Asia/Taipei\",\n  Dili: \"Asia/Dili\",\n  Ashgabat: \"Asia/Ashgabat\",\n  Damascus: \"Asia/Damascus\",\n  Riyadh: \"Asia/Riyadh\",\n  Samarkand: \"Asia/Samarkand\",\n  Tashkent: \"Asia/Tashkent\",\n  Ho_Chi_Minh: \"Asia/Ho_Chi_Minh\",\n  Andorra: \"Europe/Andorra\",\n  Tirane: \"Europe/Tirane\",\n  Vienna: \"Europe/Vienna\",\n  Brussels: \"Europe/Brussels\",\n  Sofia: \"Europe/Sofia\",\n  Minsk: \"Europe/Minsk\",\n  Zurich: \"Europe/Zurich\",\n  Prague: \"Europe/Prague\",\n  Berlin: \"Europe/Berlin\",\n  Copenhagen: \"Europe/Copenhagen\",\n  Tallinn: \"Europe/Tallinn\",\n  Madrid: \"Europe/Madrid\",\n  Helsinki: \"Europe/Helsinki\",\n  Paris: \"Europe/Paris\",\n  London: \"Europe/London\",\n  Gibraltar: \"Europe/Gibraltar\",\n  Athens: \"Europe/Athens\",\n  Budapest: \"Europe/Budapest\",\n  Dublin: \"Europe/Dublin\",\n  Rome: \"Europe/Rome\",\n  Vilnius: \"Europe/Vilnius\",\n  Luxembourg: \"Europe/Luxembourg\",\n  Riga: \"Europe/Riga\",\n  Monaco: \"Europe/Monaco\",\n  Chisinau: \"Europe/Chisinau\",\n  Malta: \"Europe/Malta\",\n  Amsterdam: \"Europe/Amsterdam\",\n  Oslo: \"Europe/Oslo\",\n  Warsaw: \"Europe/Warsaw\",\n  Lisbon: \"Europe/Lisbon\",\n  Bucharest: \"Europe/Bucharest\",\n  Belgrade: \"Europe/Belgrade\",\n  Kaliningrad: \"Europe/Kaliningrad\",\n  Moscow: \"Europe/Moscow\",\n  Simferopol: \"Europe/Simferopol\",\n  Kirov: \"Europe/Kirov\",\n  Astrakhan: \"Europe/Astrakhan\",\n  Volgograd: \"Europe/Volgograd\",\n  Saratov: \"Europe/Saratov\",\n  Ulyanovsk: \"Europe/Ulyanovsk\",\n  Samara: \"Europe/Samara\",\n  Stockholm: \"Europe/Stockholm\",\n  Istanbul: \"Europe/Istanbul\",\n  Kiev: \"Europe/Kiev\",\n  Uzhgorod: \"Europe/Uzhgorod\",\n  Zaporozhye: \"Europe/Zaporozhye\",\n  Casey: \"Antarctica/Casey\",\n  Davis: \"Antarctica/Davis\",\n  DumontDUrville: \"Antarctica/DumontDUrville\",\n  Mawson: \"Antarctica/Mawson\",\n  Palmer: \"Antarctica/Palmer\",\n  Rothera: \"Antarctica/Rothera\",\n  Syowa: \"Antarctica/Syowa\",\n  Troll: \"Antarctica/Troll\",\n  Vostok: \"Antarctica/Vostok\",\n  Macquarie: \"Antarctica/Macquarie\",\n  Buenos_Aires: \"America/Argentina/Buenos_Aires\",\n  Cordoba: \"America/Argentina/Cordoba\",\n  Salta: \"America/Argentina/Salta\",\n  Jujuy: \"America/Argentina/Jujuy\",\n  Tucuman: \"America/Argentina/Tucuman\",\n  Catamarca: \"America/Argentina/Catamarca\",\n  La_Rioja: \"America/Argentina/La_Rioja\",\n  San_Juan: \"America/Argentina/San_Juan\",\n  Mendoza: \"America/Argentina/Mendoza\",\n  San_Luis: \"America/Argentina/San_Luis\",\n  Rio_Gallegos: \"America/Argentina/Rio_Gallegos\",\n  Ushuaia: \"America/Argentina/Ushuaia\",\n  Barbados: \"America/Barbados\",\n  La_Paz: \"America/La_Paz\",\n  Belem: \"America/Belem\",\n  Fortaleza: \"America/Fortaleza\",\n  Recife: \"America/Recife\",\n  Araguaina: \"America/Araguaina\",\n  Maceio: \"America/Maceio\",\n  Bahia: \"America/Bahia\",\n  Sao_Paulo: \"America/Sao_Paulo\",\n  Campo_Grande: \"America/Campo_Grande\",\n  Cuiaba: \"America/Cuiaba\",\n  Porto_Velho: \"America/Porto_Velho\",\n  Boa_Vista: \"America/Boa_Vista\",\n  Manaus: \"America/Manaus\",\n  Eirunepe: \"America/Eirunepe\",\n  Rio_Branco: \"America/Rio_Branco\",\n  Nassau: \"America/Nassau\",\n  Belize: \"America/Belize\",\n  St_Johns: \"America/St_Johns\",\n  Halifax: \"America/Halifax\",\n  Glace_Bay: \"America/Glace_Bay\",\n  Moncton: \"America/Moncton\",\n  Goose_Bay: \"America/Goose_Bay\",\n  Blanc_Sablon: \"America/Blanc-Sablon\",\n  Toronto: \"America/Toronto\",\n  Nipigon: \"America/Nipigon\",\n  Thunder_Bay: \"America/Thunder_Bay\",\n  Iqaluit: \"America/Iqaluit\",\n  Pangnirtung: \"America/Pangnirtung\",\n  Atikokan: \"America/Atikokan\",\n  Winnipeg: \"America/Winnipeg\",\n  Rainy_River: \"America/Rainy_River\",\n  Resolute: \"America/Resolute\",\n  Rankin_Inlet: \"America/Rankin_Inlet\",\n  Regina: \"America/Regina\",\n  Swift_Current: \"America/Swift_Current\",\n  Edmonton: \"America/Edmonton\",\n  Cambridge_Bay: \"America/Cambridge_Bay\",\n  Yellowknife: \"America/Yellowknife\",\n  Inuvik: \"America/Inuvik\",\n  Creston: \"America/Creston\",\n  Dawson_Creek: \"America/Dawson_Creek\",\n  Fort_Nelson: \"America/Fort_Nelson\",\n  Vancouver: \"America/Vancouver\",\n  Whitehorse: \"America/Whitehorse\",\n  Dawson: \"America/Dawson\",\n  Santiago: \"America/Santiago\",\n  Punta_Arenas: \"America/Punta_Arenas\",\n  Bogota: \"America/Bogota\",\n  Costa_Rica: \"America/Costa_Rica\",\n  Havana: \"America/Havana\",\n  Curacao: \"America/Curacao\",\n  Santo_Domingo: \"America/Santo_Domingo\",\n  Guayaquil: \"America/Guayaquil\",\n  Cayenne: \"America/Cayenne\",\n  Godthab: \"America/Godthab\",\n  Danmarkshavn: \"America/Danmarkshavn\",\n  Scoresbysund: \"America/Scoresbysund\",\n  Thule: \"America/Thule\",\n  Guatemala: \"America/Guatemala\",\n  Guyana: \"America/Guyana\",\n  Tegucigalpa: \"America/Tegucigalpa\",\n  Port_au_Prince: \"America/Port-au-Prince\",\n  Jamaica: \"America/Jamaica\",\n  Martinique: \"America/Martinique\",\n  Mexico_City: \"America/Mexico_City\",\n  Cancun: \"America/Cancun\",\n  Merida: \"America/Merida\",\n  Monterrey: \"America/Monterrey\",\n  Matamoros: \"America/Matamoros\",\n  Caracas: \"America/Caracas\",\n  Mazatlan: \"America/Mazatlan\",\n  Chihuahua: \"America/Chihuahua\",\n  Ojinaga: \"America/Ojinaga\",\n  Hermosillo: \"America/Hermosillo\",\n  Tijuana: \"America/Tijuana\",\n  Bahia_Banderas: \"America/Bahia_Banderas\",\n  Managua: \"America/Managua\",\n  Panama: \"America/Panama\",\n  Lima: \"America/Lima\",\n  Miquelon: \"America/Miquelon\",\n  Puerto_Rico: \"America/Puerto_Rico\",\n  El_Salvador: \"America/El_Salvador\",\n  Grand_Turk: \"America/Grand_Turk\",\n  Paramaribo: \"America/Paramaribo\",\n  Asuncion: \"America/Asuncion\",\n  Port_of_Spain: \"America/Port_of_Spain\",\n  New_York: \"America/New_York\",\n  New_Jersey: \"America/New_York\",\n  Detroit: \"America/Detroit\",\n  Louisville: \"America/Kentucky/Louisville\",\n  Monticello: \"America/Kentucky/Monticello\",\n  Indianapolis: \"America/Indiana/Indianapolis\",\n  Vincennes: \"America/Indiana/Vincennes\",\n  Winamac: \"America/Indiana/Winamac\",\n  Marengo: \"America/Indiana/Marengo\",\n  Petersburg: \"America/Indiana/Petersburg\",\n  Vevay: \"America/Indiana/Vevay\",\n  Tell_City: \"America/Indiana/Tell_City\",\n  Knox: \"America/Indiana/Knox\",\n  Chicago: \"America/Chicago\",\n  Menominee: \"America/Menominee\",\n  Denver: \"America/Denver\",\n  Boise: \"America/Boise\",\n  Phoenix: \"America/Phoenix\",\n  Center: \"America/North_Dakota/Center\",\n  New_Salem: \"America/North_Dakota/New_Salem\",\n  Beulah: \"America/North_Dakota/Beulah\",\n  Los_Angeles: \"America/Los_Angeles\",\n  Anchorage: \"America/Anchorage\",\n  Alaska: \"America/Anchorage\",\n  Juneau: \"America/Juneau\",\n  Sitka: \"America/Sitka\",\n  Metlakatla: \"America/Metlakatla\",\n  Yakutat: \"America/Yakutat\",\n  Nome: \"America/Nome\",\n  Adak: \"America/Adak\",\n  Montevideo: \"America/Montevideo\",\n  Pago_Pago: \"Pacific/Pago_Pago\",\n  Rarotonga: \"Pacific/Rarotonga\",\n  Easter: \"Pacific/Easter\",\n  Galapagos: \"Pacific/Galapagos\",\n  Fiji: \"Pacific/Fiji\",\n  Chuuk: \"Pacific/Chuuk\",\n  Pohnpei: \"Pacific/Pohnpei\",\n  Kosrae: \"Pacific/Kosrae\",\n  Guam: \"Pacific/Guam\",\n  Majuro: \"Pacific/Majuro\",\n  Kwajalein: \"Pacific/Kwajalein\",\n  Tarawa: \"Pacific/Tarawa\",\n  Enderbury: \"Pacific/Enderbury\",\n  Kiritimati: \"Pacific/Kiritimati\",\n  Noumea: \"Pacific/Noumea\",\n  Norfolk: \"Pacific/Norfolk\",\n  Nauru: \"Pacific/Nauru\",\n  Niue: \"Pacific/Niue\",\n  Auckland: \"Pacific/Auckland\",\n  Chatham: \"Pacific/Chatham\",\n  Tahiti: \"Pacific/Tahiti\",\n  Marquesas: \"Pacific/Marquesas\",\n  Gambier: \"Pacific/Gambier\",\n  Port_Moresby: \"Pacific/Port_Moresby\",\n  Bougainville: \"Pacific/Bougainville\",\n  Pitcairn: \"Pacific/Pitcairn\",\n  Palau: \"Pacific/Palau\",\n  Guadalcanal: \"Pacific/Guadalcanal\",\n  Fakaofo: \"Pacific/Fakaofo\",\n  Tongatapu: \"Pacific/Tongatapu\",\n  Funafuti: \"Pacific/Funafuti\",\n  Wake: \"Pacific/Wake\",\n  Honolulu: \"Pacific/Honolulu\",\n  Efate: \"Pacific/Efate\",\n  Wallis: \"Pacific/Wallis\",\n  Apia: \"Pacific/Apia\",\n  Lord_Howe: \"Australia/Lord_Howe\",\n  Hobart: \"Australia/Hobart\",\n  Currie: \"Australia/Currie\",\n  Melbourne: \"Australia/Melbourne\",\n  Sydney: \"Australia/Sydney\",\n  Broken_Hill: \"Australia/Broken_Hill\",\n  Brisbane: \"Australia/Brisbane\",\n  Lindeman: \"Australia/Lindeman\",\n  Adelaide: \"Australia/Adelaide\",\n  Darwin: \"Australia/Darwin\",\n  Perth: \"Australia/Perth\",\n  Eucla: \"Australia/Eucla\",\n  Abidjan: \"Africa/Abidjan\",\n  Algiers: \"Africa/Algiers\",\n  Cairo: \"Africa/Cairo\",\n  El_Aaiun: \"Africa/El_Aaiun\",\n  Ceuta: \"Africa/Ceuta\",\n  Accra: \"Africa/Accra\",\n  Bissau: \"Africa/Bissau\",\n  Nairobi: \"Africa/Nairobi\",\n  Monrovia: \"Africa/Monrovia\",\n  Tripoli: \"Africa/Tripoli\",\n  Casablanca: \"Africa/Casablanca\",\n  Maputo: \"Africa/Maputo\",\n  Windhoek: \"Africa/Windhoek\",\n  Lagos: \"Africa/Lagos\",\n  Khartoum: \"Africa/Khartoum\",\n  Juba: \"Africa/Juba\",\n  Sao_Tome: \"Africa/Sao_Tome\",\n  Ndjamena: \"Africa/Ndjamena\",\n  Tunis: \"Africa/Tunis\",\n  Johannesburg: \"Africa/Johannesburg\",\n  Azores: \"Atlantic/Azores\",\n  Bermuda: \"Atlantic/Bermuda\",\n  Madeira: \"Atlantic/Madeira\",\n  Cape_Verde: \"Atlantic/Cape_Verde\",\n  Canary: \"Atlantic/Canary\",\n  Stanley: \"Atlantic/Stanley\",\n  Faroe: \"Atlantic/Faroe\",\n  South_Georgia: \"Atlantic/South_Georgia\",\n  Reykjavik: \"Atlantic/Reykjavik\",\n  Cocos: \"Indian/Cocos\",\n  Christmas: \"Indian/Christmas\",\n  Chagos: \"Indian/Chagos\",\n  Mauritius: \"Indian/Mauritius\",\n  Maldives: \"Indian/Maldives\",\n  Mahe: \"Indian/Mahe\",\n  Reunion: \"Indian/Reunion\",\n  Kerguelen: \"Indian/Kerguelen\"\n};\nfunction TimeZoneConvert() {\n  _s();\n  const defaultDateFormat = \"dddd, LL\";\n  const defaultTimeFormat = \"hh:mm A\";\n  const background = {\n    backgroundImage: `url(${timezonebg})`,\n    backgroundPosition: 'center center',\n    backgroundSize: 'contain',\n    backgroundRepeat: 'no-repeat'\n  };\n  const queryParameters = new URLSearchParams(window.location.search);\n  const getDateTime = queryParameters.get(\"datetime\");\n  const getDefaultTimeZones = queryParameters.get(\"defaulttimezone\");\n  const getTimeZones = queryParameters.get(\"timezones\");\n  const [defaultTimeZone, setDefaultTimeZone] = useState({\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\"\n  }); // Store team data\n  const [currentDateTime, setCurrentDateTime] = useState(moment().tz(defaultTimeZone[\"value\"])); // Store team data\n  const [error, setError] = useState(null); // Handle errors\n  const [loading, setLoading] = useState(true); // Loading state\n  // const [storeShowICityList, setStoreShowICityList] = useState({});\n  var storeShowICityList = {};\n  const [ipData, setIpData] = useState(false);\n  const [shareBtnText, setShareBtnText] = useState(\"Share\");\n  const [fromDateTime, setFromDateTime] = useState(\"\");\n  const [fromtimezone, setFromtimezone] = useState({\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\"\n  });\n  const [totimezone, setTotimezone] = useState({\n    label: \"New York\",\n    value: \"America/New_York\"\n  });\n  const [showCityList, setShowCityList] = useState([{\n    label: \"Dhaka\",\n    value: \"Asia/Dhaka\",\n    isFixed: true\n  }, {\n    label: \"New York\",\n    value: \"America/New_York\",\n    isFixed: true\n  }, {\n    label: \"El Salvador\",\n    value: \"America/El_Salvador\"\n  }, {\n    label: \"London\",\n    value: \"Europe/London\"\n  }, {\n    label: \"Sydney\",\n    value: \"Australia/Sydney\"\n  }, {\n    label: \"Dubai\",\n    value: \"Asia/Dubai\"\n  }, {\n    label: \"Tokyo\",\n    value: \"Asia/Tokyo\"\n  }]);\n  const datetimeToISOString = datetime => {\n    return new Date(datetime).valueOf();\n  };\n  const generateShareUrl = () => {\n    console.log(window.location);\n    let url = window.location.href.split(\"?\")[0];\n    let params = new URLSearchParams();\n    let isoDateTime = fromDateTime ? new Date(fromDateTime).toISOString() : new Date(currentDateTime).toISOString();\n    let dtmz = fromDateTime ? fromtimezone[\"label\"] : defaultTimeZone[\"label\"];\n    let timezones = showCityList.map(item => item[\"label\"].replaceAll(\" \", \"_\"));\n    params.append(\"datetime\", datetimeToISOString(isoDateTime));\n    params.append(\"defaulttimezone\", dtmz);\n    params.append(\"timezones\", timezones.join(\",\"));\n    url = window.location.origin + \"/time-zone-convert-share?\" + params.toString(\",\");\n    return url;\n  };\n  let singleLayoutClasses = \"\";\n  if (window.location.pathname === \"/time-zone-convert-share\") {\n    singleLayoutClasses = \" max-w-[1642px] mx-auto \";\n  }\n\n  // console.log(window.location.pathname)\n\n  useEffect(() => {\n    if (getDateTime) {\n      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();\n      setFromDateTime(getDateTimeParseData);\n    }\n    if (getDefaultTimeZones) {\n      setDefaultTimeZone({\n        label: getDefaultTimeZones,\n        value: timeZones[getDefaultTimeZones]\n      });\n      setFromtimezone({\n        label: getDefaultTimeZones,\n        value: timeZones[getDefaultTimeZones]\n      });\n    }\n    if (getTimeZones) {\n      let timezones = getTimeZones.split(\",\");\n      let timezonesData = timezones.map(item => {\n        return {\n          label: item,\n          value: timeZones[item]\n        };\n      });\n      setShowCityList(timezonesData);\n    }\n  }, [getDateTime, getDefaultTimeZones, getTimeZones]);\n  const convertDateTime = (totimezonevalue = \"\") => {\n    // let fromtimezonelabel = fromtimezone.label;\n    let fromtimezonevalue = fromtimezone.value || defaultTimeZone[\"value\"];\n    // let totimezonevalue = totimezone.value;\n    var fromConvertDateTime = moment.tz(fromDateTime, fromtimezonevalue);\n    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\n    if (moment(retDateTime).isValid()) {\n      return retDateTime;\n    }\n    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\n    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\n    if (moment(retDateTime).isValid()) {\n      return retDateTime;\n    }\n    return false;\n  };\n  const getTimeZoneSelectOptions = () => {\n    let data = [];\n    Object.keys(timeZones).sort().map(label => data.push({\n      label: `${label.replaceAll(\"_\", \" \")}`,\n      value: timeZones[label]\n    }));\n    return data;\n  };\n  const getLabelByTimezone = (label = \"\") => {\n    return Object.keys(timeZones).find(key => timeZones[key] === label) || \"\";\n  };\n  const calculateDifference = (timezone1, timezone2) => {\n    const now = moment().utc();\n    const time1 = now.clone().tz(timezone1);\n    const time2 = now.clone().tz(timezone2);\n    const diffInHours = (time1._offset - time2._offset) / 60;\n    let fromCityName = moment(fromDateTime).isValid() ? fromtimezone[\"label\"].replaceAll(\"_\", \" \") : defaultTimeZone[\"label\"].replaceAll(\"_\", \" \");\n    let returnText = ``;\n    if (diffInHours < 0) {\n      returnText += `${diffInHours * -1} Hours behind from ${fromCityName}`;\n    } else if (diffInHours > 0) {\n      returnText += `${diffInHours} Hours ahead of ${fromCityName}`;\n    } else {\n      returnText += `Same time as ${fromCityName}`;\n    }\n    return returnText;\n  };\n  const getSingleCityData = async (item, key) => {\n    try {\n      var _data2, _data2$nearest_area, _data2$nearest_area$, _data2$nearest_area$$, _data2$nearest_area$$2, _data3, _data3$nearest_area, _data3$nearest_area$, _data3$nearest_area$$, _data3$nearest_area$$2, _flagResponseData, _flagResponseData$, _flagResponseData$$fl, _flagResponseData2, _flagResponseData2$, _flagResponseData2$$f, _flagResponseData3, _flagResponseData3$, _flagResponseData3$$c, _flagResponseData4, _flagResponseData4$, _flagResponseData4$$m, _flagResponseData5, _flagResponseData5$, _flagResponseData6, _flagResponseData6$, _flagResponseData7, _flagResponseData7$, _flagResponseData8, _flagResponseData8$, _flagResponseData8$$t, _flagResponseData9, _flagResponseData9$, _data4, _data4$current_condit, _data5, _data6, _data6$current_condit, _data6$current_condit2, _data6$current_condit3, _data6$current_condit4, _data7, _data7$current_condit, _data7$current_condit2, _data7$current_condit3, _data7$current_condit4;\n      let city = item.label.replaceAll(\"_\", \" \");\n      let flagResponseData = {};\n      let data = {};\n      console.log(\"city\", city);\n      if (city && storeShowICityList[city]) {\n        flagResponseData = storeShowICityList[city];\n      } else {\n        var _data, _data$nearest_area, _data$nearest_area$, _data$nearest_area$$c, _data$nearest_area$$c2;\n        // First fetch weather data to get the country name\n        const weatherResponse = await fetch(`https://wttr.in/${city}?format=j1`);\n        data = await weatherResponse.json();\n\n        // Get country name from weather data\n        const country = (_data = data) === null || _data === void 0 ? void 0 : (_data$nearest_area = _data.nearest_area) === null || _data$nearest_area === void 0 ? void 0 : (_data$nearest_area$ = _data$nearest_area[0]) === null || _data$nearest_area$ === void 0 ? void 0 : (_data$nearest_area$$c = _data$nearest_area$.country) === null || _data$nearest_area$$c === void 0 ? void 0 : (_data$nearest_area$$c2 = _data$nearest_area$$c[0]) === null || _data$nearest_area$$c2 === void 0 ? void 0 : _data$nearest_area$$c2.value;\n        console.log(\"Country extracted from weather data:\", country);\n\n        // Then fetch country data using the actual country name\n        if (country) {\n          const flagResponse = await fetch(`https://restcountries.com/v3.1/name/${country}`);\n          flagResponseData = await flagResponse.json();\n          console.log(\"Country data fetched:\", flagResponseData);\n        }\n      }\n      let retData = {\n        ...item,\n        city,\n        country: ((_data2 = data) === null || _data2 === void 0 ? void 0 : (_data2$nearest_area = _data2.nearest_area) === null || _data2$nearest_area === void 0 ? void 0 : (_data2$nearest_area$ = _data2$nearest_area[0]) === null || _data2$nearest_area$ === void 0 ? void 0 : (_data2$nearest_area$$ = _data2$nearest_area$.country) === null || _data2$nearest_area$$ === void 0 ? void 0 : (_data2$nearest_area$$2 = _data2$nearest_area$$[0]) === null || _data2$nearest_area$$2 === void 0 ? void 0 : _data2$nearest_area$$2.value) || \"\",\n        state: ((_data3 = data) === null || _data3 === void 0 ? void 0 : (_data3$nearest_area = _data3.nearest_area) === null || _data3$nearest_area === void 0 ? void 0 : (_data3$nearest_area$ = _data3$nearest_area[0]) === null || _data3$nearest_area$ === void 0 ? void 0 : (_data3$nearest_area$$ = _data3$nearest_area$.region) === null || _data3$nearest_area$$ === void 0 ? void 0 : (_data3$nearest_area$$2 = _data3$nearest_area$$[0]) === null || _data3$nearest_area$$2 === void 0 ? void 0 : _data3$nearest_area$$2.value) || \"\",\n        flag: ((_flagResponseData = flagResponseData) === null || _flagResponseData === void 0 ? void 0 : (_flagResponseData$ = _flagResponseData[0]) === null || _flagResponseData$ === void 0 ? void 0 : (_flagResponseData$$fl = _flagResponseData$.flags) === null || _flagResponseData$$fl === void 0 ? void 0 : _flagResponseData$$fl.svg) || ((_flagResponseData2 = flagResponseData) === null || _flagResponseData2 === void 0 ? void 0 : (_flagResponseData2$ = _flagResponseData2[0]) === null || _flagResponseData2$ === void 0 ? void 0 : (_flagResponseData2$$f = _flagResponseData2$.flags) === null || _flagResponseData2$$f === void 0 ? void 0 : _flagResponseData2$$f.png) || \"\",\n        capital: ((_flagResponseData3 = flagResponseData) === null || _flagResponseData3 === void 0 ? void 0 : (_flagResponseData3$ = _flagResponseData3[0]) === null || _flagResponseData3$ === void 0 ? void 0 : (_flagResponseData3$$c = _flagResponseData3$.capital) === null || _flagResponseData3$$c === void 0 ? void 0 : _flagResponseData3$$c.join(\", \")) || \"\",\n        googleMapUrl: ((_flagResponseData4 = flagResponseData) === null || _flagResponseData4 === void 0 ? void 0 : (_flagResponseData4$ = _flagResponseData4[0]) === null || _flagResponseData4$ === void 0 ? void 0 : (_flagResponseData4$$m = _flagResponseData4$.maps) === null || _flagResponseData4$$m === void 0 ? void 0 : _flagResponseData4$$m.googleMaps) || \"\",\n        population: ((_flagResponseData5 = flagResponseData) === null || _flagResponseData5 === void 0 ? void 0 : (_flagResponseData5$ = _flagResponseData5[0]) === null || _flagResponseData5$ === void 0 ? void 0 : _flagResponseData5$.population) || \"\",\n        region: ((_flagResponseData6 = flagResponseData) === null || _flagResponseData6 === void 0 ? void 0 : (_flagResponseData6$ = _flagResponseData6[0]) === null || _flagResponseData6$ === void 0 ? void 0 : _flagResponseData6$.region) || \"\",\n        startOfWeek: ((_flagResponseData7 = flagResponseData) === null || _flagResponseData7 === void 0 ? void 0 : (_flagResponseData7$ = _flagResponseData7[0]) === null || _flagResponseData7$ === void 0 ? void 0 : _flagResponseData7$.startOfWeek) || \"\",\n        // timezones: flagResponseData[0].timezones.join(\", \") || \"\",\n        timezones: ((_flagResponseData8 = flagResponseData) === null || _flagResponseData8 === void 0 ? void 0 : (_flagResponseData8$ = _flagResponseData8[0]) === null || _flagResponseData8$ === void 0 ? void 0 : (_flagResponseData8$$t = _flagResponseData8$.timezones) === null || _flagResponseData8$$t === void 0 ? void 0 : _flagResponseData8$$t[0]) || \"\",\n        languages: (_flagResponseData9 = flagResponseData) !== null && _flagResponseData9 !== void 0 && (_flagResponseData9$ = _flagResponseData9[0]) !== null && _flagResponseData9$ !== void 0 && _flagResponseData9$.languages ? Object.values(flagResponseData[0].languages).join(\", \") : \"\",\n        weather: ((_data4 = data) === null || _data4 === void 0 ? void 0 : (_data4$current_condit = _data4.current_condition) === null || _data4$current_condit === void 0 ? void 0 : _data4$current_condit[0]) || \"\",\n        dailyWeather: ((_data5 = data) === null || _data5 === void 0 ? void 0 : _data5.weather) || \"\",\n        condition: ((_data6 = data) === null || _data6 === void 0 ? void 0 : (_data6$current_condit = _data6.current_condition) === null || _data6$current_condit === void 0 ? void 0 : (_data6$current_condit2 = _data6$current_condit[0]) === null || _data6$current_condit2 === void 0 ? void 0 : (_data6$current_condit3 = _data6$current_condit2.weatherDesc) === null || _data6$current_condit3 === void 0 ? void 0 : (_data6$current_condit4 = _data6$current_condit3[0]) === null || _data6$current_condit4 === void 0 ? void 0 : _data6$current_condit4.value) || \"\",\n        weatherIcon: ((_data7 = data) === null || _data7 === void 0 ? void 0 : (_data7$current_condit = _data7.current_condition) === null || _data7$current_condit === void 0 ? void 0 : (_data7$current_condit2 = _data7$current_condit[0]) === null || _data7$current_condit2 === void 0 ? void 0 : (_data7$current_condit3 = _data7$current_condit2.weatherIconUrl) === null || _data7$current_condit3 === void 0 ? void 0 : (_data7$current_condit4 = _data7$current_condit3[0]) === null || _data7$current_condit4 === void 0 ? void 0 : _data7$current_condit4.value) || \"\",\n        fullWeaterData: data,\n        fullCountryDetails: flagResponseData\n      };\n\n      // Update the showCityList state with the new data\n      setShowCityList(prevList => {\n        const newArray = [...prevList];\n        newArray[key] = retData;\n        return newArray;\n      });\n      return retData;\n    } catch (error) {\n      console.error(\"Error fetching city data:\", error);\n\n      // Return basic item data if API fails\n      const fallbackData = {\n        ...item,\n        city: item.label.replaceAll(\"_\", \" \"),\n        country: \"Unknown\",\n        state: \"Unknown\",\n        flag: \"\",\n        capital: \"Unknown\",\n        googleMapUrl: \"\",\n        population: \"\",\n        region: \"Unknown\",\n        startOfWeek: \"Unknown\",\n        timezones: \"\",\n        languages: \"Unknown\",\n        weather: {},\n        dailyWeather: [],\n        condition: \"Unknown\",\n        weatherIcon: \"\",\n        fullWeaterData: {},\n        fullCountryDetails: {}\n      };\n\n      // Update the showCityList with fallback data\n      setShowCityList(prevList => {\n        const newArray = [...prevList];\n        newArray[key] = fallbackData;\n        return newArray;\n      });\n      return fallbackData;\n    }\n  };\n  const fetchWeather = async () => {\n    try {\n      const results = await Promise.all(showCityList.map(async (item, key) => {\n        try {\n          var _data9, _data9$nearest_area, _data9$nearest_area$, _data9$nearest_area$$, _data9$nearest_area$$2, _data10, _data10$nearest_area, _data10$nearest_area$, _data10$nearest_area$2, _data10$nearest_area$3, _flagResponseData10, _flagResponseData10$, _flagResponseData10$$, _flagResponseData11, _flagResponseData11$, _flagResponseData11$$, _flagResponseData12, _flagResponseData12$, _flagResponseData12$$, _flagResponseData13, _flagResponseData13$, _flagResponseData13$$, _flagResponseData14, _flagResponseData14$, _flagResponseData15, _flagResponseData15$, _flagResponseData16, _flagResponseData16$, _flagResponseData17, _flagResponseData17$, _flagResponseData17$$, _flagResponseData18, _flagResponseData18$, _data11, _data11$current_condi, _data12, _data13, _data13$current_condi, _data13$current_condi2, _data13$current_condi3, _data13$current_condi4, _data14, _data14$current_condi, _data14$current_condi2, _data14$current_condi3, _data14$current_condi4;\n          let city = item.label.replaceAll(\"_\", \" \");\n          let flagResponseData = {};\n          let data = {};\n          if (city && storeShowICityList[city]) {\n            flagResponseData = storeShowICityList[city];\n          } else {\n            var _data8, _data8$nearest_area, _data8$nearest_area$, _data8$nearest_area$$, _data8$nearest_area$$2;\n            const response = await fetch(`https://wttr.in/${city}?format=j1`);\n            data = await response.json();\n            let country = (_data8 = data) === null || _data8 === void 0 ? void 0 : (_data8$nearest_area = _data8.nearest_area) === null || _data8$nearest_area === void 0 ? void 0 : (_data8$nearest_area$ = _data8$nearest_area[0]) === null || _data8$nearest_area$ === void 0 ? void 0 : (_data8$nearest_area$$ = _data8$nearest_area$.country) === null || _data8$nearest_area$$ === void 0 ? void 0 : (_data8$nearest_area$$2 = _data8$nearest_area$$[0]) === null || _data8$nearest_area$$2 === void 0 ? void 0 : _data8$nearest_area$$2.value;\n            if (country) {\n              const flagResponse = await fetch(`https://restcountries.com/v3.1/name/${country}`);\n              flagResponseData = await flagResponse.json();\n            }\n          }\n          let retData = {\n            ...item,\n            city,\n            country: ((_data9 = data) === null || _data9 === void 0 ? void 0 : (_data9$nearest_area = _data9.nearest_area) === null || _data9$nearest_area === void 0 ? void 0 : (_data9$nearest_area$ = _data9$nearest_area[0]) === null || _data9$nearest_area$ === void 0 ? void 0 : (_data9$nearest_area$$ = _data9$nearest_area$.country) === null || _data9$nearest_area$$ === void 0 ? void 0 : (_data9$nearest_area$$2 = _data9$nearest_area$$[0]) === null || _data9$nearest_area$$2 === void 0 ? void 0 : _data9$nearest_area$$2.value) || \"\",\n            state: ((_data10 = data) === null || _data10 === void 0 ? void 0 : (_data10$nearest_area = _data10.nearest_area) === null || _data10$nearest_area === void 0 ? void 0 : (_data10$nearest_area$ = _data10$nearest_area[0]) === null || _data10$nearest_area$ === void 0 ? void 0 : (_data10$nearest_area$2 = _data10$nearest_area$.region) === null || _data10$nearest_area$2 === void 0 ? void 0 : (_data10$nearest_area$3 = _data10$nearest_area$2[0]) === null || _data10$nearest_area$3 === void 0 ? void 0 : _data10$nearest_area$3.value) || \"\",\n            flag: ((_flagResponseData10 = flagResponseData) === null || _flagResponseData10 === void 0 ? void 0 : (_flagResponseData10$ = _flagResponseData10[0]) === null || _flagResponseData10$ === void 0 ? void 0 : (_flagResponseData10$$ = _flagResponseData10$.flags) === null || _flagResponseData10$$ === void 0 ? void 0 : _flagResponseData10$$.svg) || ((_flagResponseData11 = flagResponseData) === null || _flagResponseData11 === void 0 ? void 0 : (_flagResponseData11$ = _flagResponseData11[0]) === null || _flagResponseData11$ === void 0 ? void 0 : (_flagResponseData11$$ = _flagResponseData11$.flags) === null || _flagResponseData11$$ === void 0 ? void 0 : _flagResponseData11$$.png) || \"\",\n            capital: ((_flagResponseData12 = flagResponseData) === null || _flagResponseData12 === void 0 ? void 0 : (_flagResponseData12$ = _flagResponseData12[0]) === null || _flagResponseData12$ === void 0 ? void 0 : (_flagResponseData12$$ = _flagResponseData12$.capital) === null || _flagResponseData12$$ === void 0 ? void 0 : _flagResponseData12$$.join(\", \")) || \"\",\n            googleMapUrl: ((_flagResponseData13 = flagResponseData) === null || _flagResponseData13 === void 0 ? void 0 : (_flagResponseData13$ = _flagResponseData13[0]) === null || _flagResponseData13$ === void 0 ? void 0 : (_flagResponseData13$$ = _flagResponseData13$.maps) === null || _flagResponseData13$$ === void 0 ? void 0 : _flagResponseData13$$.googleMaps) || \"\",\n            population: ((_flagResponseData14 = flagResponseData) === null || _flagResponseData14 === void 0 ? void 0 : (_flagResponseData14$ = _flagResponseData14[0]) === null || _flagResponseData14$ === void 0 ? void 0 : _flagResponseData14$.population) || \"\",\n            region: ((_flagResponseData15 = flagResponseData) === null || _flagResponseData15 === void 0 ? void 0 : (_flagResponseData15$ = _flagResponseData15[0]) === null || _flagResponseData15$ === void 0 ? void 0 : _flagResponseData15$.region) || \"\",\n            startOfWeek: ((_flagResponseData16 = flagResponseData) === null || _flagResponseData16 === void 0 ? void 0 : (_flagResponseData16$ = _flagResponseData16[0]) === null || _flagResponseData16$ === void 0 ? void 0 : _flagResponseData16$.startOfWeek) || \"\",\n            // timezones: flagResponseData[0].timezones.join(\", \") || \"\",\n            timezones: ((_flagResponseData17 = flagResponseData) === null || _flagResponseData17 === void 0 ? void 0 : (_flagResponseData17$ = _flagResponseData17[0]) === null || _flagResponseData17$ === void 0 ? void 0 : (_flagResponseData17$$ = _flagResponseData17$.timezones) === null || _flagResponseData17$$ === void 0 ? void 0 : _flagResponseData17$$[0]) || \"\",\n            languages: (_flagResponseData18 = flagResponseData) !== null && _flagResponseData18 !== void 0 && (_flagResponseData18$ = _flagResponseData18[0]) !== null && _flagResponseData18$ !== void 0 && _flagResponseData18$.languages ? Object.values(flagResponseData[0].languages).join(\", \") : \"\",\n            weather: ((_data11 = data) === null || _data11 === void 0 ? void 0 : (_data11$current_condi = _data11.current_condition) === null || _data11$current_condi === void 0 ? void 0 : _data11$current_condi[0]) || \"\",\n            dailyWeather: ((_data12 = data) === null || _data12 === void 0 ? void 0 : _data12.weather) || \"\",\n            condition: ((_data13 = data) === null || _data13 === void 0 ? void 0 : (_data13$current_condi = _data13.current_condition) === null || _data13$current_condi === void 0 ? void 0 : (_data13$current_condi2 = _data13$current_condi[0]) === null || _data13$current_condi2 === void 0 ? void 0 : (_data13$current_condi3 = _data13$current_condi2.weatherDesc) === null || _data13$current_condi3 === void 0 ? void 0 : (_data13$current_condi4 = _data13$current_condi3[0]) === null || _data13$current_condi4 === void 0 ? void 0 : _data13$current_condi4.value) || \"\",\n            weatherIcon: ((_data14 = data) === null || _data14 === void 0 ? void 0 : (_data14$current_condi = _data14.current_condition) === null || _data14$current_condi === void 0 ? void 0 : (_data14$current_condi2 = _data14$current_condi[0]) === null || _data14$current_condi2 === void 0 ? void 0 : (_data14$current_condi3 = _data14$current_condi2.weatherIconUrl) === null || _data14$current_condi3 === void 0 ? void 0 : (_data14$current_condi4 = _data14$current_condi3[0]) === null || _data14$current_condi4 === void 0 ? void 0 : _data14$current_condi4.value) || \"\",\n            fullWeaterData: data,\n            fullCountryDetails: flagResponseData\n          };\n          return retData;\n        } catch (error) {\n          console.error(`Error fetching data for ${item.label}:`, error);\n          // Return basic item data if API fails\n          return {\n            ...item,\n            city: item.label.replaceAll(\"_\", \" \"),\n            country: \"Unknown\",\n            state: \"Unknown\",\n            flag: \"\",\n            capital: \"Unknown\",\n            googleMapUrl: \"\",\n            population: \"\",\n            region: \"Unknown\",\n            startOfWeek: \"Unknown\",\n            timezones: \"\",\n            languages: \"Unknown\",\n            weather: {},\n            dailyWeather: [],\n            condition: \"Unknown\",\n            weatherIcon: \"\",\n            fullWeaterData: {},\n            fullCountryDetails: {}\n          };\n        }\n\n        // return storeShowICityList[item];\n      }));\n\n      // results.map((item) => {\n      //   storeShowICityList[item.label] = { ...item };\n      // });\n\n      // console.log(storeShowICityList)\n\n      setLoading(false);\n      setShowCityList(results);\n    } catch (error) {\n      console.error(\"Error in fetchWeather:\", error);\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchWeather();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white dark:bg-gray-900 rounded-xl p-4 \" + singleLayoutClasses,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-start items-start flex-row mb-[20px]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-left w-1/2 \",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xl font-medium \",\n            children: \"Time Zone Converter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-start items-start flex-row mb-[50px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg  px-6 py-3 me-[10px] w-4/12 h-[210px] bg-[#0b333f] text-[#fff]\",\n          children: /*#__PURE__*/_jsxDEV(DynamicTimeCard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200 rounded-lg  mx-[10px] px-6 py-3 w-4/12 h-[210px] bg-[#DFECF1]\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            className: \"text-left\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" justify-start items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4 w-full sm:w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"start-time\",\n                  className: \"block mb-2 text-sm font-medium text-gray-900 dark:text-white\",\n                  children: \"Select Date and Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"datetime-local\",\n                    id: \"shiftStart\",\n                    disabled: loading,\n                    value: (fromDateTime || \"\").toString().substring(0, 16),\n                    onChange: e => {\n                      setFromDateTime(e.target.value);\n                    },\n                    required: true,\n                    className: \"bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4 w-1/2 sm:w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"team\",\n                    className: \"block text-sm font-medium text-gray-700 pb-4\",\n                    children: \"Select City\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    isClearable: false,\n                    isSearchable: true,\n                    isDisabled: loading,\n                    isMulti: false,\n                    required: false,\n                    placeholder: \"Choose timezone\",\n                    name: \"fromtimezone\",\n                    value: fromtimezone,\n                    options: getTimeZoneSelectOptions(),\n                    onChange: item => {\n                      setFromtimezone(item);\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border border-gray-200   rounded-lg  ms-[10px] px-6 py-3 w-4/12 h-[210px]  bg-[#DFECF1] text-[#000]\",\n          children: totimezone.value && convertDateTime(totimezone.value) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-[18px] font-bold\",\n              children: [moment(fromDateTime).isValid() && fromtimezone[\"label\"].replaceAll(\"_\", \" \"), !moment(fromDateTime).isValid() && defaultTimeZone[\"label\"].replaceAll(\"_\", \" \"), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 21\n              }, this), moment(fromDateTime).isValid() && fromtimezone[\"value\"], !moment(fromDateTime).isValid() && defaultTimeZone[\"value\"]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-[64px] font-bold\",\n              children: [moment(fromDateTime).isValid() && moment(fromDateTime).format(defaultTimeFormat), !moment(fromDateTime).isValid() && moment(currentDateTime).isValid() && moment(currentDateTime).format(defaultTimeFormat)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-[20px]  \",\n              children: [moment(fromDateTime).isValid() && moment(fromDateTime).format(defaultDateFormat), !moment(fromDateTime).isValid() && moment(currentDateTime).isValid() && moment(currentDateTime).format(defaultDateFormat)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full flex justify-start items-start flex-wrap  mb-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-start w-full \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center rounded-lg gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid shrink-0 grid-cols-1 focus-within:relative w-11/12 \",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                closeMenuOnSelect: false,\n                isClearable: true,\n                isSearchable: true,\n                isDisabled: loading,\n                isMulti: true,\n                required: false,\n                placeholder: \"Choose your city\",\n                name: \"showCityList\",\n                value: showCityList,\n                options: getTimeZoneSelectOptions(),\n                onChange: item => {\n                  setShowCityList(item);\n\n                  // Fetch data for newly added cities\n                  if (item && item.length > 0) {\n                    item.forEach((city, key) => {\n                      if (!city.country && city.label) {\n                        // Fetch data for cities that don't have country data yet\n                        setTimeout(() => getSingleCityData(city, key), 100);\n                      }\n                    });\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              disabled: loading || shareBtnText === 'Copied',\n              onClick: () => {\n                navigator.clipboard.writeText(generateShareUrl());\n                setShareBtnText(\"Copied\");\n                setTimeout(function () {\n                  setShareBtnText(\"Share\");\n                }, 5000);\n              },\n              target: \"_blank\",\n              className: \"block min-w-0 rounded-lg text-center grow py-1.5 pr-3 pl-1 border border-gray-200 bg-[#076d92] hover:bg-[#0B333F] text-base text-[#fff] placeholder:text-gray-400 focus:outline-none sm:text-sm/6\",\n              children: shareBtnText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: \"Loding....\"\n      }, void 0, false), !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full flex justify-start items-start flex-wrap  \",\n        style: background,\n        children: showCityList.length > 0 && showCityList.map((item, key) => {\n          var _item$weather, _item$weather2, _item$weather3, _item$dailyWeather, _item$dailyWeather$, _item$dailyWeather2, _item$dailyWeather2$, _item$dailyWeather2$$, _item$dailyWeather$2, _item$dailyWeather$2$, _item$dailyWeather$2$2, _item$dailyWeather3, _item$dailyWeather3$, _item$dailyWeather3$$, _item$dailyWeather$3, _item$dailyWeather$3$, _item$dailyWeather$3$2, _item$weather4, _item$weather5, _item$dailyWeather4, _item$dailyWeather$4, _item$dailyWeather5, _item$dailyWeather$5;\n          // If city data is not loaded yet, fetch it but still show the city\n          if (!item.country) {\n            getSingleCityData(item, key);\n          }\n          ;\n\n          // let label = key, timezone = timeZoneList[key];\n          let label = item[\"label\"];\n          let timezone = item[\"value\"];\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"  w-full px-[20px] \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-b-2 border-gray-400 py-4 hover:bg-[#DFECF1]  text-sm  text-start flex capitalize align-middle\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-[200px] flex-col justify-center text-center items-center\",\n                onClick: () => (item === null || item === void 0 ? void 0 : item.googleMapUrl) && window.open(item.googleMapUrl, \"_blank\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-center text-center \",\n                  children: item.flag ? /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.flag,\n                    alt: item.country || label,\n                    className: \"w-[100%] max-w-[65px] h-auto shadow-lg border border-1 mb-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-[65px] h-[45px] bg-gray-200 border border-1 mb-1 flex items-center justify-center text-xs text-gray-500\",\n                    children: \"Loading...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[14px] mb-1\",\n                  children: item.country || \"Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-bold text-xl\",\n                  children: label.replaceAll(\"_\", \" \") ? label.replaceAll(\"_\", \" \") : label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2/12\",\n                children: [item.startOfWeek ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate\",\n                  children: [\"Start Of Week: \", item.startOfWeek]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate text-gray-400\",\n                  children: \"Start Of Week: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 27\n                }, this), item.capital ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate\",\n                  children: [\"Capital: \", item.capital]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate text-gray-400\",\n                  children: \"Capital: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 27\n                }, this), item.languages ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate\",\n                  children: [\"Language: \", item.languages]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate text-gray-400\",\n                  children: \"Language: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 27\n                }, this), item.state ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate\",\n                  children: [\"State/City: \", item.state]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"truncate text-gray-400\",\n                  children: \"State/City: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2/12 \",\n                children: [(_item$weather = item.weather) !== null && _item$weather !== void 0 && _item$weather.humidity ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Humidity: \", item.weather.humidity, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400\",\n                  children: \"Humidity: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 27\n                }, this), (_item$weather2 = item.weather) !== null && _item$weather2 !== void 0 && _item$weather2.visibility ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Visibility: \", item.weather.visibility, \"km\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400\",\n                  children: \"Visibility: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 27\n                }, this), (_item$weather3 = item.weather) !== null && _item$weather3 !== void 0 && _item$weather3.windspeedKmph ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Wind Speed: \", item.weather.windspeedKmph, \"km/h\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400\",\n                  children: \"Wind Speed: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 27\n                }, this), item !== null && item !== void 0 && item.condition ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Condition: \", item.condition]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-400\",\n                  children: \"Condition: Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2/12 mb-1\",\n                children: [(item === null || item === void 0 ? void 0 : (_item$dailyWeather = item.dailyWeather) === null || _item$dailyWeather === void 0 ? void 0 : _item$dailyWeather[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" \",\n                  children: [\"Sun Hour: \", (_item$dailyWeather$ = item.dailyWeather[0]) === null || _item$dailyWeather$ === void 0 ? void 0 : _item$dailyWeather$.sunHour, \"h\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 27\n                }, this), (item === null || item === void 0 ? void 0 : (_item$dailyWeather2 = item.dailyWeather) === null || _item$dailyWeather2 === void 0 ? void 0 : (_item$dailyWeather2$ = _item$dailyWeather2[0]) === null || _item$dailyWeather2$ === void 0 ? void 0 : (_item$dailyWeather2$$ = _item$dailyWeather2$.astronomy) === null || _item$dailyWeather2$$ === void 0 ? void 0 : _item$dailyWeather2$$[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" \",\n                  children: [\"Today Sunset:\", \" \", (_item$dailyWeather$2 = item.dailyWeather[0]) === null || _item$dailyWeather$2 === void 0 ? void 0 : (_item$dailyWeather$2$ = _item$dailyWeather$2.astronomy) === null || _item$dailyWeather$2$ === void 0 ? void 0 : (_item$dailyWeather$2$2 = _item$dailyWeather$2$[0]) === null || _item$dailyWeather$2$2 === void 0 ? void 0 : _item$dailyWeather$2$2.sunset]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 27\n                }, this), (item === null || item === void 0 ? void 0 : (_item$dailyWeather3 = item.dailyWeather) === null || _item$dailyWeather3 === void 0 ? void 0 : (_item$dailyWeather3$ = _item$dailyWeather3[1]) === null || _item$dailyWeather3$ === void 0 ? void 0 : (_item$dailyWeather3$$ = _item$dailyWeather3$.astronomy) === null || _item$dailyWeather3$$ === void 0 ? void 0 : _item$dailyWeather3$$[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" \",\n                  children: [\"Tomorrow Sunrise:\", \" \", (_item$dailyWeather$3 = item.dailyWeather[1]) === null || _item$dailyWeather$3 === void 0 ? void 0 : (_item$dailyWeather$3$ = _item$dailyWeather$3.astronomy) === null || _item$dailyWeather$3$ === void 0 ? void 0 : (_item$dailyWeather$3$2 = _item$dailyWeather$3$[0]) === null || _item$dailyWeather$3$2 === void 0 ? void 0 : _item$dailyWeather$3$2.sunrise]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-1/12 mb-1 flex-col align-middle text-end\",\n                children: (item === null || item === void 0 ? void 0 : (_item$weather4 = item.weather) === null || _item$weather4 === void 0 ? void 0 : _item$weather4.temp_C) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold pb-2 pe-3\",\n                  children: [item.weather.temp_C, /*#__PURE__*/_jsxDEV(\"sup\", {\n                    children: \"\\xB0C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-1/12 mb-1 flex-col align-middle text-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Feels like: \", item === null || item === void 0 ? void 0 : (_item$weather5 = item.weather) === null || _item$weather5 === void 0 ? void 0 : _item$weather5.FeelsLikeC, /*#__PURE__*/_jsxDEV(\"sup\", {\n                    children: \"\\xB0C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 25\n                }, this), (item === null || item === void 0 ? void 0 : (_item$dailyWeather4 = item.dailyWeather) === null || _item$dailyWeather4 === void 0 ? void 0 : _item$dailyWeather4[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" \",\n                  children: [\"Max Temp: \", item === null || item === void 0 ? void 0 : (_item$dailyWeather$4 = item.dailyWeather[0]) === null || _item$dailyWeather$4 === void 0 ? void 0 : _item$dailyWeather$4.maxtempC, /*#__PURE__*/_jsxDEV(\"sup\", {\n                    children: \"\\xB0C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 27\n                }, this), (item === null || item === void 0 ? void 0 : (_item$dailyWeather5 = item.dailyWeather) === null || _item$dailyWeather5 === void 0 ? void 0 : _item$dailyWeather5[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" \",\n                  children: [\"Min Temp: \", item === null || item === void 0 ? void 0 : (_item$dailyWeather$5 = item.dailyWeather[0]) === null || _item$dailyWeather$5 === void 0 ? void 0 : _item$dailyWeather$5.mintempC, /*#__PURE__*/_jsxDEV(\"sup\", {\n                    children: \"\\xB0C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1042,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2/12 flex-col text-end align-middle\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-4xl font-bold \",\n                  children: convertDateTime(timezone) && convertDateTime(timezone).format(defaultTimeFormat)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-lg \",\n                  children: convertDateTime(timezone) && convertDateTime(timezone).format(\"dddd, LL\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1053,\n                  columnNumber: 25\n                }, this), (item === null || item === void 0 ? void 0 : item.timezones) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-xm \",\n                  children: [calculateDifference(item.value, defaultTimeZone[\"value\"]), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 90\n                  }, this), \"Timezone: \", convertDateTime(timezone) && convertDateTime(timezone).format(\"z\")]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 21\n            }, this)\n          }, \"timezone-\" + timezone + label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 19\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n}\n_s(TimeZoneConvert, \"U4jAt4CTgUHOn7Y6TmBVOMbHOMo=\");\n_c = TimeZoneConvert;\nexport default TimeZoneConvert;\nvar _c;\n$RefreshReg$(_c, \"TimeZoneConvert\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "moment", "Select", "DynamicTimeCard", "timezonebg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "timeZones", "Dubai", "Kabul", "Yerevan", "Baku", "Dhaka", "Brunei", "<PERSON><PERSON><PERSON><PERSON>", "Shanghai", "Urumqi", "Nicosia", "Famagusta", "Tbilisi", "Hong_Kong", "Jakarta", "Pontianak", "Makassar", "Jayapura", "Jerusalem", "Kolkata", "Baghdad", "Tehran", "Amman", "Tokyo", "Bishkek", "Pyongyang", "Seoul", "Almaty", "Qyzylorda", "Q<PERSON><PERSON><PERSON>", "Aqtobe", "<PERSON><PERSON><PERSON><PERSON>", "Atyrau", "Oral", "Beirut", "Colombo", "Yangon", "Ulaanbaatar", "Hovd", "<PERSON><PERSON><PERSON>", "Macau", "Kuala_Lumpur", "<PERSON><PERSON>", "Karachi", "Gaza", "Hebron", "Kathman<PERSON>", "Yekaterinburg", "Qatar", "Omsk", "Novosibirsk", "Barnaul", "Tomsk", "Novokuznetsk", "Krasnoyarsk", "Irkutsk", "<PERSON><PERSON>", "Yakutsk", "Khandyga", "Vladivostok", "Ust_Nera", "Singapore", "<PERSON><PERSON><PERSON>", "Sakhalin", "Srednekolymsk", "Kamchatka", "<PERSON><PERSON><PERSON>", "Bangkok", "<PERSON><PERSON><PERSON>", "Taipei", "<PERSON><PERSON>", "Ashgabat", "Damascus", "Riyadh", "Samarkand", "Tashkent", "<PERSON><PERSON><PERSON>", "Andorra", "<PERSON><PERSON><PERSON>", "Vienna", "Brussels", "Sofia", "Minsk", "Zurich", "Prague", "Berlin", "Copenhagen", "Tallinn", "Madrid", "Helsinki", "Paris", "London", "Gibraltar", "Athens", "Budapest", "Dublin", "Rome", "Vilnius", "Luxembourg", "Riga", "Monaco", "<PERSON><PERSON><PERSON>", "Malta", "Amsterdam", "Oslo", "Warsaw", "Lisbon", "Bucharest", "Belgrade", "Kaliningrad", "Moscow", "Simferopol", "<PERSON><PERSON>", "Astrakhan", "Volgograd", "<PERSON><PERSON>", "Ulyanovsk", "Samara", "Stockholm", "Istanbul", "Kiev", "Uzhgorod", "Zaporozhye", "<PERSON>", "<PERSON>", "DumontDUrville", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Syowa", "Troll", "Vostok", "Macquarie", "Buenos_Aires", "Cordoba", "Salta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Catamarca", "La_Rioja", "San_Juan", "Mendoza", "<PERSON><PERSON>Luis", "Rio_Gallegos", "Ushuaia", "Barbados", "La_Paz", "Belem", "Fortaleza", "Recife", "Araguaina", "Maceio", "Bahia", "Sao_Paulo", "Campo_Grande", "<PERSON><PERSON><PERSON>", "Porto_Velho", "Boa_Vista", "Manaus", "Eirunepe", "Rio_Branco", "Nassau", "Belize", "St_Johns", "Halifax", "Glace_Bay", "Moncton", "Goose_Bay", "<PERSON><PERSON>", "Toronto", "Nipigon", "Thunder_Bay", "Iqaluit", "Pangnirtung", "Atikokan", "Winnipeg", "Rainy_River", "Resolute", "Rankin_Inlet", "Regina", "Swift_Current", "Edmonton", "Cambridge_Bay", "Yellowknife", "Inuvik", "Creston", "Dawson_Creek", "Fort_Nelson", "Vancouver", "Whitehorse", "<PERSON>", "Santiago", "Punta_Arenas", "Bogota", "Costa_Rica", "Havana", "Curacao", "Santo_Domingo", "Guayaquil", "Cayenne", "<PERSON><PERSON><PERSON>", "Danmarkshavn", "Scoresbysund", "Thule", "Guatemala", "Guyana", "Tegucigalpa", "Port_au_Prince", "Jamaica", "Martinique", "Mexico_City", "Cancun", "<PERSON><PERSON>", "Monterrey", "Matamoros", "Caracas", "Mazatlan", "Chihuahua", "<PERSON><PERSON><PERSON>", "Hermosillo", "Tijuana", "Bahia_Banderas", "Managua", "Panama", "Lima", "Miquelon", "Puerto_Rico", "El_Salvador", "Grand_<PERSON><PERSON>", "Paramaribo", "Asuncion", "Port_of_Spain", "New_York", "New_Jersey", "Detroit", "Louisville", "<PERSON><PERSON><PERSON>", "Indianapolis", "Vincennes", "Winamac", "Marengo", "Petersburg", "<PERSON><PERSON><PERSON>", "Tell_City", "<PERSON>", "Chicago", "Menominee", "Denver", "Boise", "Phoenix", "Center", "New_Salem", "<PERSON><PERSON><PERSON>", "Los_Angeles", "Anchorage", "Alaska", "Juneau", "Sitka", "Metlakatla", "<PERSON><PERSON><PERSON>", "Nome", "Adak", "Montevideo", "Pago_Pago", "Rarotonga", "Easter", "Galapagos", "Fiji", "<PERSON><PERSON>", "Pohn<PERSON>i", "Kosrae", "Guam", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Noumea", "Norfolk", "Nauru", "Niue", "Auckland", "Chatham", "Tahiti", "Marquesas", "Gambier", "Port_Moresby", "Bougainville", "Pitcairn", "<PERSON><PERSON>", "Guadalcanal", "<PERSON><PERSON><PERSON><PERSON>", "Tongatapu", "Funafuti", "Wake", "Honolulu", "Efate", "<PERSON>", "Apia", "<PERSON><PERSON><PERSON>", "Hobart", "<PERSON>", "Melbourne", "Sydney", "Broken_Hill", "Brisbane", "<PERSON><PERSON><PERSON>", "Adelaide", "<PERSON>", "Perth", "<PERSON><PERSON><PERSON>", "Abidjan", "Algiers", "Cairo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Accra", "Bissau", "Nairobi", "Monrovia", "Tripoli", "Casablanca", "Maputo", "Windhoek", "Lagos", "Khartoum", "Juba", "Sao_Tome", "Ndjamena", "<PERSON><PERSON>", "Johannesburg", "Azores", "Bermuda", "Madeira", "Cape_Verde", "Canary", "<PERSON>", "Faroe", "South_Georgia", "Reykjavik", "Cocos", "Christmas", "Chagos", "Mauritius", "Maldives", "<PERSON><PERSON>", "Reunion", "<PERSON><PERSON><PERSON><PERSON>", "TimeZoneConvert", "_s", "defaultDateFormat", "defaultTimeFormat", "background", "backgroundImage", "backgroundPosition", "backgroundSize", "backgroundRepeat", "queryParameters", "URLSearchParams", "window", "location", "search", "getDateTime", "get", "getDefaultTimeZones", "getTimeZones", "defaultTimeZone", "setDefaultTimeZone", "label", "value", "currentDateTime", "setCurrentDateTime", "tz", "error", "setError", "loading", "setLoading", "storeShowICityList", "ipData", "setIpData", "shareBtnText", "setShareBtnText", "fromDateTime", "setFromDateTime", "fromtimezone", "setFromtimezone", "totimezone", "setTotimezone", "showCityList", "setShowCityList", "isFixed", "datetimeToISOString", "datetime", "Date", "valueOf", "generateShareUrl", "console", "log", "url", "href", "split", "params", "isoDateTime", "toISOString", "dtmz", "timezones", "map", "item", "replaceAll", "append", "join", "origin", "toString", "singleLayoutClasses", "pathname", "getDateTimeParseData", "Number", "timezonesData", "convertDateTime", "totimezonevalue", "fromtimezonevalue", "fromConvertDateTime", "retDateTime", "clone", "<PERSON><PERSON><PERSON><PERSON>", "getTimeZoneSelectOptions", "data", "Object", "keys", "sort", "push", "getLabelByTimezone", "find", "key", "calculateDifference", "timezone1", "timezone2", "now", "utc", "time1", "time2", "diffInHours", "_offset", "fromCityName", "returnText", "getSingleCityData", "_data2", "_data2$nearest_area", "_data2$nearest_area$", "_data2$nearest_area$$", "_data2$nearest_area$$2", "_data3", "_data3$nearest_area", "_data3$nearest_area$", "_data3$nearest_area$$", "_data3$nearest_area$$2", "_flagResponseData", "_flagResponseData$", "_flagResponseData$$fl", "_flagResponseData2", "_flagResponseData2$", "_flagResponseData2$$f", "_flagResponseData3", "_flagResponseData3$", "_flagResponseData3$$c", "_flagResponseData4", "_flagResponseData4$", "_flagResponseData4$$m", "_flagResponseData5", "_flagResponseData5$", "_flagResponseData6", "_flagResponseData6$", "_flagResponseData7", "_flagResponseData7$", "_flagResponseData8", "_flagResponseData8$", "_flagResponseData8$$t", "_flagResponseData9", "_flagResponseData9$", "_data4", "_data4$current_condit", "_data5", "_data6", "_data6$current_condit", "_data6$current_condit2", "_data6$current_condit3", "_data6$current_condit4", "_data7", "_data7$current_condit", "_data7$current_condit2", "_data7$current_condit3", "_data7$current_condit4", "city", "flagResponseData", "_data", "_data$nearest_area", "_data$nearest_area$", "_data$nearest_area$$c", "_data$nearest_area$$c2", "weatherResponse", "fetch", "json", "country", "nearest_area", "flagResponse", "retData", "state", "region", "flag", "flags", "svg", "png", "capital", "googleMapUrl", "maps", "googleMaps", "population", "startOfWeek", "languages", "values", "weather", "current_condition", "dailyWeather", "condition", "weatherDesc", "weatherIcon", "weatherIconUrl", "fullWeaterData", "fullCountryDetails", "prevList", "newArray", "fallbackD<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "results", "Promise", "all", "_data9", "_data9$nearest_area", "_data9$nearest_area$", "_data9$nearest_area$$", "_data9$nearest_area$$2", "_data10", "_data10$nearest_area", "_data10$nearest_area$", "_data10$nearest_area$2", "_data10$nearest_area$3", "_flagResponseData10", "_flagResponseData10$", "_flagResponseData10$$", "_flagResponseData11", "_flagResponseData11$", "_flagResponseData11$$", "_flagResponseData12", "_flagResponseData12$", "_flagResponseData12$$", "_flagResponseData13", "_flagResponseData13$", "_flagResponseData13$$", "_flagResponseData14", "_flagResponseData14$", "_flagResponseData15", "_flagResponseData15$", "_flagResponseData16", "_flagResponseData16$", "_flagResponseData17", "_flagResponseData17$", "_flagResponseData17$$", "_flagResponseData18", "_flagResponseData18$", "_data11", "_data11$current_condi", "_data12", "_data13", "_data13$current_condi", "_data13$current_condi2", "_data13$current_condi3", "_data13$current_condi4", "_data14", "_data14$current_condi", "_data14$current_condi2", "_data14$current_condi3", "_data14$current_condi4", "_data8", "_data8$nearest_area", "_data8$nearest_area$", "_data8$nearest_area$$", "_data8$nearest_area$$2", "response", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "disabled", "substring", "onChange", "e", "target", "required", "isClearable", "isSearchable", "isDisabled", "is<PERSON><PERSON><PERSON>", "placeholder", "name", "options", "format", "closeMenuOnSelect", "length", "for<PERSON>ach", "setTimeout", "onClick", "navigator", "clipboard", "writeText", "style", "_item$weather", "_item$weather2", "_item$weather3", "_item$dailyWeather", "_item$dailyWeather$", "_item$dailyWeather2", "_item$dailyWeather2$", "_item$dailyWeather2$$", "_item$dailyWeather$2", "_item$dailyWeather$2$", "_item$dailyWeather$2$2", "_item$dailyWeather3", "_item$dailyWeather3$", "_item$dailyWeather3$$", "_item$dailyWeather$3", "_item$dailyWeather$3$", "_item$dailyWeather$3$2", "_item$weather4", "_item$weather5", "_item$dailyWeather4", "_item$dailyWeather$4", "_item$dailyWeather5", "_item$dailyWeather$5", "timezone", "open", "src", "alt", "humidity", "visibility", "windspeedKmph", "sunHour", "astronomy", "sunset", "sunrise", "temp_C", "FeelsLikeC", "maxtempC", "mintempC", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/creativeapp/creativeApp-2.0_web/src/pages/world-time/TimeZoneConvert.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\n// import moment from \"moment\";\r\nimport moment from \"moment-timezone\";\r\nimport Select from \"react-select\";\r\nimport DynamicTimeCard from \"./DynamicTimeCard\";\r\nimport timezonebg from \"../../assets/images/timezonebg.png\";\r\n\r\nconst timeZones = {\r\n  Dubai: \"Asia/Dubai\",\r\n  Kabul: \"Asia/Kabul\",\r\n  Yerevan: \"Asia/Yerevan\",\r\n  Baku: \"Asia/Baku\",\r\n  Dhaka: \"Asia/Dhaka\",\r\n  Brunei: \"Asia/Brunei\",\r\n  Thimphu: \"Asia/Thimphu\",\r\n  Shanghai: \"Asia/Shanghai\",\r\n  Urumqi: \"Asia/Urumqi\",\r\n  Nicosia: \"Asia/Nicosia\",\r\n  Famagusta: \"Asia/Famagusta\",\r\n  Tbilisi: \"Asia/Tbilisi\",\r\n  Hong_Kong: \"Asia/Hong_Kong\",\r\n  Jakarta: \"Asia/Jakarta\",\r\n  Pontianak: \"Asia/Pontianak\",\r\n  Makassar: \"Asia/Makassar\",\r\n  Jayapura: \"Asia/Jayapura\",\r\n  Jerusalem: \"Asia/Jerusalem\",\r\n  Kolkata: \"Asia/Kolkata\",\r\n  Baghdad: \"Asia/Baghdad\",\r\n  Tehran: \"Asia/Tehran\",\r\n  Amman: \"Asia/Amman\",\r\n  Tokyo: \"Asia/Tokyo\",\r\n  Bishkek: \"Asia/Bishkek\",\r\n  Pyongyang: \"Asia/Pyongyang\",\r\n  Seoul: \"Asia/Seoul\",\r\n  Almaty: \"Asia/Almaty\",\r\n  Qyzylorda: \"Asia/Qyzylorda\",\r\n  Qostanay: \"Asia/Qostanay\",\r\n  Aqtobe: \"Asia/Aqtobe\",\r\n  Aqtau: \"Asia/Aqtau\",\r\n  Atyrau: \"Asia/Atyrau\",\r\n  Oral: \"Asia/Oral\",\r\n  Beirut: \"Asia/Beirut\",\r\n  Colombo: \"Asia/Colombo\",\r\n  Yangon: \"Asia/Yangon\",\r\n  Ulaanbaatar: \"Asia/Ulaanbaatar\",\r\n  Hovd: \"Asia/Hovd\",\r\n  Choibalsan: \"Asia/Choibalsan\",\r\n  Macau: \"Asia/Macau\",\r\n  Kuala_Lumpur: \"Asia/Kuala_Lumpur\",\r\n  Kuching: \"Asia/Kuching\",\r\n  Karachi: \"Asia/Karachi\",\r\n  Gaza: \"Asia/Gaza\",\r\n  Hebron: \"Asia/Hebron\",\r\n  Kathmandu: \"Asia/Kathmandu\",\r\n  Yekaterinburg: \"Asia/Yekaterinburg\",\r\n  Qatar: \"Asia/Qatar\",\r\n  Omsk: \"Asia/Omsk\",\r\n  Novosibirsk: \"Asia/Novosibirsk\",\r\n  Barnaul: \"Asia/Barnaul\",\r\n  Tomsk: \"Asia/Tomsk\",\r\n  Novokuznetsk: \"Asia/Novokuznetsk\",\r\n  Krasnoyarsk: \"Asia/Krasnoyarsk\",\r\n  Irkutsk: \"Asia/Irkutsk\",\r\n  Chita: \"Asia/Chita\",\r\n  Yakutsk: \"Asia/Yakutsk\",\r\n  Khandyga: \"Asia/Khandyga\",\r\n  Vladivostok: \"Asia/Vladivostok\",\r\n  Ust_Nera: \"Asia/Ust-Nera\",\r\n  Singapore: \"Asia/Singapore\",\r\n  Magadan: \"Asia/Magadan\",\r\n  Sakhalin: \"Asia/Sakhalin\",\r\n  Srednekolymsk: \"Asia/Srednekolymsk\",\r\n  Kamchatka: \"Asia/Kamchatka\",\r\n  Anadyr: \"Asia/Anadyr\",\r\n  Bangkok: \"Asia/Bangkok\",\r\n  Dushanbe: \"Asia/Dushanbe\",\r\n  Taipei: \"Asia/Taipei\",\r\n  Dili: \"Asia/Dili\",\r\n  Ashgabat: \"Asia/Ashgabat\",\r\n  Damascus: \"Asia/Damascus\",\r\n  Riyadh: \"Asia/Riyadh\",\r\n  Samarkand: \"Asia/Samarkand\",\r\n  Tashkent: \"Asia/Tashkent\",\r\n  Ho_Chi_Minh: \"Asia/Ho_Chi_Minh\",\r\n  Andorra: \"Europe/Andorra\",\r\n  Tirane: \"Europe/Tirane\",\r\n  Vienna: \"Europe/Vienna\",\r\n  Brussels: \"Europe/Brussels\",\r\n  Sofia: \"Europe/Sofia\",\r\n  Minsk: \"Europe/Minsk\",\r\n  Zurich: \"Europe/Zurich\",\r\n  Prague: \"Europe/Prague\",\r\n  Berlin: \"Europe/Berlin\",\r\n  Copenhagen: \"Europe/Copenhagen\",\r\n  Tallinn: \"Europe/Tallinn\",\r\n  Madrid: \"Europe/Madrid\",\r\n  Helsinki: \"Europe/Helsinki\",\r\n  Paris: \"Europe/Paris\",\r\n  London: \"Europe/London\",\r\n  Gibraltar: \"Europe/Gibraltar\",\r\n  Athens: \"Europe/Athens\",\r\n  Budapest: \"Europe/Budapest\",\r\n  Dublin: \"Europe/Dublin\",\r\n  Rome: \"Europe/Rome\",\r\n  Vilnius: \"Europe/Vilnius\",\r\n  Luxembourg: \"Europe/Luxembourg\",\r\n  Riga: \"Europe/Riga\",\r\n  Monaco: \"Europe/Monaco\",\r\n  Chisinau: \"Europe/Chisinau\",\r\n  Malta: \"Europe/Malta\",\r\n  Amsterdam: \"Europe/Amsterdam\",\r\n  Oslo: \"Europe/Oslo\",\r\n  Warsaw: \"Europe/Warsaw\",\r\n  Lisbon: \"Europe/Lisbon\",\r\n  Bucharest: \"Europe/Bucharest\",\r\n  Belgrade: \"Europe/Belgrade\",\r\n  Kaliningrad: \"Europe/Kaliningrad\",\r\n  Moscow: \"Europe/Moscow\",\r\n  Simferopol: \"Europe/Simferopol\",\r\n  Kirov: \"Europe/Kirov\",\r\n  Astrakhan: \"Europe/Astrakhan\",\r\n  Volgograd: \"Europe/Volgograd\",\r\n  Saratov: \"Europe/Saratov\",\r\n  Ulyanovsk: \"Europe/Ulyanovsk\",\r\n  Samara: \"Europe/Samara\",\r\n  Stockholm: \"Europe/Stockholm\",\r\n  Istanbul: \"Europe/Istanbul\",\r\n  Kiev: \"Europe/Kiev\",\r\n  Uzhgorod: \"Europe/Uzhgorod\",\r\n  Zaporozhye: \"Europe/Zaporozhye\",\r\n  Casey: \"Antarctica/Casey\",\r\n  Davis: \"Antarctica/Davis\",\r\n  DumontDUrville: \"Antarctica/DumontDUrville\",\r\n  Mawson: \"Antarctica/Mawson\",\r\n  Palmer: \"Antarctica/Palmer\",\r\n  Rothera: \"Antarctica/Rothera\",\r\n  Syowa: \"Antarctica/Syowa\",\r\n  Troll: \"Antarctica/Troll\",\r\n  Vostok: \"Antarctica/Vostok\",\r\n  Macquarie: \"Antarctica/Macquarie\",\r\n  Buenos_Aires: \"America/Argentina/Buenos_Aires\",\r\n  Cordoba: \"America/Argentina/Cordoba\",\r\n  Salta: \"America/Argentina/Salta\",\r\n  Jujuy: \"America/Argentina/Jujuy\",\r\n  Tucuman: \"America/Argentina/Tucuman\",\r\n  Catamarca: \"America/Argentina/Catamarca\",\r\n  La_Rioja: \"America/Argentina/La_Rioja\",\r\n  San_Juan: \"America/Argentina/San_Juan\",\r\n  Mendoza: \"America/Argentina/Mendoza\",\r\n  San_Luis: \"America/Argentina/San_Luis\",\r\n  Rio_Gallegos: \"America/Argentina/Rio_Gallegos\",\r\n  Ushuaia: \"America/Argentina/Ushuaia\",\r\n  Barbados: \"America/Barbados\",\r\n  La_Paz: \"America/La_Paz\",\r\n  Belem: \"America/Belem\",\r\n  Fortaleza: \"America/Fortaleza\",\r\n  Recife: \"America/Recife\",\r\n  Araguaina: \"America/Araguaina\",\r\n  Maceio: \"America/Maceio\",\r\n  Bahia: \"America/Bahia\",\r\n  Sao_Paulo: \"America/Sao_Paulo\",\r\n  Campo_Grande: \"America/Campo_Grande\",\r\n  Cuiaba: \"America/Cuiaba\",\r\n  Porto_Velho: \"America/Porto_Velho\",\r\n  Boa_Vista: \"America/Boa_Vista\",\r\n  Manaus: \"America/Manaus\",\r\n  Eirunepe: \"America/Eirunepe\",\r\n  Rio_Branco: \"America/Rio_Branco\",\r\n  Nassau: \"America/Nassau\",\r\n  Belize: \"America/Belize\",\r\n  St_Johns: \"America/St_Johns\",\r\n  Halifax: \"America/Halifax\",\r\n  Glace_Bay: \"America/Glace_Bay\",\r\n  Moncton: \"America/Moncton\",\r\n  Goose_Bay: \"America/Goose_Bay\",\r\n  Blanc_Sablon: \"America/Blanc-Sablon\",\r\n  Toronto: \"America/Toronto\",\r\n  Nipigon: \"America/Nipigon\",\r\n  Thunder_Bay: \"America/Thunder_Bay\",\r\n  Iqaluit: \"America/Iqaluit\",\r\n  Pangnirtung: \"America/Pangnirtung\",\r\n  Atikokan: \"America/Atikokan\",\r\n  Winnipeg: \"America/Winnipeg\",\r\n  Rainy_River: \"America/Rainy_River\",\r\n  Resolute: \"America/Resolute\",\r\n  Rankin_Inlet: \"America/Rankin_Inlet\",\r\n  Regina: \"America/Regina\",\r\n  Swift_Current: \"America/Swift_Current\",\r\n  Edmonton: \"America/Edmonton\",\r\n  Cambridge_Bay: \"America/Cambridge_Bay\",\r\n  Yellowknife: \"America/Yellowknife\",\r\n  Inuvik: \"America/Inuvik\",\r\n  Creston: \"America/Creston\",\r\n  Dawson_Creek: \"America/Dawson_Creek\",\r\n  Fort_Nelson: \"America/Fort_Nelson\",\r\n  Vancouver: \"America/Vancouver\",\r\n  Whitehorse: \"America/Whitehorse\",\r\n  Dawson: \"America/Dawson\",\r\n  Santiago: \"America/Santiago\",\r\n  Punta_Arenas: \"America/Punta_Arenas\",\r\n  Bogota: \"America/Bogota\",\r\n  Costa_Rica: \"America/Costa_Rica\",\r\n  Havana: \"America/Havana\",\r\n  Curacao: \"America/Curacao\",\r\n  Santo_Domingo: \"America/Santo_Domingo\",\r\n  Guayaquil: \"America/Guayaquil\",\r\n  Cayenne: \"America/Cayenne\",\r\n  Godthab: \"America/Godthab\",\r\n  Danmarkshavn: \"America/Danmarkshavn\",\r\n  Scoresbysund: \"America/Scoresbysund\",\r\n  Thule: \"America/Thule\",\r\n  Guatemala: \"America/Guatemala\",\r\n  Guyana: \"America/Guyana\",\r\n  Tegucigalpa: \"America/Tegucigalpa\",\r\n  Port_au_Prince: \"America/Port-au-Prince\",\r\n  Jamaica: \"America/Jamaica\",\r\n  Martinique: \"America/Martinique\",\r\n  Mexico_City: \"America/Mexico_City\",\r\n  Cancun: \"America/Cancun\",\r\n  Merida: \"America/Merida\",\r\n  Monterrey: \"America/Monterrey\",\r\n  Matamoros: \"America/Matamoros\",\r\n  Caracas: \"America/Caracas\",\r\n  Mazatlan: \"America/Mazatlan\",\r\n  Chihuahua: \"America/Chihuahua\",\r\n  Ojinaga: \"America/Ojinaga\",\r\n  Hermosillo: \"America/Hermosillo\",\r\n  Tijuana: \"America/Tijuana\",\r\n  Bahia_Banderas: \"America/Bahia_Banderas\",\r\n  Managua: \"America/Managua\",\r\n  Panama: \"America/Panama\",\r\n  Lima: \"America/Lima\",\r\n  Miquelon: \"America/Miquelon\",\r\n  Puerto_Rico: \"America/Puerto_Rico\",\r\n  El_Salvador: \"America/El_Salvador\",\r\n  Grand_Turk: \"America/Grand_Turk\",\r\n  Paramaribo: \"America/Paramaribo\",\r\n  Asuncion: \"America/Asuncion\",\r\n  Port_of_Spain: \"America/Port_of_Spain\",\r\n  New_York: \"America/New_York\",\r\n  New_Jersey: \"America/New_York\",\r\n  Detroit: \"America/Detroit\",\r\n  Louisville: \"America/Kentucky/Louisville\",\r\n  Monticello: \"America/Kentucky/Monticello\",\r\n  Indianapolis: \"America/Indiana/Indianapolis\",\r\n  Vincennes: \"America/Indiana/Vincennes\",\r\n  Winamac: \"America/Indiana/Winamac\",\r\n  Marengo: \"America/Indiana/Marengo\",\r\n  Petersburg: \"America/Indiana/Petersburg\",\r\n  Vevay: \"America/Indiana/Vevay\",\r\n  Tell_City: \"America/Indiana/Tell_City\",\r\n  Knox: \"America/Indiana/Knox\",\r\n  Chicago: \"America/Chicago\",\r\n  Menominee: \"America/Menominee\",\r\n  Denver: \"America/Denver\",\r\n  Boise: \"America/Boise\",\r\n  Phoenix: \"America/Phoenix\",\r\n  Center: \"America/North_Dakota/Center\",\r\n  New_Salem: \"America/North_Dakota/New_Salem\",\r\n  Beulah: \"America/North_Dakota/Beulah\",\r\n  Los_Angeles: \"America/Los_Angeles\",\r\n  Anchorage: \"America/Anchorage\",\r\n  Alaska: \"America/Anchorage\",\r\n  Juneau: \"America/Juneau\",\r\n  Sitka: \"America/Sitka\",\r\n  Metlakatla: \"America/Metlakatla\",\r\n  Yakutat: \"America/Yakutat\",\r\n  Nome: \"America/Nome\",\r\n  Adak: \"America/Adak\",\r\n  Montevideo: \"America/Montevideo\",\r\n  Pago_Pago: \"Pacific/Pago_Pago\",\r\n  Rarotonga: \"Pacific/Rarotonga\",\r\n  Easter: \"Pacific/Easter\",\r\n  Galapagos: \"Pacific/Galapagos\",\r\n  Fiji: \"Pacific/Fiji\",\r\n  Chuuk: \"Pacific/Chuuk\",\r\n  Pohnpei: \"Pacific/Pohnpei\",\r\n  Kosrae: \"Pacific/Kosrae\",\r\n  Guam: \"Pacific/Guam\",\r\n  Majuro: \"Pacific/Majuro\",\r\n  Kwajalein: \"Pacific/Kwajalein\",\r\n  Tarawa: \"Pacific/Tarawa\",\r\n  Enderbury: \"Pacific/Enderbury\",\r\n  Kiritimati: \"Pacific/Kiritimati\",\r\n  Noumea: \"Pacific/Noumea\",\r\n  Norfolk: \"Pacific/Norfolk\",\r\n  Nauru: \"Pacific/Nauru\",\r\n  Niue: \"Pacific/Niue\",\r\n  Auckland: \"Pacific/Auckland\",\r\n  Chatham: \"Pacific/Chatham\",\r\n  Tahiti: \"Pacific/Tahiti\",\r\n  Marquesas: \"Pacific/Marquesas\",\r\n  Gambier: \"Pacific/Gambier\",\r\n  Port_Moresby: \"Pacific/Port_Moresby\",\r\n  Bougainville: \"Pacific/Bougainville\",\r\n  Pitcairn: \"Pacific/Pitcairn\",\r\n  Palau: \"Pacific/Palau\",\r\n  Guadalcanal: \"Pacific/Guadalcanal\",\r\n  Fakaofo: \"Pacific/Fakaofo\",\r\n  Tongatapu: \"Pacific/Tongatapu\",\r\n  Funafuti: \"Pacific/Funafuti\",\r\n  Wake: \"Pacific/Wake\",\r\n  Honolulu: \"Pacific/Honolulu\",\r\n  Efate: \"Pacific/Efate\",\r\n  Wallis: \"Pacific/Wallis\",\r\n  Apia: \"Pacific/Apia\",\r\n  Lord_Howe: \"Australia/Lord_Howe\",\r\n  Hobart: \"Australia/Hobart\",\r\n  Currie: \"Australia/Currie\",\r\n  Melbourne: \"Australia/Melbourne\",\r\n  Sydney: \"Australia/Sydney\",\r\n  Broken_Hill: \"Australia/Broken_Hill\",\r\n  Brisbane: \"Australia/Brisbane\",\r\n  Lindeman: \"Australia/Lindeman\",\r\n  Adelaide: \"Australia/Adelaide\",\r\n  Darwin: \"Australia/Darwin\",\r\n  Perth: \"Australia/Perth\",\r\n  Eucla: \"Australia/Eucla\",\r\n  Abidjan: \"Africa/Abidjan\",\r\n  Algiers: \"Africa/Algiers\",\r\n  Cairo: \"Africa/Cairo\",\r\n  El_Aaiun: \"Africa/El_Aaiun\",\r\n  Ceuta: \"Africa/Ceuta\",\r\n  Accra: \"Africa/Accra\",\r\n  Bissau: \"Africa/Bissau\",\r\n  Nairobi: \"Africa/Nairobi\",\r\n  Monrovia: \"Africa/Monrovia\",\r\n  Tripoli: \"Africa/Tripoli\",\r\n  Casablanca: \"Africa/Casablanca\",\r\n  Maputo: \"Africa/Maputo\",\r\n  Windhoek: \"Africa/Windhoek\",\r\n  Lagos: \"Africa/Lagos\",\r\n  Khartoum: \"Africa/Khartoum\",\r\n  Juba: \"Africa/Juba\",\r\n  Sao_Tome: \"Africa/Sao_Tome\",\r\n  Ndjamena: \"Africa/Ndjamena\",\r\n  Tunis: \"Africa/Tunis\",\r\n  Johannesburg: \"Africa/Johannesburg\",\r\n  Azores: \"Atlantic/Azores\",\r\n  Bermuda: \"Atlantic/Bermuda\",\r\n  Madeira: \"Atlantic/Madeira\",\r\n  Cape_Verde: \"Atlantic/Cape_Verde\",\r\n  Canary: \"Atlantic/Canary\",\r\n  Stanley: \"Atlantic/Stanley\",\r\n  Faroe: \"Atlantic/Faroe\",\r\n  South_Georgia: \"Atlantic/South_Georgia\",\r\n  Reykjavik: \"Atlantic/Reykjavik\",\r\n  Cocos: \"Indian/Cocos\",\r\n  Christmas: \"Indian/Christmas\",\r\n  Chagos: \"Indian/Chagos\",\r\n  Mauritius: \"Indian/Mauritius\",\r\n  Maldives: \"Indian/Maldives\",\r\n  Mahe: \"Indian/Mahe\",\r\n  Reunion: \"Indian/Reunion\",\r\n  Kerguelen: \"Indian/Kerguelen\",\r\n};\r\n\r\nfunction TimeZoneConvert() {\r\n  const defaultDateFormat = \"dddd, LL\";\r\n  const defaultTimeFormat = \"hh:mm A\";\r\n\r\n  const background = {\r\n    backgroundImage: `url(${timezonebg})`,\r\n    backgroundPosition: 'center center',\r\n    backgroundSize: 'contain',\r\n    backgroundRepeat: 'no-repeat'\r\n  }\r\n\r\n  const queryParameters = new URLSearchParams(window.location.search);\r\n  const getDateTime = queryParameters.get(\"datetime\");\r\n  const getDefaultTimeZones = queryParameters.get(\"defaulttimezone\");\r\n  const getTimeZones = queryParameters.get(\"timezones\");\r\n\r\n  const [defaultTimeZone, setDefaultTimeZone] = useState({\r\n    label: \"Dhaka\",\r\n    value: \"Asia/Dhaka\",\r\n  }); // Store team data\r\n  const [currentDateTime, setCurrentDateTime] = useState(\r\n    moment().tz(defaultTimeZone[\"value\"])\r\n  ); // Store team data\r\n  const [error, setError] = useState(null); // Handle errors\r\n  const [loading, setLoading] = useState(true); // Loading state\r\n  // const [storeShowICityList, setStoreShowICityList] = useState({});\r\n  var storeShowICityList = {};\r\n  const [ipData, setIpData] = useState(false);\r\n  const [shareBtnText, setShareBtnText] = useState(\"Share\");\r\n  const [fromDateTime, setFromDateTime] = useState(\"\");\r\n  const [fromtimezone, setFromtimezone] = useState({\r\n    label: \"Dhaka\",\r\n    value: \"Asia/Dhaka\",\r\n  });\r\n  const [totimezone, setTotimezone] = useState({\r\n    label: \"New York\",\r\n    value: \"America/New_York\",\r\n  });\r\n  const [showCityList, setShowCityList] = useState([\r\n     {\r\n       label: \"Dhaka\",\r\n       value: \"Asia/Dhaka\",\r\n       isFixed: true,\r\n     },\r\n     {\r\n       label: \"New York\",\r\n       value: \"America/New_York\",\r\n       isFixed: true,\r\n     },\r\n     {\r\n       label: \"El Salvador\",\r\n       value: \"America/El_Salvador\",\r\n     },\r\n \r\n     {\r\n       label: \"London\",\r\n       value: \"Europe/London\",\r\n     },\r\n    \r\n     {\r\n       label: \"Sydney\",\r\n       value: \"Australia/Sydney\",\r\n     },\r\n    \r\n     {\r\n       label: \"Dubai\",\r\n       value: \"Asia/Dubai\",\r\n     },\r\n    \r\n     {\r\n       label: \"Tokyo\",\r\n       value: \"Asia/Tokyo\",\r\n     },\r\n   ]);\r\n\r\n  const datetimeToISOString = (datetime) => {\r\n    return new Date(datetime).valueOf();\r\n  };\r\n\r\n  const generateShareUrl = () => {\r\n    console.log(window.location)\r\n    let url = window.location.href.split(\"?\")[0];\r\n    let params = new URLSearchParams();\r\n\r\n    let isoDateTime = fromDateTime\r\n      ? new Date(fromDateTime).toISOString()\r\n      : new Date(currentDateTime).toISOString();\r\n    let dtmz = fromDateTime ? fromtimezone[\"label\"] : defaultTimeZone[\"label\"];\r\n    let timezones = showCityList.map((item) =>\r\n      item[\"label\"].replaceAll(\" \", \"_\")\r\n    );\r\n\r\n    params.append(\"datetime\", datetimeToISOString(isoDateTime));\r\n    params.append(\"defaulttimezone\", dtmz);\r\n    params.append(\"timezones\", timezones.join(\",\"));\r\n    url = window.location.origin+\"/time-zone-convert-share?\" + params.toString(\",\");\r\n    return url;\r\n  };\r\n\r\n  let singleLayoutClasses = \"\";\r\n\r\n  if (window.location.pathname === \"/time-zone-convert-share\") {\r\n    singleLayoutClasses = \" max-w-[1642px] mx-auto \";\r\n  }\r\n\r\n  // console.log(window.location.pathname)\r\n\r\n  useEffect(() => {\r\n    if (getDateTime) {\r\n      let getDateTimeParseData = new Date(Number(getDateTime)).toISOString();\r\n      setFromDateTime(getDateTimeParseData);\r\n    }\r\n\r\n    if (getDefaultTimeZones) {\r\n      setDefaultTimeZone({\r\n        label: getDefaultTimeZones,\r\n        value: timeZones[getDefaultTimeZones],\r\n      });\r\n      setFromtimezone({\r\n        label: getDefaultTimeZones,\r\n        value: timeZones[getDefaultTimeZones],\r\n      });\r\n    }\r\n\r\n    if (getTimeZones) {\r\n      let timezones = getTimeZones.split(\",\");\r\n      let timezonesData = timezones.map((item) => {\r\n        return { label: item, value: timeZones[item] };\r\n      });\r\n      setShowCityList(timezonesData);\r\n    }\r\n  }, [getDateTime, getDefaultTimeZones, getTimeZones]);\r\n\r\n  const convertDateTime = (totimezonevalue = \"\") => {\r\n    // let fromtimezonelabel = fromtimezone.label;\r\n    let fromtimezonevalue = fromtimezone.value || defaultTimeZone[\"value\"];\r\n    // let totimezonevalue = totimezone.value;\r\n    var fromConvertDateTime = moment.tz(fromDateTime, fromtimezonevalue);\r\n    var retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\r\n\r\n    if (moment(retDateTime).isValid()) {\r\n      return retDateTime;\r\n    }\r\n\r\n    fromConvertDateTime = moment.tz(currentDateTime, fromtimezonevalue);\r\n    retDateTime = fromConvertDateTime.clone().tz(totimezonevalue);\r\n\r\n    if (moment(retDateTime).isValid()) {\r\n      return retDateTime;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  const getTimeZoneSelectOptions = () => {\r\n    let data = [];\r\n    Object.keys(timeZones)\r\n      .sort()\r\n      .map((label) =>\r\n        data.push({\r\n          label: `${label.replaceAll(\"_\", \" \")}`,\r\n          value: timeZones[label],\r\n        })\r\n      );\r\n\r\n    return data;\r\n  };\r\n\r\n  const getLabelByTimezone = (label = \"\") => {\r\n    return Object.keys(timeZones).find((key) => timeZones[key] === label) || \"\";\r\n  };\r\n\r\n  const calculateDifference = (timezone1, timezone2) => {\r\n    const now = moment().utc(); \r\n    const time1 = now.clone().tz(timezone1);\r\n    const time2 = now.clone().tz(timezone2);\r\n    const diffInHours = (time1._offset - time2._offset) / 60;\r\n\r\n    let fromCityName = moment(fromDateTime).isValid()? fromtimezone[\"label\"].replaceAll(\"_\", \" \") : defaultTimeZone[\"label\"].replaceAll(\"_\", \" \");\r\n\r\n    let returnText = ``;\r\n\r\n    if(diffInHours < 0){\r\n      returnText += `${diffInHours*-1} Hours behind from ${fromCityName}`;\r\n    }else if(diffInHours > 0){\r\n      returnText += `${diffInHours} Hours ahead of ${fromCityName}`;\r\n    }else {\r\n      returnText += `Same time as ${fromCityName}`;\r\n    }\r\n\r\n\r\n    return returnText;\r\n  };\r\n\r\n\r\n  const getSingleCityData = async (item, key) => {\r\n    try {\r\n      let city = item.label.replaceAll(\"_\", \" \");\r\n      let flagResponseData = {};\r\n      let data = {};\r\n      console.log(\"city\", city)\r\n  \r\n      if (city && storeShowICityList[city]) {\r\n        flagResponseData = storeShowICityList[city];\r\n      } else {\r\n        // First fetch weather data to get the country name\r\n        const weatherResponse = await fetch(`https://wttr.in/${city}?format=j1`);\r\n        data = await weatherResponse.json();\r\n\r\n        // Get country name from weather data\r\n        const country = data?.nearest_area?.[0]?.country?.[0]?.value;\r\n        console.log(\"Country extracted from weather data:\", country);\r\n\r\n        // Then fetch country data using the actual country name\r\n        if (country) {\r\n          const flagResponse = await fetch(`https://restcountries.com/v3.1/name/${country}`);\r\n          flagResponseData = await flagResponse.json();\r\n          console.log(\"Country data fetched:\", flagResponseData);\r\n        }\r\n      }\r\n  \r\n      let retData = {\r\n        ...item,\r\n        city,\r\n        country: data?.nearest_area?.[0]?.country?.[0]?.value || \"\",\r\n        state: data?.nearest_area?.[0]?.region?.[0]?.value || \"\",\r\n        flag:\r\n          flagResponseData?.[0]?.flags?.svg ||\r\n          flagResponseData?.[0]?.flags?.png ||\r\n          \"\",\r\n        capital: flagResponseData?.[0]?.capital?.join(\", \") || \"\",\r\n        googleMapUrl: flagResponseData?.[0]?.maps?.googleMaps || \"\",\r\n        population: flagResponseData?.[0]?.population || \"\",\r\n        region: flagResponseData?.[0]?.region || \"\",\r\n        startOfWeek: flagResponseData?.[0]?.startOfWeek || \"\",\r\n        // timezones: flagResponseData[0].timezones.join(\", \") || \"\",\r\n        timezones: flagResponseData?.[0]?.timezones?.[0] || \"\",\r\n        languages:\r\n          flagResponseData?.[0]?.languages ? Object.values(flagResponseData[0].languages).join(\", \") : \"\",\r\n        weather: data?.current_condition?.[0] || \"\",\r\n        dailyWeather: data?.weather || \"\",\r\n        condition: data?.current_condition?.[0]?.weatherDesc?.[0]?.value || \"\",\r\n        weatherIcon: data?.current_condition?.[0]?.weatherIconUrl?.[0]?.value || \"\",\r\n        fullWeaterData: data,\r\n        fullCountryDetails: flagResponseData,\r\n      };\r\n  \r\n      // Update the showCityList state with the new data\r\n      setShowCityList(prevList => {\r\n        const newArray = [...prevList];\r\n        newArray[key] = retData;\r\n        return newArray;\r\n      });\r\n\r\n      return retData;\r\n    } catch (error) {\r\n      console.error(\"Error fetching city data:\", error);\r\n\r\n      // Return basic item data if API fails\r\n      const fallbackData = {\r\n        ...item,\r\n        city: item.label.replaceAll(\"_\", \" \"),\r\n        country: \"Unknown\",\r\n        state: \"Unknown\",\r\n        flag: \"\",\r\n        capital: \"Unknown\",\r\n        googleMapUrl: \"\",\r\n        population: \"\",\r\n        region: \"Unknown\",\r\n        startOfWeek: \"Unknown\",\r\n        timezones: \"\",\r\n        languages: \"Unknown\",\r\n        weather: {},\r\n        dailyWeather: [],\r\n        condition: \"Unknown\",\r\n        weatherIcon: \"\",\r\n        fullWeaterData: {},\r\n        fullCountryDetails: {},\r\n      };\r\n\r\n      // Update the showCityList with fallback data\r\n      setShowCityList(prevList => {\r\n        const newArray = [...prevList];\r\n        newArray[key] = fallbackData;\r\n        return newArray;\r\n      });\r\n\r\n      return fallbackData;\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const fetchWeather = async () => {\r\n    try {\r\n      const results = await Promise.all(\r\n        showCityList.map(async (item, key) => {\r\n          try {\r\n            let city = item.label.replaceAll(\"_\", \" \");\r\n            let flagResponseData = {};\r\n            let data = {};\r\n\r\n            if (city && storeShowICityList[city]) {\r\n              flagResponseData = storeShowICityList[city];\r\n            } else {\r\n              const response = await fetch(`https://wttr.in/${city}?format=j1`);\r\n              data = await response.json();\r\n\r\n              let country = data?.nearest_area?.[0]?.country?.[0]?.value;\r\n\r\n              if (country) {\r\n                const flagResponse = await fetch(\r\n                  `https://restcountries.com/v3.1/name/${country}`\r\n                );\r\n                flagResponseData = await flagResponse.json();\r\n              }\r\n            }\r\n\r\n        let retData = {\r\n          ...item,\r\n          city,\r\n          country: data?.nearest_area?.[0]?.country?.[0]?.value || \"\",\r\n          state: data?.nearest_area?.[0]?.region?.[0]?.value || \"\",\r\n          flag:\r\n            flagResponseData?.[0]?.flags?.svg ||\r\n            flagResponseData?.[0]?.flags?.png ||\r\n            \"\",\r\n          capital: flagResponseData?.[0]?.capital?.join(\", \") || \"\",\r\n          googleMapUrl: flagResponseData?.[0]?.maps?.googleMaps || \"\",\r\n          population: flagResponseData?.[0]?.population || \"\",\r\n          region: flagResponseData?.[0]?.region || \"\",\r\n          startOfWeek: flagResponseData?.[0]?.startOfWeek || \"\",\r\n          // timezones: flagResponseData[0].timezones.join(\", \") || \"\",\r\n          timezones: flagResponseData?.[0]?.timezones?.[0] || \"\",\r\n          languages:\r\n            flagResponseData?.[0]?.languages ? Object.values(flagResponseData[0].languages).join(\", \") : \"\",\r\n          weather: data?.current_condition?.[0] || \"\",\r\n          dailyWeather: data?.weather || \"\",\r\n          condition: data?.current_condition?.[0]?.weatherDesc?.[0]?.value || \"\",\r\n          weatherIcon: data?.current_condition?.[0]?.weatherIconUrl?.[0]?.value || \"\",\r\n          fullWeaterData: data,\r\n          fullCountryDetails: flagResponseData,\r\n        };\r\n\r\n            return retData;\r\n          } catch (error) {\r\n            console.error(`Error fetching data for ${item.label}:`, error);\r\n            // Return basic item data if API fails\r\n            return {\r\n              ...item,\r\n              city: item.label.replaceAll(\"_\", \" \"),\r\n              country: \"Unknown\",\r\n              state: \"Unknown\",\r\n              flag: \"\",\r\n              capital: \"Unknown\",\r\n              googleMapUrl: \"\",\r\n              population: \"\",\r\n              region: \"Unknown\",\r\n              startOfWeek: \"Unknown\",\r\n              timezones: \"\",\r\n              languages: \"Unknown\",\r\n              weather: {},\r\n              dailyWeather: [],\r\n              condition: \"Unknown\",\r\n              weatherIcon: \"\",\r\n              fullWeaterData: {},\r\n              fullCountryDetails: {},\r\n            };\r\n          }\r\n\r\n          // return storeShowICityList[item];\r\n        })\r\n      );\r\n\r\n      // results.map((item) => {\r\n      //   storeShowICityList[item.label] = { ...item };\r\n      // });\r\n\r\n      // console.log(storeShowICityList)\r\n\r\n      setLoading(false);\r\n      setShowCityList(results);\r\n    } catch (error) {\r\n      console.error(\"Error in fetchWeather:\", error);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchWeather();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n     \r\n        <div\r\n          className={\r\n            \"bg-white dark:bg-gray-900 rounded-xl p-4 \" + singleLayoutClasses\r\n          }\r\n          \r\n        >\r\n          <div className=\"flex justify-start items-start flex-row mb-[20px]\">\r\n            <div className=\"text-left w-1/2 \">\r\n              <h4 className=\"text-xl font-medium \">Time Zone Converter</h4>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-start items-start flex-row mb-[50px]\">\r\n            {/* Current Date Time */}\r\n            <div className=\"border border-gray-200 rounded-lg  px-6 py-3 me-[10px] w-4/12 h-[210px] bg-[#0b333f] text-[#fff]\">\r\n              <DynamicTimeCard />\r\n            </div>\r\n\r\n            <div className=\"border border-gray-200 rounded-lg  mx-[10px] px-6 py-3 w-4/12 h-[210px] bg-[#DFECF1]\">\r\n              <form className=\"text-left\">\r\n                <div className=\" justify-start items-start\">\r\n                  <div className=\"mb-4 w-full sm:w-full\">\r\n                    <label\r\n                      htmlFor=\"start-time\"\r\n                      className=\"block mb-2 text-sm font-medium text-gray-900 dark:text-white\"\r\n                    >\r\n                      Select Date and Time\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"datetime-local\"\r\n                        id=\"shiftStart\"\r\n                        disabled={loading}\r\n                        value={(fromDateTime || \"\").toString().substring(0, 16)}\r\n                        onChange={(e) => {\r\n                          setFromDateTime(e.target.value);\r\n                        }}\r\n                        required\r\n                        className=\"bg-gray-50 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <div className=\"mb-4 w-1/2 sm:w-full\">\r\n                      <label\r\n                        htmlFor=\"team\"\r\n                        className=\"block text-sm font-medium text-gray-700 pb-4\"\r\n                      >\r\n                        Select City\r\n                      </label>\r\n                      <Select\r\n                        isClearable={false}\r\n                        isSearchable={true}\r\n                        isDisabled={loading}\r\n                        isMulti={false}\r\n                        required={false}\r\n                        placeholder=\"Choose timezone\"\r\n                        name=\"fromtimezone\"\r\n                        value={fromtimezone}\r\n                        options={getTimeZoneSelectOptions()}\r\n                        onChange={(item) => {\r\n                          setFromtimezone(item);\r\n                        }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </form>\r\n            </div>\r\n            <div className=\"border border-gray-200   rounded-lg  ms-[10px] px-6 py-3 w-4/12 h-[210px]  bg-[#DFECF1] text-[#000]\">\r\n              {totimezone.value && convertDateTime(totimezone.value) && (\r\n                <>\r\n                  <div className=\"text-[18px] font-bold\">\r\n                    {moment(fromDateTime).isValid() &&\r\n                      fromtimezone[\"label\"].replaceAll(\"_\", \" \")}\r\n                    {!moment(fromDateTime).isValid() &&\r\n                      defaultTimeZone[\"label\"].replaceAll(\"_\", \" \")}\r\n                    <br />\r\n                    {moment(fromDateTime).isValid() && fromtimezone[\"value\"]}\r\n                    {!moment(fromDateTime).isValid() &&\r\n                      defaultTimeZone[\"value\"]}\r\n                  </div>\r\n\r\n                  <h1 className=\"text-[64px] font-bold\">\r\n                    {moment(fromDateTime).isValid() &&\r\n                      moment(fromDateTime).format(defaultTimeFormat)}\r\n                    {!moment(fromDateTime).isValid() &&\r\n                      moment(currentDateTime).isValid() &&\r\n                      moment(currentDateTime).format(defaultTimeFormat)}\r\n                  </h1>\r\n                  <div className=\"text-[20px]  \">\r\n                    {moment(fromDateTime).isValid() &&\r\n                      moment(fromDateTime).format(defaultDateFormat)}\r\n                    {!moment(fromDateTime).isValid() &&\r\n                      moment(currentDateTime).isValid() &&\r\n                      moment(currentDateTime).format(defaultDateFormat)}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"w-full flex justify-start items-start flex-wrap  mb-10\">\r\n            <div className=\"text-start w-full \">\r\n              <div className=\"flex items-center rounded-lg gap-3 bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600\">\r\n                <div className=\"grid shrink-0 grid-cols-1 focus-within:relative w-11/12 \">\r\n                  {/* <label>Add your city</label> */}\r\n                  <Select\r\n                    closeMenuOnSelect={false}\r\n                    isClearable={true}\r\n                    isSearchable={true}\r\n                    isDisabled={loading}\r\n                    isMulti={true}\r\n                    required={false}\r\n                    placeholder=\"Choose your city\"\r\n                    name=\"showCityList\"\r\n                    value={showCityList}\r\n                    options={getTimeZoneSelectOptions()}\r\n                    onChange={(item) => {\r\n                      setShowCityList(item);\r\n\r\n                      // Fetch data for newly added cities\r\n                      if (item && item.length > 0) {\r\n                        item.forEach((city, key) => {\r\n                          if (!city.country && city.label) {\r\n                            // Fetch data for cities that don't have country data yet\r\n                            setTimeout(() => getSingleCityData(city, key), 100);\r\n                          }\r\n                        });\r\n                      }\r\n                    }}\r\n                  />\r\n                </div>\r\n                <button\r\n                  disabled={loading || (shareBtnText === 'Copied')}\r\n                  onClick={() => {\r\n                    navigator.clipboard.writeText(generateShareUrl());\r\n                    setShareBtnText(\"Copied\");\r\n                    setTimeout(function() { setShareBtnText(\"Share\"); }, 5000);\r\n                  }}\r\n                  target=\"_blank\"\r\n                  className=\"block min-w-0 rounded-lg text-center grow py-1.5 pr-3 pl-1 border border-gray-200 bg-[#076d92] hover:bg-[#0B333F] text-base text-[#fff] placeholder:text-gray-400 focus:outline-none sm:text-sm/6\"\r\n                >\r\n                  {shareBtnText}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {loading && <>Loding....</>}\r\n          {!loading && (\r\n          <div className=\"w-full flex justify-start items-start flex-wrap  \" style={background}>\r\n            {/* Contacts Navigation */}\r\n\r\n            {/* All Contacts timeZone */}\r\n\r\n            {/* {Object.keys(showCityList).sort().map((label) => { */}\r\n            {showCityList.length > 0 &&\r\n              showCityList.map((item, key) => {\r\n\r\n                // If city data is not loaded yet, fetch it but still show the city\r\n                if (!item.country) {\r\n                  getSingleCityData(item, key);\r\n                };\r\n\r\n                // let label = key, timezone = timeZoneList[key];\r\n                let label = item[\"label\"];\r\n                let timezone = item[\"value\"];\r\n\r\n                return (\r\n                  <div\r\n                    key={\"timezone-\" + timezone+label}\r\n                    className=\"  w-full px-[20px] \"\r\n                  >\r\n                    <div className=\"border-b-2 border-gray-400 py-4 hover:bg-[#DFECF1]  text-sm  text-start flex capitalize align-middle\">\r\n                      <div\r\n                        className=\"w-[200px] flex-col justify-center text-center items-center\"\r\n                        onClick={() =>\r\n                          item?.googleMapUrl &&\r\n                          window.open(item.googleMapUrl, \"_blank\")\r\n                        }\r\n                      >\r\n                        <div className=\"flex items-center justify-center text-center \">\r\n                          {item.flag ? (\r\n                            <img\r\n                              src={item.flag}\r\n                              alt={item.country || label}\r\n                              className=\"w-[100%] max-w-[65px] h-auto shadow-lg border border-1 mb-1\"\r\n                            />\r\n                          ) : (\r\n                            <div className=\"w-[65px] h-[45px] bg-gray-200 border border-1 mb-1 flex items-center justify-center text-xs text-gray-500\">\r\n                              Loading...\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        <div className=\" text-[14px] mb-1\">{item.country || \"Loading...\"}</div>\r\n                        <div className=\"font-bold text-xl\">\r\n                          {label.replaceAll(\"_\", \" \")\r\n                            ? label.replaceAll(\"_\", \" \")\r\n                            : label}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"w-2/12\">\r\n                        {item.startOfWeek ? (\r\n                          <div className=\"truncate\">Start Of Week: {item.startOfWeek}</div>\r\n                        ) : (\r\n                          <div className=\"truncate text-gray-400\">Start Of Week: Loading...</div>\r\n                        )}\r\n                        {item.capital ? (\r\n                          <div className=\"truncate\">Capital: {item.capital}</div>\r\n                        ) : (\r\n                          <div className=\"truncate text-gray-400\">Capital: Loading...</div>\r\n                        )}\r\n                        {item.languages ? (\r\n                          <div className=\"truncate\">Language: {item.languages}</div>\r\n                        ) : (\r\n                          <div className=\"truncate text-gray-400\">Language: Loading...</div>\r\n                        )}\r\n                        {item.state ? (\r\n                          <div className=\"truncate\">State/City: {item.state}</div>\r\n                        ) : (\r\n                          <div className=\"truncate text-gray-400\">State/City: Loading...</div>\r\n                        )}\r\n                        {/* {item.region && <div>Region: {item.region}</div>} */}\r\n                      </div>\r\n                      <div className=\"w-2/12 \">\r\n                        {item.weather?.humidity ? (\r\n                          <div>Humidity: {item.weather.humidity}%</div>\r\n                        ) : (\r\n                          <div className=\"text-gray-400\">Humidity: Loading...</div>\r\n                        )}\r\n                        {item.weather?.visibility ? (\r\n                          <div>Visibility: {item.weather.visibility}km</div>\r\n                        ) : (\r\n                          <div className=\"text-gray-400\">Visibility: Loading...</div>\r\n                        )}\r\n                        {item.weather?.windspeedKmph ? (\r\n                          <div>Wind Speed: {item.weather.windspeedKmph}km/h</div>\r\n                        ) : (\r\n                          <div className=\"text-gray-400\">Wind Speed: Loading...</div>\r\n                        )}\r\n                        {item?.condition ? (\r\n                          <div>Condition: {item.condition}</div>\r\n                        ) : (\r\n                          <div className=\"text-gray-400\">Condition: Loading...</div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"w-2/12 mb-1\">\r\n                        {item?.dailyWeather?.[0] && (\r\n                          <div className=\" \">\r\n                            Sun Hour: {item.dailyWeather[0]?.sunHour}h\r\n                          </div>\r\n                        )}\r\n                        {item?.dailyWeather?.[0]?.astronomy?.[0] && (\r\n                          <div className=\" \">\r\n                            Today Sunset:{\" \"}\r\n                            {item.dailyWeather[0]?.astronomy?.[0]?.sunset}\r\n                          </div>\r\n                        )}\r\n                        {item?.dailyWeather?.[1]?.astronomy?.[0] && (\r\n                          <div className=\" \">\r\n                            Tomorrow Sunrise:{\" \"}\r\n                            {item.dailyWeather[1]?.astronomy?.[0]?.sunrise}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"w-1/12 mb-1 flex-col align-middle text-end\">\r\n                        {item?.weather?.temp_C && (\r\n                          <div className=\"text-4xl font-bold pb-2 pe-3\">\r\n                            {item.weather.temp_C}\r\n                            <sup>°C</sup>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <div className=\"w-1/12 mb-1 flex-col align-middle text-start\">\r\n                        <div>\r\n                          Feels like: {item?.weather?.FeelsLikeC}\r\n                          <sup>°C</sup>\r\n                        </div>\r\n                        {item?.dailyWeather?.[0] && (\r\n                          <div className=\" \">\r\n                            Max Temp: {item?.dailyWeather[0]?.maxtempC}\r\n                            <sup>°C</sup>\r\n                          </div>\r\n                        )}\r\n                        {item?.dailyWeather?.[0] && (\r\n                          <div className=\" \">\r\n                            Min Temp: {item?.dailyWeather[0]?.mintempC}\r\n                            <sup>°C</sup>\r\n                          </div>\r\n                        )}\r\n                        {/* {item.dailyWeather[0] && (<div className=\" \">Avg. Temp: {item.dailyWeather[0].avgtempC}<sup>°C</sup></div>)} */}\r\n                      </div>\r\n                      <div className=\"w-2/12 flex-col text-end align-middle\">\r\n                        <div className=\" text-4xl font-bold \">\r\n                          {convertDateTime(timezone) &&\r\n                            convertDateTime(timezone).format(defaultTimeFormat)}\r\n                        </div>\r\n\r\n                        <div className=\" text-lg \">\r\n                          {convertDateTime(timezone) &&\r\n                            convertDateTime(timezone).format(\"dddd, LL\")}\r\n                          \r\n                        </div>\r\n                        {item?.timezones && (\r\n                            <div className=\" text-xm \">\r\n                              {calculateDifference(item.value, defaultTimeZone[\"value\"])}<br/> \r\n                              Timezone: {convertDateTime(timezone) && convertDateTime(timezone).format(\"z\")}\r\n                            </div>\r\n                          )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n          )}\r\n        </div>\r\n      \r\n    </>\r\n  );\r\n}\r\n\r\nexport default TimeZoneConvert;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD;AACA,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,UAAU,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,MAAM,EAAE,aAAa;EACrBC,WAAW,EAAE,kBAAkB;EAC/BC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,iBAAiB;EAC7BC,KAAK,EAAE,YAAY;EACnBC,YAAY,EAAE,mBAAmB;EACjCC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,cAAc;EACvBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,aAAa,EAAE,oBAAoB;EACnCC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,YAAY,EAAE,mBAAmB;EACjCC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,kBAAkB;EAC/BC,QAAQ,EAAE,eAAe;EACzBC,SAAS,EAAE,gBAAgB;EAC3BC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,aAAa,EAAE,oBAAoB;EACnCC,SAAS,EAAE,gBAAgB;EAC3BC,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE,aAAa;EACrBC,SAAS,EAAE,gBAAgB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,kBAAkB;EAC/BC,OAAO,EAAE,gBAAgB;EACzBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,UAAU,EAAE,mBAAmB;EAC/BC,OAAO,EAAE,gBAAgB;EACzBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,gBAAgB;EACzBC,UAAU,EAAE,mBAAmB;EAC/BC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,eAAe;EACvBC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,oBAAoB;EACjCC,MAAM,EAAE,eAAe;EACvBC,UAAU,EAAE,mBAAmB;EAC/BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,SAAS,EAAE,kBAAkB;EAC7BC,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,UAAU,EAAE,mBAAmB;EAC/BC,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,kBAAkB;EACzBC,cAAc,EAAE,2BAA2B;EAC3CC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE,mBAAmB;EAC3BC,OAAO,EAAE,oBAAoB;EAC7BC,KAAK,EAAE,kBAAkB;EACzBC,KAAK,EAAE,kBAAkB;EACzBC,MAAM,EAAE,mBAAmB;EAC3BC,SAAS,EAAE,sBAAsB;EACjCC,YAAY,EAAE,gCAAgC;EAC9CC,OAAO,EAAE,2BAA2B;EACpCC,KAAK,EAAE,yBAAyB;EAChCC,KAAK,EAAE,yBAAyB;EAChCC,OAAO,EAAE,2BAA2B;EACpCC,SAAS,EAAE,6BAA6B;EACxCC,QAAQ,EAAE,4BAA4B;EACtCC,QAAQ,EAAE,4BAA4B;EACtCC,OAAO,EAAE,2BAA2B;EACpCC,QAAQ,EAAE,4BAA4B;EACtCC,YAAY,EAAE,gCAAgC;EAC9CC,OAAO,EAAE,2BAA2B;EACpCC,QAAQ,EAAE,kBAAkB;EAC5BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,YAAY,EAAE,sBAAsB;EACpCC,OAAO,EAAE,iBAAiB;EAC1BC,OAAO,EAAE,iBAAiB;EAC1BC,WAAW,EAAE,qBAAqB;EAClCC,OAAO,EAAE,iBAAiB;EAC1BC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,kBAAkB;EAC5BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,aAAa,EAAE,uBAAuB;EACtCC,QAAQ,EAAE,kBAAkB;EAC5BC,aAAa,EAAE,uBAAuB;EACtCC,WAAW,EAAE,qBAAqB;EAClCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,QAAQ,EAAE,kBAAkB;EAC5BC,YAAY,EAAE,sBAAsB;EACpCC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,aAAa,EAAE,uBAAuB;EACtCC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,YAAY,EAAE,sBAAsB;EACpCC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,cAAc,EAAE,wBAAwB;EACxCC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,oBAAoB;EAChCC,WAAW,EAAE,qBAAqB;EAClCC,MAAM,EAAE,gBAAgB;EACxBC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,QAAQ,EAAE,kBAAkB;EAC5BC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,oBAAoB;EAChCC,OAAO,EAAE,iBAAiB;EAC1BC,cAAc,EAAE,wBAAwB;EACxCC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,WAAW,EAAE,qBAAqB;EAClCC,UAAU,EAAE,oBAAoB;EAChCC,UAAU,EAAE,oBAAoB;EAChCC,QAAQ,EAAE,kBAAkB;EAC5BC,aAAa,EAAE,uBAAuB;EACtCC,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,kBAAkB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,UAAU,EAAE,6BAA6B;EACzCC,UAAU,EAAE,6BAA6B;EACzCC,YAAY,EAAE,8BAA8B;EAC5CC,SAAS,EAAE,2BAA2B;EACtCC,OAAO,EAAE,yBAAyB;EAClCC,OAAO,EAAE,yBAAyB;EAClCC,UAAU,EAAE,4BAA4B;EACxCC,KAAK,EAAE,uBAAuB;EAC9BC,SAAS,EAAE,2BAA2B;EACtCC,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,6BAA6B;EACrCC,SAAS,EAAE,gCAAgC;EAC3CC,MAAM,EAAE,6BAA6B;EACrCC,WAAW,EAAE,qBAAqB;EAClCC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,mBAAmB;EAC3BC,MAAM,EAAE,gBAAgB;EACxBC,KAAK,EAAE,eAAe;EACtBC,UAAU,EAAE,oBAAoB;EAChCC,OAAO,EAAE,iBAAiB;EAC1BC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,oBAAoB;EAChCC,SAAS,EAAE,mBAAmB;EAC9BC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,eAAe;EACtBC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,iBAAiB;EAC1BC,KAAK,EAAE,eAAe;EACtBC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,OAAO,EAAE,iBAAiB;EAC1BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,mBAAmB;EAC9BC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,sBAAsB;EACpCC,YAAY,EAAE,sBAAsB;EACpCC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,eAAe;EACtBC,WAAW,EAAE,qBAAqB;EAClCC,OAAO,EAAE,iBAAiB;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,QAAQ,EAAE,kBAAkB;EAC5BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,eAAe;EACtBC,MAAM,EAAE,gBAAgB;EACxBC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAE,qBAAqB;EAChCC,MAAM,EAAE,kBAAkB;EAC1BC,MAAM,EAAE,kBAAkB;EAC1BC,SAAS,EAAE,qBAAqB;EAChCC,MAAM,EAAE,kBAAkB;EAC1BC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,oBAAoB;EAC9BC,MAAM,EAAE,kBAAkB;EAC1BC,KAAK,EAAE,iBAAiB;EACxBC,KAAK,EAAE,iBAAiB;EACxBC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE,gBAAgB;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,KAAK,EAAE,cAAc;EACrBC,MAAM,EAAE,eAAe;EACvBC,OAAO,EAAE,gBAAgB;EACzBC,QAAQ,EAAE,iBAAiB;EAC3BC,OAAO,EAAE,gBAAgB;EACzBC,UAAU,EAAE,mBAAmB;EAC/BC,MAAM,EAAE,eAAe;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,KAAK,EAAE,cAAc;EACrBC,YAAY,EAAE,qBAAqB;EACnCC,MAAM,EAAE,iBAAiB;EACzBC,OAAO,EAAE,kBAAkB;EAC3BC,OAAO,EAAE,kBAAkB;EAC3BC,UAAU,EAAE,qBAAqB;EACjCC,MAAM,EAAE,iBAAiB;EACzBC,OAAO,EAAE,kBAAkB;EAC3BC,KAAK,EAAE,gBAAgB;EACvBC,aAAa,EAAE,wBAAwB;EACvCC,SAAS,EAAE,oBAAoB;EAC/BC,KAAK,EAAE,cAAc;EACrBC,SAAS,EAAE,kBAAkB;EAC7BC,MAAM,EAAE,eAAe;EACvBC,SAAS,EAAE,kBAAkB;EAC7BC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,gBAAgB;EACzBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,iBAAiB,GAAG,UAAU;EACpC,MAAMC,iBAAiB,GAAG,SAAS;EAEnC,MAAMC,UAAU,GAAG;IACjBC,eAAe,EAAE,OAAOtW,UAAU,GAAG;IACrCuW,kBAAkB,EAAE,eAAe;IACnCC,cAAc,EAAE,SAAS;IACzBC,gBAAgB,EAAE;EACpB,CAAC;EAED,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EACnE,MAAMC,WAAW,GAAGL,eAAe,CAACM,GAAG,CAAC,UAAU,CAAC;EACnD,MAAMC,mBAAmB,GAAGP,eAAe,CAACM,GAAG,CAAC,iBAAiB,CAAC;EAClE,MAAME,YAAY,GAAGR,eAAe,CAACM,GAAG,CAAC,WAAW,CAAC;EAErD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGxX,QAAQ,CAAC;IACrDyX,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,CAAC;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5X,QAAQ,CACpDC,MAAM,CAAC,CAAC,CAAC4X,EAAE,CAACN,eAAe,CAAC,OAAO,CAAC,CACtC,CAAC,CAAC,CAAC;EACH,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAG/X,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACgY,OAAO,EAAEC,UAAU,CAAC,GAAGjY,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9C;EACA,IAAIkY,kBAAkB,GAAG,CAAC,CAAC;EAC3B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGpY,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqY,YAAY,EAAEC,eAAe,CAAC,GAAGtY,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAACuY,YAAY,EAAEC,eAAe,CAAC,GAAGxY,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyY,YAAY,EAAEC,eAAe,CAAC,GAAG1Y,QAAQ,CAAC;IAC/CyX,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAG5Y,QAAQ,CAAC;IAC3CyX,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAG9Y,QAAQ,CAAC,CAC9C;IACEyX,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,YAAY;IACnBqB,OAAO,EAAE;EACX,CAAC,EACD;IACEtB,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,kBAAkB;IACzBqB,OAAO,EAAE;EACX,CAAC,EACD;IACEtB,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EAED;IACED,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,CACF,CAAC;EAEH,MAAMsB,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,OAAO,IAAIC,IAAI,CAACD,QAAQ,CAAC,CAACE,OAAO,CAAC,CAAC;EACrC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BC,OAAO,CAACC,GAAG,CAACtC,MAAM,CAACC,QAAQ,CAAC;IAC5B,IAAIsC,GAAG,GAAGvC,MAAM,CAACC,QAAQ,CAACuC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAIC,MAAM,GAAG,IAAI3C,eAAe,CAAC,CAAC;IAElC,IAAI4C,WAAW,GAAGpB,YAAY,GAC1B,IAAIW,IAAI,CAACX,YAAY,CAAC,CAACqB,WAAW,CAAC,CAAC,GACpC,IAAIV,IAAI,CAACvB,eAAe,CAAC,CAACiC,WAAW,CAAC,CAAC;IAC3C,IAAIC,IAAI,GAAGtB,YAAY,GAAGE,YAAY,CAAC,OAAO,CAAC,GAAGlB,eAAe,CAAC,OAAO,CAAC;IAC1E,IAAIuC,SAAS,GAAGjB,YAAY,CAACkB,GAAG,CAAEC,IAAI,IACpCA,IAAI,CAAC,OAAO,CAAC,CAACC,UAAU,CAAC,GAAG,EAAE,GAAG,CACnC,CAAC;IAEDP,MAAM,CAACQ,MAAM,CAAC,UAAU,EAAElB,mBAAmB,CAACW,WAAW,CAAC,CAAC;IAC3DD,MAAM,CAACQ,MAAM,CAAC,iBAAiB,EAAEL,IAAI,CAAC;IACtCH,MAAM,CAACQ,MAAM,CAAC,WAAW,EAAEJ,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/CZ,GAAG,GAAGvC,MAAM,CAACC,QAAQ,CAACmD,MAAM,GAAC,2BAA2B,GAAGV,MAAM,CAACW,QAAQ,CAAC,GAAG,CAAC;IAC/E,OAAOd,GAAG;EACZ,CAAC;EAED,IAAIe,mBAAmB,GAAG,EAAE;EAE5B,IAAItD,MAAM,CAACC,QAAQ,CAACsD,QAAQ,KAAK,0BAA0B,EAAE;IAC3DD,mBAAmB,GAAG,0BAA0B;EAClD;;EAEA;;EAEAva,SAAS,CAAC,MAAM;IACd,IAAIoX,WAAW,EAAE;MACf,IAAIqD,oBAAoB,GAAG,IAAItB,IAAI,CAACuB,MAAM,CAACtD,WAAW,CAAC,CAAC,CAACyC,WAAW,CAAC,CAAC;MACtEpB,eAAe,CAACgC,oBAAoB,CAAC;IACvC;IAEA,IAAInD,mBAAmB,EAAE;MACvBG,kBAAkB,CAAC;QACjBC,KAAK,EAAEJ,mBAAmB;QAC1BK,KAAK,EAAEjX,SAAS,CAAC4W,mBAAmB;MACtC,CAAC,CAAC;MACFqB,eAAe,CAAC;QACdjB,KAAK,EAAEJ,mBAAmB;QAC1BK,KAAK,EAAEjX,SAAS,CAAC4W,mBAAmB;MACtC,CAAC,CAAC;IACJ;IAEA,IAAIC,YAAY,EAAE;MAChB,IAAIwC,SAAS,GAAGxC,YAAY,CAACmC,KAAK,CAAC,GAAG,CAAC;MACvC,IAAIiB,aAAa,GAAGZ,SAAS,CAACC,GAAG,CAAEC,IAAI,IAAK;QAC1C,OAAO;UAAEvC,KAAK,EAAEuC,IAAI;UAAEtC,KAAK,EAAEjX,SAAS,CAACuZ,IAAI;QAAE,CAAC;MAChD,CAAC,CAAC;MACFlB,eAAe,CAAC4B,aAAa,CAAC;IAChC;EACF,CAAC,EAAE,CAACvD,WAAW,EAAEE,mBAAmB,EAAEC,YAAY,CAAC,CAAC;EAEpD,MAAMqD,eAAe,GAAGA,CAACC,eAAe,GAAG,EAAE,KAAK;IAChD;IACA,IAAIC,iBAAiB,GAAGpC,YAAY,CAACf,KAAK,IAAIH,eAAe,CAAC,OAAO,CAAC;IACtE;IACA,IAAIuD,mBAAmB,GAAG7a,MAAM,CAAC4X,EAAE,CAACU,YAAY,EAAEsC,iBAAiB,CAAC;IACpE,IAAIE,WAAW,GAAGD,mBAAmB,CAACE,KAAK,CAAC,CAAC,CAACnD,EAAE,CAAC+C,eAAe,CAAC;IAEjE,IAAI3a,MAAM,CAAC8a,WAAW,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;MACjC,OAAOF,WAAW;IACpB;IAEAD,mBAAmB,GAAG7a,MAAM,CAAC4X,EAAE,CAACF,eAAe,EAAEkD,iBAAiB,CAAC;IACnEE,WAAW,GAAGD,mBAAmB,CAACE,KAAK,CAAC,CAAC,CAACnD,EAAE,CAAC+C,eAAe,CAAC;IAE7D,IAAI3a,MAAM,CAAC8a,WAAW,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;MACjC,OAAOF,WAAW;IACpB;IACA,OAAO,KAAK;EACd,CAAC;EAED,MAAMG,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,IAAI,GAAG,EAAE;IACbC,MAAM,CAACC,IAAI,CAAC5a,SAAS,CAAC,CACnB6a,IAAI,CAAC,CAAC,CACNvB,GAAG,CAAEtC,KAAK,IACT0D,IAAI,CAACI,IAAI,CAAC;MACR9D,KAAK,EAAE,GAAGA,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;MACtCvC,KAAK,EAAEjX,SAAS,CAACgX,KAAK;IACxB,CAAC,CACH,CAAC;IAEH,OAAO0D,IAAI;EACb,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAC/D,KAAK,GAAG,EAAE,KAAK;IACzC,OAAO2D,MAAM,CAACC,IAAI,CAAC5a,SAAS,CAAC,CAACgb,IAAI,CAAEC,GAAG,IAAKjb,SAAS,CAACib,GAAG,CAAC,KAAKjE,KAAK,CAAC,IAAI,EAAE;EAC7E,CAAC;EAED,MAAMkE,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK;IACpD,MAAMC,GAAG,GAAG7b,MAAM,CAAC,CAAC,CAAC8b,GAAG,CAAC,CAAC;IAC1B,MAAMC,KAAK,GAAGF,GAAG,CAACd,KAAK,CAAC,CAAC,CAACnD,EAAE,CAAC+D,SAAS,CAAC;IACvC,MAAMK,KAAK,GAAGH,GAAG,CAACd,KAAK,CAAC,CAAC,CAACnD,EAAE,CAACgE,SAAS,CAAC;IACvC,MAAMK,WAAW,GAAG,CAACF,KAAK,CAACG,OAAO,GAAGF,KAAK,CAACE,OAAO,IAAI,EAAE;IAExD,IAAIC,YAAY,GAAGnc,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,GAAExC,YAAY,CAAC,OAAO,CAAC,CAACwB,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG1C,eAAe,CAAC,OAAO,CAAC,CAAC0C,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;IAE7I,IAAIoC,UAAU,GAAG,EAAE;IAEnB,IAAGH,WAAW,GAAG,CAAC,EAAC;MACjBG,UAAU,IAAI,GAAGH,WAAW,GAAC,CAAC,CAAC,sBAAsBE,YAAY,EAAE;IACrE,CAAC,MAAK,IAAGF,WAAW,GAAG,CAAC,EAAC;MACvBG,UAAU,IAAI,GAAGH,WAAW,mBAAmBE,YAAY,EAAE;IAC/D,CAAC,MAAK;MACJC,UAAU,IAAI,gBAAgBD,YAAY,EAAE;IAC9C;IAGA,OAAOC,UAAU;EACnB,CAAC;EAGD,MAAMC,iBAAiB,GAAG,MAAAA,CAAOtC,IAAI,EAAE0B,GAAG,KAAK;IAC7C,IAAI;MAAA,IAAAa,MAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,MAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,MAAA,EAAAC,qBAAA,EAAAC,MAAA,EAAAC,MAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,MAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,IAAIC,IAAI,GAAGrF,IAAI,CAACvC,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;MAC1C,IAAIqF,gBAAgB,GAAG,CAAC,CAAC;MACzB,IAAInE,IAAI,GAAG,CAAC,CAAC;MACb9B,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE+F,IAAI,CAAC;MAEzB,IAAIA,IAAI,IAAInH,kBAAkB,CAACmH,IAAI,CAAC,EAAE;QACpCC,gBAAgB,GAAGpH,kBAAkB,CAACmH,IAAI,CAAC;MAC7C,CAAC,MAAM;QAAA,IAAAE,KAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACL;QACA,MAAMC,eAAe,GAAG,MAAMC,KAAK,CAAC,mBAAmBR,IAAI,YAAY,CAAC;QACxElE,IAAI,GAAG,MAAMyE,eAAe,CAACE,IAAI,CAAC,CAAC;;QAEnC;QACA,MAAMC,OAAO,IAAAR,KAAA,GAAGpE,IAAI,cAAAoE,KAAA,wBAAAC,kBAAA,GAAJD,KAAA,CAAMS,YAAY,cAAAR,kBAAA,wBAAAC,mBAAA,GAAlBD,kBAAA,CAAqB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBM,OAAO,cAAAL,qBAAA,wBAAAC,sBAAA,GAAhCD,qBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuCjI,KAAK;QAC5D2B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEyG,OAAO,CAAC;;QAE5D;QACA,IAAIA,OAAO,EAAE;UACX,MAAME,YAAY,GAAG,MAAMJ,KAAK,CAAC,uCAAuCE,OAAO,EAAE,CAAC;UAClFT,gBAAgB,GAAG,MAAMW,YAAY,CAACH,IAAI,CAAC,CAAC;UAC5CzG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgG,gBAAgB,CAAC;QACxD;MACF;MAEA,IAAIY,OAAO,GAAG;QACZ,GAAGlG,IAAI;QACPqF,IAAI;QACJU,OAAO,EAAE,EAAAxD,MAAA,GAAApB,IAAI,cAAAoB,MAAA,wBAAAC,mBAAA,GAAJD,MAAA,CAAMyD,YAAY,cAAAxD,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyBsD,OAAO,cAAArD,qBAAA,wBAAAC,sBAAA,GAAhCD,qBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuCjF,KAAK,KAAI,EAAE;QAC3DyI,KAAK,EAAE,EAAAvD,MAAA,GAAAzB,IAAI,cAAAyB,MAAA,wBAAAC,mBAAA,GAAJD,MAAA,CAAMoD,YAAY,cAAAnD,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyBsD,MAAM,cAAArD,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCtF,KAAK,KAAI,EAAE;QACxD2I,IAAI,EACF,EAAApD,iBAAA,GAAAqC,gBAAgB,cAAArC,iBAAA,wBAAAC,kBAAA,GAAhBD,iBAAA,CAAmB,CAAC,CAAC,cAAAC,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBoD,KAAK,cAAAnD,qBAAA,uBAA5BA,qBAAA,CAA8BoD,GAAG,OAAAnD,kBAAA,GACjCkC,gBAAgB,cAAAlC,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAArBD,mBAAA,CAAuBiD,KAAK,cAAAhD,qBAAA,uBAA5BA,qBAAA,CAA8BkD,GAAG,KACjC,EAAE;QACJC,OAAO,EAAE,EAAAlD,kBAAA,GAAA+B,gBAAgB,cAAA/B,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAArBD,mBAAA,CAAuBiD,OAAO,cAAAhD,qBAAA,uBAA9BA,qBAAA,CAAgCtD,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;QACzDuG,YAAY,EAAE,EAAAhD,kBAAA,GAAA4B,gBAAgB,cAAA5B,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAArBD,mBAAA,CAAuBgD,IAAI,cAAA/C,qBAAA,uBAA3BA,qBAAA,CAA6BgD,UAAU,KAAI,EAAE;QAC3DC,UAAU,EAAE,EAAAhD,kBAAA,GAAAyB,gBAAgB,cAAAzB,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,uBAArBA,mBAAA,CAAuB+C,UAAU,KAAI,EAAE;QACnDT,MAAM,EAAE,EAAArC,kBAAA,GAAAuB,gBAAgB,cAAAvB,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,uBAArBA,mBAAA,CAAuBoC,MAAM,KAAI,EAAE;QAC3CU,WAAW,EAAE,EAAA7C,kBAAA,GAAAqB,gBAAgB,cAAArB,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,uBAArBA,mBAAA,CAAuB4C,WAAW,KAAI,EAAE;QACrD;QACAhH,SAAS,EAAE,EAAAqE,kBAAA,GAAAmB,gBAAgB,cAAAnB,kBAAA,wBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,wBAAAC,qBAAA,GAArBD,mBAAA,CAAuBtE,SAAS,cAAAuE,qBAAA,uBAAhCA,qBAAA,CAAmC,CAAC,CAAC,KAAI,EAAE;QACtD0C,SAAS,EACP,CAAAzC,kBAAA,GAAAgB,gBAAgB,cAAAhB,kBAAA,gBAAAC,mBAAA,GAAhBD,kBAAA,CAAmB,CAAC,CAAC,cAAAC,mBAAA,eAArBA,mBAAA,CAAuBwC,SAAS,GAAG3F,MAAM,CAAC4F,MAAM,CAAC1B,gBAAgB,CAAC,CAAC,CAAC,CAACyB,SAAS,CAAC,CAAC5G,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QACjG8G,OAAO,EAAE,EAAAzC,MAAA,GAAArD,IAAI,cAAAqD,MAAA,wBAAAC,qBAAA,GAAJD,MAAA,CAAM0C,iBAAiB,cAAAzC,qBAAA,uBAAvBA,qBAAA,CAA0B,CAAC,CAAC,KAAI,EAAE;QAC3C0C,YAAY,EAAE,EAAAzC,MAAA,GAAAvD,IAAI,cAAAuD,MAAA,uBAAJA,MAAA,CAAMuC,OAAO,KAAI,EAAE;QACjCG,SAAS,EAAE,EAAAzC,MAAA,GAAAxD,IAAI,cAAAwD,MAAA,wBAAAC,qBAAA,GAAJD,MAAA,CAAMuC,iBAAiB,cAAAtC,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BwC,WAAW,cAAAvC,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA4C,CAAC,CAAC,cAAAC,sBAAA,uBAA9CA,sBAAA,CAAgDrH,KAAK,KAAI,EAAE;QACtE4J,WAAW,EAAE,EAAAtC,MAAA,GAAA7D,IAAI,cAAA6D,MAAA,wBAAAC,qBAAA,GAAJD,MAAA,CAAMkC,iBAAiB,cAAAjC,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BqC,cAAc,cAAApC,sBAAA,wBAAAC,sBAAA,GAA5CD,sBAAA,CAA+C,CAAC,CAAC,cAAAC,sBAAA,uBAAjDA,sBAAA,CAAmD1H,KAAK,KAAI,EAAE;QAC3E8J,cAAc,EAAErG,IAAI;QACpBsG,kBAAkB,EAAEnC;MACtB,CAAC;;MAED;MACAxG,eAAe,CAAC4I,QAAQ,IAAI;QAC1B,MAAMC,QAAQ,GAAG,CAAC,GAAGD,QAAQ,CAAC;QAC9BC,QAAQ,CAACjG,GAAG,CAAC,GAAGwE,OAAO;QACvB,OAAOyB,QAAQ;MACjB,CAAC,CAAC;MAEF,OAAOzB,OAAO;IAChB,CAAC,CAAC,OAAOpI,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;MAEjD;MACA,MAAM8J,YAAY,GAAG;QACnB,GAAG5H,IAAI;QACPqF,IAAI,EAAErF,IAAI,CAACvC,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;QACrC8F,OAAO,EAAE,SAAS;QAClBI,KAAK,EAAE,SAAS;QAChBE,IAAI,EAAE,EAAE;QACRI,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,EAAE;QAChBG,UAAU,EAAE,EAAE;QACdT,MAAM,EAAE,SAAS;QACjBU,WAAW,EAAE,SAAS;QACtBhH,SAAS,EAAE,EAAE;QACbiH,SAAS,EAAE,SAAS;QACpBE,OAAO,EAAE,CAAC,CAAC;QACXE,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE,SAAS;QACpBE,WAAW,EAAE,EAAE;QACfE,cAAc,EAAE,CAAC,CAAC;QAClBC,kBAAkB,EAAE,CAAC;MACvB,CAAC;;MAED;MACA3I,eAAe,CAAC4I,QAAQ,IAAI;QAC1B,MAAMC,QAAQ,GAAG,CAAC,GAAGD,QAAQ,CAAC;QAC9BC,QAAQ,CAACjG,GAAG,CAAC,GAAGkG,YAAY;QAC5B,OAAOD,QAAQ;MACjB,CAAC,CAAC;MAEF,OAAOC,YAAY;IACrB;EACF,CAAC;EAID,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC/BnJ,YAAY,CAACkB,GAAG,CAAC,OAAOC,IAAI,EAAE0B,GAAG,KAAK;QACpC,IAAI;UAAA,IAAAuG,MAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,OAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,OAAA,EAAAC,qBAAA,EAAAC,OAAA,EAAAC,OAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,OAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UACF,IAAIzF,IAAI,GAAGrF,IAAI,CAACvC,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;UAC1C,IAAIqF,gBAAgB,GAAG,CAAC,CAAC;UACzB,IAAInE,IAAI,GAAG,CAAC,CAAC;UAEb,IAAIkE,IAAI,IAAInH,kBAAkB,CAACmH,IAAI,CAAC,EAAE;YACpCC,gBAAgB,GAAGpH,kBAAkB,CAACmH,IAAI,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAA0F,MAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACL,MAAMC,QAAQ,GAAG,MAAMvF,KAAK,CAAC,mBAAmBR,IAAI,YAAY,CAAC;YACjElE,IAAI,GAAG,MAAMiK,QAAQ,CAACtF,IAAI,CAAC,CAAC;YAE5B,IAAIC,OAAO,IAAAgF,MAAA,GAAG5J,IAAI,cAAA4J,MAAA,wBAAAC,mBAAA,GAAJD,MAAA,CAAM/E,YAAY,cAAAgF,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyBlF,OAAO,cAAAmF,qBAAA,wBAAAC,sBAAA,GAAhCD,qBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuCzN,KAAK;YAE1D,IAAIqI,OAAO,EAAE;cACX,MAAME,YAAY,GAAG,MAAMJ,KAAK,CAC9B,uCAAuCE,OAAO,EAChD,CAAC;cACDT,gBAAgB,GAAG,MAAMW,YAAY,CAACH,IAAI,CAAC,CAAC;YAC9C;UACF;UAEJ,IAAII,OAAO,GAAG;YACZ,GAAGlG,IAAI;YACPqF,IAAI;YACJU,OAAO,EAAE,EAAAkC,MAAA,GAAA9G,IAAI,cAAA8G,MAAA,wBAAAC,mBAAA,GAAJD,MAAA,CAAMjC,YAAY,cAAAkC,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyBpC,OAAO,cAAAqC,qBAAA,wBAAAC,sBAAA,GAAhCD,qBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuC3K,KAAK,KAAI,EAAE;YAC3DyI,KAAK,EAAE,EAAAmC,OAAA,GAAAnH,IAAI,cAAAmH,OAAA,wBAAAC,oBAAA,GAAJD,OAAA,CAAMtC,YAAY,cAAAuC,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAqB,CAAC,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBpC,MAAM,cAAAqC,sBAAA,wBAAAC,sBAAA,GAA/BD,sBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsChL,KAAK,KAAI,EAAE;YACxD2I,IAAI,EACF,EAAAsC,mBAAA,GAAArD,gBAAgB,cAAAqD,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuBtC,KAAK,cAAAuC,qBAAA,uBAA5BA,qBAAA,CAA8BtC,GAAG,OAAAuC,mBAAA,GACjCxD,gBAAgB,cAAAwD,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuBzC,KAAK,cAAA0C,qBAAA,uBAA5BA,qBAAA,CAA8BxC,GAAG,KACjC,EAAE;YACJC,OAAO,EAAE,EAAAwC,mBAAA,GAAA3D,gBAAgB,cAAA2D,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuBzC,OAAO,cAAA0C,qBAAA,uBAA9BA,qBAAA,CAAgChJ,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE;YACzDuG,YAAY,EAAE,EAAA0C,mBAAA,GAAA9D,gBAAgB,cAAA8D,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuB1C,IAAI,cAAA2C,qBAAA,uBAA3BA,qBAAA,CAA6B1C,UAAU,KAAI,EAAE;YAC3DC,UAAU,EAAE,EAAA0C,mBAAA,GAAAjE,gBAAgB,cAAAiE,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,uBAArBA,oBAAA,CAAuB3C,UAAU,KAAI,EAAE;YACnDT,MAAM,EAAE,EAAAqD,mBAAA,GAAAnE,gBAAgB,cAAAmE,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,uBAArBA,oBAAA,CAAuBtD,MAAM,KAAI,EAAE;YAC3CU,WAAW,EAAE,EAAA6C,mBAAA,GAAArE,gBAAgB,cAAAqE,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,uBAArBA,oBAAA,CAAuB9C,WAAW,KAAI,EAAE;YACrD;YACAhH,SAAS,EAAE,EAAA+J,mBAAA,GAAAvE,gBAAgB,cAAAuE,mBAAA,wBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAArBD,oBAAA,CAAuBhK,SAAS,cAAAiK,qBAAA,uBAAhCA,qBAAA,CAAmC,CAAC,CAAC,KAAI,EAAE;YACtDhD,SAAS,EACP,CAAAiD,mBAAA,GAAA1E,gBAAgB,cAAA0E,mBAAA,gBAAAC,oBAAA,GAAhBD,mBAAA,CAAmB,CAAC,CAAC,cAAAC,oBAAA,eAArBA,oBAAA,CAAuBlD,SAAS,GAAG3F,MAAM,CAAC4F,MAAM,CAAC1B,gBAAgB,CAAC,CAAC,CAAC,CAACyB,SAAS,CAAC,CAAC5G,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACjG8G,OAAO,EAAE,EAAAiD,OAAA,GAAA/I,IAAI,cAAA+I,OAAA,wBAAAC,qBAAA,GAAJD,OAAA,CAAMhD,iBAAiB,cAAAiD,qBAAA,uBAAvBA,qBAAA,CAA0B,CAAC,CAAC,KAAI,EAAE;YAC3ChD,YAAY,EAAE,EAAAiD,OAAA,GAAAjJ,IAAI,cAAAiJ,OAAA,uBAAJA,OAAA,CAAMnD,OAAO,KAAI,EAAE;YACjCG,SAAS,EAAE,EAAAiD,OAAA,GAAAlJ,IAAI,cAAAkJ,OAAA,wBAAAC,qBAAA,GAAJD,OAAA,CAAMnD,iBAAiB,cAAAoD,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BlD,WAAW,cAAAmD,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA4C,CAAC,CAAC,cAAAC,sBAAA,uBAA9CA,sBAAA,CAAgD/M,KAAK,KAAI,EAAE;YACtE4J,WAAW,EAAE,EAAAoD,OAAA,GAAAvJ,IAAI,cAAAuJ,OAAA,wBAAAC,qBAAA,GAAJD,OAAA,CAAMxD,iBAAiB,cAAAyD,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAA0B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BrD,cAAc,cAAAsD,sBAAA,wBAAAC,sBAAA,GAA5CD,sBAAA,CAA+C,CAAC,CAAC,cAAAC,sBAAA,uBAAjDA,sBAAA,CAAmDpN,KAAK,KAAI,EAAE;YAC3E8J,cAAc,EAAErG,IAAI;YACpBsG,kBAAkB,EAAEnC;UACtB,CAAC;UAEG,OAAOY,OAAO;QAChB,CAAC,CAAC,OAAOpI,KAAK,EAAE;UACduB,OAAO,CAACvB,KAAK,CAAC,2BAA2BkC,IAAI,CAACvC,KAAK,GAAG,EAAEK,KAAK,CAAC;UAC9D;UACA,OAAO;YACL,GAAGkC,IAAI;YACPqF,IAAI,EAAErF,IAAI,CAACvC,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC;YACrC8F,OAAO,EAAE,SAAS;YAClBI,KAAK,EAAE,SAAS;YAChBE,IAAI,EAAE,EAAE;YACRI,OAAO,EAAE,SAAS;YAClBC,YAAY,EAAE,EAAE;YAChBG,UAAU,EAAE,EAAE;YACdT,MAAM,EAAE,SAAS;YACjBU,WAAW,EAAE,SAAS;YACtBhH,SAAS,EAAE,EAAE;YACbiH,SAAS,EAAE,SAAS;YACpBE,OAAO,EAAE,CAAC,CAAC;YACXE,YAAY,EAAE,EAAE;YAChBC,SAAS,EAAE,SAAS;YACpBE,WAAW,EAAE,EAAE;YACfE,cAAc,EAAE,CAAC,CAAC;YAClBC,kBAAkB,EAAE,CAAC;UACvB,CAAC;QACH;;QAEA;MACF,CAAC,CACH,CAAC;;MAED;MACA;MACA;;MAEA;;MAEAxJ,UAAU,CAAC,KAAK,CAAC;MACjBa,eAAe,CAACgJ,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOhK,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlY,SAAS,CAAC,MAAM;IACd8hB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvhB,OAAA,CAAAE,SAAA;IAAA6kB,QAAA,eAEI/kB,OAAA;MACEglB,SAAS,EACP,2CAA2C,GAAGhL,mBAC/C;MAAA+K,QAAA,gBAGD/kB,OAAA;QAAKglB,SAAS,EAAC,mDAAmD;QAAAD,QAAA,eAChE/kB,OAAA;UAAKglB,SAAS,EAAC,kBAAkB;UAAAD,QAAA,eAC/B/kB,OAAA;YAAIglB,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENplB,OAAA;QAAKglB,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBAEhE/kB,OAAA;UAAKglB,SAAS,EAAC,kGAAkG;UAAAD,QAAA,eAC/G/kB,OAAA,CAACH,eAAe;YAAAolB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAENplB,OAAA;UAAKglB,SAAS,EAAC,sFAAsF;UAAAD,QAAA,eACnG/kB,OAAA;YAAMglB,SAAS,EAAC,WAAW;YAAAD,QAAA,eACzB/kB,OAAA;cAAKglB,SAAS,EAAC,4BAA4B;cAAAD,QAAA,gBACzC/kB,OAAA;gBAAKglB,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACpC/kB,OAAA;kBACEqlB,OAAO,EAAC,YAAY;kBACpBL,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EACzE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRplB,OAAA;kBAAKglB,SAAS,EAAC,UAAU;kBAAAD,QAAA,eACvB/kB,OAAA;oBACEslB,IAAI,EAAC,gBAAgB;oBACrBC,EAAE,EAAC,YAAY;oBACfC,QAAQ,EAAE9N,OAAQ;oBAClBN,KAAK,EAAE,CAACa,YAAY,IAAI,EAAE,EAAE8B,QAAQ,CAAC,CAAC,CAAC0L,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE;oBACxDC,QAAQ,EAAGC,CAAC,IAAK;sBACfzN,eAAe,CAACyN,CAAC,CAACC,MAAM,CAACxO,KAAK,CAAC;oBACjC,CAAE;oBACFyO,QAAQ;oBACRb,SAAS,EAAC;kBAA8I;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENplB,OAAA;gBAAKglB,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,eAChD/kB,OAAA;kBAAKglB,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,gBACnC/kB,OAAA;oBACEqlB,OAAO,EAAC,MAAM;oBACdL,SAAS,EAAC,8CAA8C;oBAAAD,QAAA,EACzD;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRplB,OAAA,CAACJ,MAAM;oBACLkmB,WAAW,EAAE,KAAM;oBACnBC,YAAY,EAAE,IAAK;oBACnBC,UAAU,EAAEtO,OAAQ;oBACpBuO,OAAO,EAAE,KAAM;oBACfJ,QAAQ,EAAE,KAAM;oBAChBK,WAAW,EAAC,iBAAiB;oBAC7BC,IAAI,EAAC,cAAc;oBACnB/O,KAAK,EAAEe,YAAa;oBACpBiO,OAAO,EAAExL,wBAAwB,CAAC,CAAE;oBACpC8K,QAAQ,EAAGhM,IAAI,IAAK;sBAClBtB,eAAe,CAACsB,IAAI,CAAC;oBACvB;kBAAE;oBAAAuL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNplB,OAAA;UAAKglB,SAAS,EAAC,qGAAqG;UAAAD,QAAA,EACjH1M,UAAU,CAACjB,KAAK,IAAIiD,eAAe,CAAChC,UAAU,CAACjB,KAAK,CAAC,iBACpDpX,OAAA,CAAAE,SAAA;YAAA6kB,QAAA,gBACE/kB,OAAA;cAAKglB,SAAS,EAAC,uBAAuB;cAAAD,QAAA,GACnCplB,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC7BxC,YAAY,CAAC,OAAO,CAAC,CAACwB,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,EAC3C,CAACha,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC9B1D,eAAe,CAAC,OAAO,CAAC,CAAC0C,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,eAC/C3Z,OAAA;gBAAAilB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACLzlB,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAAIxC,YAAY,CAAC,OAAO,CAAC,EACvD,CAACxY,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC9B1D,eAAe,CAAC,OAAO,CAAC;YAAA;cAAAgO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAENplB,OAAA;cAAIglB,SAAS,EAAC,uBAAuB;cAAAD,QAAA,GAClCplB,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC7Bhb,MAAM,CAACsY,YAAY,CAAC,CAACoO,MAAM,CAACnQ,iBAAiB,CAAC,EAC/C,CAACvW,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC9Bhb,MAAM,CAAC0X,eAAe,CAAC,CAACsD,OAAO,CAAC,CAAC,IACjChb,MAAM,CAAC0X,eAAe,CAAC,CAACgP,MAAM,CAACnQ,iBAAiB,CAAC;YAAA;cAAA+O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACLplB,OAAA;cAAKglB,SAAS,EAAC,eAAe;cAAAD,QAAA,GAC3BplB,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC7Bhb,MAAM,CAACsY,YAAY,CAAC,CAACoO,MAAM,CAACpQ,iBAAiB,CAAC,EAC/C,CAACtW,MAAM,CAACsY,YAAY,CAAC,CAAC0C,OAAO,CAAC,CAAC,IAC9Bhb,MAAM,CAAC0X,eAAe,CAAC,CAACsD,OAAO,CAAC,CAAC,IACjChb,MAAM,CAAC0X,eAAe,CAAC,CAACgP,MAAM,CAACpQ,iBAAiB,CAAC;YAAA;cAAAgP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA,eACN;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENplB,OAAA;QAAKglB,SAAS,EAAC,wDAAwD;QAAAD,QAAA,eACrE/kB,OAAA;UAAKglB,SAAS,EAAC,oBAAoB;UAAAD,QAAA,eACjC/kB,OAAA;YAAKglB,SAAS,EAAC,yNAAyN;YAAAD,QAAA,gBACtO/kB,OAAA;cAAKglB,SAAS,EAAC,0DAA0D;cAAAD,QAAA,eAEvE/kB,OAAA,CAACJ,MAAM;gBACL0mB,iBAAiB,EAAE,KAAM;gBACzBR,WAAW,EAAE,IAAK;gBAClBC,YAAY,EAAE,IAAK;gBACnBC,UAAU,EAAEtO,OAAQ;gBACpBuO,OAAO,EAAE,IAAK;gBACdJ,QAAQ,EAAE,KAAM;gBAChBK,WAAW,EAAC,kBAAkB;gBAC9BC,IAAI,EAAC,cAAc;gBACnB/O,KAAK,EAAEmB,YAAa;gBACpB6N,OAAO,EAAExL,wBAAwB,CAAC,CAAE;gBACpC8K,QAAQ,EAAGhM,IAAI,IAAK;kBAClBlB,eAAe,CAACkB,IAAI,CAAC;;kBAErB;kBACA,IAAIA,IAAI,IAAIA,IAAI,CAAC6M,MAAM,GAAG,CAAC,EAAE;oBAC3B7M,IAAI,CAAC8M,OAAO,CAAC,CAACzH,IAAI,EAAE3D,GAAG,KAAK;sBAC1B,IAAI,CAAC2D,IAAI,CAACU,OAAO,IAAIV,IAAI,CAAC5H,KAAK,EAAE;wBAC/B;wBACAsP,UAAU,CAAC,MAAMzK,iBAAiB,CAAC+C,IAAI,EAAE3D,GAAG,CAAC,EAAE,GAAG,CAAC;sBACrD;oBACF,CAAC,CAAC;kBACJ;gBACF;cAAE;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNplB,OAAA;cACEwlB,QAAQ,EAAE9N,OAAO,IAAKK,YAAY,KAAK,QAAU;cACjD2O,OAAO,EAAEA,CAAA,KAAM;gBACbC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/N,gBAAgB,CAAC,CAAC,CAAC;gBACjDd,eAAe,CAAC,QAAQ,CAAC;gBACzByO,UAAU,CAAC,YAAW;kBAAEzO,eAAe,CAAC,OAAO,CAAC;gBAAE,CAAC,EAAE,IAAI,CAAC;cAC5D,CAAE;cACF4N,MAAM,EAAC,QAAQ;cACfZ,SAAS,EAAC,mMAAmM;cAAAD,QAAA,EAE5MhN;YAAY;cAAAkN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL1N,OAAO,iBAAI1X,OAAA,CAAAE,SAAA;QAAA6kB,QAAA,EAAE;MAAU,gBAAE,CAAC,EAC1B,CAACrN,OAAO,iBACT1X,OAAA;QAAKglB,SAAS,EAAC,mDAAmD;QAAC8B,KAAK,EAAE3Q,UAAW;QAAA4O,QAAA,EAMlFxM,YAAY,CAACgO,MAAM,GAAG,CAAC,IACtBhO,YAAY,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAE0B,GAAG,KAAK;UAAA,IAAA2L,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,oBAAA;UAE9B;UACA,IAAI,CAAC3O,IAAI,CAAC+F,OAAO,EAAE;YACjBzD,iBAAiB,CAACtC,IAAI,EAAE0B,GAAG,CAAC;UAC9B;UAAC;;UAED;UACA,IAAIjE,KAAK,GAAGuC,IAAI,CAAC,OAAO,CAAC;UACzB,IAAI4O,QAAQ,GAAG5O,IAAI,CAAC,OAAO,CAAC;UAE5B,oBACE1Z,OAAA;YAEEglB,SAAS,EAAC,qBAAqB;YAAAD,QAAA,eAE/B/kB,OAAA;cAAKglB,SAAS,EAAC,sGAAsG;cAAAD,QAAA,gBACnH/kB,OAAA;gBACEglB,SAAS,EAAC,4DAA4D;gBACtE0B,OAAO,EAAEA,CAAA,KACP,CAAAhN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,YAAY,KAClB1J,MAAM,CAAC6R,IAAI,CAAC7O,IAAI,CAAC0G,YAAY,EAAE,QAAQ,CACxC;gBAAA2E,QAAA,gBAED/kB,OAAA;kBAAKglB,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAC3DrL,IAAI,CAACqG,IAAI,gBACR/f,OAAA;oBACEwoB,GAAG,EAAE9O,IAAI,CAACqG,IAAK;oBACf0I,GAAG,EAAE/O,IAAI,CAAC+F,OAAO,IAAItI,KAAM;oBAC3B6N,SAAS,EAAC;kBAA6D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,gBAEFplB,OAAA;oBAAKglB,SAAS,EAAC,2GAA2G;oBAAAD,QAAA,EAAC;kBAE3H;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNplB,OAAA;kBAAKglB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,EAAErL,IAAI,CAAC+F,OAAO,IAAI;gBAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvEplB,OAAA;kBAAKglB,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,EAC/B5N,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GACvBxC,KAAK,CAACwC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,GAC1BxC;gBAAK;kBAAA8N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNplB,OAAA;gBAAKglB,SAAS,EAAC,QAAQ;gBAAAD,QAAA,GACpBrL,IAAI,CAAC8G,WAAW,gBACfxgB,OAAA;kBAAKglB,SAAS,EAAC,UAAU;kBAAAD,QAAA,GAAC,iBAAe,EAACrL,IAAI,CAAC8G,WAAW;gBAAA;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEjEplB,OAAA;kBAAKglB,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACvE,EACA1L,IAAI,CAACyG,OAAO,gBACXngB,OAAA;kBAAKglB,SAAS,EAAC,UAAU;kBAAAD,QAAA,GAAC,WAAS,EAACrL,IAAI,CAACyG,OAAO;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEvDplB,OAAA;kBAAKglB,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACjE,EACA1L,IAAI,CAAC+G,SAAS,gBACbzgB,OAAA;kBAAKglB,SAAS,EAAC,UAAU;kBAAAD,QAAA,GAAC,YAAU,EAACrL,IAAI,CAAC+G,SAAS;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAE1DplB,OAAA;kBAAKglB,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAClE,EACA1L,IAAI,CAACmG,KAAK,gBACT7f,OAAA;kBAAKglB,SAAS,EAAC,UAAU;kBAAAD,QAAA,GAAC,cAAY,EAACrL,IAAI,CAACmG,KAAK;gBAAA;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAExDplB,OAAA;kBAAKglB,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACpE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eACNplB,OAAA;gBAAKglB,SAAS,EAAC,SAAS;gBAAAD,QAAA,GACrB,CAAAgC,aAAA,GAAArN,IAAI,CAACiH,OAAO,cAAAoG,aAAA,eAAZA,aAAA,CAAc2B,QAAQ,gBACrB1oB,OAAA;kBAAA+kB,QAAA,GAAK,YAAU,EAACrL,IAAI,CAACiH,OAAO,CAAC+H,QAAQ,EAAC,GAAC;gBAAA;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAE7CplB,OAAA;kBAAKglB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACzD,EACA,CAAA4B,cAAA,GAAAtN,IAAI,CAACiH,OAAO,cAAAqG,cAAA,eAAZA,cAAA,CAAc2B,UAAU,gBACvB3oB,OAAA;kBAAA+kB,QAAA,GAAK,cAAY,EAACrL,IAAI,CAACiH,OAAO,CAACgI,UAAU,EAAC,IAAE;gBAAA;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAElDplB,OAAA;kBAAKglB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAC3D,EACA,CAAA6B,cAAA,GAAAvN,IAAI,CAACiH,OAAO,cAAAsG,cAAA,eAAZA,cAAA,CAAc2B,aAAa,gBAC1B5oB,OAAA;kBAAA+kB,QAAA,GAAK,cAAY,EAACrL,IAAI,CAACiH,OAAO,CAACiI,aAAa,EAAC,MAAI;gBAAA;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAEvDplB,OAAA;kBAAKglB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAC3D,EACA1L,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoH,SAAS,gBACd9gB,OAAA;kBAAA+kB,QAAA,GAAK,aAAW,EAACrL,IAAI,CAACoH,SAAS;gBAAA;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEtCplB,OAAA;kBAAKglB,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAC1D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNplB,OAAA;gBAAKglB,SAAS,EAAC,aAAa;gBAAAD,QAAA,GACzB,CAAArL,IAAI,aAAJA,IAAI,wBAAAwN,kBAAA,GAAJxN,IAAI,CAAEmH,YAAY,cAAAqG,kBAAA,uBAAlBA,kBAAA,CAAqB,CAAC,CAAC,kBACtBlnB,OAAA;kBAAKglB,SAAS,EAAC,GAAG;kBAAAD,QAAA,GAAC,YACP,GAAAoC,mBAAA,GAACzN,IAAI,CAACmH,YAAY,CAAC,CAAC,CAAC,cAAAsG,mBAAA,uBAApBA,mBAAA,CAAsB0B,OAAO,EAAC,GAC3C;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,EACA,CAAA1L,IAAI,aAAJA,IAAI,wBAAA0N,mBAAA,GAAJ1N,IAAI,CAAEmH,YAAY,cAAAuG,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyByB,SAAS,cAAAxB,qBAAA,uBAAlCA,qBAAA,CAAqC,CAAC,CAAC,kBACtCtnB,OAAA;kBAAKglB,SAAS,EAAC,GAAG;kBAAAD,QAAA,GAAC,eACJ,EAAC,GAAG,GAAAwC,oBAAA,GAChB7N,IAAI,CAACmH,YAAY,CAAC,CAAC,CAAC,cAAA0G,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBuB,SAAS,cAAAtB,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCsB,MAAM;gBAAA;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CACN,EACA,CAAA1L,IAAI,aAAJA,IAAI,wBAAAgO,mBAAA,GAAJhO,IAAI,CAAEmH,YAAY,cAAA6G,mBAAA,wBAAAC,oBAAA,GAAlBD,mBAAA,CAAqB,CAAC,CAAC,cAAAC,oBAAA,wBAAAC,qBAAA,GAAvBD,oBAAA,CAAyBmB,SAAS,cAAAlB,qBAAA,uBAAlCA,qBAAA,CAAqC,CAAC,CAAC,kBACtC5nB,OAAA;kBAAKglB,SAAS,EAAC,GAAG;kBAAAD,QAAA,GAAC,mBACA,EAAC,GAAG,GAAA8C,oBAAA,GACpBnO,IAAI,CAACmH,YAAY,CAAC,CAAC,CAAC,cAAAgH,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBiB,SAAS,cAAAhB,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCiB,OAAO;gBAAA;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENplB,OAAA;gBAAKglB,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EACxD,CAAArL,IAAI,aAAJA,IAAI,wBAAAsO,cAAA,GAAJtO,IAAI,CAAEiH,OAAO,cAAAqH,cAAA,uBAAbA,cAAA,CAAeiB,MAAM,kBACpBjpB,OAAA;kBAAKglB,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,GAC1CrL,IAAI,CAACiH,OAAO,CAACsI,MAAM,eACpBjpB,OAAA;oBAAA+kB,QAAA,EAAK;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNplB,OAAA;gBAAKglB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,gBAC3D/kB,OAAA;kBAAA+kB,QAAA,GAAK,cACS,EAACrL,IAAI,aAAJA,IAAI,wBAAAuO,cAAA,GAAJvO,IAAI,CAAEiH,OAAO,cAAAsH,cAAA,uBAAbA,cAAA,CAAeiB,UAAU,eACtClpB,OAAA;oBAAA+kB,QAAA,EAAK;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,EACL,CAAA1L,IAAI,aAAJA,IAAI,wBAAAwO,mBAAA,GAAJxO,IAAI,CAAEmH,YAAY,cAAAqH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC,kBACtBloB,OAAA;kBAAKglB,SAAS,EAAC,GAAG;kBAAAD,QAAA,GAAC,YACP,EAACrL,IAAI,aAAJA,IAAI,wBAAAyO,oBAAA,GAAJzO,IAAI,CAAEmH,YAAY,CAAC,CAAC,CAAC,cAAAsH,oBAAA,uBAArBA,oBAAA,CAAuBgB,QAAQ,eAC1CnpB,OAAA;oBAAA+kB,QAAA,EAAK;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACN,EACA,CAAA1L,IAAI,aAAJA,IAAI,wBAAA0O,mBAAA,GAAJ1O,IAAI,CAAEmH,YAAY,cAAAuH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC,kBACtBpoB,OAAA;kBAAKglB,SAAS,EAAC,GAAG;kBAAAD,QAAA,GAAC,YACP,EAACrL,IAAI,aAAJA,IAAI,wBAAA2O,oBAAA,GAAJ3O,IAAI,CAAEmH,YAAY,CAAC,CAAC,CAAC,cAAAwH,oBAAA,uBAArBA,oBAAA,CAAuBe,QAAQ,eAC1CppB,OAAA;oBAAA+kB,QAAA,EAAK;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,eACNplB,OAAA;gBAAKglB,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,gBACpD/kB,OAAA;kBAAKglB,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,EAClC1K,eAAe,CAACiO,QAAQ,CAAC,IACxBjO,eAAe,CAACiO,QAAQ,CAAC,CAACjC,MAAM,CAACnQ,iBAAiB;gBAAC;kBAAA+O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eAENplB,OAAA;kBAAKglB,SAAS,EAAC,WAAW;kBAAAD,QAAA,EACvB1K,eAAe,CAACiO,QAAQ,CAAC,IACxBjO,eAAe,CAACiO,QAAQ,CAAC,CAACjC,MAAM,CAAC,UAAU;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3C,CAAC,EACL,CAAA1L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEF,SAAS,kBACZxZ,OAAA;kBAAKglB,SAAS,EAAC,WAAW;kBAAAD,QAAA,GACvB1J,mBAAmB,CAAC3B,IAAI,CAACtC,KAAK,EAAEH,eAAe,CAAC,OAAO,CAAC,CAAC,eAACjX,OAAA;oBAAAilB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,cACtD,EAAC/K,eAAe,CAACiO,QAAQ,CAAC,IAAIjO,eAAe,CAACiO,QAAQ,CAAC,CAACjC,MAAM,CAAC,GAAG,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA7ID,WAAW,GAAGkD,QAAQ,GAACnR,KAAK;YAAA8N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8I9B,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC,gBAER,CAAC;AAEP;AAACpP,EAAA,CA7sBQD,eAAe;AAAAsT,EAAA,GAAftT,eAAe;AA+sBxB,eAAeA,eAAe;AAAC,IAAAsT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}