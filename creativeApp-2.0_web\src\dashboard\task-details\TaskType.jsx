import React from 'react';
import TableLayoutWrapper2 from './../../common/table/TableLayoutWrapper2';
import TableHeader from './../../common/table/TableHeader';
import TablePagination from './../../common/table/TablePagination';
import TaskTypeList from '../../pages/task-details//tasktype/TaskTypeList';

const TaskType = () => {
  return (
    <div className='bg-gray-50 dark:bg-gray-900 rounded-xl'>
      <TableLayoutWrapper2>
        <TableHeader routeName="/add-task-type" buttonName="Add Task Type" /> 
        <TaskTypeList /> 
        <TablePagination />
      </TableLayoutWrapper2>
    </div>
  );
};

export default TaskType;
