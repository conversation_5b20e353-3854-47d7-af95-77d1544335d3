{"__meta": {"id": "X9ba141a896a08347aeeeed867515ebfb", "datetime": "2025-07-11 15:03:05", "utime": **********.117418, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752224584.659363, "end": **********.117426, "duration": 0.45806288719177246, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1752224584.659363, "relative_start": 0, "end": **********.0974, "relative_end": **********.0974, "duration": 0.4380369186401367, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097408, "relative_start": 0.43804502487182617, "end": **********.117427, "relative_end": 1.1920928955078125e-06, "duration": 0.020019054412841797, "duration_str": "20.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20966224, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome", "param_count": null, "params": [], "start": **********.113329, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\resources\\views/welcome.blade.phpwelcome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Fresources%2Fviews%2Fwelcome.blade.php&line=1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#262\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#257 …}\n  file: \"C:\\xampp\\htdocs\\creativeapp\\creativeApp-2.0_server\\routes\\web.php\"\n  line: \"16 to 18\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fcreativeapp%2FcreativeApp-2.0_server%2Froutes%2Fweb.php&line=16\" onclick=\"\">routes/web.php:16-18</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mHUMxUMsAkyCIlCiMaYGeRHyUMIduuraGRuaEnOm", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1534493657 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1534493657\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1577415901 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1577415901\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1297892124 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1297892124\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-724673922 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724673922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-838587896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-838587896\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-831831288 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 11 Jul 2025 09:03:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"459 characters\">XSRF-TOKEN=eyJpdiI6ImwyaUFBTVpHTlc2UkVWUFFuNU1QK2c9PSIsInZhbHVlIjoiSTBQTHA1cWlMMHdYZU9yWG01aEdGMVAxWGc5V3o0aS94NUhvN0prZW5EQnFXKzFhVUtJRVRNcVFJaVNOdkdobVBRMy9CMnBGc3oweENTSUdxYmdkeHUwZVRLMEpqVDRLR0pmdmNKdUZQZmFhM2dNRGw5VGFrMi9Oa0Zrdk5kb2QiLCJtYWMiOiIyMTE0ZmRmNTcxZjUwYjJjYzE5OGZkYTYxZGZlNDkxYTI1ZTY0YTQwNWE3ODM5OTkwMmJhYTg5ODJjZmFkNmI4IiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:03:05 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"478 characters\">creativeapp_session=eyJpdiI6ImxNSXg1UmZmM2Z4MVlFeVRKMjFYcEE9PSIsInZhbHVlIjoiMVorWVRjanYrOXorWFZHYmZIRVQ0UmtsRWxIay9oUGlhL0FvT1hxaDIvU1pYUEFobUQzaURabksrUVFCLzNGcjdTc0t4WWlUWUhTcmFCSE5SNUoyMWpjSDRObFRPQVE2Mm4rekNMS2JFcEtETGNCK21CblI5WW9aS3JpYmw4enUiLCJtYWMiOiI5NWM4N2FiMDQ2MDI4NTA5ZDExNWUxNmM5MzU0ZmJmZjk2MjVkYjM2NjYxYTRjN2YwNDE0OGIxZDI4ZGQwOWJkIiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:03:05 GMT; Max-Age=7200; path=/; domain=.creativeapp.sebpo.net; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"431 characters\">XSRF-TOKEN=eyJpdiI6ImwyaUFBTVpHTlc2UkVWUFFuNU1QK2c9PSIsInZhbHVlIjoiSTBQTHA1cWlMMHdYZU9yWG01aEdGMVAxWGc5V3o0aS94NUhvN0prZW5EQnFXKzFhVUtJRVRNcVFJaVNOdkdobVBRMy9CMnBGc3oweENTSUdxYmdkeHUwZVRLMEpqVDRLR0pmdmNKdUZQZmFhM2dNRGw5VGFrMi9Oa0Zrdk5kb2QiLCJtYWMiOiIyMTE0ZmRmNTcxZjUwYjJjYzE5OGZkYTYxZGZlNDkxYTI1ZTY0YTQwNWE3ODM5OTkwMmJhYTg5ODJjZmFkNmI4IiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:03:05 GMT; domain=.creativeapp.sebpo.net; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"450 characters\">creativeapp_session=eyJpdiI6ImxNSXg1UmZmM2Z4MVlFeVRKMjFYcEE9PSIsInZhbHVlIjoiMVorWVRjanYrOXorWFZHYmZIRVQ0UmtsRWxIay9oUGlhL0FvT1hxaDIvU1pYUEFobUQzaURabksrUVFCLzNGcjdTc0t4WWlUWUhTcmFCSE5SNUoyMWpjSDRObFRPQVE2Mm4rekNMS2JFcEtETGNCK21CblI5WW9aS3JpYmw4enUiLCJtYWMiOiI5NWM4N2FiMDQ2MDI4NTA5ZDExNWUxNmM5MzU0ZmJmZjk2MjVkYjM2NjYxYTRjN2YwNDE0OGIxZDI4ZGQwOWJkIiwidGFnIjoiIn0%3D; expires=Fri, 11-Jul-2025 11:03:05 GMT; domain=.creativeapp.sebpo.net; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-831831288\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-18874448 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mHUMxUMsAkyCIlCiMaYGeRHyUMIduuraGRuaEnOm</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18874448\", {\"maxDepth\":0})</script>\n"}}